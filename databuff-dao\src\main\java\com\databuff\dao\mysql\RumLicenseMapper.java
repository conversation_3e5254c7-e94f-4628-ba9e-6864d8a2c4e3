package com.databuff.dao.mysql;

import com.databuff.entity.rum.mysql.RumLicense;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface RumLicenseMapper {
    List<RumLicense> getValidLicenses(@Param("currentTime") Long currentTime);

    List<RumLicense> findAll();

    void insertRumLicense(RumLicense rumLicense);
}
