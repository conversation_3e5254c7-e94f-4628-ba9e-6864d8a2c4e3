package com.databuff.common.utils.socket;

import java.io.*;
import java.net.Socket;

public class SocketClient {
    private String host;
    private int port;
    private String encoding;

    public SocketClient(String host, int port, String encoding) {
        this.host = host;
        this.port = port;
        this.encoding = encoding;
    }

    public void sendMessage(String message) {
        try (Socket socket = new Socket(host, port)) {
            // Send data
            OutputStream output = socket.getOutputStream();
            BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(output, encoding));
            writer.write(message);
            writer.newLine();
            writer.flush();

            // Receive data
            InputStream input = socket.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(input, encoding));
            String response = reader.readLine();
            System.out.println("Received from server: " + response);
        } catch (IOException e) {
            System.err.println("Client error: " + e.getMessage());
        }
    }

    public static void main(String[] args) {
        String host = "127.0.0.1"; // Server address
        int port = 12345; // Server port
        String encoding = "UTF-8"; // Encoding format
        String message = "Hello, Server!"; // Message to send

        SocketClient client = new SocketClient(host, port, encoding);
        client.sendMessage(message);
    }
}