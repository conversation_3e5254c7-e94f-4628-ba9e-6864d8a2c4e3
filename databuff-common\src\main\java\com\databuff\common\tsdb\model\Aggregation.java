package com.databuff.common.tsdb.model;

import lombok.Data;

import java.io.Serializable;

@Data
public class Aggregation implements Serializable {
    private AggFun function;
    private String field;
    private String alias;

    // 构造方法
    public Aggregation(AggFun function, String field, String alias) {
        this.function = function;
        this.field = field;
        this.alias = alias;
    }

    public Aggregation(AggFun function, String field) {
        this.function = function;
        this.field = field;
    }

    public Aggregation(String field) {
        this.field = field;
    }

    public Aggregation setAlias(String alias) {
        this.alias = alias;
        return this;
    }

    // 生成 SQL 语法
    public String getAggregationFunction() {
        return getAggregationFunction(this.function.name());
    }

    public String getAggregationFunction(String function) {
        if (field == null || field.trim().isEmpty()) {
            throw new IllegalArgumentException("Field name cannot be null or empty.");
        }

        // 判断字段是否已经被引号包裹
        boolean isAlreadyQuoted = (field.startsWith("\"") && field.endsWith("\"")) ||
                                 (field.startsWith("'") && field.endsWith("'"));

        // 判断字段是否是公式或聚合函数
        boolean isFormulaOrFunction = field.contains("(") || field.contains(")") ||
                                     field.contains("+") || field.contains("-") ||
                                     field.contains("*") || field.contains("/");

        if (function == null || function.trim().isEmpty()) {
            // 如果字段已经被引号包裹或是公式/聚合函数，不需要再次包裹
            if (isAlreadyQuoted || isFormulaOrFunction) {
                return field + formatAlias("");
            } else {
                // 单独的字段名需要用双引号包裹
                return "\"" + field + "\"" + formatAlias("");
            }
        }

        // 如果字段已经被引号包裹或是公式/聚合函数，不需要再次包裹
        if (isAlreadyQuoted || isFormulaOrFunction) {
            return function + "(" + field + ")" + formatAlias("");
        } else {
            return function + "(\"" + field + "\")" + formatAlias("");
        }
    }

    // 处理别名
    public String formatAlias(String defaultAlias) {
        if (alias == null || alias.trim().isEmpty()) {
            return defaultAlias;
        }
        return " AS \"" + alias + "\"";  // 避免别名与 SQL 关键字冲突
    }

    // 工厂方法
    public static Aggregation of(AggFun function, String column) {
        return new Aggregation(function, column);
    }

    public static Aggregation of(AggFun function, String column, String alias) {
        return new Aggregation(function, column, alias);
    }
    public static Aggregation of(String column, String alias) {
        return new Aggregation(null, column, alias);
    }
    public static Aggregation of(String column) {
        return new Aggregation(null, column);
    }

    // 新增拷贝构造函数
    public Aggregation(Aggregation other) {
        this.function = other.function;
        this.field = other.field;
        this.alias = other.alias;
    }
    // 移除 clone() 方法，改为提供 copy() 方法
    public Aggregation copy() {
        return new Aggregation(this);
    }

}
