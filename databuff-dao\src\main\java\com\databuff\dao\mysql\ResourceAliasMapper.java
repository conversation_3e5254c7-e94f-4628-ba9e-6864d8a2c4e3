package com.databuff.dao.mysql;

import com.databuff.entity.ResourceAlias;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Service;

import java.util.List;

@Mapper
public interface ResourceAliasMapper {

    @Delete("delete from dc_resource_alias where service_id = #{serviceId} and resource = #{resource}")
    void delete(@Param("serviceId") String serviceId, @Param("resource") String resource);

    @Insert("insert into dc_resource_alias (service_id, resource, alias) values (#{serviceId}, #{resource}, #{alias})")
    void insert(@Param("serviceId") String serviceId, @Param("resource") String resource, @Param("alias") String alias);

    @Select("select * from dc_resource_alias")
    List<ResourceAlias> selectAll();

    @Select("select * from dc_resource_alias where alias = #{alias}")
    List<ResourceAlias> selectByAlias(@Param("alias") String alias);
}
