package com.databuff.common.utils.email;

import com.databuff.common.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.mail.Address;
import javax.mail.Authenticator;
import javax.mail.BodyPart;
import javax.mail.Multipart;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import javax.mail.internet.MimeUtility;
import java.io.File;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;

/**
 * Created by gaoliang on 2019-05-28 09:26.
 */
@Slf4j
public class MailUtil {

    /**
     * 发送邮件
     * @param toEmail  收件人邮箱地址
     * @param subject 邮件标题
     * @param content  邮件内容 可以是html内容
     */
    public static String send(String toEmail, String subject, String content,String smtpHost, int smtpPort, Boolean useSsl, String user,String password) {
        MailConfig mailConfig = new MailConfig(smtpHost,smtpPort,useSsl,user,password);
        return send(toEmail, subject, content,mailConfig);
    }


    /**
     * 发送邮件 带附件
     * @param toEmails  收件人邮箱地址 多人逗号分隔
     * @param subject  邮件标题
     * @param content  邮件内容 可以是html内容
     * @param attachPath 附件路径
     */
    public static void send(String toEmails, String subject, String content, String attachPath,MailConfig mailConfig) {
        Session session = loadMailSession(mailConfig);

        MimeMessage mm = new MimeMessage(session);
        try {
            //发件人
            mm.setFrom(new InternetAddress(mailConfig.getUser()));
            //内容
            Multipart multipart = new MimeMultipart();
            //body部分
            BodyPart contentPart = new MimeBodyPart();
            contentPart.setContent(content, "text/html;charset=utf-8");
            multipart.addBodyPart(contentPart);

            // 创建Word附件部分
            MimeBodyPart attachmentBodyPart = new MimeBodyPart();
            File wordFile = new File(attachPath);
            DataSource source = new FileDataSource(wordFile);
            attachmentBodyPart.setDataHandler(new DataHandler(source));
            attachmentBodyPart.setFileName(wordFile.getName());
            multipart.addBodyPart(attachmentBodyPart);

            Address[] a = new Address[1];
            a[0] = new InternetAddress(mailConfig.getUser());
            mm.setReplyTo(a);
            List<String> emails = Arrays.asList(toEmails.split(","));

            for(String toEmail: emails){
                // 设置收件人
                InternetAddress to = new InternetAddress(toEmail);
                mm.setRecipient(MimeMessage.RecipientType.TO, to);
                // 设置邮件标题
                mm.setSubject(subject,"UTF-8");
                mm.setContent(multipart);
                // 发送邮件
                Transport.send(mm);
            }
        } catch (Exception e) {
            log.error("error:{}",e);
        }

    }

    /**
     * 发送邮件
     * @param toEmails  收件人邮箱地址 多人逗号分隔
     * @param subject 邮件标题
     * @param content  邮件内容 可以是html内容
     * @param content  邮件内容 可以是html内容
     */
    public static String send(String toEmails, String subject, String content, MailConfig mailConfig) {
        String ret = "成功";
        Session session = loadMailSession(mailConfig);
        // 创建邮件消息
        MimeMessage message = new MimeMessage(session);
        try {
            // 设置发件人
            if (StringUtils.isNotBlank(mailConfig.getNick())){
                //设置自定义发件人昵称
                message.setFrom(new InternetAddress(MimeUtility.encodeText(mailConfig.getNick())+" <"+mailConfig.getUser()+">"));
            }else{
                message.setFrom(new InternetAddress(mailConfig.getUser()));
            }
            Address[] a = new Address[1];
            a[0] = new InternetAddress(mailConfig.getUser());
            message.setReplyTo(a);
            List<String> emails = Arrays.asList(toEmails.split(","));

            for(String toEmail: emails){
                // 设置收件人
                InternetAddress to = new InternetAddress(toEmail);
                message.setRecipient(MimeMessage.RecipientType.TO, to);
                // 设置邮件标题
                message.setSubject(subject,"UTF-8");
                // 设置邮件的内容体
                message.setContent(content, "text/html;charset=UTF-8");
                // 发送邮件
                Transport.send(message);
            }
        } catch (Exception e) {
            log.error("error:{}",e);
            throw new CustomException("邮件发送错误："+e.getMessage());
        }
        return ret;
    }

    private static Session loadMailSession(MailConfig mailConfig) {
        try {
            // 配置发送邮件的环境属性
            final Properties props = new Properties();
            // 表示SMTP发送邮件，需要进行身份验证
            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.host", mailConfig.getSmtpHost());
            if(mailConfig.getUseSsl()){
                // 如果使用ssl，则去掉使用25端口的配置，进行如下配置,
                props.put("mail.smtp.socketFactory.class","javax.net.ssl.SSLSocketFactory");
                //阿里ssl smtp端口 465
                props.put("mail.smtp.socketFactory.port", mailConfig.getSmtpPort());
                //阿里ssl smtp端口 465
                props.put("mail.smtp.port", mailConfig.getSmtpPort());
                props.put("mail.smtp.ssl.checkserveridentity", true);
                props.put("mail.smtp.starttls.enable", true);
            }else {
                //不适用ssl
                props.put("mail.smtp.port", mailConfig.getSmtpPort());
            }

            // 发件人的账号
            props.put("mail.user", mailConfig.getUser());
            // 访问SMTP服务时需要提供的密码
            props.put("mail.password", mailConfig.getPassword());
            // 构建授权信息，用于进行SMTP进行身份验证
            Authenticator authenticator = new Authenticator() {
                @Override
                protected PasswordAuthentication getPasswordAuthentication() {
                    // 用户名、密码
                    String userName = props.getProperty("mail.user");
                    String code =   mailConfig.getCode();
                    String password = props.getProperty("mail.password");
                    return new PasswordAuthentication(userName, StringUtils.isNotBlank(code)?code:password);
                }
            };
            // 使用环境属性和授权信息，创建邮件会话
            return Session.getInstance(props, authenticator);
        } catch (Exception e) {
            log.error("mail session is null:{}",e);
            throw new CustomException("创建邮件会话错误："+e.getMessage());
        }
    }

}

