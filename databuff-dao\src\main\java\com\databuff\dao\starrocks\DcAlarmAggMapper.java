package com.databuff.dao.starrocks;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.databuff.entity.dto.DcAlarmAggregate;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Mapper
@Repository
public interface DcAlarmAggMapper extends BaseMapper<DcAlarmAggregate> {

    List<DcAlarmAggregate> existsByTimeAndIdList(@Param("list") List<String> list);

    int batchInsert(@Param("list") List<DcAlarmAggregate> list);

}
