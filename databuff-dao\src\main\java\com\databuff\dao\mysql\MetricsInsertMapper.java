package com.databuff.dao.mysql;

import com.alibaba.fastjson.JSONObject;
import com.databuff.entity.MetricsInsert;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface MetricsInsertMapper {

    // 查询指标写入列表
    @Select({
            "<script>",
            "SELECT * FROM dc_databuff_metrics_insert",
            "WHERE 1=1",
            "<if test='app != null'>AND app = #{app}</if>",
            "<if test='isOpen != null'>AND isOpen = #{isOpen}</if>",
            "</script>"
    })
    @Results({
            @Result(column = "tags", property = "tags", javaType = JSONObject.class),
    })
    List<MetricsInsert> findByApp(@Param("app") String app, @Param("isOpen") Boolean isOpen);
}
