package com.databuff.common.tsdb.wrapper;

import com.databuff.common.tsdb.model.DatabaseType;

import java.util.Map;

/**
 * 数据库连接包装器接口
 * 定义了与数据库交互的基本方法
 */
public interface DatabaseWrapper {

    /**
     * 获取数据库配置信息
     * @return 配置信息映射
     */
    Map<String, Object> getConfigs();

    /**
     * 获取数据库类型
     * @return 数据库类型
     */
    DatabaseType getDatabaseType();

    /**
     * 关闭数据库连接并释放资源
     */
    void close();

    /**
     * 检查数据库连接是否有效
     * @return true表示连接有效，false表示连接已断开或无效
     */
    boolean isConnected();

    /**
     * 重新连接数据库
     * 当连接失效时调用此方法尝试重新建立连接
     * @return true表示重连成功，false表示重连失败
     */
    boolean reconnect();

    /**
     * 重置连接状态
     * 在激活对象时调用，用于重置任何临时状态
     */
    void resetState();

    /**
     * 清理临时状态
     * 在钝化对象时调用，用于清理临时状态但保持连接打开
     */
    void clearTemporaryState();
}
