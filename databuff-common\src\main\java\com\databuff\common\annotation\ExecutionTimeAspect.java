package com.databuff.common.annotation;

import com.databuff.common.threadLocal.NowTimeThreadLocal;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class ExecutionTimeAspect {

    /**
     * 环绕通知，记录方法执行时间
     *
     * @param joinPoint           切入点
     * @param recordExecutionTime 自定义注解
     * @return 方法执行结果
     * @throws Throwable 抛出异常
     */
    @Around("@annotation(recordExecutionTime)")
    public Object recordExecutionTime(ProceedingJoinPoint joinPoint, RecordExecutionTime recordExecutionTime) throws Throwable {
        final Long now = NowTimeThreadLocal.getNowTime();
        if (now == null) {
            // 在方法执行前记录当前时间
            NowTimeThreadLocal.setNowTime(System.currentTimeMillis());
        }
        try {
            // 执行目标方法
            return joinPoint.proceed();
        } finally {
            // 在方法执行后清除ThreadLocal中的时间
            NowTimeThreadLocal.remove();
        }
    }

    /**
     * 获取开始时间
     *
     * @return 开始时间
     */
    public static Long getNowTime() {
        return NowTimeThreadLocal.getNowTime();
    }
}