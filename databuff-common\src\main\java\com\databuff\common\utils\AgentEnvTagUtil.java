package com.databuff.common.utils;

import com.databuff.common.constants.Constant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.databuff.common.constants.Constant.ENV_MAP;


@Slf4j
public class AgentEnvTagUtil {

    public static String decodeAgentHeaderTag(String hostEnvTags) {
        if (StringUtils.isNotEmpty(hostEnvTags)) {
            try {
                hostEnvTags = URLDecoder.decode(hostEnvTags, "UTF-8");
            } catch (UnsupportedEncodingException e) {
                log.error("Error decoding host environment tags", e);
            }
        }
        return hostEnvTags;
    }

    /**
     * 将agent的环境变量标签city:北京,xingqiu:地球形式 转换为Map
     *
     * @param headerMap
     * @return
     */
    public static Map<String, String> getAgentEnvTags(Map<String, Object> headerMap) {
        Object hostTagObj = headerMap.get(Constant.AGENT_HOST_TAG);
        String hostEnvTags = hostTagObj == null ? "" : hostTagObj.toString();
        if (StringUtils.isEmpty(hostEnvTags)) {
            return ENV_MAP;
        }
        String[] tags = hostEnvTags.split(",");
        Map<String, String> tagMap = new java.util.HashMap<>(ENV_MAP);
        for (String tag : tags) {
            String[] keyValue = tag.split(":");
            if (keyValue.length == 2 && ENV_MAP.containsKey(keyValue[0].trim())) {
                tagMap.put(keyValue[0].trim(), keyValue[1].trim());
            }
        }
        return tagMap;
    }
}
