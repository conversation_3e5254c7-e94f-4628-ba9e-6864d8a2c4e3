package com.databuff.dao.mysql.datahub;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.databuff.entity.datahubv2.DataHubProcessor;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

@Mapper
@Repository
public interface DataHubProcessorMapper extends BaseMapper<DataHubProcessor> {
    
    /**
     * 根据ID查询Processor
     * 
     * @param processorId Processor ID
     * @return Processor实体
     */
    DataHubProcessor selectById(@Param("processorId") Integer processorId);

    /**
     * 插入Processor
     * 
     * @param processor Processor实体
     * @return 影响行数
     */
    int insert(DataHubProcessor processor);
    
    /**
     * 更新Processor
     * 
     * @param processor Processor实体
     * @return 影响行数
     */
    int updateById(@Param(Constants.ENTITY) DataHubProcessor processor);
    
    /**
     * 删除Processor
     * 
     * @param processorId Processor ID
     * @return 影响行数
     */
    int deleteById(@Param("processorId") Integer processorId);

    /**
     * 根据ID 修改Processor favorite 状态
     */
    @Update("update datahub_processor set is_favorite = #{favorite} where processor_id = #{processorId}")
    int updateFavoriteById(@Param("processorId") Integer processorId, @Param("favorite") int favorite);
} 