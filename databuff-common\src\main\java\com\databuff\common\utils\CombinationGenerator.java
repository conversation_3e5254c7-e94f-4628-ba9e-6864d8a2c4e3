package com.databuff.common.utils;

import com.alibaba.fastjson.JSONObject;

import java.util.*;

/**
 * CombinationGenerator 类用于生成给定键值对集合的所有可能组合。
 * <p>
 * 该类提供了一个静态方法 generateCombinations，用于生成所有可能的组合。
 * 组合结果以 JSONObject 的形式返回。
 * <p>
 * 使用方法：
 * 1. 创建一个包含键和值集合的 Map 对象。
 * 2. 调用 generateCombinations 方法，传入该 Map 对象。
 * 3. 该方法将返回一个包含所有可能组合的 Collection<JSONObject>。
 * 4. 遍历并打印每个组合。
 * <p>
 * 示例：
 * <pre>
 * {@code
 * Map<String, Collection<String>> abc = new HashMap<>();
 * abc.put("key1", Arrays.asList("a", "b"));
 * abc.put("key2", Arrays.asList("1", "2"));
 *
 * Collection<JSONObject> combinations = CombinationGenerator.generateCombinations(abc);
 * for (JSONObject combination : combinations) {
 *     System.out.println(combination);
 * }
 * }
 * </pre>
 */
public class CombinationGenerator {

    /**
     * 生成所有可能的组合。
     *
     * @param inputMap 包含键和值集合的映射。
     * @return 包含所有可能组合的集合，每个组合表示为 JSONObject。
     */
    public static Set<JSONObject> generateCombinations(Map<String, Set> inputMap) {
        Set<JSONObject> result = new HashSet<>();
        generateCombinationsRecursive(inputMap, new LinkedList<>(inputMap.keySet()), new JSONObject(), result);
        return result;
    }

    /**
     * 递归生成组合。
     *
     * @param inputMap           包含键和值集合的映射。
     * @param keys               键的列表。
     * @param currentCombination 当前组合的 JSONObject。
     * @param result             存储所有组合的列表。
     */
    private static void generateCombinationsRecursive(Map<String, Set> inputMap, LinkedList<String> keys, JSONObject currentCombination, Set<JSONObject> result) {
        if (keys.isEmpty()) {
            result.add(JSONUtils.deepCopy(currentCombination));
            return;
        }

        String key = keys.removeFirst();
        Collection<?> values = inputMap.get(key);

        for (Object value : values) {
            currentCombination.put(key, value);
            generateCombinationsRecursive(inputMap, keys, currentCombination, result);
        }

        keys.addFirst(key);
    }

    /**
     * 主方法，用于测试生成组合的方法。
     *
     * @param args 命令行参数。
     */
    public static void main(String[] args) {
        Map<String, Set> abc = new HashMap<>();
        abc.put("key1", new HashSet<>(Arrays.asList("a", "b", "c", "d", "e", "f", "g")));
        abc.put("key2", new HashSet<>(Arrays.asList("1", "2")));
        abc.put("key3", new HashSet<>(Arrays.asList("H")));
        abc.put("key4", new HashSet<>(Arrays.asList("B", "I")));

        Collection<JSONObject> combinations = generateCombinations(abc);
        for (JSONObject combination : combinations) {
            System.out.println(combination);
        }
    }
}