package com.databuff.dao.mysql;

import com.databuff.entity.ReportTemplateEntity;
import com.databuff.entity.ReportEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface ReportMapper {
    List<ReportTemplateEntity> getReportTemplateList();

    /**
     * 生成报告记录
     */
    Integer addReport(ReportEntity reportEntity);

    /**
     * 获取报告详情
     */
    ReportEntity getReportById(@Param("id") Integer id);

    /**
     * 根据文件名称删除数据库中记录
     */
    Integer deleteReportByName(@Param("name") String name);

    /**
     * 更新报告记录生成状态
     */
    Integer updateReportStatus(@Param("id") Integer id, @Param("status") Integer status);
}
