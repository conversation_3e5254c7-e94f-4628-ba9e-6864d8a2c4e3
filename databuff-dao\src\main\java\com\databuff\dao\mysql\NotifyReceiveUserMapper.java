package com.databuff.dao.mysql;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.databuff.entity.RcvUser;
import com.databuff.entity.ReceiveUserParams;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author:TianMing
 * @date: 2023/12/13
 * @time: 9:34
 */
@Mapper
@Repository
public interface NotifyReceiveUserMapper extends BaseMapper<RcvUser> {
    @Select(
            "<script>select t0.* from df_notify_receive_user t0 " +
                    "where 1=1" +
                    "<when test='id!=null and id!=&apos;&apos; '> and t0.id=#{id}</when> " +
                    "<when test='enabled!=null'> and t0.enabled=#{enabled}</when> " +
                    "<when test='rcvName!=null and rcvName!=&apos;&apos; '> and t0.rcv_name like binary CONCAT('%', #{rcvName},'%')</when> " +
                    "<when test='phone!=null and phone!=&apos;&apos; '> and t0.phone like binary CONCAT('%', #{phone},'%')</when> " +
                    "<when test='email!=null and email!=&apos;&apos; '> and t0.email like binary CONCAT('%', #{email},'%')</when> " +
                    "<when test='apiKey!=null and apiKey!=&apos;&apos; '> and t0.api_key=#{apiKey}</when> " +
                    "<when test='ids!=null and ids.size()>0 '> " +
                    "AND t0.id IN" +
                    "<foreach collection='ids' item='id' open='(' separator=',' close=')'>"+
                    "#{id}" +
                    "</foreach>" +
                    "</when> " +
                    //add here if need page limit
//                    " limit ${page},${limit} " +
                    " </script>")
    List<RcvUser> pageAll(ReceiveUserParams queryParamDTO);


    @Delete("<script>" +
            "DELETE FROM df_notify_receive_user " +
            "WHERE api_key=#{apiKey} AND id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int deleteBatchIds(@Param("ids") List<Integer> ids, @Param("apiKey") String apiKey);


}
