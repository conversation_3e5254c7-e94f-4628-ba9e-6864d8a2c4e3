package com.databuff.common.utils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;

/**
 * 生成唯一的事件 ID。
 */
public class AlarmIdGenerator {

    /**
     * 根据提供的源、类型和监视器 ID 生成唯一的事件 ID。
     *
     * @param source    事件的来源。这表示为两位数。
     * @param type      事件的类型。这被表示为一个字符。
     * @return 唯一的事件ID。
     */
    public static String generateEventId(int source, String type, String id) {
        StringBuilder eventId = new StringBuilder();

        // 1. Add 'E' for Event
        eventId.append('E');

        // 2. Add source
        eventId.append(String.format("%02d", source));

        // 3. Add type
        eventId.append(getFirstLettersInUpperCase(type));

        // 4. Add date (yyyyMMdd)
        LocalDate date = LocalDate.now();
        eventId.append(date.format(DateTimeFormatter.ofPattern("yyyyMMdd")));

        // 5. Generate a unique ID based on the monitorId and a random UUID
//        final String id = "M" + monitorId;
        eventId.append(id);

        return eventId.toString();
    }

    public static String generateEventId(IdGenerator idGenerator) {
        LocalDate date = LocalDate.now();
        return idGenerator.generate(date.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
    }

    /**
     * 根据提供的源、类型和监视器 ID 生成唯一的事件 ID。
     *
     * @param type      事件的类型。这被表示为一个字符。
     * @param monitorId 监视器的 ID。这用于确保事件 ID 的唯一性。
     * @return 唯一的事件ID。
     */
    public static String generateEventId(String type, String id) {
        // 10 代表databuff
        return generateEventId(10, type, id);
    }

    public static String getFirstLettersInUpperCase(String input) {
        if (input.length() > 1) {
            return input.substring(0, 1).toUpperCase();
        } else {
            return input.toUpperCase();
        }
    }

    /**
     * 从给定的事件 ID 中提取日期。
     * <p>
     * 事件 ID 的格式应为“E”+ 两位数来源 + 类型的第一个大写字母 + 日期 (yyyyMMdd) + ID。
     * 因此，日期始终位于事件 ID 的第 5 个和第 12 个字符之间。
     *
     * @param eventId 要从中提取日期的事件 ID。
     * @return 从事件 ID 中提取的日期，以整数形式，格式为 yyyyMMdd。
     * @throws NumberFormatException 如果事件 ID 的日期部分不是有效的整数。
     */
    public static int extractDateFromEventId(String eventId) {
        String dateStr = eventId.substring(4, 12);
        return Integer.parseInt(dateStr);
    }

    /**
     * 从ID中提取日期字符串并转换为标准格式
     *
     * @param id 包含日期的原始字符串ID，格式要求：第二个字符开始的8位连续数字表示日期
     *           格式示例：假设ID为"E20230315001"，则有效日期段为"20230315"
     * @return String 格式化后的日期时间字符串（"yyyy-MM-dd HH:mm:ss"格式），
     * 当输入不符合规范或解析失败时返回null
     */
    public static String extractStrDateFromId(String id) {
        // 处理空值边界情况
        if (id == null) {
            return null;
        }

        // 截取日期核心段：从第2位开始取8位数字（索引1到9）
        String dateStr = id.substring(1, 9);
        if (dateStr == null) {
            return null;
        }

        try {
            // 日期解析与转换流程：
            // 1. 将8位数字解析为基准日期
            // 2. 添加当日零点时间构成完整时间戳
            // 3. 格式化为标准日期时间字符串
            LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
            LocalDateTime dateTime = date.atStartOfDay();
            return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } catch (DateTimeParseException e) {
            // 日期格式不符合预期时的异常处理
            return null;
        }
    }

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    
    /**
     * 从ID中提取基准日期字符串，并生成包含基准日前后指定天数的日期集合
     * 
     * @param id 输入ID（需至少包含9个字符，日期从第2-9位提取）
     * @param plusDays 需要生成基准日之后的天数（非负整数）
     * @param minusDays 需要生成基准日之前的天数（非负整数）
     * @return 日期字符串集合，格式为yyyy-MM-dd。包含：
     *         1. 基准日
     *         2. 基准日之后plusDays天的日期（若plusDays>0）
     *         3. 基准日之前minusDays天的日期（若minusDays>0）
     * @throws IllegalArgumentException 当plusDays或minusDays为负数时抛出
     */
    public static Collection<String> extractStrDateFromId(String id, long plusDays, long minusDays) {
        // 参数有效性校验：ID格式检查和天数非负检查
        if (id == null || id.length() < 9) {
            return Collections.emptyList();
        }
    
        if (plusDays < 0 || minusDays < 0) {
            throw new IllegalArgumentException("Days parameters must be non-negative");
        }

        // 从ID第2-9位提取8位日期字符串（格式应为yyyyMMdd）
        String dateStr = id.substring(1, 9);
        Collection<String> result = new ArrayList<>(1 + (int)plusDays + (int)minusDays);

        try {
            // 基准日期解析（BASIC_ISO_DATE格式：yyyyMMdd）
            LocalDate baseDate = LocalDate.parse(dateStr, DateTimeFormatter.BASIC_ISO_DATE);
            result.add(baseDate.format(DATE_FORMATTER));

            // 生成未来日期序列：基准日之后plusDays天的日期
            for (int i = 1; i <= plusDays; i++) {
                result.add(baseDate.plusDays(1).format(DATE_FORMATTER));
            }

            // 生成历史日期序列：基准日之前minusDays天的日期
            for (int i = 1; i <= minusDays; i++) {
                result.add(baseDate.minusDays(1).format(DATE_FORMATTER));
            }
        } catch (DateTimeParseException e) {
            // 日期解析失败时返回空集合
            return Collections.emptyList();
        }
        return result;
    }

    private static SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
    public static String extractStrDateFrom(Long startTime) {
        return dateFormat.format(new Date(startTime));
    }
}