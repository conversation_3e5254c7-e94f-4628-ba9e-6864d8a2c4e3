package com.databuff.common.tsdb.adapter;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.metric.dto.Query;
import com.databuff.common.tsdb.builder.AbstractQueryBuilder;
import com.databuff.common.tsdb.builder.OpenGeminiQueryBuilder;
import com.databuff.common.tsdb.builder.QueryBuilder;
import com.databuff.common.tsdb.model.*;
import com.databuff.common.tsdb.pool.TSDBConnectPool;
import com.databuff.common.tsdb.wrapper.DatabaseWrapper;
import com.databuff.common.tsdb.wrapper.OpenGeminiWrapper;
import com.databuff.common.utils.PercentageLatencyUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.databuff.common.constants.MetricConstant.COMPONENT_CALLS;
import static com.databuff.common.constants.MetricConstant.COMPONENT_MAX_DURATION;
import static com.databuff.common.utils.PercentageLatencyUtil.HISTOGRAM_FIELD;

@Slf4j
public class OpenGeminiAdapter implements DatabaseAdapter {

    private TSDBConnectPool pool;

    public OpenGeminiAdapter(TSDBConnectPool pool) {
        this.pool = pool;
    }

    @Override
    public TSDBResultSet executeQuery(DatabaseWrapper dbClient, String sql, String databaseName, QueryBuilder builder) {
        try {
            OpenGeminiWrapper openGemini = (OpenGeminiWrapper) dbClient;
            // 使用 InfluxDB 客户端执行查询，因为 OpenGemini 与 InfluxDB 兼容
            log.debug("OpenGemini executing query: {}, database: {}", sql, databaseName);
            return openGemini.query(new Query(sql, databaseName), TimeUnit.MILLISECONDS, builder);
        } catch (Exception e) {
            log.error("查询数据失败,databaseName:{},sql:{},error:", databaseName, sql, e);
            throw new RuntimeException("查询数据失败", e);
        }
    }

    /**
     * 执行查询，返回标签值集合
     *
     * @param dbClient
     * @param query        查询SQL，应为SHOW TAG VALUES格式
     * @param databaseName 数据库名称
     * @return 标签名到标签值集合的映射
     */
    @Override
    public Map<String, Set<String>> executeShowTagValues(DatabaseWrapper dbClient, String query, String databaseName) {
        Map<String, Set<String>> result = new HashMap<>();

        try {
            OpenGeminiWrapper openGemini = (OpenGeminiWrapper) dbClient;

            // 执行查询并获取结果
            log.debug("OpenGemini executing show tag values query: {}, database: {}", query, databaseName);
            TSDBResultSet tsdbResultSet = openGemini.query(new Query(query, databaseName), TimeUnit.MILLISECONDS, null);

            // 解析结果集
            if (tsdbResultSet == null || tsdbResultSet.hasError() || tsdbResultSet.getResults() == null) {
                log.warn("查询标签值失败: {}", tsdbResultSet != null ? tsdbResultSet.getError() : "结果为空");
                return result;
            }

            // 遍历结果集中的每个结果
            for (TSDBResult tsdbResult : tsdbResultSet.getResults()) {
                if (tsdbResult.hasError() || tsdbResult.getSeries() == null) {
                    continue;
                }

                // 遍历每个系列
                for (TSDBSeries series : tsdbResult.getSeries()) {
                    List<String> columns = series.getColumns();
                    if (columns == null || columns.size() < 2) {
                        continue;
                    }

                    // 查找key和value列的索引
                    int keyIndex = columns.indexOf("key");
                    int valueIndex = columns.indexOf("value");

                    if (keyIndex == -1 || valueIndex == -1) {
                        continue;
                    }

                    // 处理每一行数据
                    for (List<Object> row : series.getValues()) {
                        if (row == null || row.size() <= Math.max(keyIndex, valueIndex)) {
                            continue;
                        }

                        String key = String.valueOf(row.get(keyIndex));
                        String value = String.valueOf(row.get(valueIndex));

                        if (StringUtils.isNotEmpty(value)) {
                            // 将值添加到对应标签的集合中
                            result.computeIfAbsent(key, k -> new HashSet<>()).add(value);
                        }
                    }
                }
            }

            return result;
        } catch (Exception e) {
            log.error("查询标签值失败,databaseName:{},query:{},error:", databaseName, query, e);
            throw new RuntimeException("查询标签值失败", e);
        }
    }

    @Override
    public Collection<String> executeShowTagKeys(DatabaseWrapper dbClient, String sql, String databaseName) {
        try {
            OpenGeminiWrapper openGemini = (OpenGeminiWrapper) dbClient;
            return openGemini.showTagKeys(new Query(sql, databaseName));
        } catch (Exception e) {
            log.error("查询数据失败,databaseName:{},sql:{}, error: {}", databaseName, sql, e);
            throw new RuntimeException("查询数据失败", e);
        }
    }

    @Override
    public boolean createDatabase(DatabaseWrapper dbClient, TSDBDatabaseInfo databaseInfo) {
        if (databaseInfo == null || StringUtils.isBlank(databaseInfo.getDatabaseName())) {
            throw new IllegalArgumentException("数据库信息不能为空，且 databaseName 不能为空");
        }
        try {
            OpenGeminiWrapper openGemini = (OpenGeminiWrapper) dbClient;
            if (!openGemini.databaseExists(databaseInfo.getDatabaseName())) {
                log.info("创建数据库: {}", databaseInfo.getDatabaseName());
                openGemini.createDatabase(
                        databaseInfo.getDatabaseName(),
                        databaseInfo.getUserName(),
                        databaseInfo.getPassword(),
                        databaseInfo.getShard(),
                        databaseInfo.getReplication(),
                        databaseInfo.getKeepDay(),
                        databaseInfo.getInterval()
                );
                log.info("数据库创建成功: {}", databaseInfo.getDatabaseName());
            } else {
                log.info("数据库 {} 已存在", databaseInfo.getDatabaseName());
            }
        } catch (Exception e) {
            log.error("创建OpenGemini数据库失败", e);
            return false;
        }
        return true;
    }

    @Override
    public boolean writePoints(DatabaseWrapper dbClient, String databaseName, List<TSDBPoint> tsdbPoints) {
        if (CollectionUtils.isEmpty(tsdbPoints)) {
            return true;
        }

        for (TSDBPoint tsdbPoint : tsdbPoints) {
            if (StringUtils.isBlank(tsdbPoint.getMeasurement())) {
                throw new RuntimeException("No measurement defined");
            }
        }
        try {
            OpenGeminiWrapper openGemini = (OpenGeminiWrapper) dbClient;
            openGemini.write(databaseName, tsdbPoints);
        } catch (Throwable e) {
            log.error("插入数据失败", e);
            return false;
        }
        return true;
    }

    @Override
    public Map<String, Object> getConfigs() {
        OpenGeminiWrapper openGemini = null;
        try {
            openGemini = (OpenGeminiWrapper) pool.getTSDBClient();
            return openGemini.getConfigs();
        } catch (Exception e) {
            log.error("获取tsdb config map error:", e);
            throw new RuntimeException("获取tsdb config map erro", e);
        } finally {
            if (openGemini != null) {
                pool.releaseConnection(openGemini);
            }
        }
    }

    @Override
    public void close() throws Exception {
//        pool.close();
    }

    /**
     * 构建完整的SQL查询语句
     * <p>
     * 根据查询类型选择不同的查询构建策略：
     * - STANDARD: 标准查询，直接使用OpenGeminiQueryBuilder构建
     * - NON_TIME_ONLY_ORDER: 只有非time字段排序的查询
     * - MIXED_ORDER: 同时有time和非time字段排序的查询
     * - TIME_ONLY_ORDER: 用于生成topn(max(xx))时序图的查询，使用两步查询方法
     * <p>
     * 示例：
     * 1. 标准查询: SELECT mean("usage") FROM "system.cpu" WHERE time >= '2023-01-01' GROUP BY time(60s)
     * 2. 时序图查询: 先查询标签值，然后构建带有标签条件的查询，用于生成topn(max(xx))时序图
     *
     * @param builder 查询构建器，包含查询所需的所有参数
     * @return 构建的SQL查询字符串
     */
    @Override
    public String buildFullSql(QueryBuilder builder) {
        // 参数验证
        if (builder == null) {
            throw new IllegalArgumentException("QueryBuilder不能为空");
        }

        // 使用OpenGeminiQueryBuilder构建基础SQL
        QueryBuilder cloned = builder.copy();  // 安全的深度克隆
        final String tempSql = new OpenGeminiQueryBuilder(cloned).buildQuery();
        final QueryBuilder.QueryType queryType = cloned.getQueryType();

        // 根据查询类型选择不同的构建策略
        if (queryType == null) {
            return tempSql; // 默认使用标准查询
        }

        // 使用策略模式处理不同类型的查询
        switch (queryType) {
            case STANDARD:
                builder.setQueryType(queryType);
                return handleStandardQuery(builder, tempSql);

            case NON_TIME_ONLY_ORDER:
                builder.setQueryType(queryType);
                return handleNonTimeOrderQuery(builder, tempSql);

            case MIXED_ORDER:
                builder.setQueryType(queryType);
                return handleMixedOrderQuery(builder, tempSql);

            case TOPN_MAX_TIME:
                OpenGeminiWrapper openGemini = null;
                try {
                    openGemini = (OpenGeminiWrapper) pool.getTSDBClient();
                    return handleTimeOnlyOrderQuery(openGemini, builder, tempSql);
                } catch (Exception e) {
                    log.error("handleTimeOnlyOrderQuery 组装sql异常", e);
                } finally {
                    if (openGemini != null) {
                        pool.releaseConnection(openGemini);
                    }
                }
                return tempSql;
            default:
                return tempSql;
        }
    }

    /**
     * 处理标准查询
     *
     * @param builder 查询构建器
     * @param sql     已构建的SQL
     * @return 处理后的SQL
     */
    private String handleStandardQuery(QueryBuilder builder, String sql) {
        // 标准查询直接返回原SQL
        log.debug("使用标准查询: {}", sql);
        return sql;
    }

    /**
     * 处理只有非time字段排序的查询
     *
     * @param builder 查询构建器
     * @param sql     已构建的SQL
     * @return 处理后的SQL
     */
    private String handleNonTimeOrderQuery(QueryBuilder builder, String sql) {
        // 非time字段排序查询直接返回原SQL，因为OpenGeminiQueryBuilder已经处理了嵌套查询
        log.debug("使用非time字段排序查询: {}", sql);
        return sql;
    }

    /**
     * 处理同时有time和非time字段排序的查询
     *
     * @param builder 查询构建器
     * @param sql     已构建的SQL
     * @return 处理后的SQL
     */
    private String handleMixedOrderQuery(QueryBuilder builder, String sql) {
        // 混合排序查询直接返回原SQL，因为OpenGeminiQueryBuilder已经处理了嵌套查询
        log.debug("使用混合排序查询: {}", sql);
        return sql;
    }

    /**
     * 处理用于生成topn(max(xx))时序图的查询
     * <p>
     * 此方法使用两步查询策略：
     * 1. 首先执行临时查询获取标签值
     * 2. 然后将这些标签值作为条件构建最终查询
     * <p>
     * 这种查询类型主要用于生成显示多个时间序列的图表，如topn(max(xx))时序图
     *
     * @param dbClient
     * @param builder  查询构建器
     * @param tempSql  临时SQL
     * @return 处理后的SQL
     */
    private String handleTimeOnlyOrderQuery(DatabaseWrapper dbClient, QueryBuilder builder, String tempSql) {
        log.debug("使用topn时序图查询，执行两步查询策略");
        return buildTimeOnlyOrderQuery(dbClient, builder, tempSql, builder.getGroupBy());
    }

    /**
     * 构建用于生成topn(max(xx))时序图的查询语句
     * <p>
     * 此方法实现了两步查询策略：
     * 1. 执行临时查询获取标签值（如服务名、主机名等）
     * 2. 将这些标签值作为条件构建最终查询，以生成多条时间序列的图表
     *
     * @param builder 查询构建器
     * @param tempSql 临时SQL
     * @param groupBy 分组字段列表
     * @return 构建的SQL查询字符串
     */
    @Nullable
    private String buildTimeOnlyOrderQuery(DatabaseWrapper dbClient, QueryBuilder builder, String tempSql, List<String> groupBy) {
        // 步骤1: 执行临时查询获取标签值
        final TSDBResultSet tsdbResultSet = executeFirstStepQuery(dbClient, builder, tempSql);
        if (tsdbResultSet == null) {
            return null;
        }

        // 步骤2: 从结果中提取标签条件
        List<Where> tagConditions = extractTagConditionsFromResults(tsdbResultSet, groupBy);

        // 步骤3: 将标签条件添加到查询构建器
        addTagConditionsToBuilder(builder, tagConditions);

        // 步骤4: 构建最终查询
        return buildFinalTimeOrderQuery(builder);
    }

    /**
     * 执行第一步查询，获取标签值
     *
     * @param dbClient
     * @param builder  查询构建器
     * @param tempSql  临时SQL
     * @return 查询结果集
     */
    private TSDBResultSet executeFirstStepQuery(DatabaseWrapper dbClient, QueryBuilder builder, String tempSql) {
        try {
            final TSDBResultSet tsdbResultSet = this.executeQuery(dbClient, tempSql, builder.getDatabaseName(), builder);
            if (tsdbResultSet.hasError()) {
                log.error("查询数据失败,databaseName:{},sql:{}, error: {}",
                        builder.getDatabaseName(), tempSql, tsdbResultSet.getError());
                throw new RuntimeException("查询数据失败: " + tsdbResultSet.getError());
            }

            final List<TSDBResult> results = tsdbResultSet.getResults();
            if (results == null || results.isEmpty()) {
                log.debug("查询结果为空，无法构建time字段排序查询");
                return null;
            }

            return tsdbResultSet;
        } catch (Exception e) {
            log.error("执行第一步查询失败", e);
            throw new RuntimeException("执行第一步查询失败", e);
        }
    }

    /**
     * 从查询结果中提取标签条件
     *
     * @param tsdbResultSet 查询结果集
     * @param groupBy       分组字段列表
     * @return 提取的标签条件列表
     */
    private List<Where> extractTagConditionsFromResults(TSDBResultSet tsdbResultSet, List<String> groupBy) {
        // 创建一个外层的OR条件集合，用于存储不同seriesValues的条件
        List<Where> orConditions = new ArrayList<>();

        for (TSDBResult result : tsdbResultSet.getResults()) {
            if (result == null || result.getSeries() == null) {
                continue;
            }

            for (TSDBSeries series : result.getSeries()) {
                if (series == null || series.getColumns() == null || series.getValues() == null) {
                    continue;
                }

                extractConditionsFromSeries(series, groupBy, orConditions);
            }
        }

        return orConditions;
    }

    /**
     * 从单个系列中提取条件
     *
     * @param series       数据系列
     * @param groupBy      分组字段列表
     * @param orConditions 要填充的OR条件列表
     */
    private void extractConditionsFromSeries(TSDBSeries series, List<String> groupBy, List<Where> orConditions) {
        final List<String> columns = series.getColumns();

        for (List<Object> seriesValues : series.getValues()) {
            if (seriesValues == null || seriesValues.size() != columns.size()) {
                continue;
            }

            // 创建一个内层的AND条件集合，用于存储同一seriesValues的条件
            List<Where> andConditions = new ArrayList<>();

            for (int i = 0; i < columns.size(); i++) {
                final Object tagValue = seriesValues.get(i);
                final String tagKey = columns.get(i);

                // 只处理字符串类型的标签值，且标签键必须在groupBy列表中
                if (isValidTagValue(tagValue, tagKey, groupBy)) {
                    // 将tagKey和tagValue添加到当前seriesValues的AND条件中
                    andConditions.add(new Where(tagKey, WhereOp.EQ, tagValue.toString()));
                }
            }

            // 如果当前seriesValues有条件，则创建一个AND条件并添加到OR条件集合中
            if (!andConditions.isEmpty()) {
                Where andCondition = Where.and(andConditions);
                orConditions.add(andCondition);
            }
        }
    }

    /**
     * 检查标签值是否有效
     *
     * @param tagValue 标签值
     * @param tagKey   标签键
     * @param groupBy  分组字段列表
     * @return 是否是有效的标签值
     */
    private boolean isValidTagValue(Object tagValue, String tagKey, List<String> groupBy) {
        return tagValue instanceof String && groupBy != null && groupBy.contains(tagKey);
    }

    /**
     * 将标签条件添加到查询构建器
     *
     * @param builder       查询构建器
     * @param tagConditions 标签条件列表
     */
    private void addTagConditionsToBuilder(QueryBuilder builder, List<Where> tagConditions) {
        if (!tagConditions.isEmpty()) {
            Where orCondition = Where.or(tagConditions);
            builder.addWhere(orCondition);
            log.debug("添加了{}个标签条件到查询构建器", tagConditions.size());
        } else {
            log.debug("没有标签条件需要添加");
        }
    }

    /**
     * 构建最终的时间排序查询
     *
     * @param builder 查询构建器
     * @return 构建的SQL查询字符串
     */
    private String buildFinalTimeOrderQuery(QueryBuilder builder) {
        // 创建一个新的查询构建器，避免修改原始构建器
        QueryBuilder tempBuilder = cloneQueryBuilderForTimeOrder(builder);

        // 使用OpenGeminiQueryBuilder构建最终SQL
        String finalSql = new OpenGeminiQueryBuilder(tempBuilder).buildQuery();
        log.debug("构建的最终time字段排序查询: {}", finalSql);

        return finalSql;
    }

    /**
     * 克隆查询构建器用于时间排序查询
     *
     * @param builder 原始查询构建器
     * @return 克隆的查询构建器
     */
    private QueryBuilder cloneQueryBuilderForTimeOrder(QueryBuilder builder) {
        QueryBuilder tempBuilder = new QueryBuilder();

        // 复制原始查询构建器的必要属性
        tempBuilder.setDatabaseName(builder.getDatabaseName());
        tempBuilder.setMeasurement(builder.getMeasurement());
        tempBuilder.setAggregations(builder.getAggregations());
        tempBuilder.setWheres(builder.getWheres());
        tempBuilder.setGroupBy(builder.getGroupBy());

        // 设置时间间隔 - 对于时序图查询是必要的
        tempBuilder.setInterval(builder.getInterval());
        tempBuilder.setIntervalUnit(builder.getIntervalUnit());

        // 复制时间范围
        if (builder.getStart() > 0) {
            tempBuilder.start(builder.getStart());
        }
        if (builder.getEnd() > 0) {
            tempBuilder.end(builder.getEnd());
        }

        return tempBuilder;
    }

    /**
     * 构建显示标签值的SQL语句。
     *
     * @param builder 查询构建器，包含测量名、数据库名、分组字段和聚合参数
     * @return 生成的SQL语句字符串，若失败返回null
     */
    @Override
    public Map<String, String> buildShowTagValueSql(DatabaseWrapper dbClient, QueryBuilder builder) {
        try {
            // 获取测量名并创建查询标签键的SQL语句
            final String measurement = builder.getMeasurement();
            String tagSql = createShowStatement("tag keys", measurement);
            final String databaseName = builder.getDatabaseName();
            Collection<String> tagKeys = this.executeShowTagKeys(dbClient, tagSql, databaseName);
            if (tagKeys == null) {
                throw new RuntimeException("查询tag keys失败");
            }

            // 处理分组字段参数，确保其有效性
            List<String> groupBy = builder.getGroupBy();
            if (groupBy == null || groupBy.isEmpty()) {
                groupBy = new ArrayList<>(tagKeys);
            }

            Set<String> tagKeysSet = new HashSet<>(tagKeys);
            Set<String> validGroupSet = new HashSet<>(groupBy);
            validGroupSet.retainAll(tagKeysSet);
            builder.setGroupBy(new ArrayList<>(validGroupSet));

            // 处理聚合参数的默认值
            List<Aggregation> aggregations = builder.getAggregations();
            if (CollectionUtils.isEmpty(aggregations)) {
                String fieldSql = createShowStatement("field keys", measurement);
                Collection<String> fieldKeys = this.executeShowTagKeys(dbClient, fieldSql, databaseName);
                if (fieldKeys == null) {
                    throw new RuntimeException("查询field keys失败");
                }

                aggregations = fieldKeys.stream()
                        .map(key -> new Aggregation(AggFun.LAST, key))
                        .collect(Collectors.toList());
                builder.setAggregations(aggregations);
            }

            // 设置查询参数并构建最终SQL语句
            builder.setInterval(null);
            builder.setLimit(1000);
            return new OpenGeminiQueryBuilder(builder).buildShowTagValueQueryMap();
        } catch (Exception e) {
            log.error("创建show语句失败", e);
            return null;
        }
    }

    private String createShowStatement(String type, String measurement) {
        // 对measurement进行转义处理（如替换单引号）
        String escapedMeasurement = measurement.replace("'", "''");
        return new StringBuilder("show ").append(type).append(" from '")
                .append(escapedMeasurement)
                .append("' limit 200").toString();
    }

    @Override
    public String buildWhereSql(QueryBuilder builder) {
        return new OpenGeminiQueryBuilder(builder).buildWhere();
    }

    @Override
    public TSDBResultSet executeQuery(DatabaseWrapper dbClient, QueryBuilder builder, AggFun tsAgg, AggFun valAgg, AggFun topAgg, JSONObject otherParam) throws Exception {
        if (builder == null) {
            log.error("builder is null");
            return null;
        }
        //检验
        builder.validate();

        // 判断是否需要使用嵌套聚合函数
        boolean needsNestedAggregation = (tsAgg != null && valAgg != null) ||
                (tsAgg != null && topAgg != null) ||
                (valAgg != null && topAgg != null);

        if (needsNestedAggregation) {
            // 对于嵌套查询，我们需要修改SQL构建方式，确保WHERE条件应用到最内层查询
            String sql = buildNestedQuery(builder, tsAgg, valAgg, topAgg);
            return executeQuery(dbClient, sql, builder.getDatabaseName(), builder);
        } else {
            // 非嵌套查询，使用原有逻辑
            OpenGeminiQueryBuilder openGeminiQueryBuilder = new OpenGeminiQueryBuilder(builder);
            StringBuilder sqlBuilder = openGeminiQueryBuilder.buildDiffAggSpecificSelect(tsAgg, valAgg, topAgg, otherParam);
            openGeminiQueryBuilder.appendMeasurement(sqlBuilder);
            sqlBuilder.append(openGeminiQueryBuilder.buildWhere());
            // 添加 GROUP BY
            openGeminiQueryBuilder.appendGroupBy(sqlBuilder);
            // 时间间隔
            openGeminiQueryBuilder.appendSpecificInterval(sqlBuilder);
            // ORDER BY
            openGeminiQueryBuilder.appendOrderBy(sqlBuilder);
            // LIMIT / OFFSET
            openGeminiQueryBuilder.appendLimitAndOffset(sqlBuilder);
            return executeQuery(dbClient, sqlBuilder.toString(), builder.getDatabaseName(), builder);
        }
    }

    /**
     * 构建嵌套查询SQL，确保WHERE条件应用到最内层查询
     *
     * @param builder 查询构建器
     * @param tsAgg   时间序列聚合函数
     * @param valAgg  值聚合函数
     * @param topAgg  顶层聚合函数
     * @return 构建的SQL查询字符串
     */
    private String buildNestedQuery(QueryBuilder builder, AggFun tsAgg, AggFun valAgg, AggFun topAgg) {
        StringBuilder sql = new StringBuilder();

        // 验证聚合函数
        List<Aggregation> aggregations = builder.getAggregations();
        if (aggregations == null || aggregations.isEmpty()) {
            throw new IllegalArgumentException("Aggregations cannot be null or empty for nested query");
        }

        // 准备字段
        String field = aggregations.get(0).getField();
        String processedField = processFieldName(field);

        // 开始构建 SQL
        sql.append("SELECT ");

        if (valAgg != null && topAgg != null) {
            // 三层嵌套查询
            buildTripleNestedQuery(sql, builder, tsAgg, valAgg, topAgg, processedField);
        } else if (valAgg != null) {
            // 两层嵌套查询 - tsAgg 和 valAgg
            buildDoubleNestedQuery(sql, builder, tsAgg, valAgg, processedField);
        } else if (topAgg != null) {
            // 两层嵌套查询 - tsAgg 和 topAgg
            buildDoubleNestedQuery(sql, builder, tsAgg, topAgg, processedField);
        } else {
            throw new IllegalArgumentException("At least one of valAgg or topAgg must be non-null");
        }

        // 在外层查询中添加时间范围条件
//        appendTimeRangeConditions(sql, builder);

        // 添加外层 GROUP BY
        appendOuterGroupBy(sql, builder);

        // 添加 ORDER BY, LIMIT, OFFSET
        appendOrderByLimitOffset(sql, builder);

        return sql.toString();
    }

    /**
     * 构建三层嵌套查询
     *
     * @param sql            SQL构建器
     * @param builder        查询构建器
     * @param tsAgg          时间序列聚合函数，可能为null
     * @param valAgg         值聚合函数，可能为null
     * @param topAgg         顶层聚合函数，可能为null
     * @param processedField 处理后的字段名
     */
    private void buildTripleNestedQuery(StringBuilder sql, QueryBuilder builder, AggFun tsAgg, AggFun valAgg, AggFun topAgg, String processedField) {
        // 准备函数和别名，处理null情况
        String innerFunction = valAgg != null ? getFunctionNameSafe(valAgg) : "last";
        String innerAlias = "inner_value";
        String middleFunction = topAgg != null ? getTopFunctionNameSafe(topAgg) : "last";
        String middleAlias = "middle_value";
        String outerFunction = tsAgg != null ? getFunctionNameSafe(tsAgg) : "last";

        // 构建最外层查询
        sql.append(outerFunction).append("(").append(middleAlias).append(")");

        // 构建中间层查询
        sql.append(" FROM (SELECT ").append(middleFunction).append("(").append(innerAlias).append(") AS ").append(middleAlias);

        // 构建最内层查询
        sql.append(" FROM (SELECT ").append(innerFunction).append("(").append(processedField).append(") AS ").append(innerAlias);
        sql.append(" FROM \"").append(builder.getMeasurement()).append("\"");

        // 在最内层添加 WHERE 条件
        appendWhereConditions(sql, builder);
        final List<String> groupBy = builder.getGroupBy();
        // 在最内层添加 GROUP BY 时间间隔
        if (builder.getInterval() != null && builder.getInterval() > 0) {
            sql.append("GROUP BY time(").append(builder.getInterval())
                    .append(builder.getIntervalUnit() != null ? builder.getIntervalUnit() : "s")
                    .append(")");
            if (!CollectionUtils.isEmpty(groupBy)) {
                sql.append(", ")
                        .append(groupBy.stream()
                                .map(g -> "\"" + g.trim() + "\"")
                                .collect(Collectors.joining(", ")));
            }
        } else if (!CollectionUtils.isEmpty(groupBy)) {
            sql.append(" GROUP BY ")
                    .append(groupBy.stream()
                            .map(g -> "\"" + g.trim() + "\"")
                            .collect(Collectors.joining(", ")));
        }

        // 关闭内层查询
        sql.append(")");
        if (!CollectionUtils.isEmpty(groupBy)) {
            sql.append(" GROUP BY ")
                    .append(groupBy.stream()
                            .map(g -> "\"" + g.trim() + "\"")
                            .collect(Collectors.joining(", ")));
        }
        // 关闭中间层查询并添加别名
        sql.append(") AS \"usage\"");
    }

    /**
     * 构建两层嵌套查询
     *
     * @param sql            SQL构建器
     * @param builder        查询构建器
     * @param innerAgg       内层聚合函数，可能为null
     * @param outerAgg       外层聚合函数，可能为null
     * @param processedField 处理后的字段名
     */
    private void buildDoubleNestedQuery(StringBuilder sql, QueryBuilder builder, AggFun innerAgg, AggFun outerAgg, String processedField) {
        // 准备函数和别名，处理null情况
        String innerFunction = innerAgg != null ? getFunctionNameSafe(innerAgg) : "last"; // 默认使用last函数
        String innerAlias = "inner_value";

        // 处理外层函数，如果为null或与内层相同
        String outerFunction;
        if (outerAgg == null) {
            outerFunction = "last"; // 默认使用last函数
        } else if (outerAgg == innerAgg) {
            outerFunction = getTopFunctionNameSafe(outerAgg);
        } else {
            outerFunction = getFunctionNameSafe(outerAgg);
        }

        // 构建外层查询
        sql.append(outerFunction).append("(").append(innerAlias).append(")");

        // 构建内层查询
        sql.append(" FROM (SELECT ").append(innerFunction).append("(").append(processedField).append(") AS ").append(innerAlias);
        sql.append(" FROM \"").append(builder.getMeasurement()).append("\"");

        // 在内层添加 WHERE 条件
        appendWhereConditions(sql, builder);

        // 在内层添加 GROUP BY 时间间隔
        if (builder.getInterval() != null && builder.getInterval() > 0) {
            sql.append(" GROUP BY time(").append(builder.getInterval())
                    .append(builder.getIntervalUnit() != null ? builder.getIntervalUnit() : "s")
                    .append(")");
        }

        // 关闭内层查询
        sql.append(")) AS \"usage\"");
    }

    /**
     * 添加外层 GROUP BY 子句
     */
    private void appendOuterGroupBy(StringBuilder sql, QueryBuilder builder) {
        List<String> groupBy = builder.getGroupBy();
        if (groupBy != null && !groupBy.isEmpty()) {
            sql.append(" GROUP BY ")
                    .append(groupBy.stream()
                            .map(g -> "\"" + g.trim() + "\"")
                            .collect(Collectors.joining(", ")));

            // 添加时间间隔到外层 GROUP BY
//            if (builder.getInterval() != null && builder.getInterval() > 0) {
//                sql.append(",time(")
//                        .append(builder.getInterval())
//                        .append(builder.getIntervalUnit() != null ? builder.getIntervalUnit() : "s")
//                        .append(")");
//            }
        }
    }

    /**
     * 添加 WHERE 条件，确保时间范围查询条件正确应用
     */
    private void appendWhereConditions(StringBuilder sql, QueryBuilder builder) {
        // 使用 OpenGeminiQueryBuilder 构建 WHERE 条件
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);
        String whereClause = queryBuilder.buildWhere();
        sql.append(whereClause);
    }

    /**
     * 在外层查询中添加时间范围条件
     */
    private void appendTimeRangeConditions(StringBuilder sql, QueryBuilder builder) {
        // 使用AbstractQueryBuilder中的通用方法
        AbstractQueryBuilder.appendTimeRangeConditions(sql, builder);
    }

    /**
     * 添加 ORDER BY, LIMIT, OFFSET 子句
     */
    private void appendOrderByLimitOffset(StringBuilder sql, QueryBuilder builder) {
        // 添加 ORDER BY
        List<OrderBy> orderBy = builder.getOrderBy();
        if (orderBy != null && !orderBy.isEmpty()) {
            sql.append(" ORDER BY ")
                    .append(orderBy.stream()
                            .map(order -> "\"" + order.getField().trim() + "\" " + (order.isAsc() ? "ASC" : "DESC"))
                            .collect(Collectors.joining(", ")));
        }

        // 添加 LIMIT
        if (builder.getOpenGeminiLimit() != null && builder.getOpenGeminiLimit() > 0) {
            sql.append(" LIMIT ").append(builder.getOpenGeminiLimit());
        } else if (builder.getLimit() != null && builder.getLimit() > 0) {
            sql.append(" LIMIT ").append(builder.getLimit());
        }

        // 添加 OFFSET
        if (builder.getOpengeminiOffset() != null && builder.getOpengeminiOffset() > 0) {
            sql.append(" OFFSET ").append(builder.getOpengeminiOffset());
        } else if (builder.getOffset() != null && builder.getOffset() > 0) {
            sql.append(" OFFSET ").append(builder.getOffset());
        }
    }

    /**
     * 处理字段名，确保正确包裹
     *
     * @param field 字段名
     * @return 处理后的字段名
     */
    private String processFieldName(String field) {
        if (field == null || field.trim().isEmpty()) {
            throw new IllegalArgumentException("Field name cannot be null or empty");
        }

        // 判断字段是否已经被引号包裹
        boolean isAlreadyQuoted = (field.startsWith("\"") && field.endsWith("\"")) ||
                (field.startsWith("'") && field.endsWith("'"));

        // 判断字段是否是公式或聚合函数
        boolean isFormulaOrFunction = field.contains("(") || field.contains(")") ||
                field.contains("+") || field.contains("-") ||
                field.contains("*") || field.contains("/");

        if (isAlreadyQuoted || isFormulaOrFunction) {
            // 如果字段已经被引号包裹或是公式/聚合函数，不需要再次包裹
            return field;
        } else {
            // 单独的字段名需要用双引号包裹
            return "\"" + field + "\"";
        }
    }

    /**
     * 获取聚合函数名称
     *
     * @param aggFun 聚合函数类型
     * @return 聚合函数名称
     * @throws IllegalArgumentException 如果aggFun为null
     */
    private String getFunctionName(AggFun aggFun) {
        if (aggFun == null) {
            throw new IllegalArgumentException("Aggregation function cannot be null");
        }

        // OpenGemini 不支持 avg，转为 mean
        if (aggFun == AggFun.AVG) {
            return "mean";
        }

        // 使用简化的函数名映射
        switch (aggFun) {
            case MEAN:
                return "mean";
            case COUNT:
                return "count";
            case SUM:
                return "sum";
            case MAX:
                return "max";
            case MIN:
                return "min";
            case LAST:
                return "last";
            case PERCENTILE:
                return "percentile";
            default:
                return aggFun.name().toLowerCase();
        }
    }

    /**
     * 获取聚合函数名称的安全版本，处理null情况
     *
     * @param aggFun 聚合函数类型，可能为null
     * @return 聚合函数名称，如果aggFun为null则返回"last"
     */
    private String getFunctionNameSafe(AggFun aggFun) {
        if (aggFun == null) {
            return "last"; // 默认使用last函数
        }

        // OpenGemini 不支持 avg，转为 mean
        if (aggFun == AggFun.AVG) {
            return "mean";
        }

        // 使用简化的函数名映射
        switch (aggFun) {
            case MEAN:
                return "mean";
            case COUNT:
                return "count";
            case SUM:
                return "sum";
            case MAX:
                return "max";
            case MIN:
                return "min";
            case LAST:
                return "last";
            case PERCENTILE:
                return "percentile";
            default:
                return aggFun.name().toLowerCase();
        }
    }

    /**
     * 获取顶层聚合函数名称，与普通聚合函数相同
     *
     * @param aggFun 聚合函数类型
     * @return 聚合函数名称
     * @throws IllegalArgumentException 如果aggFun为null
     */
    private String getTopFunctionName(AggFun aggFun) {
        // OpenGemini 不支持 t_max 等函数，使用标准聚合函数
        // 对于 OpenGemini，TOP_FUNCTIONS 与 FUNCTIONS 相同
        return getFunctionName(aggFun);
    }

    /**
     * 获取顶层聚合函数名称的安全版本，处理null情况
     *
     * @param aggFun 聚合函数类型，可能为null
     * @return 聚合函数名称，如果aggFun为null则返回"last"
     */
    private String getTopFunctionNameSafe(AggFun aggFun) {
        if (aggFun == null) {
            return "last"; // 默认使用last函数
        }
        // 对于非null值，调用原始方法
        return getFunctionNameSafe(aggFun);
    }

    @Override
    public TSDBResultSet apmPercentageLatency(DatabaseWrapper dbClient, QueryBuilder builder) {
        List<Aggregation> aggregations = new ArrayList<>();
        for (int i = 0; i < 111; i++) {
            aggregations.add(Aggregation.of(AggFun.SUM, HISTOGRAM_FIELD + i, HISTOGRAM_FIELD + i));
        }
        aggregations.add(Aggregation.of(AggFun.MAX, COMPONENT_MAX_DURATION, COMPONENT_MAX_DURATION));
        aggregations.add(Aggregation.of(AggFun.SUM, COMPONENT_CALLS, COMPONENT_CALLS));
        List<Double> percents = new ArrayList<>();
        Map<String, Double> percentMap = new LinkedHashMap<>();
        List<Aggregation> oldAggs = builder.getAggregations();
        if (oldAggs != null && !oldAggs.isEmpty()) {
            for (Aggregation oldAgg : oldAggs) {
                if (oldAgg.getFunction().equals(AggFun.PERCENTILE)) {
                    Double percent = Double.parseDouble(oldAgg.getField());
                    percents.add(percent);
                    percentMap.put(StringUtils.isEmpty(oldAgg.getAlias()) ? oldAgg.getField() : oldAgg.getAlias(), percent);
                }
            }
        }
        builder.setAggregations(aggregations);
        //检验
        builder.validate();
        String sql = new OpenGeminiQueryBuilder(builder).buildQuery();
        TSDBResultSet resultSet = executeQuery(dbClient, sql, builder.getDatabaseName(), builder);
        List<TSDBResult> rets = resultSet.getResults();
        List<TSDBSeries> seriesList = rets.get(0).getSeries();

        TSDBResult result = new TSDBResult();
        result.setQueryType("QUERY");
        TSDBResultSet newResultSet = new TSDBResultSet();
        List<TSDBResult> newResults = new ArrayList<>();
        newResultSet.setResults(newResults);
        newResults.add(result);
        List<TSDBSeries> series = new ArrayList<>();
        result.setSeries(series);
        if (seriesList == null) {
            return newResultSet;
        }
        seriesList.forEach(s -> {
            TSDBSeries newSeries = new TSDBSeries();
            List<String> columns = new ArrayList<>();
            columns.add("time");
            columns.addAll(percentMap.keySet());
            newSeries.setColumns(columns);
            newSeries.setTags(s.getTags());
            List<List<Object>> values = new ArrayList();
            s.getValues().forEach(v -> {
                JSONObject openRet = new JSONObject();
                List<Object> objs = new ArrayList<>();
                for (int i = 0; i < s.getColumns().size(); i++) {
                    openRet.put(s.getColumns().get(i), v.get(i));
                    if (s.getColumns().get(i).equals("time")) {
                        objs.add(v.get(i));
                    }
                }
                //百分位计算
                Map<Double, Long> percentageLatency = PercentageLatencyUtil.percentageLatency(openRet, percents);
                for (Map.Entry<String, Double> entry : percentMap.entrySet()) {
                    Double value = entry.getValue();
                    if (percentageLatency.containsKey(value)) {
                        objs.add(percentageLatency.get(value));
                    } else {
                        objs.add(null);
                    }
                }
                values.add(objs);
            });
            newSeries.setValues(values);
            series.add(newSeries);
        });
        return newResultSet;
    }
}