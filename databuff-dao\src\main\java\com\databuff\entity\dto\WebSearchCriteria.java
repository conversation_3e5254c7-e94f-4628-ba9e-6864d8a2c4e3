package com.databuff.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collection;

@Data
public class WebSearchCriteria extends CommonSearchParams {
    @ApiModelProperty(value = "应用ID", example = "12345")
    private String appId;

    @ApiModelProperty(value = "traceId", example = "12345")
    private String traceId;

    @ApiModelProperty(value = "pageId", example = "12345")
    private String pageId;

    @ApiModelProperty(value = "spanId", example = "12345")
    private String spanId;

    @ApiModelProperty(value = "用户ID", example = "user_001")
    private String userId;

    @ApiModelProperty(value = "操作名称", example = "click")
    private String actionName;

    @ApiModelProperty(value = "操作名称(模糊)", example = "click")
    private String actionNameLike;

    @ApiModelProperty(value = "操作Id", example = "123456")
    private String actionId;

    @ApiModelProperty(value = "服务名称", example = "serviceA")
    private String service;

    @ApiModelProperty(value = "标准化过的URL", example = "http://example.com")
    private String processedLocationHref;

    @ApiModelProperty(value = "标准化过的URL(模糊)", example = "http://example.com")
    private String processedLocationHrefLike;

    @ApiModelProperty(value = "未标准化过的URL", example = "http://example.com")
    private String locationHref;

    @ApiModelProperty(value = "未标准化过的URL(模糊)", example = "http://example.com")
    private String locationHrefLike;

    @ApiModelProperty(value = "ip", example = "***********")
    private String ip;

    @ApiModelProperty(value = "别名", example = "home_page")
    private String alias;

    @ApiModelProperty(value = "字段", example = "pv, ttfb, fcp, lcp, fid, cls, fullLoadTime, tti, tbt, jsErrorPageCount, slowPageCount")
    private String field;

    @ApiModelProperty(value = "字段", example = "pv, ttfb, fcp, lcp, fid, cls, fullLoadTime, tti, tbt, jsErrorPageCount, slowPageCount")
    private Collection<String> fields;

    @ApiModelProperty(value = "聚合函数", example = "avg,upper")
    private String aggregation;

    @ApiModelProperty(value = "upper函数", example = "99,95,90,75,50")
    private Integer upperNumber;

    @ApiModelProperty(value = "会话ID", example = "12345")
    private String sessionId;
}