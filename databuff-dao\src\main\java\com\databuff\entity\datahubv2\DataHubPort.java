package com.databuff.entity.datahubv2;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "datahub_ports", autoResultMap = true)
public class DataHubPort {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    private Integer port;
    @TableField("processor_id")
    private Integer processorId;
}
