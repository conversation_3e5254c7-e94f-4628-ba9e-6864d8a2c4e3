package com.databuff.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Component
@RefreshScope
@Data
public class DAORefreshScopeConfig {

    /**
     * 无数据周期,默认3小时
     */
    @Value("${metric.query.noDataPeriod:10800}")
    private int noDataPeriod;


    @Value("${moreDbsqlEscape.character:}")
    private String specialCharacters;

    @Value("${alarm.listAllGroupTag.enabled:false}")
    private Boolean listAllGroupTagEnabled;

    @Value("${alarm.systemMetric.enabled:false}")
    private Boolean systemMetricEnabled;

    @Value("${root-engine.influence.abnormal.diff:2}")
    private Integer influenceAbnormalDiff;
}
