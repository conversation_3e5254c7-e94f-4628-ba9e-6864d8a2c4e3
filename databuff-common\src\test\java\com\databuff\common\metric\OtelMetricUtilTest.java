package com.databuff.common.metric;

import com.databuff.common.utils.OtelMetricUtil;
import com.databuff.common.utils.SleepUtil;
import io.opentelemetry.api.GlobalOpenTelemetry;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

import java.util.HashMap;
import java.util.Map;

public class OtelMetricUtilTest {

    @Before
    public void beforeInit() {
        GlobalOpenTelemetry.resetForTest();
        OtelMetricUtil.initWithLogExporter("test", 1);
    }

    @After
    public void afterInit() {
        SleepUtil.sleep(1500);
    }

    @Test
    public void testLogCounter() {
        Map<String, String> tags = new HashMap<>();
        tags.put("key1", "value1");
        OtelMetricUtil.logCounter("testCounter", tags, 5);

        tags = new HashMap<>();
        tags.put("key1", "value2");
        OtelMetricUtil.logCounter("testCounter", tags, 5);
        SleepUtil.sleep(60000);

        Assertions.assertTrue(tags.containsKey("key1"));
    }

    @Test
    public void testLogHistogram() {
        Map<String, String> tags = new HashMap<>();
        tags.put("key1", "value1");
        OtelMetricUtil.logHistogram("testHistogram", tags, 1);
        Assertions.assertTrue(tags.containsKey("key1"));
    }

    @Test
    public void logException() {
        RuntimeException exception = new RuntimeException();
        OtelMetricUtil.logException("logException", exception);
        Assertions.assertTrue(exception.getMessage().length()>1);
    }
}
