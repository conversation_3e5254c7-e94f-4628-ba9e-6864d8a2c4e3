package com.databuff.common.tsdb.builder;


import com.databuff.common.tsdb.model.Where;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryBuilderX extends QueryBuilder {

    private Map<String, Where> wheresMap = new HashMap<>();

    /**
     * 将指定的Where条件添加到查询构建器中，或替换已存在的同名条件。
     * 如果已存在相同字段的条件，则替换其属性值（如操作符、值、逻辑运算符等）；否则将新条件添加到内部映射和父级where列表中。
     *
     * @param where 要添加或替换的Where条件对象，其字段名不可为空或空白
     * @return 当前QueryBuilderX实例，支持链式调用
     */
    public QueryBuilderX addORReplaceWhere(Where where, Collection<String> whiteKeys) {
        if (where != null) {
            // 验证字段名是否有效
            String field = where.getField();
            if (field == null || field.isEmpty()) {
                super.addWhere(where);
                return this;
            }
            if (whiteKeys != null && !whiteKeys.isEmpty() && !whiteKeys.contains(field)) {
                return this;
            }
            Where existing = this.wheresMap.get(field);
            if (existing != null) {
                // 检查现有条件的field是否与传入的field一致
                if (existing.getField() == null || !existing.getField().equals(field)) {
                    throw new IllegalArgumentException("Field mismatch between existing and new condition");
                }
                // 替换现有条件的属性值
                this.wheresMap.put(field, where);
            } else {
                // 添加新条件到映射
                this.wheresMap.put(field, where);
            }
            // 添加或替换条件到父级where列表
            super.addWhere(where);
        }
        return this;
    }

    public QueryBuilderX addORReplaceWhere(Where where) {
        return addORReplaceWhere(where, null);
    }


    @ApiModelProperty(value = "自动填充值，当为null时启用自动填充")
    private Double autoFill;

    @ApiModelProperty(value = "指标名称")
    private String metric;

    @ApiModelProperty(value = "是否返回丰富格式的结果数据")
    private Boolean rich;

    /**
     * 批量添加GID集合（默认字段名"gid"）
     *
     * @param gids 待添加的GID集合，不允许为null，但允许为空集合
     * @throws IllegalArgumentException 如果gids为null
     */
    public void setWhereGids(Collection<String> gids) {
        setWhereGids(gids, null, null); // 调用带参数的重载方法
    }

    /**
     * 批量添加GID集合（支持自定义字段名）
     *
     * @param gids   待添加的GID集合，不允许为null，但允许为空集合
     * @param prefix 表名前缀（null时默认空字符串）
     * @param field  表字段（null时默认使用 {@code prefix + "gid"}）
     * @throws IllegalArgumentException 如果gids为null
     */
    public void setWhereGids(Collection<String> gids, String prefix, String field) {
        if (gids == null) {
            throw new IllegalArgumentException("Parameter 'gids' cannot be null");
        }
        // 自行实现GID过滤逻辑（非空+有效性验证）
        List<String> validGids = gids.stream()
                .filter(gid -> {
                    if (gid == null) return false;
                    String trimmed = gid.trim();
                    return !trimmed.isEmpty() && isValidGid(trimmed);
                })
                .map(String::trim)  // 确保使用处理后的值
                .collect(Collectors.toList());
        // 生成IN条件（字段名根据prefix和field动态拼接）
        if (!validGids.isEmpty()) {
            String effectivePrefix = (prefix == null) ? "" : prefix;
            String effectiveField = (field == null) ? effectivePrefix + "gid" : effectivePrefix + field;
            this.addORReplaceWhere(Where.in(effectiveField, validGids));
        }
    }

    /**
     * 验证GID是否有效（已处理trim后的字符串）
     *
     * @param trimmedGid 已去除空白的GID字符串
     * @return true表示GID有效（非空），false表示无效
     * <AUTHOR>
     */
    private boolean isValidGid(String trimmedGid) {
        return !trimmedGid.isEmpty();
    }

    /**
     * 设置分页参数（当pageSize=0时表示不分页查询）
     *
     * @param pageSize 每页记录数，0表示不分页（必须>=0）
     * @param pageNum  当前页码，当pageSize>0时必须>=1；当pageSize=0时该参数无效
     * @throws IllegalArgumentException 参数非法时抛出
     */
    public void setPage(int pageSize, int pageNum) {
        // 处理不分页场景
        if (pageSize <= 0 || (pageSize > 0 && pageNum < 1)) {
            this.setLimit(0L);
            this.setOffset(0);
            return;
        }

        // 计算分页参数
        try {
            int offset = Math.multiplyExact(pageNum - 1, pageSize);
            this.setLimit(pageSize);
            this.setOffset(offset);
        } catch (ArithmeticException e) {
            throw new IllegalArgumentException("Integer overflow in offset calculation", e);
        }
    }

}
