package com.databuff.entity.dto.topo;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(description = "拓扑分组实体")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Table(name = "topology_group")
public class TopologyGroup {

    @ApiModelProperty(value = "apiKey", example = "your-api-key")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    private String apiKey;

    @ApiModelProperty(value = "分组的唯一标识符", required = true, example = "group-id-123")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    private String id;

    @ApiModelProperty(value = "分组的名称", example = "Group Name")
    @TableField(value = "`name`")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    private String name;

    @ApiModelProperty(value = "分组的创建时间", example = "2023-10-01T12:00:00")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    private Date createTime;

    @ApiModelProperty(value = "分组的更新时间", example = "2023-10-02T12:00:00")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    private Date updateTime;
}