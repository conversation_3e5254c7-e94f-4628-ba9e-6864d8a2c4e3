package com.databuff.entity.dto;

import net.jpountz.xxhash.XXHash32;
import net.jpountz.xxhash.XXHashFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class IDGenerator {

    private static final XXHashFactory xxHashFactory = XXHashFactory.fastestInstance();
    private static final XXHash32 xxHash32 = xxHashFactory.hash32();
    private static final int SEED = 0x9747b28c;
    private static Map<String, Integer> idMap = new HashMap<>();

    public static int getNextID(int layer, String resource, int parentId) {
        String key = layer + resource + parentId;
        if (!idMap.containsKey(key)) {
            idMap.put(key, key.hashCode());
        }
        return idMap.get(key);
    }

    public static String getNextID(int layer, String resource, String parentId) {
        String key = layer + resource + parentId;
        return UUID.nameUUIDFromBytes(key.getBytes()).toString().replace("-", "");
    }

    public static String getNextID(String seed) {
        return UUID.nameUUIDFromBytes(seed.getBytes()).toString().replace("-", "");
    }


//    private static final ConcurrentHashMap<String, String> idCache = new ConcurrentHashMap<>();

    /**
     * 使用一致的哈希方法来确保相同的种子返回相同的ID,并且保证线程安全
     * 优点：更加高效
     * 缺点：非定长
     * @param seed
     * @return
     */
    public static String getNextHashID(String seed) {
//        return idCache.computeIfAbsent(seed, k -> generateID(k));
        return generateID(seed);
    }

    /**
     * 使用一致的哈希方法来确保相同的种子返回相同的ID
     * @param seed
     * @return
     */
    private static String generateID(String seed) {
        return Integer.toHexString(seed.hashCode());
    }

    public static String generateID(int seed) {
        return Integer.toHexString(seed);
    }

    public static int generateXXHashID(String seed) {
        if (seed == null) {
            return 0;
        }
        final byte[] bytes = seed.getBytes();
        return xxHash32.hash(bytes, 0, bytes.length, SEED);
    }
}