package com.databuff.common.tsdb.dto;

import com.databuff.common.tsdb.model.LogicalOperator;
import com.databuff.common.tsdb.model.WhereOp;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表示一个复合条件，通过指定的连接符（如AND或OR）将左右两侧的条件列表组合起来。
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CompositeCondition implements FilterCondition {

    @ApiModelProperty(value = "条件类型，固定为composite", allowableValues = "composite")
    private String type = "composite";

    @ApiModelProperty(value = "逻辑操作符，支持AND/OR", example = "AND", allowableValues = "AND,OR")
    private LogicalOperator connector;

    @ApiModelProperty(value = "左侧条件表达式（可以是简单条件或复合条件）", example = "{\"field\":\"host\", \"operator\":\"=\", \"value\":\"server1\"}")
    private Object left;

    @ApiModelProperty(value = "右侧条件表达式（可以是简单条件或复合条件）", example = "{\"field\":\"service\", \"operator\":\"!=\", \"value\":\"web\"}")
    private Object right;

    @ApiModelProperty(value = "是否启用大小写不敏感匹配", example = "false")
    private Boolean caseInsensitive;

    @ApiModelProperty(value = "操作符符号（如=、!=、>等）", example = ">", allowableValues = "==,!=,>,<,>=,<=,IN,NOT IN")
    private String operator;

    @ApiModelProperty(hidden = true)
    public WhereOp getOperatorEnum() {
        return WhereOp.fromSymbol(this.operator);
    }
}