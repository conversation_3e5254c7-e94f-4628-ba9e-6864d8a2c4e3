package com.databuff.common.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.LongAdder;

public class ScheduledMetricUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(ScheduledMetricUtil.class);

    private static final ScheduledExecutorService sch = Executors.newSingleThreadScheduledExecutor();
    private static final Object obj = new Object();
    private static final Map<Counter, Object> COUNTERS = new ConcurrentHashMap<>();
    private static final Map<Timer, Object> TIMERS = new ConcurrentHashMap<>();

    static {
        sch.scheduleAtFixedRate(() -> logMetric(), 10, 10, TimeUnit.SECONDS);
    }

    private static void logMetric() {
        for (Map.Entry<Counter, Object> entry : COUNTERS.entrySet()) {
            try {
                Counter counter = entry.getKey();
                long count = counter.adder.sumThenReset();
                if (count > 0) {
                    OtelMetricUtil.logCounter(counter.metricName, counter.tags, count);
                }
            } catch (Throwable e) {
                LOGGER.error("logCounter error", e);
            }
        }
        for (Map.Entry<Timer, Object> entry : TIMERS.entrySet()) {
            try {
                Timer timer = entry.getKey();
                long count = timer.countAdder.sumThenReset();
                long sum = timer.sumAdder.sumThenReset();
                if (count > 0) {
                    OtelMetricUtil.logHistogram(timer.metricName, timer.tags, sum / count);
                }
            } catch (Throwable e) {
                LOGGER.error("logTimer error", e);
            }
        }
    }

    public static class Counter {

        private String metricName;
        private Map<String, String> tags;

        public Counter(String metricName, Map<String, String> tags) {
            this.metricName = metricName;
            this.tags = tags;
            COUNTERS.put(this, obj);
        }

        private LongAdder adder = new LongAdder();

        public void add(long count) {
            adder.add(count);
        }
    }

    public static class Timer {

        private String metricName;
        private Map<String, String> tags;

        public Timer(String metricName, Map<String, String> tags) {
            this.metricName = metricName;
            this.tags = tags;
            TIMERS.put(this, obj);
        }

        private LongAdder countAdder = new LongAdder();
        private LongAdder sumAdder = new LongAdder();

        public void add(long duration) {
            countAdder.add(1);
            sumAdder.add(duration);
        }
    }

}
