package com.databuff.common.utils;

import com.alibaba.fastjson.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.databuff.common.constants.MetricConstant.COMPONENT_CALLS;
import static com.databuff.common.constants.MetricConstant.COMPONENT_MAX_DURATION;

/**
 * @author:TianMing
 * @date: 2023/8/10
 * @time: 14:01
 */
public class PercentageLatencyUtil {

    public static final String HISTOGRAM_FIELD = "histogramField";

    /**
     * 如果 duration 小于等于 0，则返回 0。
     * 如果 duration 小于等于 1000，返回 (byte) (duration / 200)，即 duration 除以 200 的结果作为索引。取值范围在 0 到 5 之间。
     * 如果 duration 小于等于 20,000，返回 (byte) (4 + duration / 1000)，即 duration 除以 1000 的结果加上 4 作为索引。取值范围在 5 到 24 之间。
     * 如果 duration 小于 200,000，返回 (byte) (24 + (duration - 20,000) / 10,000)，即 (duration - 20,000) 除以 10,000 的结果加上 24 作为索引。取值范围在 24 到 41 之间。
     * 如果 duration 小于 500,000，返回 (byte) (42 + (duration - 200,000) / 20,000)，即 (duration - 200,000) 除以 20,000 的结果加上 42 作为索引。取值范围在 42 到 56 之间。
     * 如果 duration 小于 2,000,000，返回 (byte) (57 + (duration - 500,000) / 50,000)，即 (duration - 500,000) 除以 50,000 的结果加上 57 作为索引。取值范围在 57 到 86 之间。
     * 如果 duration 小于 5,000,000，返回 (byte) (87 + (duration - 2,000,000) / 500,000)，即 (duration - 2,000,000) 除以 500,000 的结果加上 87 作为索引。取值范围在 87 到 92 之间。
     * 如果 duration 小于 10,000,000，返回 (byte) (93 + (duration - 5,000,000) / 1,000,000)，即 (duration - 5,000,000) 除以 1,000,000 的结果加上 93 作为索引。取值范围在 93 到 97 之间。
     * 如果 duration 小于 20,000,000，返回 (byte) (98 + (duration - 10,000,000) / 2,000,000)，即 (duration - 10,000,000) 除以 2,000,000 的结果加上 98 作为索引。取值范围在 98 到 102 之间。
     * 如果 duration 小于 35,000,000，返回 (byte) (103 + (duration - 20,000,000) / 3,000,000)，即 (duration - 20,000,000) 除以 3,000,000 的结果加上 103 作为索引。取值范围在 103 到 107 之间。
     * 如果 duration 小于 45,000,000，返回 (byte) (108 + (duration - 35,000,000) / 5,000,000)，即 (duration - 35,000,000) 除以 5,000,000 的结果加上 108 作为索引。取值范围在 108 到 109 之间。
     * 如果 duration 大于等于 45,000,000，返回 110。
     *
     * @param duration
     * @return
     */
    public static byte computeMicrosIndex(long duration) {
        if (duration <= 0) {
            return 0;
        } else if (duration < 1000) {
            return (byte) (duration / 200);//[0-4]
        } else if (duration < 20_000) {
            return (byte) (4 + duration / 1000);//[5-23]
        } else if (duration < 200_000) {
            return (byte) (24 + (duration - 20_000) / 10_000);//18 [24-41]
        } else if (duration < 500_000) {
            return (byte) (42 + (duration - 200_000) / 20_000);//15 [42-56]
        } else if (duration < 2000_000) {
            return (byte) (57 + (duration - 500_000) / 50_000);//30 [57-86]
        } else if (duration < 5000_000) {
            return (byte) (87 + (duration - 2000_000) / 500_000);//6 [87-92]
        } else if (duration < 10000_000) {
            return (byte) (93 + (duration - 5000_000) / 1000_000);//5 [93-97]
        } else if (duration < 20000_000) {
            return (byte) (98 + (duration - 10_000_000) / 2000_000);//5 [98-102]
        } else if (duration < 35000_000) {
            return (byte) (103 + (duration - 20_000_000) / 3000_000);//5 [103-107]
        } else if (duration < 45000_000) {
            return (byte) (108 + (duration - 35_000_000) / 5000_000);//2 [108-109]
        } else {
            return 110;
        }
    }

    public static long[] getIndexTimeInterval(int index) {
        long[] durationRange = new long[2];

        if (index == 0) {
            durationRange[0] = 0;
            durationRange[1] = 200;
        } else if (index <= 4) {
            durationRange[0] = index * 200;
            durationRange[1] = (index + 1) * 200;
        } else if (index <= 23) {
            durationRange[0] = (index - 4) * 1000;
            durationRange[1] = (index - 3) * 1000;
        } else if (index <= 41) {
            durationRange[0] = 20000 + (index - 24) * 10000;
            durationRange[1] = 20000 + (index - 23) * 10000;
        } else if (index <= 56) {
            durationRange[0] = 200000 + (index - 42) * 20000;
            durationRange[1] = 200000 + (index - 41) * 20000;
        } else if (index <= 86) {
            durationRange[0] = 500000 + (index - 57) * 50000;
            durationRange[1] = 500000 + (index - 56) * 50000;
        } else if (index <= 92) {
            durationRange[0] = 2000000 + (index - 87) * 500000;
            durationRange[1] = 2000000 + (index - 86) * 500000;
        } else if (index <= 97) {
            durationRange[0] = 5000000 + (index - 93) * 1000000;
            durationRange[1] = 5000000 + (index - 92) * 1000000;
        } else if (index <= 102) {
            durationRange[0] = 10000000 + (index - 98) * 2000000;
            durationRange[1] = 10000000 + (index - 97) * 2000000;
        } else if (index <= 107) {
            durationRange[0] = 20000000 + (index - 103) * 3000000;
            durationRange[1] = 20000000 + (index - 102) * 3000000;
        } else if (index <= 109) {
            durationRange[0] = 35000000 + (index - 108) * 5000000;
            durationRange[1] = 35000000 + (index - 107) * 5000000;
        } else if (index == 110) {
            durationRange[0] = 45000000;
            durationRange[1] = Long.MAX_VALUE;
        } else {
            throw new IllegalArgumentException("Invalid index value: " + index);
        }

        return durationRange;
    }


    /**
     * 获取span数据的延迟百分比
     *
     * @param openRet {
     *                  <p>
     *                  "percentileB97": 3,
     *                  "percentileB96": 6,
     *                  "percentileB11": 19,
     *                  ...
     *                  "percentileB27": 10,
     *                  "percentileB29": 13,
     *                  "percentileB20": 7,
     *                  "percentileB22": 6,
     *                  "percentileB21": 8,
     *                  "percentileB24": 66,
     *                  "percentileB23": 11,
     *                  "percentileB37": 11,
     *                  "percentileB36": 9,
     *                  "percentileB39": 6,
     *                  "percentileB42": 8	}
     * @param percents  百分比 50.0, 75.0,90.0, 95.0, 99.0,100
     *                  需要查询percentileB0-110的值和maxLatency即库中的p100Latency,callCnt
     * @return
     */
    public static Map<Double, Long> percentageLatency(JSONObject openRet, List<Double> percents) {
        Map<Double, Long> ret = new HashMap<>(6);
        if (openRet.getLong(COMPONENT_CALLS) == null || openRet.getLong(COMPONENT_MAX_DURATION) == null) {
            for (int i = 0; i < percents.size(); i++) {
                ret.put(percents.get(i), null);
            }
            return ret;
        }
        long maxLatency = openRet.getLong(COMPONENT_MAX_DURATION);
        for (int i = 0; i < percents.size(); i++) {
            double percent = percents.get(i);
            if (percent == 100) {
                ret.put(percent, maxLatency);
                continue;
            }
            //influxRet种percentileB0为第一个桶，percentileB110为最后一个桶，以此类推，其value为桶的个数，callCnt为所有桶个数的和，percent为要取的百分位值，比如75.0，callCnt*（1-0.75）为需要从后往前推的个数,现在需要找出这个个数在哪个桶中，且占桶的百分比为多少
            //需要找到的百分位数的位置（从后往前）
            int bucketNum = (int) Math.ceil(openRet.getLong(COMPONENT_CALLS) * (1 - percent / 100));
            //桶的索引
            int bucketIndex = 0;
            //从后往前当前累计的桶的个数
            int bucketCount = 0;
            //锁定桶后百分位数位置在当前桶的位置（从前往后）
            int bucketCountIndex = 0;
            //y 桶的个数 z密度
            double z1 = 0;
            double z2 = 0;

            for (int j = 110; j >= 0; j--) {
                z2 = z1;
                double y1 = openRet.getLongValue(HISTOGRAM_FIELD + j);
                long[] latencyInterval = PercentageLatencyUtil.getIndexTimeInterval(j);
                z1 = y1 / (latencyInterval[1] * 1000 - latencyInterval[0] * 1000);
                bucketCount += openRet.getLongValue(HISTOGRAM_FIELD + j);
                //当累计桶的数大于百分位桶数时，说明当前桶就是要找的桶
                if (bucketCount >= bucketNum) {
                    bucketIndex = j;
                    bucketCountIndex = bucketCount - bucketNum;
                    break;
                }
            }
            //获取桶的时间区间
            long[] latencyInterval = PercentageLatencyUtil.getIndexTimeInterval(bucketIndex);
            //转换ns
            long latencyIntervalStart = latencyInterval[0] * 1000;
            long latencyIntervalEnd = latencyInterval[1] * 1000;
            if (bucketIndex == 110 || latencyIntervalEnd > maxLatency) {
                //如果在最后一个桶则最大值为最大延迟 , 或者桶的结束时间大于最大延迟，则桶的结束时间为最大延迟
                //转换ns
                latencyIntervalEnd = maxLatency;
            }
            if (openRet.getLongValue(HISTOGRAM_FIELD + bucketIndex) == 0) {
                ret.put(percent, null);
                continue;
            }
            //在当前桶的时间区间内的延迟耗时
            long indexLatencyInterval = bucketCountIndex * (latencyIntervalEnd - latencyIntervalStart) / openRet.getLongValue(HISTOGRAM_FIELD + bucketIndex);
            //按占比计算百分位数的延迟 相等取平均值，密度变大乘系数1.2，密度变小乘系数0.8
            double latency;
            if (z2 == z1) {
                latency = latencyIntervalStart + indexLatencyInterval;
            } else if (z2 > z1) {
                latency = latencyIntervalStart + indexLatencyInterval * 1.2;
            } else {
                latency = latencyIntervalStart + indexLatencyInterval * 0.8;
            }
            ret.put(percent, (long) latency);
        }
        return ret;
    }
}