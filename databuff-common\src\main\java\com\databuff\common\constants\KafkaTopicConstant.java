package com.databuff.common.constants;

/**
 * <AUTHOR>
 * @Date 2022/8/10
 * @Description: kafka topic 常量
 */
public class KafkaTopicConstant {

    //事件topic
    public static final String EVENT_TOPIC = "dc_event";
    public static final String EVENT_SYSTEM_TOPIC = "dc_event_system";
    public static final String EVENT_CONVERGENCE_TOPIC = "dc_event_convergence";
    public static final String ALARM_TOPIC = "dc_alarm";
    //指标topic
    public static final String METRIC_TOPIC = "dc_databuff_metric";
    //原始指标数据topic
    public static final String RAW_METRIC_TOPIC = "dc_databuff_metric_test_raw";
    //管理域动作topic
    public static final String DC_DATABUFF_ACTION_GROUP = "dc_databuff_action_group";
    public static final String DC_DATABUFF_ACTION_GROUP_ADD = "添加";
    public static final String DC_DATABUFF_ACTION_GROUP_DELETE = "刪除";
    public static final String DC_DATABUFF_ACTION_GROUP_UPDATE = "更新";

}
