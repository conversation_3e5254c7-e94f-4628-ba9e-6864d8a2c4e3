package com.databuff.entity.rum.web;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.util.Date;

@ApiModel(description = "JS错误趋势图查询")
@Data
public class JsErrorTrendGraphRequest {

    @NotNull(message = "应用ID不能为空")
    @Positive(message = "应用ID必须为正数")
    @ApiModelProperty(value = "应用id", required = true)
    private int appId;

    @ApiModelProperty(value = "apiKey", example = "NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4")
    private String apiKey;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date fromTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date toTime;

    @ApiModelProperty(value = "间隔(秒)", example = "60")
    private Integer interval;

    @NotNull(message = "指标类型不能为空")
    @ApiModelProperty(value = "指标类型", required = true, example = "error_rate")
    private JsErrorMetricType metricType;

    public enum JsErrorMetricType {
        /**
         * JS错误率趋势
         */
        error_rate,
        /**
         * 影响用户数趋势图 uv
         */
        uv,
        /**
         * 影响用户数趋势图 JS错误次数
         */
        error_count;

        public static JsErrorMetricType fromString(String value) {
            for (JsErrorMetricType type : JsErrorMetricType.values()) {
                if (type.name().equalsIgnoreCase(value)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("Unknown JsErrorMetricType: " + value);
        }
    }



}
