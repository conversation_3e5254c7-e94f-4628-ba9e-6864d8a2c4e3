package com.databuff.common.utils;


public class RedisKeyUtil {

    public static final String HOST_PREFIX = "host:";
    public static final String PROCESS_PID_PREFIX = "process:pid:";
    public static final String SERVICE_PID_PREFIX = "service:pid:";
    public static final String SERVICE_CID_PREFIX = "service:cid:";
    public static final String SERVICE_INSTANCE_PREFIX = "service:instance:env:";
    public static final String SERVICE_POD_NAME_PREFIX = "service:podname:";
    public static final String POD_CID_PREFIX = "pod:cid:";
    public static final String CNTR_CID_PREFIX = "cntr:cid:";
    public static final String SERVICE_REMOTE_PREFIX = "service:remote:";

    public static String generateProcessPidHost(String apiKey, String hostName, String pid) {
        return PROCESS_PID_PREFIX + apiKey + ":" + hostName + ":" + pid;
    }
    public static String generatePidServiceInfo(String apiKey, String hostName, String pid) {
        return SERVICE_PID_PREFIX + apiKey + ":" + hostName + ":" + pid;
    }
    public static String generateContainerServiceInfo(String apiKey, String k8sClusterIdOrHost, String cid) {
        return SERVICE_CID_PREFIX + apiKey + ":" + k8sClusterIdOrHost + ":" + cid;
    }
    public static String generatePodNameServiceInfo(String apiKey, String k8sClusterId, String podName) {
        return SERVICE_POD_NAME_PREFIX + apiKey + ":" + k8sClusterId + ":" + podName;
    }
    public static String generatePodCidInfo(String apiKey, String k8sClusterId, String cid) {
        return POD_CID_PREFIX + apiKey + ":" + k8sClusterId + ":" + cid;
    }
    public static String generateContainerCidInfo(String apiKey, String hostName, String cid) {
        return CNTR_CID_PREFIX + apiKey + ":" + hostName + ":" + cid;
    }
    public static String generateHostIpName(String apiKey, String hostIp) {
        return HOST_PREFIX + apiKey + ":" + hostIp;
    }
    public static String generateRemotelySvc(String apiKey, String remotelySvc) {
        return SERVICE_REMOTE_PREFIX + apiKey + ":" + remotelySvc;
    }
    public static String generateInstanceEnv(String serviceId, String serviceInstance) {
        return SERVICE_INSTANCE_PREFIX + serviceId + ":" + serviceInstance;
    }
}
