package com.databuff.common.threadLocal;

import java.util.Collection;

/**
 * InheritableThreadLocal 是 ThreadLocal 的子类，允许子线程继承父线程的本地变量。
 * 当创建子线程时，子线程会从父线程中拷贝一份本地变量的值。
 * 适用于需要在父子线程之间共享数据的场景。
 */
public class InheritableThreadLocalUtil {
    private static final ThreadLocal<ThreadLocalEntity> THREAD_LOCAL = new InheritableThreadLocal<>();

    public static void setThreadLocal(ThreadLocalEntity value) {
        THREAD_LOCAL.set(value);
    }
    public static ThreadLocalEntity getThreadLocal() {
        return  THREAD_LOCAL.get();
    }
    public static void removeThreadLocal() {
        THREAD_LOCAL.remove();
    }

    public static Collection<String> getGid() {
        return THREAD_LOCAL.get()==null?null:THREAD_LOCAL.get().getAgis();
    }
    public static String getAccount() {
        return THREAD_LOCAL.get()==null?null:THREAD_LOCAL.get().getAccount();
    }

    public static boolean hasAnnotate() {
        final ThreadLocalEntity threadLocalEntity = THREAD_LOCAL.get();
        return threadLocalEntity == null ? false : threadLocalEntity.isHasAnnotate();
    }

    protected static void setAnnotate(boolean hasAnnotate) {
        final ThreadLocalEntity threadLocal = getThreadLocal();
        if (threadLocal != null) {
            threadLocal.setHasAnnotate(hasAnnotate);
            threadLocal.setUpdateFlag(true);
        } else {
            setThreadLocal(ThreadLocalEntity.builder()
                    .hasAnnotate(hasAnnotate)
                    .updateFlag(false)
                    .build());
        }
    }
}
