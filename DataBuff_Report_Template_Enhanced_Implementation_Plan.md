# DataBuff 报告模板系统增强 - 最新详细实施计划

## 项目概述

基于数据结构设计方案和ERD分析，对DataBuff报告模板系统进行全面升级，实现：
- 保持API完全兼容的前提下扩展功能 
- 新增标题、文本、异常评估三种组件类型
- 图表组件增强异常检测和AI洞察功能
- **所有新功能配置都存储在content字段中**
- **复用现有AI服务和动态基线接口**

## 系统架构设计

### 整体架构图

```mermaid
graph TB
    subgraph "前端 Vue.js"
        A[整页编辑器] --> B[拖拽组件面板]
        B --> C[配置属性面板]
        C --> D[预览渲染区]
    end
    
    subgraph "后端 Spring Boot"
        E[API兼容层] --> F[组件处理器工厂]
        F --> G[标题处理器]
        F --> H[文本处理器] 
        F --> I[异常评估处理器]
        F --> J[图表增强处理器]
        
        J --> K[现有OpenAI服务]
        J --> L[现有MetricAggregator]
        I --> M[异常数据收集器]
    end
    
    subgraph "数据存储"
        N[(dc_report_template)]
        O[(现有告警数据)]
        P[Word文档输出]
    end
    
    A --> E
    E --> N
    M --> O
    G --> P
    H --> P
    I --> P
    J --> P
```

### 组件处理流程图

```mermaid
flowchart TD
    A[用户编辑模板] -->|保存| B[content JSON解析]
    B --> C{组件类型识别}
    
    C -->|title| D[标题处理器]
    C -->|text| E[文本处理器]
    C -->|anomaly_evaluation| F[异常评估处理器]
    C -->|bar/line/pie| G[图表增强处理器]
    
    D --> H[自动编号生成]
    E --> I[富文本处理]
    F --> J[收集异常数据]
    G --> K[动态基线检测]
    G --> L[AI洞察生成]
    
    H --> M[Word内容生成]
    I --> M
    J --> M
    K --> M
    L --> M
    
    M --> N[报告文档输出]
```

### 异常评估数据流

```mermaid
sequenceDiagram
    participant U as 用户
    participant E as 编辑器
    participant P as 异常评估处理器
    participant C as 异常数据收集器
    participant A as 告警数据库
    participant W as Word生成器
    
    U->>E: 添加异常评估组件
    E->>P: 处理异常评估配置
    P->>C: 收集异常事件数据
    C->>A: 查询时间范围内告警
    A-->>C: 返回告警事件列表
    C-->>P: 转换为异常事件
    P->>P: 应用模板格式化
    P-->>W: 生成Word段落内容
    W-->>U: 输出最终报告
```

## 总体架构变更

### 核心设计原则
1. **API兼容性优先**: 现有API行为保持不变，不修改dc_report_template表结构
2. **content字段扩展**: 所有新组件配置通过extra字段存储在content JSON中
3. **服务复用**: 复用现有openAIService.sendMessage()和baselineResult()接口
4. **向后兼容**: 新旧组件数据格式并存

### 数据流架构
```
用户请求 → API兼容层 → 组件处理器 → content解析 → 现有服务调用 → Word生成
                ↓
报告生成 ← 现有OpenAI服务 ← 现有动态基线服务 ← 组件解析
```

## Content字段数据结构设计

### 新组件在content中的存储格式

#### 标题组件 (title)
```json
{
  "index": 0,
  "type": "title", 
  "title": "",
  "text": "报告标题内容",
  "query": "",
  "limit": 0,
  "interval": "",
  "extra": "{
    \"version\": \"2.0\",
    \"uuid\": \"生成的UUID\",
    \"type\": \"title\",
    \"titleType\": \"large|medium|small\",
    \"content\": \"标题内容 (≤50字)\",
    \"autoNumber\": true,
    \"level\": 1,
    \"wordLimit\": 50
  }",
  "unit": ""
}
```

#### 文本组件 (text)
```json
{
  "index": 1,
  "type": "text",
  "title": "",
  "text": "文本内容预览", 
  "query": "",
  "limit": 0,
  "interval": "",
  "extra": "{
    \"version\": \"2.0\",
    \"uuid\": \"生成的UUID\",
    \"type\": \"text\",
    \"content\": \"<p>富文本HTML内容</p>\",
    \"contentType\": \"html|plain\",
    \"wordLimit\": 1000,
    \"currentWordCount\": 0
  }",
  "unit": ""
}
```

#### 异常评估组件 (anomaly_evaluation)
```json
{
  "index": 2,
  "type": "anomaly_evaluation",
  "title": "异常情况评估",
  "text": "异常评估内容预览",
  "query": "",
  "limit": 0,
  "interval": "",
  "extra": "{
    \"version\": \"2.0\",
    \"uuid\": \"生成的UUID\",
    \"type\": \"anomaly_evaluation\",
    \"auto_generated\": true,
    \"template\": \"【{system}】【{service}】【{metric}】在 {time} 开始异常，持续 {duration} 分钟\",
    \"no_anomaly_text\": \"本次报告周期内无异常\"
  }",
  "unit": ""
}
```

#### 增强图表组件 (现有图表 + AI + 异常检测)
```json
{
  "index": 3,
  "type": "bar",
  "title": "databuff.alarm_sr.cnt",
  "text": "",
  "query": "{现有查询结构}",
  "limit": 5,
  "interval": 600,
  "extra": "{
    \"version\": \"2.0\",
    \"uuid\": \"现有UUID\",
    \"type\": \"bar\",
    \"metrics\": {现有metrics结构},
    \"order\": \"top\",
    \"limit\": 5,
    \"interval\": 600,
    \"text\": \"\",
    \"unit\": \"个\",
    \"anomaly_detection\": {
      \"enabled\": true,
      \"method\": \"dynamic_baseline\",
      \"baselineScope\": 1.5,
      \"comparison\": \">\",
      \"thresholds\": {\"critical\": 3, \"warning\": 1}
    },
    \"ai_insights\": {
      \"enabled\": true,
      \"auto_generate\": true,
      \"max_length\": 500
    }
  }",
  "unit": "个"
}
```

## 详细任务分解

---

## 阶段一：组件处理器与验证系统

### TASK-001: 组件处理器架构开发
**优先级**: 最高  
**估计工期**: 2工作日 (16小时)  
**负责模块**: 后端组件处理

#### 1.1 组件处理器基础架构 (0.5工作日)

**实施位置**: `webapp/src/main/java/com/databuff/webapp/report/processor/`

**ComponentProcessor.java** (接口):
```java
public interface ComponentProcessor {
    
    /**
     * 组件类型标识
     */
    String getComponentType();
    
    /**
     * 验证组件配置
     */
    ValidationResult validate(ComponentConfig config);
    
    /**
     * 处理组件配置
     */
    ProcessedComponent process(ComponentConfig config, ReportContext context);
    
    /**
     * 生成Word文档内容
     */
    void generateWordContent(XWPFDocument document, ProcessedComponent component);
    
    /**
     * 支持的配置版本
     */
    List<String> getSupportedVersions();
}
```

#### 1.2 标题组件处理器 (0.5工作日)

**TitleComponentProcessor.java**:
```java
@Component
public class TitleComponentProcessor implements ComponentProcessor {
    
    private static final String COMPONENT_TYPE = "title";
    
    @Override
    public String getComponentType() {
        return COMPONENT_TYPE;
    }
    
    @Override
    public ValidationResult validate(ComponentConfig config) {
        ValidationResult result = new ValidationResult();
        
        // 内容验证
        String content = config.getStringValue("content");
        if (StringUtils.isEmpty(content)) {
            result.addError("content", "标题内容不能为空");
        } else if (content.length() > 50) {
            result.addError("content", "标题内容不能超过50字");
        }
        
        return result;
    }
    
    @Override
    public ProcessedComponent process(ComponentConfig config, ReportContext context) {
        ProcessedComponent processed = new ProcessedComponent(COMPONENT_TYPE);
        
        String content = config.getStringValue("content");
        boolean autoNumber = config.getBooleanValue("autoNumber", true);
        int level = config.getIntValue("level", 1);
        
        // 自动编号生成
        if (autoNumber) {
            String generatedNumber = generateAutoNumber(context.getTitleComponents(), level);
            processed.addProperty("generated_number", generatedNumber);
            processed.addProperty("display_content", generatedNumber + " " + content);
        } else {
            processed.addProperty("display_content", content);
        }
        
        return processed;
    }
    
    @Override
    public void generateWordContent(XWPFDocument document, ProcessedComponent component) {
        String displayContent = component.getStringProperty("display_content");
        String titleType = component.getStringProperty("titleType");
        
        XWPFParagraph paragraph = document.createParagraph();
        XWPFRun run = paragraph.createRun();
        run.setText(displayContent);
        run.setBold(true);
        
        // 根据标题类型设置字体大小
        switch (titleType) {
            case "large": run.setFontSize(18); break;
            case "medium": run.setFontSize(16); break;
            case "small": run.setFontSize(14); break;
        }
    }
    
    private String generateAutoNumber(List<ProcessedComponent> titleComponents, int currentLevel) {
        // 自动编号逻辑：一、二、三 / 1.1、1.2 / 1.1.1、1.1.2
        // 实现章节编号生成算法
        return "自动生成的编号";
    }
    
    @Override
    public List<String> getSupportedVersions() {
        return Arrays.asList("2.0");
    }
}
```

#### 1.3 文本组件处理器 (0.5工作日)

**TextComponentProcessor.java**:
```java
@Component  
public class TextComponentProcessor implements ComponentProcessor {
    
    private static final String COMPONENT_TYPE = "text";
    
    @Override
    public ValidationResult validate(ComponentConfig config) {
        ValidationResult result = new ValidationResult();
        
        String content = config.getStringValue("content");
        if (StringUtils.isEmpty(content)) {
            result.addError("content", "文本内容不能为空");
        }
        
        int wordLimit = config.getIntValue("wordLimit", 1000);
        if (wordLimit < 10 || wordLimit > 2000) {
            result.addError("wordLimit", "字数限制必须在10-2000之间");
        }
        
        return result;
    }
    
    @Override
    public ProcessedComponent process(ComponentConfig config, ReportContext context) {
        ProcessedComponent processed = new ProcessedComponent(COMPONENT_TYPE);
        
        String content = config.getStringValue("content");
        String contentType = config.getStringValue("contentType", "plain");
        
        // 处理富文本内容
        String processedContent = processContent(content, contentType);
        processed.addProperty("content", processedContent);
        processed.addProperty("contentType", contentType);
        
        return processed;
    }
    
    @Override
    public void generateWordContent(XWPFDocument document, ProcessedComponent component) {
        String content = component.getStringProperty("content");
        String contentType = component.getStringProperty("contentType");
        
        XWPFParagraph paragraph = document.createParagraph();
        XWPFRun run = paragraph.createRun();
        
        if ("html".equals(contentType)) {
            // 简化HTML解析，提取纯文本
            String plainText = removeHtmlTags(content);
            run.setText(plainText);
        } else {
            run.setText(content);
        }
    }
    
    private String processContent(String content, String contentType) {
        if ("html".equals(contentType)) {
            return sanitizeHtml(content);
        }
        return content;
    }
    
    private String removeHtmlTags(String html) {
        return html.replaceAll("<[^>]+>", "");
    }
    
    private String sanitizeHtml(String html) {
        // 清理危险的HTML标签
        return html.replaceAll("<script[^>]*>.*?</script>", "");
    }
    
    @Override
    public String getComponentType() {
        return COMPONENT_TYPE;
    }
    
    @Override
    public List<String> getSupportedVersions() {
        return Arrays.asList("2.0");
    }
}
```

#### 1.4 异常评估组件处理器 (0.5工作日)

**AnomalyEvaluationComponentProcessor.java**:
```java
@Component
public class AnomalyEvaluationComponentProcessor implements ComponentProcessor {
    
    private static final String COMPONENT_TYPE = "anomaly_evaluation";
    
    @Autowired
    private AnomalyDataCollector anomalyDataCollector;
    
    @Override
    public ValidationResult validate(ComponentConfig config) {
        ValidationResult result = new ValidationResult();
        
        String template = config.getStringValue("template");
        if (StringUtils.isEmpty(template)) {
            result.addError("template", "异常评估模板不能为空");
        } else if (!template.matches(".*\\{[^}]+\\}.*")) {
            result.addError("template", "模板必须包含至少一个变量 {variable}");
        }
        
        return result;
    }
    
    @Override
    public ProcessedComponent process(ComponentConfig config, ReportContext context) {
        ProcessedComponent processed = new ProcessedComponent(COMPONENT_TYPE);
        
        boolean autoGenerated = config.getBooleanValue("auto_generated", true);
        String template = config.getStringValue("template");
        String noAnomalyText = config.getStringValue("no_anomaly_text", "本次报告周期内无异常");
        
        if (autoGenerated) {
            // 收集异常数据
            List<AnomalyEvent> anomalies = collectAnomalies(context);
            
            if (anomalies.isEmpty()) {
                processed.addProperty("content", noAnomalyText);
                processed.addProperty("has_anomalies", false);
            } else {
                String generatedContent = generateAnomalyContent(template, anomalies);
                processed.addProperty("content", generatedContent);
                processed.addProperty("has_anomalies", true);
                processed.addProperty("anomaly_count", anomalies.size());
            }
        } else {
            processed.addProperty("content", config.getStringValue("content", noAnomalyText));
            processed.addProperty("has_anomalies", false);
        }
        
        return processed;
    }
    
    private List<AnomalyEvent> collectAnomalies(ReportContext context) {
        // 根据报告时间范围收集异常事件
        long startTime = context.getStartTime();
        long endTime = context.getEndTime();  
        String apiKey = context.getApiKey();
        
        return anomalyDataCollector.collectAnomalies(apiKey, startTime, endTime);
    }
    
    private String generateAnomalyContent(String template, List<AnomalyEvent> anomalies) {
        StringBuilder content = new StringBuilder();
        
        for (int i = 0; i < anomalies.size(); i++) {
            AnomalyEvent event = anomalies.get(i);
            String eventText = processTemplate(template, event);
            
            content.append(i + 1).append(". ").append(eventText);
            if (i < anomalies.size() - 1) {
                content.append("\n");
            }
        }
        
        return content.toString();
    }
    
    private String processTemplate(String template, AnomalyEvent event) {
        // 格式化时间为易读格式
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formattedTime = dateFormat.format(event.getStartTime());
        
        return template
            .replace("{system}", event.getSystem())
            .replace("{service}", event.getService())
            .replace("{metric}", event.getMetric())
            .replace("{time}", formattedTime)
            .replace("{duration}", String.valueOf(event.getDuration()));
    }
    
    @Override
    public void generateWordContent(XWPFDocument document, ProcessedComponent component) {
        String content = component.getStringProperty("content");
        boolean hasAnomalies = component.getBooleanProperty("has_anomalies");
        
        XWPFParagraph paragraph = document.createParagraph();
        XWPFRun run = paragraph.createRun();
        run.setText(content);
        
        if (hasAnomalies) {
            run.setColor("D32F2F"); // 红色表示有异常
            run.setBold(true);
        } else {
            run.setColor("2E7D32"); // 绿色表示正常
        }
    }
    
    @Override
    public String getComponentType() {
        return COMPONENT_TYPE;
    }
    
    @Override
    public List<String> getSupportedVersions() {
        return Arrays.asList("2.0");
    }
}
```

**验收标准**:
- [ ] 组件处理器架构完整
- [ ] 标题自动编号算法正确
- [ ] 富文本处理功能正常
- [ ] 异常评估模板替换正确
- [ ] Word生成功能完整

---

## 阶段二：现有服务集成与图表增强

### TASK-002: 复用现有AI服务和动态基线接口
**优先级**: 高  
**估计工期**: 1.5工作日 (12小时)  
**负责模块**: 服务集成

#### 2.1 图表组件增强处理器 (1工作日)

**ChartComponentProcessor.java** (增强版):
```java
@Component
public class ChartComponentProcessor implements ComponentProcessor {
    
    @Autowired
    private OpenAIService openAIService; // 复用现有AI服务
    
    @Autowired
    private MetricAggregator metricAggregator; // 复用现有动态基线服务
    
    @Autowired
    private AIService aiService; // 复用现有AI配置服务
    
    @Override
    public String getComponentType() {
        return "chart"; // 处理所有图表类型
    }
    
    @Override
    public ProcessedComponent process(ComponentConfig config, ReportContext context) {
        ProcessedComponent processed = new ProcessedComponent("chart");
        
        String chartType = config.getStringValue("type");
        Map<String, Object> query = config.getMapValue("query");
        Map<String, Object> extra = config.getMapValue("extra");
        
        // 获取图表数据（现有逻辑）
        ChartData chartData = getChartData(query, context);
        processed.addProperty("chartData", chartData);
        processed.addProperty("chartType", chartType);
        
        // 处理动态基线异常检测（复用现有接口）
        if (isAnomalyDetectionEnabled(extra)) {
            AnomalyDetectionResult anomalyResult = processAnomalyDetection(chartData, extra, context);
            processed.addProperty("anomalyResult", anomalyResult);
        }
        
        // 处理AI洞察（复用现有接口）
        if (isAIInsightsEnabled(extra)) {
            CompletableFuture<String> insightFuture = processAIInsights(chartData, extra, context);
            processed.addProperty("aiInsightFuture", insightFuture);
        }
        
        return processed;
    }
    
    private boolean isAnomalyDetectionEnabled(Map<String, Object> extra) {
        if (extra == null) return false;
        Map<String, Object> anomalyConfig = (Map<String, Object>) extra.get("anomaly_detection");
        return anomalyConfig != null && Boolean.TRUE.equals(anomalyConfig.get("enabled"));
    }
    
    private boolean isAIInsightsEnabled(Map<String, Object> extra) {
        if (extra == null) return false;
        Map<String, Object> aiConfig = (Map<String, Object>) extra.get("ai_insights");
        return aiConfig != null && Boolean.TRUE.equals(aiConfig.get("enabled"));
    }
    
    /**
     * 复用现有动态基线接口进行异常检测
     */
    private AnomalyDetectionResult processAnomalyDetection(ChartData chartData, 
                                                         Map<String, Object> extra, 
                                                         ReportContext context) {
        Map<String, Object> anomalyConfig = (Map<String, Object>) extra.get("anomaly_detection");
        String method = (String) anomalyConfig.get("method");
        
        if ("dynamic_baseline".equals(method)) {
            // 复用现有baselineResult接口
            QueryRequest query = buildQueryRequest(chartData, context);
            String comparison = (String) anomalyConfig.get("comparison");
            Double baselineScope = (Double) anomalyConfig.get("baselineScope");
            
            try {
                Map<Map<String, String>, JSONObject> baselineResult = 
                    metricAggregator.baselineResult(query, comparison, baselineScope);
                
                return analyzeBaselineResult(baselineResult, chartData);
                
            } catch (Exception e) {
                logger.error("动态基线检测失败: {}", e.getMessage(), e);
                return AnomalyDetectionResult.failed("动态基线检测暂时不可用");
            }
        } else {
            // 阈值检测逻辑
            return detectByThreshold(chartData, anomalyConfig);
        }
    }
    
    /**
     * 复用现有openAIService接口生成AI洞察
     */
    private CompletableFuture<String> processAIInsights(ChartData chartData, 
                                                       Map<String, Object> extra, 
                                                       ReportContext context) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 获取现有AI配置
                AIConfig aiConfig = getCurrentAIConfig();
                if (!aiConfig.isOpen()) {
                    return "AI洞察功能未启用";
                }
                
                // 构建AI分析提示词
                String prompt = buildChartAnalysisPrompt(chartData, context);
                List<OpenAIMessage> messages = Arrays.asList(
                    new OpenAIMessage(RoleType.system, "你是一个专业的监控数据分析师，请用中文分析图表数据。"),
                    new OpenAIMessage(RoleType.user, prompt)
                );
                
                // 复用现有openAIService.sendMessage接口
                OpenAIResponse response = openAIService.sendMessage(
                    aiConfig.getUrl(), 
                    aiConfig.getApiKey(), 
                    aiConfig.getModel(), 
                    messages
                );
                
                return response.getContent();
                
            } catch (Exception e) {
                logger.error("AI洞察生成失败: {}", e.getMessage(), e);
                return "AI洞察生成暂时不可用，请稍后重试。";
            }
        });
    }
    
    private QueryRequest buildQueryRequest(ChartData chartData, ReportContext context) {
        QueryRequest query = new QueryRequest();
        query.setMetric(chartData.getMetricName());
        query.setStart(context.getStartTime());
        query.setEnd(context.getEndTime());
        query.setInterval(chartData.getInterval());
        query.setAggs(chartData.getAggregation());
        
        return query;
    }
    
    private AnomalyDetectionResult analyzeBaselineResult(Map<Map<String, String>, JSONObject> baselineResult, 
                                                        ChartData chartData) {
        // 分析基线检测结果
        for (Map.Entry<Map<String, String>, JSONObject> entry : baselineResult.entrySet()) {
            JSONObject baseline = entry.getValue();
            double baselineValue = baseline.getDoubleValue("baseline");
            double num = baseline.getDoubleValue("num");
            
            if (num < 2016) {
                return AnomalyDetectionResult.builder()
                    .hasAnomalies(false)
                    .summary("数据点不足，无法进行有效的动态基线检测")
                    .confidence(0.3)
                    .build();
            }
            
            // 比较当前数据与基线
            boolean hasAnomaly = compareWithBaseline(chartData, baselineValue);
            
            return AnomalyDetectionResult.builder()
                .hasAnomalies(hasAnomaly)
                .summary(hasAnomaly ? "检测到异常：当前值超出动态基线范围" : "当前数据在正常范围内")
                .baseline(baselineValue)
                .confidence(0.85)
                .build();
        }
        
        return AnomalyDetectionResult.failed("基线检测结果为空");
    }
    
    private boolean compareWithBaseline(ChartData chartData, double baseline) {
        // 获取当前数据的最新值
        double currentValue = chartData.getLatestValue();
        
        // 简单的异常判断逻辑（可根据需要扩展）
        return Math.abs(currentValue - baseline) > baseline * 0.2; // 超出基线20%认为异常
    }
    
    private String buildChartAnalysisPrompt(ChartData chartData, ReportContext context) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请分析以下监控图表数据：\n\n");
        prompt.append("指标名称：").append(chartData.getMetricName()).append("\n");
        prompt.append("图表类型：").append(chartData.getChartType()).append("\n");
        prompt.append("时间范围：").append(context.getTimeRangeDescription()).append("\n");
        prompt.append("数据点数量：").append(chartData.getDataPoints().size()).append("\n\n");
        
        if (!chartData.getDataPoints().isEmpty()) {
            double max = chartData.getMaxValue();
            double min = chartData.getMinValue();
            double avg = chartData.getAverageValue();
            
            prompt.append("数据统计：\n");
            prompt.append("- 最大值：").append(formatValue(max, chartData.getUnit())).append("\n");
            prompt.append("- 最小值：").append(formatValue(min, chartData.getUnit())).append("\n");
            prompt.append("- 平均值：").append(formatValue(avg, chartData.getUnit())).append("\n\n");
        }
        
        prompt.append("请从以下几个方面进行分析：\n");
        prompt.append("1. 数据趋势分析（上升、下降、稳定）\n");
        prompt.append("2. 异常点识别（如果有的话）\n");
        prompt.append("3. 性能评估（是否在正常范围内）\n");
        prompt.append("4. 可能的原因分析\n");
        prompt.append("5. 优化建议（如果需要的话）\n\n");
        prompt.append("请控制在300字以内，重点突出关键信息。");
        
        return prompt.toString();
    }
    
    private String formatValue(double value, String unit) {
        if (unit == null) unit = "";
        
        switch (unit.toLowerCase()) {
            case "percent":
            case "%":
                return String.format("%.2f%%", value);
            case "mb":
            case "gb":
                return String.format("%.2f %s", value, unit);
            case "ms":
                return String.format("%.0f %s", value, unit);
            default:
                return String.format("%.2f %s", value, unit);
        }
    }
    
    private AIConfig getCurrentAIConfig() {
        // 复用现有的AI配置获取逻辑
        return aiService.getAIConfig(); // 假设有这个方法
    }
    
    @Override
    public void generateWordContent(XWPFDocument document, ProcessedComponent component) {
        ChartData chartData = (ChartData) component.getProperty("chartData");
        String chartType = component.getStringProperty("chartType");
        
        // 生成图表（复用现有逻辑）
        generateChartImage(document, chartData, chartType);
        
        // 添加异常检测结果
        AnomalyDetectionResult anomalyResult = (AnomalyDetectionResult) component.getProperty("anomalyResult");
        if (anomalyResult != null && anomalyResult.hasAnomalies()) {
            generateAnomalyResultParagraph(document, anomalyResult);
        }
        
        // 添加AI洞察
        CompletableFuture<String> aiInsightFuture = (CompletableFuture<String>) component.getProperty("aiInsightFuture");
        if (aiInsightFuture != null) {
            try {
                String insight = aiInsightFuture.get(10, TimeUnit.SECONDS); // 10秒超时
                generateAIInsightParagraph(document, insight);
            } catch (Exception e) {
                logger.warn("AI洞察生成超时或失败: {}", e.getMessage());
                generateFailedInsightParagraph(document);
            }
        }
    }
    
    private void generateAnomalyResultParagraph(XWPFDocument document, AnomalyDetectionResult result) {
        XWPFParagraph paragraph = document.createParagraph();
        XWPFRun run = paragraph.createRun();
        run.setText("异常检测结果: ");
        run.setBold(true);
        
        run = paragraph.createRun();
        run.setText(result.getSummary());
        run.setColor("D32F2F"); // 红色
    }
    
    private void generateAIInsightParagraph(XWPFDocument document, String insight) {
        XWPFParagraph paragraph = document.createParagraph();
        XWPFRun run = paragraph.createRun();
        run.setText("AI分析洞察: ");
        run.setBold(true);
        run.setColor("1976D2"); // 蓝色
        
        run = paragraph.createRun();
        run.setText(insight);
        run.setFontSize(11);
    }
    
    private void generateFailedInsightParagraph(XWPFDocument document) {
        XWPFParagraph paragraph = document.createParagraph();
        XWPFRun run = paragraph.createRun();
        run.setText("AI洞察生成暂时不可用");
        run.setColor("757575"); // 灰色
        run.setItalic(true);
    }
    
    @Override
    public List<String> getSupportedVersions() {
        return Arrays.asList("1.0", "2.0");
    }
}
```

#### 2.2 服务适配层 (0.5工作日)

**ReportServiceAdapter.java**:
```java
@Component
public class ReportServiceAdapter {
    
    @Autowired
    private OpenAIService openAIService;
    
    @Autowired
    private MetricAggregator metricAggregator;
    
    @Autowired
    private AIService aiService;
    
    /**
     * 统一的AI洞察生成接口
     */
    public CompletableFuture<String> generateInsight(String prompt, String context) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                AIConfig config = aiService.getAIConfig();
                if (!config.isOpen()) {
                    return "AI功能未启用";
                }
                
                List<OpenAIMessage> messages = Arrays.asList(
                    new OpenAIMessage(RoleType.system, "你是DataBuff监控系统的智能分析助手，请用中文回答。"),
                    new OpenAIMessage(RoleType.user, prompt + "\n\n上下文：" + context)
                );
                
                OpenAIResponse response = openAIService.sendMessage(
                    config.getUrl(), config.getApiKey(), config.getModel(), messages);
                
                return response.getContent();
                
            } catch (Exception e) {
                logger.error("AI洞察生成失败: {}", e.getMessage());
                return "AI分析暂时不可用";
            }
        });
    }
    
    /**
     * 统一的动态基线检测接口
     */
    public AnomalyDetectionResult detectAnomalyByBaseline(String metricName, 
                                                         long startTime, 
                                                         long endTime,
                                                         String comparison,
                                                         Double baselineScope) {
        try {
            QueryRequest query = new QueryRequest();
            query.setMetric(metricName);
            query.setStart(startTime);
            query.setEnd(endTime);
            
            Map<Map<String, String>, JSONObject> result = 
                metricAggregator.baselineResult(query, comparison, baselineScope);
            
            return processBaselineResult(result);
            
        } catch (Exception e) {
            logger.error("动态基线检测失败: {}", e.getMessage());
            return AnomalyDetectionResult.failed("动态基线检测失败: " + e.getMessage());
        }
    }
    
    private AnomalyDetectionResult processBaselineResult(Map<Map<String, String>, JSONObject> result) {
        if (result == null || result.isEmpty()) {
            return AnomalyDetectionResult.failed("基线检测结果为空");
        }
        
        // 处理基线检测结果
        for (JSONObject baseline : result.values()) {
            double baselineValue = baseline.getDoubleValue("baseline");
            double num = baseline.getDoubleValue("num");
            
            if (num < 2016) {
                return AnomalyDetectionResult.builder()
                    .hasAnomalies(false)
                    .summary("历史数据不足，建议积累更多数据后再进行基线检测")
                    .confidence(0.3)
                    .build();
            }
            
            return AnomalyDetectionResult.builder()
                .hasAnomalies(false) // 这里需要具体的异常判断逻辑
                .summary("基线值: " + String.format("%.2f", baselineValue))
                .baseline(baselineValue)
                .confidence(0.8)
                .build();
        }
        
        return AnomalyDetectionResult.failed("无法处理基线检测结果");
    }
}
```

**验收标准**:
- [ ] 图表组件AI增强功能正常
- [ ] 动态基线检测接口复用成功
- [ ] OpenAI服务接口复用成功
- [ ] 异常处理和降级机制完善
- [ ] Word文档增强内容正确显示

### TASK-003: 组件处理器架构开发
**优先级**: 高  
**估计工期**: 3工作日 (24小时)  
**负责模块**: 后端组件处理

#### 3.1 组件处理器基础架构 (1工作日)

**实施位置**: `webapp/src/main/java/com/databuff/webapp/report/processor/`

**ComponentProcessor.java** (接口):
```java
public interface ComponentProcessor {
    
    /**
     * 组件类型标识
     */
    String getComponentType();
    
    /**
     * 验证组件配置
     */
    ValidationResult validate(ComponentConfig config);
    
    /**
     * 处理组件配置
     */
    ProcessedComponent process(ComponentConfig config, ReportContext context);
    
    /**
     * 生成Word文档内容
     */
    void generateWordContent(XWPFDocument document, ProcessedComponent component);
    
    /**
     * 支持的配置版本
     */
    List<String> getSupportedVersions();
}
```

**ComponentProcessorFactory.java**:
```java
@Component
public class ComponentProcessorFactory {
    
    private final Map<String, ComponentProcessor> processors = new HashMap<>();
    
    @Autowired
    public ComponentProcessorFactory(List<ComponentProcessor> processorList) {
        processorList.forEach(processor -> 
            processors.put(processor.getComponentType(), processor));
    }
    
    public ComponentProcessor getProcessor(String componentType) {
        ComponentProcessor processor = processors.get(componentType);
        if (processor == null) {
            throw new UnsupportedComponentException("不支持的组件类型: " + componentType);
        }
        return processor;
    }
    
    public List<String> getSupportedTypes() {
        return new ArrayList<>(processors.keySet());
    }
}
```

#### 3.2 标题组件处理器 (1工作日)

**TitleComponentProcessor.java**:
```java
@Component
public class TitleComponentProcessor implements ComponentProcessor {
    
    private static final String COMPONENT_TYPE = "title";
    
    @Override
    public String getComponentType() {
        return COMPONENT_TYPE;
    }
    
    @Override
    public ValidationResult validate(ComponentConfig config) {
        ValidationResult result = new ValidationResult();
        
        // 内容验证
        String content = config.getStringValue("content");
        if (StringUtils.isEmpty(content)) {
            result.addError("content", "标题内容不能为空");
        } else if (content.length() > 50) {
            result.addError("content", "标题内容不能超过50字");
        }
        
        // 标题类型验证
        String titleType = config.getStringValue("titleType", "medium");
        if (!Arrays.asList("large", "medium", "small").contains(titleType)) {
            result.addError("titleType", "标题类型必须是 large、medium 或 small");
        }
        
        // 层级验证
        Integer level = config.getIntValue("level", 1);
        if (level < 1 || level > 3) {
            result.addError("level", "标题层级必须在1-3之间");
        }
        
        return result;
    }
    
    @Override
    public ProcessedComponent process(ComponentConfig config, ReportContext context) {
        ProcessedComponent processed = new ProcessedComponent(COMPONENT_TYPE);
        
        String content = config.getStringValue("content");
        String titleType = config.getStringValue("titleType", "medium");
        boolean autoNumber = config.getBooleanValue("autoNumber", true);
        int level = config.getIntValue("level", 1);
        
        // 自动编号生成
        if (autoNumber) {
            String generatedNumber = generateAutoNumber(context.getTitleComponents(), level);
            processed.addProperty("generated_number", generatedNumber);
            processed.addProperty("display_content", generatedNumber + " " + content);
        } else {
            processed.addProperty("display_content", content);
        }
        
        processed.addProperty("content", content);
        processed.addProperty("titleType", titleType);
        processed.addProperty("level", level);
        processed.addProperty("autoNumber", autoNumber);
        
        return processed;
    }
    
    @Override
    public void generateWordContent(XWPFDocument document, ProcessedComponent component) {
        String displayContent = component.getStringProperty("display_content");
        String titleType = component.getStringProperty("titleType");
        int level = component.getIntProperty("level");
        
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.LEFT);
        
        XWPFRun run = paragraph.createRun();
        run.setText(displayContent);
        run.setBold(true);
        
        // 根据标题类型设置样式
        switch (titleType) {
            case "large":
                run.setFontSize(18);
                break;
            case "medium":
                run.setFontSize(16);
                break;
            case "small":
                run.setFontSize(14);
                break;
        }
        
        // 设置标题样式
        paragraph.setStyle("Heading" + level);
    }
    
    /**
     * 生成自动编号
     */
    private String generateAutoNumber(List<ProcessedComponent> titleComponents, int currentLevel) {
        // 统计各级标题数量
        Map<Integer, Integer> levelCounts = new HashMap<>();
        
        for (ProcessedComponent title : titleComponents) {
            int level = title.getIntProperty("level");
            if (level <= currentLevel) {
                levelCounts.put(level, levelCounts.getOrDefault(level, 0) + 1);
                
                // 清零下级计数
                if (level < currentLevel) {
                    for (int i = level + 1; i <= 3; i++) {
                        levelCounts.remove(i);
                    }
                }
            }
        }
        
        // 生成编号
        switch (currentLevel) {
            case 1:
                return convertToChineseNumber(levelCounts.getOrDefault(1, 0) + 1) + "、";
            case 2:
                int parentCount = levelCounts.getOrDefault(1, 1);
                int currentCount = levelCounts.getOrDefault(2, 0) + 1;
                return parentCount + "." + currentCount;
            case 3:
                int grandParentCount = levelCounts.getOrDefault(1, 1);
                int parentLevel2Count = levelCounts.getOrDefault(2, 1);
                int currentLevel3Count = levelCounts.getOrDefault(3, 0) + 1;
                return grandParentCount + "." + parentLevel2Count + "." + currentLevel3Count;
            default:
                return "";
        }
    }
    
    private String convertToChineseNumber(int number) {
        String[] chineseNumbers = {"", "一", "二", "三", "四", "五", "六", "七", "八", "九", "十"};
        if (number <= 10) {
            return chineseNumbers[number];
        }
        return String.valueOf(number);
    }
    
    @Override
    public List<String> getSupportedVersions() {
        return Arrays.asList("2.0");
    }
}
```

#### 3.3 文本组件处理器 (1工作日)

**TextComponentProcessor.java**:
```java
@Component
public class TextComponentProcessor implements ComponentProcessor {
    
    private static final String COMPONENT_TYPE = "text";
    
    @Override
    public ValidationResult validate(ComponentConfig config) {
        ValidationResult result = new ValidationResult();
        
        String content = config.getStringValue("content");
        if (StringUtils.isEmpty(content)) {
            result.addError("content", "文本内容不能为空");
        }
        
        int wordLimit = config.getIntValue("wordLimit", 1000);
        if (wordLimit < 10 || wordLimit > 2000) {
            result.addError("wordLimit", "字数限制必须在10-2000之间");
        }
        
        // 实际字数统计（去除HTML标签）
        if (!StringUtils.isEmpty(content)) {
            String plainText = removeHtmlTags(content);
            if (plainText.length() > wordLimit) {
                result.addError("content", "文本内容超过字数限制 " + wordLimit + " 字");
            }
        }
        
        return result;
    }
    
    @Override
    public ProcessedComponent process(ComponentConfig config, ReportContext context) {
        ProcessedComponent processed = new ProcessedComponent(COMPONENT_TYPE);
        
        String content = config.getStringValue("content");
        String contentType = config.getStringValue("contentType", "plain");
        int wordLimit = config.getIntValue("wordLimit", 1000);
        
        // 处理富文本内容
        String processedContent = processContent(content, contentType);
        int wordCount = removeHtmlTags(processedContent).length();
        
        processed.addProperty("content", processedContent);
        processed.addProperty("contentType", contentType);
        processed.addProperty("wordLimit", wordLimit);
        processed.addProperty("currentWordCount", wordCount);
        processed.addProperty("display_content", processedContent);
        
        return processed;
    }
    
    @Override
    public void generateWordContent(XWPFDocument document, ProcessedComponent component) {
        String content = component.getStringProperty("content");
        String contentType = component.getStringProperty("contentType");
        
        if ("html".equals(contentType)) {
            generateRichTextParagraph(document, content);
        } else {
            generatePlainTextParagraph(document, content);
        }
    }
    
    private void generateRichTextParagraph(XWPFDocument document, String htmlContent) {
        // HTML转Word的简化实现
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.LEFT);
        
        // 简单的HTML解析和样式应用
        String plainText = removeHtmlTags(htmlContent);
        XWPFRun run = paragraph.createRun();
        run.setText(plainText);
        
        // 检测并应用基本格式
        if (htmlContent.contains("<b>") || htmlContent.contains("<strong>")) {
            run.setBold(true);
        }
        if (htmlContent.contains("<i>") || htmlContent.contains("<em>")) {
            run.setItalic(true);
        }
        if (htmlContent.contains("<u>")) {
            run.setUnderline(UnderlinePatterns.SINGLE);
        }
    }
    
    private void generatePlainTextParagraph(XWPFDocument document, String content) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.LEFT);
        
        XWPFRun run = paragraph.createRun();
        run.setText(content);
        run.setFontSize(12);
    }
    
    private String processContent(String content, String contentType) {
        if ("html".equals(contentType)) {
            // 清理和验证HTML内容
            return sanitizeHtml(content);
        }
        return content;
    }
    
    private String sanitizeHtml(String html) {
        // 简单的HTML清理，保留基本格式标签
        return html.replaceAll("<script[^>]*>.*?</script>", "")
                  .replaceAll("<style[^>]*>.*?</style>", "")
                  .replaceAll("on\\w+\\s*=\\s*[\"'][^\"']*[\"']", "");
    }
    
    private String removeHtmlTags(String html) {
        return html.replaceAll("<[^>]+>", "");
    }
    
    @Override
    public String getComponentType() {
        return COMPONENT_TYPE;
    }
    
    @Override
    public List<String> getSupportedVersions() {
        return Arrays.asList("2.0");
    }
}
```

**验收标准**:
- [ ] 组件处理器架构完整
- [ ] 标题自动编号算法正确
- [ ] 富文本处理功能正常
- [ ] 配置验证规则生效
- [ ] Word生成功能完整

---

### TASK-004: 异常评估组件开发
**优先级**: 高  
**估计工期**: 2工作日 (16小时)  
**负责模块**: 异常检测集成

#### 4.1 异常评估组件处理器 (1.5工作日)

**AnomalyEvaluationComponentProcessor.java**:
```java
@Component
public class AnomalyEvaluationComponentProcessor implements ComponentProcessor {
    
    private static final String COMPONENT_TYPE = "anomaly_evaluation";
    
    @Autowired
    private AnomalyDataCollector anomalyDataCollector;
    
    @Autowired
    private TemplateVariableProcessor templateProcessor;
    
    @Override
    public ValidationResult validate(ComponentConfig config) {
        ValidationResult result = new ValidationResult();
        
        String template = config.getStringValue("template");
        if (StringUtils.isEmpty(template)) {
            result.addError("template", "异常评估模板不能为空");
        } else if (!template.matches(".*\\{[^}]+\\}.*")) {
            result.addError("template", "模板必须包含至少一个变量 {variable}");
        }
        
        // 验证变量定义
        List<Map<String, Object>> variables = config.getListValue("variables");
        if (variables == null || variables.isEmpty()) {
            result.addError("variables", "必须定义模板变量");
        }
        
        return result;
    }
    
    @Override
    public ProcessedComponent process(ComponentConfig config, ReportContext context) {
        ProcessedComponent processed = new ProcessedComponent(COMPONENT_TYPE);
        
        boolean autoGenerated = config.getBooleanValue("auto_generated", true);
        String template = config.getStringValue("template");
        String noAnomalyText = config.getStringValue("no_anomaly_text", "本次报告周期内无异常");
        
        if (autoGenerated) {
            // 收集异常数据
            List<AnomalyEvent> anomalies = collectAnomalies(context);
            
            if (anomalies.isEmpty()) {
                processed.addProperty("content", noAnomalyText);
                processed.addProperty("has_anomalies", false);
            } else {
                String generatedContent = generateAnomalyContent(template, anomalies);
                processed.addProperty("content", generatedContent);
                processed.addProperty("has_anomalies", true);
                processed.addProperty("anomaly_count", anomalies.size());
            }
        } else {
            // 手动配置的内容
            processed.addProperty("content", config.getStringValue("content", noAnomalyText));
            processed.addProperty("has_anomalies", false);
        }
        
        processed.addProperty("template", template);
        processed.addProperty("auto_generated", autoGenerated);
        
        return processed;
    }
    
    private List<AnomalyEvent> collectAnomalies(ReportContext context) {
        // 根据报告时间范围收集异常事件
        long startTime = context.getStartTime();
        long endTime = context.getEndTime();
        String apiKey = context.getApiKey();
        
        // 收集所有启用异常检测的图表组件的异常事件
        List<AnomalyEvent> anomalies = new ArrayList<>();
        
        // 获取当前模板中启用异常检测的组件
        List<ComponentConfig> chartComponents = context.getChartComponentsWithAnomalyDetection();
        
        for (ComponentConfig component : chartComponents) {
            Map<String, Object> anomalyConfig = component.getMapValue("anomaly_detection");
            if (anomalyConfig != null && Boolean.TRUE.equals(anomalyConfig.get("enabled"))) {
                // 根据组件配置收集异常数据
                List<AnomalyEvent> componentAnomalies = collectComponentAnomalies(component, apiKey, startTime, endTime);
                anomalies.addAll(componentAnomalies);
            }
        }
        
        return anomalies;
    }
    
    private String generateAnomalyContent(String template, List<AnomalyEvent> anomalies) {
        StringBuilder content = new StringBuilder();
        
        for (int i = 0; i < anomalies.size(); i++) {
            AnomalyEvent event = anomalies.get(i);
            String eventText = templateProcessor.processTemplate(template, event);
            
            content.append(i + 1).append(". ").append(eventText);
            if (i < anomalies.size() - 1) {
                content.append("\n");
            }
        }
        
        return content.toString();
    }
    
    @Override
    public void generateWordContent(XWPFDocument document, ProcessedComponent component) {
        String content = component.getStringProperty("content");
        boolean hasAnomalies = component.getBooleanProperty("has_anomalies");
        
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.LEFT);
        
        XWPFRun run = paragraph.createRun();
        run.setText(content);
        
        if (hasAnomalies) {
            run.setColor("D32F2F"); // 红色表示有异常
            run.setBold(true);
        } else {
            run.setColor("2E7D32"); // 绿色表示正常
        }
        
        run.setFontSize(12);
    }
    
    @Override
    public String getComponentType() {
        return COMPONENT_TYPE;
    }
    
    @Override
    public List<String> getSupportedVersions() {
        return Arrays.asList("2.0");
    }
}
```

#### 4.2 异常数据收集服务 (0.5工作日)

**AnomalyDataCollector.java**:
```java
@Service
public class AnomalyDataCollector {
    
    @Autowired
    private MetricAggregator metricAggregator; // 复用现有动态基线接口
    
    @Autowired
    private TSDBReader tsdbReader; // TSDB数据读取器
    
    /**
     * 收集指定组件的异常事件（实时检测，不依赖历史告警数据）
     */
    public List<AnomalyEvent> collectComponentAnomalies(ComponentConfig component, String apiKey, long startTime, long endTime) {
        List<AnomalyEvent> events = new ArrayList<>();
        
        Map<String, Object> anomalyConfig = component.getMapValue("anomaly_detection");
        if (anomalyConfig == null || !Boolean.TRUE.equals(anomalyConfig.get("enabled"))) {
            return events;
        }
        
        String method = (String) anomalyConfig.get("method");
        
        try {
            if ("threshold".equals(method)) {
                // 阈值检测异常收集
                events.addAll(detectThresholdAnomalies(component, apiKey, startTime, endTime, anomalyConfig));
            } else if ("dynamic_baseline".equals(method)) {
                // 动态基线检测异常收集
                events.addAll(detectBaselineAnomalies(component, apiKey, startTime, endTime, anomalyConfig));
            }
        } catch (Exception e) {
            log.error("组件异常检测失败: {}, 错误: {}", component.getTitle(), e.getMessage(), e);
        }
        
        return events;
    }
    
    /**
     * 阈值检测异常 - 实时检测指标数据
     */
    private List<AnomalyEvent> detectThresholdAnomalies(ComponentConfig component, String apiKey, 
                                                       long startTime, long endTime, 
                                                       Map<String, Object> anomalyConfig) {
        List<AnomalyEvent> events = new ArrayList<>();
        
        // 获取阈值配置
        Map<String, Object> thresholds = (Map<String, Object>) anomalyConfig.get("thresholds");
        if (thresholds == null) {
            return events;
        }
        
        Double upperThreshold = (Double) thresholds.get("upper");
        Double lowerThreshold = (Double) thresholds.get("lower");
        
        // 构造查询请求
        QueryRequest query = buildQueryRequestFromComponent(component, apiKey, startTime, endTime);
        
        // 获取指标数据
        List<WrapData> dataList = tsdbReader.query(query);
        
        for (WrapData data : dataList) {
            List<List<Number>> values = data.getValues();
            
            for (List<Number> valueRow : values) {
                if (valueRow.size() >= 2) {
                    long timestamp = valueRow.get(0).longValue();
                    double value = valueRow.get(1).doubleValue();
                    
                    boolean isAnomalous = false;
                    String anomalyType = "";
                    
                    // 检查上阈值
                    if (upperThreshold != null && value > upperThreshold) {
                        isAnomalous = true;
                        anomalyType = "超出上阈值";
                    }
                    // 检查下阈值
                    else if (lowerThreshold != null && value < lowerThreshold) {
                        isAnomalous = true;
                        anomalyType = "低于下阈值";
                    }
                    
                    if (isAnomalous) {
                        AnomalyEvent event = createAnomalyEvent(
                            component, data.getTags(), timestamp, value, anomalyType, "threshold"
                        );
                        events.add(event);
                    }
                }
            }
        }
        
        return events;
    }
    
    /**
     * 动态基线检测异常 - 复用现有baselineResult接口
     */
    private List<AnomalyEvent> detectBaselineAnomalies(ComponentConfig component, String apiKey,
                                                      long startTime, long endTime, 
                                                      Map<String, Object> anomalyConfig) {
        List<AnomalyEvent> events = new ArrayList<>();
        
        // 获取基线配置
        String comparison = (String) anomalyConfig.get("comparison");
        Double baselineScope = (Double) anomalyConfig.get("baselineScope");
        
        if (comparison == null) comparison = ">";
        if (baselineScope == null) baselineScope = 1.0;
        
        // 构造查询请求
        QueryRequest query = buildQueryRequestFromComponent(component, apiKey, startTime, endTime);
        
        try {
            // 调用现有的动态基线接口
            Map<Map<String, String>, JSONObject> baselineResult = 
                metricAggregator.baselineResult(query, comparison, baselineScope);
            
            // 获取当前指标数据用于异常判断
            List<WrapData> currentData = tsdbReader.query(query);
            
            // 分析基线检测结果
            for (Map.Entry<Map<String, String>, JSONObject> entry : baselineResult.entrySet()) {
                Map<String, String> tags = entry.getKey();
                JSONObject baseline = entry.getValue();
                
                double num = baseline.getDoubleValue("num");
                double baselineValue = baseline.getDoubleValue("baseline");
                
                // 检查数据质量：至少需要2016个数据点
                if (num < 2016) {
                    log.debug("标签组合 {} 的数据点不足: {}，跳过基线检测", tags, num);
                    continue;
                }
                
                // 检查当前数据是否超出基线
                List<AnomalyEvent> tagAnomalies = checkBaselineAnomalies(
                    component, currentData, tags, baselineValue, comparison);
                events.addAll(tagAnomalies);
            }
            
        } catch (Exception e) {
            log.error("动态基线检测失败: {}", e.getMessage(), e);
        }
        
        return events;
    }
    
    /**
     * 检查基线异常
     */
    private List<AnomalyEvent> checkBaselineAnomalies(ComponentConfig component, List<WrapData> currentData,
                                                     Map<String, String> targetTags, double baselineValue, 
                                                     String comparison) {
        List<AnomalyEvent> events = new ArrayList<>();
        
        for (WrapData data : currentData) {
            // 匹配标签组合
            if (!tagsMatch(data.getTags(), targetTags)) {
                continue;
            }
            
            List<List<Number>> values = data.getValues();
            for (List<Number> valueRow : values) {
                if (valueRow.size() >= 2) {
                    long timestamp = valueRow.get(0).longValue();
                    double value = valueRow.get(1).doubleValue();
                    
                    boolean isAnomalous = false;
                    String anomalyType = "";
                    
                    // 根据比较符号判断异常
                    if (">".equals(comparison) || ">=".equals(comparison)) {
                        if (value > baselineValue) {
                            isAnomalous = true;
                            anomalyType = "超出动态基线上限";
                        }
                    } else if ("<".equals(comparison) || "<=".equals(comparison)) {
                        if (value < baselineValue) {
                            isAnomalous = true;
                            anomalyType = "低于动态基线下限";
                        }
                    }
                    
                    if (isAnomalous) {
                        AnomalyEvent event = createAnomalyEvent(
                            component, data.getTags(), timestamp, value, anomalyType, "dynamic_baseline"
                        );
                        events.add(event);
                    }
                }
            }
        }
        
        return events;
    }
    
    /**
     * 构造QueryRequest
     */
    private QueryRequest buildQueryRequestFromComponent(ComponentConfig component, String apiKey, 
                                                       long startTime, long endTime) {
        // 从组件query字段解析查询条件
        String queryJson = component.getQuery();
        Map<String, Object> queryMap = JSON.parseObject(queryJson, Map.class);
        
        QueryRequest query = new QueryRequest();
        query.setApiKey(apiKey);
        query.setStart(startTime);
        query.setEnd(endTime);
        query.setMetric((String) queryMap.get("metric"));
        query.setAggs((String) queryMap.get("aggs"));
        query.setInterval(component.getInterval() != null ? 
            Integer.valueOf(component.getInterval().toString()) : 3600);
        
        // 设置分组字段
        List<String> by = (List<String>) queryMap.get("by");
        if (by != null) {
            query.setBy(by);
        }
        
        // 设置过滤条件
        List<Map<String, Object>> conditions = (List<Map<String, Object>>) queryMap.get("from");
        if (conditions != null) {
            List<CompositeCondition> compositeConditions = new ArrayList<>();
            for (Map<String, Object> condition : conditions) {
                CompositeCondition cc = new CompositeCondition();
                cc.setLeft((String) condition.get("left"));
                cc.setRight((String) condition.get("right"));
                cc.setOperator((String) condition.get("operator"));
                compositeConditions.add(cc);
            }
            query.setFrom(compositeConditions);
        }
        
        return query;
    }
    
    /**
     * 创建异常事件
     */
    private AnomalyEvent createAnomalyEvent(ComponentConfig component, Map<String, String> tags,
                                          long timestamp, double value, String anomalyType, String detectionMethod) {
        
        // 从标签和组件配置中提取信息
        String system = extractSystemFromTags(tags, component);
        String service = extractServiceFromTags(tags, component);
        String metric = extractMetricFromComponent(component);
        
        return AnomalyEvent.builder()
            .system(system)
            .service(service)
            .metric(metric)
            .startTime(new Date(timestamp))
            .endTime(null) // 单点异常，暂无结束时间
            .duration(0) // 单点异常，持续时间为0
            .description(anomalyType)
            .severity(AnomalySeverity.MEDIUM)
            .detectionMethod(detectionMethod)
            .actualValue(value)
            .build();
    }
    
    /**
     * 从标签中提取系统名
     */
    private String extractSystemFromTags(Map<String, String> tags, ComponentConfig component) {
        if (tags.containsKey("system")) {
            return tags.get("system");
        }
        if (tags.containsKey("application")) {
            return tags.get("application");
        }
        if (tags.containsKey("app")) {
            return tags.get("app");
        }
        return "未知系统";
    }
    
    /**
     * 从标签中提取服务名
     */
    private String extractServiceFromTags(Map<String, String> tags, ComponentConfig component) {
        if (tags.containsKey("service")) {
            return tags.get("service");
        }
        if (tags.containsKey("serviceName")) {
            return tags.get("serviceName");
        }
        if (tags.containsKey("host")) {
            return tags.get("host");
        }
        return "未知服务";
    }
    
    /**
     * 从组件配置中提取指标名
     */
    private String extractMetricFromComponent(ComponentConfig component) {
        try {
            Map<String, Object> queryMap = JSON.parseObject(component.getQuery(), Map.class);
            String metric = (String) queryMap.get("metric");
            return metric != null ? metric : component.getTitle();
        } catch (Exception e) {
            return component.getTitle();
        }
    }
    
    /**
     * 判断标签是否匹配
     */
    private boolean tagsMatch(Map<String, String> dataTags, Map<String, String> targetTags) {
        if (targetTags == null || targetTags.isEmpty()) {
            return true;
        }
        
        for (Map.Entry<String, String> entry : targetTags.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            
            if (!value.equals(dataTags.get(key))) {
                return false;
            }
        }
        
        return true;
    }
    
}
```

**验收标准**:
- [ ] 异常评估组件功能完整
- [ ] 模板变量替换正确：支持 `【{system}系统的{service}服务的{metric}指标在{time}开始异常，持续{duration}分钟】`
- [ ] **实时异常检测**：不依赖历史告警数据，直接对指标数据进行检测
- [ ] **阈值检测**：支持上下阈值配置，实时判断指标值是否超出范围
- [ ] **动态基线检测**：复用现有 `metricAggregator.baselineResult()` 接口，支持基线计算和异常判断
- [ ] **数据质量检查**：动态基线检测需要至少2016个数据点才进行有效检测
- [ ] 按模板中组件顺序显示异常事件
- [ ] 从标签和组件配置中正确提取系统、服务、指标信息
- [ ] Word生成样式正确
- [ ] 无异常状态处理正常

---

## 阶段四：报告生成引擎与集成测试

### TASK-004: 报告生成引擎升级
**优先级**: 中  
**估计工期**: 1.5工作日 (12小时)  
**负责模块**: 报告生成

#### 4.1 ReportWordUtil 增强 (1工作日)

**实施位置**: `webapp/src/main/java/com/databuff/webapp/report/utils/ReportWordUtil.java`

```java
@Component
public class ReportWordUtil {
    
    @Autowired
    private ComponentProcessorFactory processorFactory;
    
    @Autowired
    private ReportServiceAdapter serviceAdapter;
    
    /**
     * 增强的报告生成方法
     */
    public String generateEnhancedReport(ReportTemplateEntity template, ReportContext context) {
        try {
            // 解析模板content中的组件
            List<ComponentConfig> components = parseTemplateContent(template.getContent());
            
            // 创建Word文档
            XWPFDocument document = new XWPFDocument();
            
            // 按index顺序处理组件
            components.sort(Comparator.comparing(ComponentConfig::getIndex));
            
            for (ComponentConfig config : components) {
                try {
                    // 获取对应的组件处理器
                    ComponentProcessor processor = processorFactory.getProcessor(config.getType());
                    
                    // 验证组件配置
                    ValidationResult validation = processor.validate(config);
                    if (!validation.isValid()) {
                        log.warn("组件配置验证失败: {}, 错误: {}", config.getType(), validation.getErrors());
                        continue;
                    }
                    
                    // 处理组件
                    ProcessedComponent processed = processor.process(config, context);
                    
                    // 生成Word内容
                    processor.generateWordContent(document, processed);
                    
                } catch (Exception e) {
                    log.error("处理组件失败: {}, 错误: {}", config.getType(), e.getMessage(), e);
                    // 继续处理下一个组件，不中断整个报告生成
                }
            }
            
            // 保存Word文档
            String fileName = generateFileName(template.getName(), context);
            String filePath = saveWordDocument(document, fileName);
            
            return filePath;
            
        } catch (Exception e) {
            log.error("生成报告失败: {}", e.getMessage(), e);
            throw new RuntimeException("报告生成失败", e);
        }
    }
    
    /**
     * 解析模板content JSON为组件配置列表
     */
    private List<ComponentConfig> parseTemplateContent(String content) {
        try {
            List<Map<String, Object>> contentList = JSON.parseArray(content, Map.class);
            List<ComponentConfig> components = new ArrayList<>();
            
            for (Map<String, Object> item : contentList) {
                ComponentConfig config = new ComponentConfig();
                config.setIndex((Integer) item.get("index"));
                config.setType((String) item.get("type"));
                config.setTitle((String) item.get("title"));
                config.setText((String) item.get("text"));
                config.setQuery((String) item.get("query"));
                config.setLimit((Integer) item.get("limit"));
                config.setInterval(item.get("interval"));
                config.setUnit((String) item.get("unit"));
                
                // 解析extra字段
                String extraStr = (String) item.get("extra");
                if (StringUtils.isNotEmpty(extraStr)) {
                    Map<String, Object> extraMap = JSON.parseObject(extraStr, Map.class);
                    config.setExtraMap(extraMap);
                }
                
                components.add(config);
            }
            
            return components;
            
        } catch (Exception e) {
            log.error("解析模板内容失败: {}", e.getMessage(), e);
            throw new RuntimeException("模板格式错误", e);
        }
    }
    
    private String generateFileName(String templateName, ReportContext context) {
        String timeRange = context.getTimeRangeDescription();
        return String.format("%s_%s_%s.docx", 
            templateName, 
            timeRange, 
            System.currentTimeMillis());
    }
    
    private String saveWordDocument(XWPFDocument document, String fileName) throws IOException {
        String uploadPath = "/uploads/reports/";
        File directory = new File(uploadPath);
        if (!directory.exists()) {
            directory.mkdirs();
        }
        
        String filePath = uploadPath + fileName;
        try (FileOutputStream out = new FileOutputStream(filePath)) {
            document.write(out);
        }
        
        return filePath;
    }
}
```

#### 4.2 组件配置数据模型 (0.5工作日)

**ComponentConfig.java**:
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ComponentConfig {
    private Integer index;
    private String type;
    private String title;
    private String text;
    private String query;
    private Integer limit;
    private Object interval;
    private String unit;
    private Map<String, Object> extraMap;
    
    public String getStringValue(String key) {
        return getStringValue(key, null);
    }
    
    public String getStringValue(String key, String defaultValue) {
        if (extraMap == null) return defaultValue;
        Object value = extraMap.get(key);
        return value != null ? value.toString() : defaultValue;
    }
    
    public Integer getIntValue(String key) {
        return getIntValue(key, 0);
    }
    
    public Integer getIntValue(String key, Integer defaultValue) {
        if (extraMap == null) return defaultValue;
        Object value = extraMap.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return defaultValue;
    }
    
    public Boolean getBooleanValue(String key) {
        return getBooleanValue(key, false);
    }
    
    public Boolean getBooleanValue(String key, Boolean defaultValue) {
        if (extraMap == null) return defaultValue;
        Object value = extraMap.get(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        return defaultValue;
    }
    
    @SuppressWarnings("unchecked")
    public Map<String, Object> getMapValue(String key) {
        if (extraMap == null) return new HashMap<>();
        Object value = extraMap.get(key);
        if (value instanceof Map) {
            return (Map<String, Object>) value;
        }
        return new HashMap<>();
    }
}
```

**验收标准**:
- [ ] 报告生成引擎升级完成
- [ ] 支持新旧组件混合处理
- [ ] 错误处理机制完善
- [ ] Word文档生成功能正常
- [ ] 文件保存和命名规范

---

