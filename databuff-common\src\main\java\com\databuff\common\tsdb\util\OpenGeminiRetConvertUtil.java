package com.databuff.common.tsdb.util;

import com.databuff.common.tsdb.builder.QueryBuilder;
import com.databuff.common.tsdb.model.TSDBResult;
import com.databuff.common.tsdb.model.TSDBResultSet;
import com.databuff.common.tsdb.model.TSDBSeries;
import org.influxdb.dto.QueryResult;

import java.time.Instant;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * OpenGemini查询结果转换工具类
 * 用于将OpenGemini的查询结果转换为统一的TSDB结果集格式
 */
public class OpenGeminiRetConvertUtil {

    /**
     * 将OpenGemini查询结果转换为TSDBResultSet
     *
     * @param tsdbResult TSDBResult对象，用于存储转换后的结果
     * @param queryResult OpenGemini查询结果
     * @param timeUnit 时间单位，"s"表示秒，"ms"表示毫秒
     * @param hasCountFunction 是否包含count函数
     * @return 转换后的TSDBResultSet
     */
    public static TSDBResultSet formatResult(TSDBResult tsdbResult, QueryResult queryResult, String timeUnit, AtomicBoolean hasCountFunction, QueryBuilder.QueryType type) {
        TSDBResultSet resultSet = new TSDBResultSet();
        List<TSDBResult> results = new ArrayList<>();
        results.add(tsdbResult);

        if (queryResult.hasError()) {
            tsdbResult.setError(queryResult.getError());
            resultSet.setResults(results);
            return resultSet;
        }

        // 如果TSDBResult的queryType为"unknown"，则根据查询结果确定查询类型
        if ("unknown".equals(tsdbResult.getQueryType())) {
            String queryType = determineQueryType(queryResult);
            tsdbResult.setQueryType(queryType);
        }
        String queryType = tsdbResult.getQueryType();
        tsdbResult.setSeries(new ArrayList<>());
        for (QueryResult.Result result : queryResult.getResults()) {
            if (result == null) {
                continue;
            }
            if (result.getError() != null) {
                tsdbResult.setError(result.getError());
            } else {
                final List<QueryResult.Series> resultSeries = result.getSeries();
                if (resultSeries != null) {
                    List<TSDBSeries> seriesList = new ArrayList<>();

                    if (type == null) {
                        // 根据查询类型处理结果
                        if ("SHOW MEASUREMENTS".equals(queryType)) {
                            buildShowMeasurements(seriesList, resultSeries);
                        } else if ("SHOW TAG KEYS".equals(queryType)) {
                            buildShowTagKeys(seriesList, resultSeries);
                        } else if ("SHOW TAG VALUES".equals(queryType)) {
                            buildShowTagValues(seriesList, resultSeries);
                        } else if ("SHOW FIELD KEYS".equals(queryType)) {
                            buildShowFieldKeys(seriesList, resultSeries);
                        } else {
                            // 处理普通查询结果
                            buildQueryResult(seriesList, resultSeries, timeUnit, hasCountFunction);
                        }

                        tsdbResult.setSeries(seriesList);
                        resultSet.setResults(results);
                        return resultSet;
                    }
                    switch (type) {
                        case TOPN_MAX_TIME:
                        case MIXED_ORDER:
                        case STANDARD:
                            // 根据查询类型处理结果
                            if ("SHOW MEASUREMENTS".equals(queryType)) {
                                buildShowMeasurements(seriesList, resultSeries);
                            } else if ("SHOW TAG KEYS".equals(queryType)) {
                                buildShowTagKeys(seriesList, resultSeries);
                            } else if ("SHOW TAG VALUES".equals(queryType)) {
                                buildShowTagValues(seriesList, resultSeries);
                            } else if ("SHOW FIELD KEYS".equals(queryType)) {
                                buildShowFieldKeys(seriesList, resultSeries);
                            } else {
                                // 处理普通查询结果
                                buildQueryResult(seriesList, resultSeries, timeUnit, hasCountFunction);
                            }

                            tsdbResult.setSeries(seriesList);
                            break;
                        case NON_TIME_ONLY_ORDER:
                            if (result == null) {
                                continue;
                            }
                            final List<QueryResult.Series> influxSeries = resultSeries;
                            for (QueryResult.Series series : influxSeries) {
                                if (series == null) {
                                    continue;
                                }

                                final List<List<Object>> values = series.getValues();
                                for (List<Object> tagValues : values) {
                                    seriesList.add(TSDBSeries.fromSeriesAndTagValues(series, tagValues, timeUnit));
                                }
                            }
                            tsdbResult.setSeries(seriesList);
                            break;
                    }
                } else {
                    tsdbResult.setError("no data");
                }
            }
        }

        resultSet.setResults(results);
        return resultSet;
    }

    /**
     * 根据查询结果确定查询类型
     *
     * @param queryResult 查询结果
     * @return 查询类型字符串
     */
    private static String determineQueryType(QueryResult queryResult) {
        if (queryResult.getResults() == null || queryResult.getResults().isEmpty()) {
            return "unknown";
        }

        QueryResult.Result result = queryResult.getResults().get(0);
        if (result.getSeries() == null || result.getSeries().isEmpty()) {
            return "unknown";
        }

        QueryResult.Series series = result.getSeries().get(0);
        String name = series.getName();
        List<String> columns = series.getColumns();

        // 根据结果的特征判断查询类型
        if ("measurements".equals(name)) {
            return "SHOW MEASUREMENTS";
        } else if (columns != null && columns.contains("tagKey")) {
            return "SHOW TAG KEYS";
        } else if (columns != null && columns.contains("key") && columns.contains("value")) {
            return "SHOW TAG VALUES";
        } else if (columns != null && columns.contains("fieldKey")) {
            return "SHOW FIELD KEYS";
        } else {
            return "QUERY";
        }
    }

    /**
     * 构建普通查询结果
     *
     * @param seriesList 结果系列列表
     * @param influxSeries InfluxDB系列列表
     * @param timeUnit 时间单位
     * @param hasCountFunction 是否包含count函数
     */
    private static void buildQueryResult(List<TSDBSeries> seriesList, List<QueryResult.Series> influxSeries, String timeUnit, AtomicBoolean hasCountFunction) {
        for (QueryResult.Series series : influxSeries) {
            TSDBSeries tsdbSeries = new TSDBSeries();
            tsdbSeries.setName(series.getName());
            tsdbSeries.setColumns(series.getColumns());

            // 处理时间戳格式转换
            List<List<Object>> convertedValues = convertTimestampFormat(series.getValues(), series.getColumns(), timeUnit);
            tsdbSeries.setValues(convertedValues);

            if (series.getTags() != null) {
                tsdbSeries.setTags(series.getTags());
            }

            seriesList.add(tsdbSeries);
        }
    }

    /**
     * 构建SHOW MEASUREMENTS查询结果
     *
     * @param seriesList 结果系列列表
     * @param influxSeries InfluxDB系列列表
     */
    private static void buildShowMeasurements(List<TSDBSeries> seriesList, List<QueryResult.Series> influxSeries) {
        for (QueryResult.Series series : influxSeries) {
            TSDBSeries tsdbSeries = new TSDBSeries();
            tsdbSeries.setName("measurements");
            tsdbSeries.setColumns(Arrays.asList("name"));

            List<List<Object>> values = new ArrayList<>();
            if (series.getValues() != null) {
                for (List<Object> value : series.getValues()) {
                    if (value != null && !value.isEmpty()) {
                        values.add(Arrays.asList(value.get(0)));
                    }
                }
            }

            tsdbSeries.setValues(values);
            seriesList.add(tsdbSeries);
        }
    }

    /**
     * 构建SHOW TAG KEYS查询结果
     *
     * @param seriesList 结果系列列表
     * @param influxSeries InfluxDB系列列表
     */
    private static void buildShowTagKeys(List<TSDBSeries> seriesList, List<QueryResult.Series> influxSeries) {
        for (QueryResult.Series series : influxSeries) {
            TSDBSeries tsdbSeries = new TSDBSeries();
            tsdbSeries.setName(series.getName());
            tsdbSeries.setColumns(Arrays.asList("tagKey"));

            List<List<Object>> values = new ArrayList<>();
            if (series.getValues() != null) {
                for (List<Object> value : series.getValues()) {
                    if (value != null && !value.isEmpty()) {
                        values.add(Arrays.asList(value.get(0)));
                    }
                }
            }

            tsdbSeries.setValues(values);
            seriesList.add(tsdbSeries);
        }
    }

    /**
     * 构建SHOW TAG VALUES查询结果
     *
     * @param seriesList 结果系列列表
     * @param influxSeries InfluxDB系列列表
     */
    private static void buildShowTagValues(List<TSDBSeries> seriesList, List<QueryResult.Series> influxSeries) {
        for (QueryResult.Series series : influxSeries) {
            TSDBSeries tsdbSeries = new TSDBSeries();
            tsdbSeries.setName(series.getName());
            tsdbSeries.setColumns(Arrays.asList("key", "value"));

            List<List<Object>> values = new ArrayList<>();
            if (series.getValues() != null) {
                for (List<Object> value : series.getValues()) {
                    if (value != null && value.size() >= 2) {
                        values.add(Arrays.asList(value.get(0), value.get(1)));
                    }
                }
            }

            tsdbSeries.setValues(values);
            seriesList.add(tsdbSeries);
        }
    }

    /**
     * 构建SHOW FIELD KEYS查询结果
     *
     * @param seriesList 结果系列列表
     * @param influxSeries InfluxDB系列列表
     */
    private static void buildShowFieldKeys(List<TSDBSeries> seriesList, List<QueryResult.Series> influxSeries) {
        for (QueryResult.Series series : influxSeries) {
            TSDBSeries tsdbSeries = new TSDBSeries();
            tsdbSeries.setName(series.getName());

            // InfluxDB返回的SHOW FIELD KEYS结果包含字段名和字段类型
            tsdbSeries.setColumns(Arrays.asList("fieldKey", "fieldType"));

            List<List<Object>> values = new ArrayList<>();
            if (series.getValues() != null) {
                for (List<Object> value : series.getValues()) {
                    if (value != null && value.size() >= 2) {
                        values.add(Arrays.asList(value.get(0), value.get(1)));
                    }
                }
            }

            tsdbSeries.setValues(values);
            seriesList.add(tsdbSeries);
        }
    }

    /**
     * 将ISO 8601格式的时间戳转换为毫秒或秒时间戳
     *
     * @param values 原始数据值列表
     * @param columns 列名列表
     * @param timeUnit 时间单位，"s"表示秒，"ms"表示毫秒
     * @return 转换后的数据值列表
     */
    private static List<List<Object>> convertTimestampFormat(List<List<Object>> values, List<String> columns, String timeUnit) {
        if (values == null || values.isEmpty() || columns == null || columns.isEmpty()) {
            return values;
        }

        // 查找time列的索引
        int timeIndex = columns.indexOf("time");
        if (timeIndex < 0) {
            return values;
        }

        List<List<Object>> convertedValues = new ArrayList<>(values.size());
        long divisor = "s".equals(timeUnit) ? 1000 : 1; // 如果是秒，需要除以1000

        for (List<Object> row : values) {
            if (row == null || row.size() <= timeIndex) {
                convertedValues.add(row);
                continue;
            }

            Object timeValue = row.get(timeIndex);
            if (timeValue == null) {
                convertedValues.add(row);
                continue;
            }

            String timeStr = timeValue.toString();
            // 检查是否是ISO 8601格式的时间戳 (例如: "2025-04-16T02:27:00Z")
            if (timeStr.matches("\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}Z")) {
                try {
                    // 将ISO 8601格式转换为时间戳
                    long timestamp = Instant.parse(timeStr).toEpochMilli();
                    if (divisor > 1) {
                        timestamp = timestamp / divisor;
                    }

                    // 创建新的行并替换时间戳
                    List<Object> newRow = new ArrayList<>(row);
                    newRow.set(timeIndex, timestamp);
                    convertedValues.add(newRow);
                } catch (DateTimeParseException e) {
                    // 如果解析失败，保留原始值
                    convertedValues.add(row);
                }
            } else if (timeValue instanceof Number) {
                // 如果已经是数字格式，检查是否需要转换单位
                Number numValue = (Number) timeValue;
                if (divisor > 1) {
                    // 创建新的行并替换时间戳
                    List<Object> newRow = new ArrayList<>(row);
                    newRow.set(timeIndex, numValue.longValue() / divisor);
                    convertedValues.add(newRow);
                } else {
                    convertedValues.add(row);
                }
            } else {
                // 如果不是ISO格式或数字，保留原始值
                convertedValues.add(row);
            }
        }

        return convertedValues;
    }

    /**
     * 格式化SQL查询语句，处理特殊情况
     *
     * @param sql 原始SQL查询语句
     * @param hasCountFunction 是否包含count函数的标志
     * @return 格式化后的SQL查询语句
     */
    public static String formatInfluxSql(String sql, AtomicBoolean hasCountFunction) {
        // 处理show语句
        String lowerCaseSql = sql.toLowerCase();
        if (lowerCaseSql.contains("show")) {
            boolean showMeasurement = false;
            if (lowerCaseSql.contains("measurements") || lowerCaseSql.contains("tag keys")) {
                showMeasurement = true;
                sql = removeWhere(sql);
                sql = sql.replaceAll("/", "").trim();
                sql = sql.replaceAll("~", "").trim();
            }
            sql = sql.replaceAll("\\\\", "").trim();
            sql = sql.replaceAll("\\(\\?i\\)", "").trim();
            if (showMeasurement) {
                lowerCaseSql = sql.toLowerCase();
                int start = sql.indexOf("=");
                if (start > 0) {
                    int end = lowerCaseSql.indexOf("limit");
                    if (end < 0) {
                        end = sql.length();
                    }
                    if (end < start) {
                        end = start;
                    }
                    String prefix = sql.substring(start + 1, end).trim();
                    sql = sql.substring(0, start + 1) + " '" + prefix + "' " + sql.substring(end, sql.length());
                }
            }
        }

        // 处理count函数
        String countFun = "count(";
        int index = sql.indexOf(countFun);
        if (index > 0) {
            hasCountFunction.set(true);
            int countEnd = sql.indexOf(")", index);
            sql = sql.substring(0, index) + sql.substring(index + countFun.length(), countEnd) + sql.substring(countEnd + 1);
        }

        return sql;
    }

    /**
     * 移除SQL中的WHERE子句
     *
     * @param sql 原始SQL查询语句
     * @return 移除WHERE子句后的SQL查询语句
     */
    private static String removeWhere(String sql) {
        int whereIndex = sql.indexOf("WHERE");
        if (whereIndex > 0) {
            sql = sql.substring(0, whereIndex).trim();
        } else {
            whereIndex = sql.indexOf("where");
            if (whereIndex > 0) {
                sql = sql.substring(0, whereIndex).trim();
            }
        }
        return sql;
    }
}
