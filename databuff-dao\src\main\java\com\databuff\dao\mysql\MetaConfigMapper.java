package com.databuff.dao.mysql;

import com.databuff.entity.MetaConfigEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

/**
 * Databuff通用系统全局配置表
 * @package com.databuff.webapp.monitor.mapper
 * @company: dacheng
 * @author: yuzhili
 * @createDate: 2023/6/7
 */
@Mapper
@Repository
public interface MetaConfigMapper {

    @Select("select * from dc_databuff.dc_sys_meta_config where id = #{id}")
    MetaConfigEntity get(@Param("id") String id);

    @Select("select * from dc_databuff.dc_sys_meta_config where code = #{code} ORDER BY update_time DESC limit 1")
    MetaConfigEntity getByCode(@Param("code") String code);

    @Select("select * from dc_databuff.dc_sys_meta_config where code = #{code} and enabled=1 ORDER BY update_time DESC limit 1")
    MetaConfigEntity getEnabledConfig(@Param("code") String code);

    @Insert("INSERT INTO dc_databuff.dc_sys_meta_config (code, `describe`, params, api_key) VALUES(#{code}, #{describe}, #{params}, #{apiKey})")
    void add(MetaConfigEntity metaConfigEntity);

    @Update("UPDATE dc_databuff.dc_sys_meta_config SET code=#{code}, `describe`=#{describe}, params=#{params}, enabled=#{enabled}, api_key=#{apiKey} WHERE id=#{id} limit 1")
    void update(MetaConfigEntity metaConfigEntity);

    @Update("UPDATE dc_databuff.dc_sys_meta_config SET params=#{params} WHERE code=#{code} limit 1")
    void updateParamByCode(@Param("code") String code, @Param("params") String params);

    @Update("UPDATE dc_databuff.dc_sys_meta_config SET enabled=1 WHERE code=#{code} limit 1")
    void enableByCode(@Param("code") String code);

    @Update("UPDATE dc_databuff.dc_sys_meta_config SET enabled=0 WHERE code=#{code} limit 1")
    void disableByCode(@Param("code") String code);

    @Delete("delete from dc_databuff.dc_sys_meta_config where id = #{id} limit 1")
    void delete(@Param("id") Integer id);

    @Delete("delete from dc_databuff.dc_sys_meta_config where code = #{code} limit 1")
    void deleteByCode(@Param("code") Integer code);
}
