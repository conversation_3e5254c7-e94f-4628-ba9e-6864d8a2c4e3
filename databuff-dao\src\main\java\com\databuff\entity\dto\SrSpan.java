package com.databuff.entity.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @author:TianMing
 * @date: 2024/1/24
 * @time: 10:13
 */
@Data
public class SrSpan implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 开始时间
     */
    private Date startTime;

    private Long minutes;

    private Integer isParent;

    /**
     * 服务id
     */
    private String serviceId;

    /**
     * 服务实例
     */
    private String serviceInstance;

    /**
     * 资源
     */
    private String resource;

    /**
     * 结束时间
     */
    private String end;

    /**
     * 主机名
     */
    private String hostName;

    /**
     * 错误数
     */
    private Long error;

    /**
     * 慢请求数
     */
    private Long slow;

    /**
     * 类型
     */
    private String type;

    /**
     * 耗时
     */
    private Long duration;

    /**
     * 是否入口
     */
    private Integer isIn;

    /**
     * traceId
     */
    private String traceId;

    /**
     * spanId
     */
    private String spanId;

    /**
     * apiKey
     */
    private String apiKey;

    /**
     * 开始时间
     */
    private String start;

    /**
     * 主机id
     */
    private String hostId;

    /**
     * 服务名
     */
    private String service;

    /**
     * 父spanId
     */
    private String parentId;

    /**
     * meta信息
     */
    private String meta;

    /**
     * name
     */
    private String name;

    /**
     * 是否出口
     */
    private Integer isOut;

    /**
     * metrics信息
     */
    private String metrics;

    /**
     * http状态码
     */
    private String httpStatusCode;

    /**
     * 错误类型
     */
    private String errorType;

    /**
     * httpHost
     */
    private String peerHostname;

    /**
     * httpMethod
     */
    private String httpMethod;

    /**
     * httpUrl )
     */
    private String httpUrl;

    private String dbType;
    private String dbInstance;
    private String dbOperation;
    private String mqTopic;
    private String mqGroup;
    private String mqBroker;
    private String mqPartition;

    private String clientService;
    private String clientServiceId;
    private String clientServiceInstance;

    public Long getError() {
        if (null == slow) {
            return error;
        }
        if (null == error) {
            return null;
        }
        if (error == 0 && slow == 1) {
            return 2L;
        }
        return error;
    }

}
