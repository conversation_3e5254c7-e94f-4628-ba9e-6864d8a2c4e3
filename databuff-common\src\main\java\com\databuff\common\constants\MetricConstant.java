package com.databuff.common.constants;

import com.google.common.collect.Sets;
import org.apache.commons.collections.BidiMap;
import org.apache.commons.collections.bidimap.DualHashBidiMap;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static com.databuff.common.constants.Constant.Npm.*;
import static com.databuff.common.constants.Constant.Trace.*;
import static com.databuff.common.constants.TSDBIndex.TSDB_METRIC_EXCEPTION_TABLE_NAME;

/**
 * @author:TianMing
 * @date: 2021/7/23
 * @time: 13:45
 */
public class MetricConstant {
    /**
     * 基础设施 主机 应用程序排除以下
     */
    public static final Set<String> EXCLUDE_APP = Sets.newHashSet(
            "host",  "checks",  "network",  "conntrack",  "k8s",  "cluster",  "statsd",
            "cluster_state","npm","confd","databuff","trace","ebpf","netlink",
            "process","uptime", "cpu", "io", "disk", "memory", "system_core", "network", "load", "file_handle", "agent_watch"
    );

    /**
     * 基础指标转服务指标
     */
    public static final Map<String, String> BASE_METRIC_TO_SERVICE_METRIC_MAP = new HashMap<>();

    public static final String SERVICE_CPU_USAGE_PCT = "service.cpu.usage_pct";
    public static final String SERVICE_MEM_USAGE_PCT = "service.mem.usage_pct";
    public static final String SERVICE_MEM_USED = "service.mem.used";
    public static final String SERVICE_MEM_SIZE = "service.mem.size";
    public static final String SERVICE_NET_BYTES_SENT = "service.net.bytes_sent";
    public static final String SERVICE_NET_BYTES_RCVD = "service.net.bytes_rcvd";
    public static final String SERVICE_IO_READ_RATE = "service.io.read.rate";
    public static final String SERVICE_IO_WRITE_RATE = "service.io.write.rate";
    public static final String SERVICE_TCP_CONNS_ESTABLISHED = "service.tcp.conns_established";
    public static final String SERVICE_TCP_RETRANSMIT = "service.tcp.retransmit";


    public static final String KUBELET_CPU_USAGE_PCT = "kubernetes.cpu.usage.pct";
    public static final String PROCESS_CPU_USAGE_PCT = "process.cpu.usage.pct";
    public static final String CONTAINER_CPU_USAGE_PCT = "container.usage.cpu.usage";
    public static final String KUBELET_MEM_USAGE_PCT = "kubernetes.memory.usage.pct";
    public static final String PROCESS_MEM_USAGE_PCT = "process.mem.usage.pct";
    public static final String CONTAINER_MEM_USAGE_PCT = "container.usage.mem.in_use";
    public static final String KUBELET_MEM_USAGE = "kubernetes.memory.usage";
    public static final String PROCESS_MEM_USED = "process.mem.used";
    public static final String CONTAINER_MEM_USED = "container.usage.mem.rss";
    public static final String KUBELET_MEM_SIZE = "kubernetes.memory.size";
    public static final String PROCESS_MEM_TOTAL = "process.mem.total";
    public static final String CONTAINER_MEM_TOTAL = "container.usage.mem.limit";
    public static final String KUBELET_NET_BYTES_SENT = "kubernetes.network.tx_bytes";
    public static final String KUBELET_NET_BYTES_RCVD = "kubernetes.network.rx_bytes";
    public static final String PROCESS_NET_BYTES_SENT = "process.net.bytes_sent";
    public static final String PROCESS_NET_BYTES_RCVD = "process.net.bytes_rcvd";

    public static final String CONTAINER_NET_BYTES_SENT = "container.usage.net.bytes_sent";
    public static final String CONTAINER_NET_BYTES_RCVD = "container.usage.net.bytes_rcvd";

    public static final String KUBELET_IO_READ_RATE = "kubernetes.io.read_bytes";
    public static final String KUBELET_IO_WRITE_RATE = "kubernetes.io.write_bytes";
    public static final String PROCESS_IO_READ_RATE = "process.io.read.rate";
    public static final String PROCESS_IO_WRITE_RATE = "process.io.write.rate";
    public static final String CONTAINER_IO_READ_RATE = "container.usage.io.read_bytes";
    public static final String CONTAINER_IO_WRITE_RATE = "container.usage.io.write_bytes";
    public static final String KUBELET_TCP_CONNS_ESTABLISHED = "kubernetes.tcp.conns_established";
    public static final String KUBELET_TCP_RETRANSMIT = "kubernetes.tcp.retransmit";
    public static final String PROCESS_TCP_CONNS_ESTABLISHED = "process.tcp.conns_established";
    public static final String PROCESS_TCP_RETRANSMIT = "process.tcp.retransmit";
    public static final String CONTAINER_TCP_CONNS_ESTABLISHED = "container.usage.tcp.connections";
    public static final String CONTAINER_TCP_RETRANSMIT = "container.usage.tcp.retranssegs";


    static {
        BASE_METRIC_TO_SERVICE_METRIC_MAP.put(KUBELET_CPU_USAGE_PCT, SERVICE_CPU_USAGE_PCT);
        BASE_METRIC_TO_SERVICE_METRIC_MAP.put(PROCESS_CPU_USAGE_PCT, SERVICE_CPU_USAGE_PCT);
        BASE_METRIC_TO_SERVICE_METRIC_MAP.put(CONTAINER_CPU_USAGE_PCT, SERVICE_CPU_USAGE_PCT);
        BASE_METRIC_TO_SERVICE_METRIC_MAP.put(KUBELET_MEM_USAGE_PCT, SERVICE_MEM_USAGE_PCT);
        BASE_METRIC_TO_SERVICE_METRIC_MAP.put(PROCESS_MEM_USAGE_PCT, SERVICE_MEM_USAGE_PCT);
        BASE_METRIC_TO_SERVICE_METRIC_MAP.put(CONTAINER_MEM_USAGE_PCT, SERVICE_MEM_USAGE_PCT);
        BASE_METRIC_TO_SERVICE_METRIC_MAP.put(KUBELET_MEM_USAGE, SERVICE_MEM_USED);
        BASE_METRIC_TO_SERVICE_METRIC_MAP.put(PROCESS_MEM_USED, SERVICE_MEM_USED);
        BASE_METRIC_TO_SERVICE_METRIC_MAP.put(CONTAINER_MEM_USED, SERVICE_MEM_USED);
        BASE_METRIC_TO_SERVICE_METRIC_MAP.put(KUBELET_MEM_SIZE, SERVICE_MEM_SIZE);
        BASE_METRIC_TO_SERVICE_METRIC_MAP.put(PROCESS_MEM_TOTAL, SERVICE_MEM_SIZE);
        BASE_METRIC_TO_SERVICE_METRIC_MAP.put(CONTAINER_MEM_TOTAL, SERVICE_MEM_SIZE);
        BASE_METRIC_TO_SERVICE_METRIC_MAP.put(KUBELET_NET_BYTES_SENT, SERVICE_NET_BYTES_SENT);
        BASE_METRIC_TO_SERVICE_METRIC_MAP.put(PROCESS_NET_BYTES_SENT, SERVICE_NET_BYTES_SENT);
        BASE_METRIC_TO_SERVICE_METRIC_MAP.put(CONTAINER_NET_BYTES_SENT, SERVICE_NET_BYTES_SENT);
        BASE_METRIC_TO_SERVICE_METRIC_MAP.put(KUBELET_NET_BYTES_RCVD, SERVICE_NET_BYTES_RCVD);
        BASE_METRIC_TO_SERVICE_METRIC_MAP.put(PROCESS_NET_BYTES_RCVD, SERVICE_NET_BYTES_RCVD);
        BASE_METRIC_TO_SERVICE_METRIC_MAP.put(CONTAINER_NET_BYTES_RCVD, SERVICE_NET_BYTES_RCVD);
        BASE_METRIC_TO_SERVICE_METRIC_MAP.put(KUBELET_IO_READ_RATE, SERVICE_IO_READ_RATE);
        BASE_METRIC_TO_SERVICE_METRIC_MAP.put(PROCESS_IO_READ_RATE, SERVICE_IO_READ_RATE);
        BASE_METRIC_TO_SERVICE_METRIC_MAP.put(CONTAINER_IO_READ_RATE, SERVICE_IO_READ_RATE);
        BASE_METRIC_TO_SERVICE_METRIC_MAP.put(KUBELET_IO_WRITE_RATE, SERVICE_IO_WRITE_RATE);
        BASE_METRIC_TO_SERVICE_METRIC_MAP.put(PROCESS_IO_WRITE_RATE, SERVICE_IO_WRITE_RATE);
        BASE_METRIC_TO_SERVICE_METRIC_MAP.put(CONTAINER_IO_WRITE_RATE, SERVICE_IO_WRITE_RATE);
        BASE_METRIC_TO_SERVICE_METRIC_MAP.put(KUBELET_TCP_CONNS_ESTABLISHED, SERVICE_TCP_CONNS_ESTABLISHED);
        BASE_METRIC_TO_SERVICE_METRIC_MAP.put(PROCESS_TCP_CONNS_ESTABLISHED, SERVICE_TCP_CONNS_ESTABLISHED);
        BASE_METRIC_TO_SERVICE_METRIC_MAP.put(CONTAINER_TCP_CONNS_ESTABLISHED, SERVICE_TCP_CONNS_ESTABLISHED);
        BASE_METRIC_TO_SERVICE_METRIC_MAP.put(KUBELET_TCP_RETRANSMIT, SERVICE_TCP_RETRANSMIT);
        BASE_METRIC_TO_SERVICE_METRIC_MAP.put(PROCESS_TCP_RETRANSMIT, SERVICE_TCP_RETRANSMIT);
        BASE_METRIC_TO_SERVICE_METRIC_MAP.put(CONTAINER_TCP_RETRANSMIT, SERVICE_TCP_RETRANSMIT);
    }


    public static final BidiMap I6000_METRIC_MAP = new DualHashBidiMap();

    static {
        I6000_METRIC_MAP.put("jvm.memory.heap.max", "JVMHeapSizeMax");
        I6000_METRIC_MAP.put("jvm.thread_count", "JVMThreadsNum");
        I6000_METRIC_MAP.put("jvm.memory.noheap.committed", "JVMNonHeapCommittedSize");
        I6000_METRIC_MAP.put("jvm.memory.noheap.used", "JVMNonHeapUsedSize");
        I6000_METRIC_MAP.put("jvm.gc.eden_size", "JVMEdenGenSize");
        I6000_METRIC_MAP.put("jvm.gc.survivor_size", "JVMSurvivorSpaceSize");
        I6000_METRIC_MAP.put("jvm.gc.old_gen_size", "JVMOldGenSize");
        I6000_METRIC_MAP.put("jvm.memory.heap.used", "JVMHeapUsedSize");

        I6000_METRIC_MAP.put("jvm.gc.minor_collection_count", "JVMNewGenGCNum");// 新生代GC次数
        I6000_METRIC_MAP.put("jvm.gc.minor_collection_time", "JVMNewGenGCCost");// 新生代GC耗时
        I6000_METRIC_MAP.put("jvm.gc.major_collection_count", "JVMOldGenGCNum");// 老年代GC次数
        I6000_METRIC_MAP.put("jvm.gc.major_collection_time", "JVMOldGenGCCost");// 老年代GC耗时
        I6000_METRIC_MAP.put(TSDB_METRIC_EXCEPTION_TABLE_NAME, "APMServiceExceptionCount");// 异常数量
        I6000_METRIC_MAP.put(SERVICE_CPU_USAGE_PCT, "CpuUsedRatio");// Cpu使用率
        I6000_METRIC_MAP.put(SERVICE_MEM_USAGE_PCT, "MemUsedRatio");// 内存使用率
        I6000_METRIC_MAP.put(SERVICE_MEM_USED, "MemUsedSize");// 已使用内存大小
        I6000_METRIC_MAP.put(SERVICE_MEM_SIZE, "MemSize");// 主机内存大小
    }

    public static final BidiMap FIELD_NAME_MAP = new DualHashBidiMap();

    static {
//        FIELD_NAME_MAP.put("callCnt", "每分钟请求数");
        FIELD_NAME_MAP.put("minReqCnt", "每分钟请求数");
//        FIELD_NAME_MAP.put("secReqCnt", "每分钟请求数");
        FIELD_NAME_MAP.put("minErrCnt", "每分钟错误数");
//        FIELD_NAME_MAP.put("secErrCnt", "每分钟错误数");
        FIELD_NAME_MAP.put("errRate", "错误率");
        FIELD_NAME_MAP.put("errCnt", "错误数");
        FIELD_NAME_MAP.put("avgLatency", "平均响应时间");
        FIELD_NAME_MAP.put("p50Latency", "响应时间的50百分位");
        FIELD_NAME_MAP.put("p75Latency", "响应时间的75百分位");
        FIELD_NAME_MAP.put("p90Latency", "响应时间的90百分位");
        FIELD_NAME_MAP.put("p95Latency", "响应时间的95百分位");
        FIELD_NAME_MAP.put("p99Latency", "响应时间的99百分位");
        FIELD_NAME_MAP.put("p100Latency", "最大响应时间");
    }

    /**
     * ================================trace 服务相关指标==========================================
     * 平均相关指标索引 dc_service_metrics_avg_
     * 数量相关指标索引 dc_service_metrics_count_
     */
    /**
     * 单分钟服务调用次数系统
     */
    public static final String SERVICE_CALLS = "cnt";
    /**
     * 各个接口组件请求
     */
    public static final String COMPONENT_CALLS = "cnt";
    public static final String COMPONENT_SLOW_CNT = "slow";
    public static final String COMPONENT_ERR_CNT = "error";
    public static final String COMPONENT_SUM_DURATION = "sumDuration";
    public static final String COMPONENT_MAX_DURATION = "maxDuration";
    public static final String COMPONENT_MIN_DURATION = "minDuration";
    public static final String COMPONENT_SLOW_CALLS = "slowCnt";
    public static final String COMPONENT_READ_ROWS_CNT = "readRowsCnt";
    public static final String COMPONENT_READ_ROWS = "readRows";
    public static final String COMPONENT_UPDATE_ROWS_CNT = "updateRowsCnt";
    public static final String COMPONENT_UPDATE_ROWS = "updateRows";

    /**
     * mq消费延迟
     */
    public static final String COMPONENT_DELAY = "delay";

    /**
     * 单分钟服务调用失败次数
     */
    public static final String SERVICE_ERR_CNT = "error";

    /**
     * 单分钟服务慢调用次数
     */
    public static final String SERVICE_SLOW_CNT = "slowCnt";
    /**
     * 单分钟服务非常慢调用次数
     */
    public static final String SERVICE_VERY_SLOW_CNT = "verySlowCnt";
    public static final String CPU_TIME = "cpuTime";
    public static final String MQ_DELAY = "delay";
    public static final String MQ_PARTITION = "partition";
    public static final String RESPONSE_BODY_LENGTH = "respBodyLength";
    public static final String REQUEST_BODY_LENGTH = "reqBodyLength";
    public static final String MQ_BODY_LENGTH = "mqBodyLength";
    /**
     * 单分钟服务响应时间 Apdex
     */
    public static final String SERVICE_APDEX = "apdex";
    /**
     * 单分钟服务总响应时间
     */
    public static final String SERVICE_SUM_RESP_TIME = "sumDuration";
    /**
     * 单分钟服务cpu总时间
     */
    public static final String SERVICE_SUM_CPU_TIME = "sumCpuTime";
    /**
     * 单分钟服务最大响应时间
     */
    public static final String SERVICE_MAX_RESP_TIME = "maxDuration";
    /**
     * 单分钟服务最小响应时间
     */
    public static final String SERVICE_MIN_RESP_TIME = "minDuration";

    /**
     * 单分钟服务健康度状态
     */
    public static final String SERVICE_HEALTH_STATUS = "healthStatus";


    /**
     * 业务系统请求
     */
    public static final String BUSINESS_CALLS = "cnt";
    public static final String BUSINESS_ERR_CNT = "error";
    public static final String BUSINESS_SUM_DURATION = "sumDuration";

    /**
     * ================================trace 服务相关==========================================
     */

    /**
     * 服务db列
     */
    public static final Set<String> SUPPORTED_DB_TYPES = Sets.newHashSet("mongo", "mysql", "oracle", "postgresql", "sqlserver", "db2", "h2", "hsqldb", "derby", "oceanbase", "gaussdb");
    /**
     * 服务缓存列
     */
    public static final String[] CACHE_TYPE = new String[]{
            "memcached", "redis", "cache"
    };
    /**
     * rum 相关指标
     */
    // 错误类型指标
    public static final String ERROR_TYPE_CNT = "errorTypeCnt";
    // 错误来源指标
    public static final String ERROR_SOURCE_CNT = "errorSourceCnt";

    public static final String ERROR_TYPE_SOURCE_CNT = "errorTypeSourceCnt";

    // ttfb 指标
    public static final String TTFB10 = "ttfb10";

    public static final String TTFB75 = "ttfb75";

    public static final String TTFB90 = "ttfb90";

    public static final String TTFB50 = "ttfb50";

    public static final String TTFBMIN = "ttfbmin";

    public static final String TTFBMAX = "ttfbmax";

    public static final String TTFBAVG = "ttfbavg";

    // lcp 指标
    public static final String LCP10 = "lcp10";

    public static final String LCP75 = "lcp75";

    public static final String LCP90 = "lcp90";

    public static final String LCP50 = "lcp50";

    public static final String LCPMIN = "lcpmin";

    public static final String LCPMAX = "lcpmax";

    public static final String LCPAVG = "lcpavg";

    // fid 指标
    public static final String FID10 = "fid10";

    public static final String FID75 = "fid75";

    public static final String FID90 = "fid90";

    public static final String FID50 = "fid50";

    public static final String FIDMIN = "fidmin";

    public static final String FIDMAX = "fidmax";

    public static final String FIDAVG = "fidavg";

    // cls 指标
    public static final String CLS10 = "cls10";

    public static final String CLS75 = "cls75";

    public static final String CLS90 = "cls90";

    public static final String CLS50 = "cls50";

    public static final String CLSMIN = "clsmin";

    public static final String CLSMAX = "clsmax";

    public static final String CLSAVG = "clsavg";

    // plt 指标
    public static final String PLT10 = "plt10";

    public static final String PLT75 = "plt75";

    public static final String PLT90 = "plt90";

    public static final String PLT50 = "plt50";

    public static final String PLTMIN = "pltmin";

    public static final String PLTMAX = "pltmax";

    public static final String PLTAVG = "pltavg";

    // 错误数指标
    public static final String ERROR_CNT = "errCnt";
    // 会话 action 数量指标
    public static final String SESSION_ACTION_CNT = "sessionActionCnt";
    // 会话 error 数量指标
    public static final String SESSION_ERROR_CNT = "sessionErrorCnt";
    // 会话 开始时间指标
    public static final String SESSION_START_TIME = "sessionStartTime";
    // 会话 结束时间指标
    public static final String SESSION_END_TIME = "sessionEndTime";
    // 页面会话数指标
    public static final String VIEW_SESSION_CNT = "viewSessionCnt";

    // 浏览器性能分布指标
    public static final String BROWSER_PLT_MIN = "browserpltmin";

    public static final String BROWSER_PLT_MAX = "browserpltmax";

    public static final String BROWSER_PLT_AVG = "browserpltavg";

    public static final String BROWSER_PLT_SUM = "browserpltsum";

    public static final String BROWSER_PLT_10 = "browserplt10";

    public static final String BROWSER_PLT_75 = "browserplt75";

    public static final String BROWSER_PLT_90 = "browserplt90";

    // pv 指标
    public static final String PV = "pv";

    public static final String UV = "uv";

    public static final String SESSION_BROWSER = "sessionBrowser";

    public static final String APP_ERROR_CNT = "appErrorCnt";


    public static final String VIEW_ACTION_CNT = "viewActionCnt";

    public static final String VIEW_PLT_75 = "viewplt75";

    // 会话加载资源数量
    public static final String SESSION_RESOURCE_CNT = "sessionResourceCnt";

    // 设备
    public static final String DEVICENAME = "deviceName";

    public static final Set<String> NPM_FILL_SERVER_TAGS = new HashSet<>();
    public static final Set<String> NPM_FILL_SERVER_DOMAIN_TAGS = new HashSet<>();
    public static final Set<String> NPM_FILL_CLIENT_TAGS = new HashSet<>();

    static {
        NPM_FILL_SERVER_DOMAIN_TAGS.add(CLUSTER_ID);
        NPM_FILL_SERVER_DOMAIN_TAGS.add(DEPLOYMENT);
        NPM_FILL_SERVER_DOMAIN_TAGS.add(SERVICE_ID);
        NPM_FILL_SERVER_DOMAIN_TAGS.add(SERVICE);

        NPM_FILL_SERVER_TAGS.addAll(NPM_FILL_SERVER_DOMAIN_TAGS);
        NPM_FILL_SERVER_TAGS.add(POD_NAME);
        NPM_FILL_SERVER_TAGS.add(SERVICE_INSTANCE);
        NPM_FILL_SERVER_TAGS.add(CID);
        NPM_FILL_SERVER_TAGS.add(CNAME);
        NPM_FILL_SERVER_TAGS.add(HOST_NAME);
        NPM_FILL_SERVER_TAGS.add(PID);
        NPM_FILL_SERVER_TAGS.add(PNAME);

        NPM_FILL_CLIENT_TAGS.add(SRC_CLUSTER_ID);
        NPM_FILL_CLIENT_TAGS.add(SRC_DEPLOYMENT);
        NPM_FILL_CLIENT_TAGS.add(SRC_SERVICE_ID);
        NPM_FILL_CLIENT_TAGS.add(SRC_SERVICE);
        NPM_FILL_CLIENT_TAGS.add(SRC_POD_NAME);
        NPM_FILL_CLIENT_TAGS.add(SRC_SERVICE_INSTANCE);
        NPM_FILL_CLIENT_TAGS.add(SRC_CID);
        NPM_FILL_CLIENT_TAGS.add(SRC_CNAME);
        NPM_FILL_CLIENT_TAGS.add(SRC_HOSTNAME);
        NPM_FILL_CLIENT_TAGS.add(SRC_PID);
        NPM_FILL_CLIENT_TAGS.add(SRC_PNAME);
    }

    public static final Map<String, Integer> WORK_LOAD_NAME_TYPE_MAP = new DualHashBidiMap();

    static {
        WORK_LOAD_NAME_TYPE_MAP.put("Pod", 41);
        WORK_LOAD_NAME_TYPE_MAP.put("ReplicaSet", 42);
        WORK_LOAD_NAME_TYPE_MAP.put("Deployment", 43);
        WORK_LOAD_NAME_TYPE_MAP.put("Service", 44);
        WORK_LOAD_NAME_TYPE_MAP.put("Node", 45);
        WORK_LOAD_NAME_TYPE_MAP.put("Cluster", 46);
        WORK_LOAD_NAME_TYPE_MAP.put("Job", 47);
        WORK_LOAD_NAME_TYPE_MAP.put("CronJob", 48);
        WORK_LOAD_NAME_TYPE_MAP.put("DaemonSet", 49);
        WORK_LOAD_NAME_TYPE_MAP.put("StatefulSet", 50);
        WORK_LOAD_NAME_TYPE_MAP.put("Namespace", 51);
    }

    public static final Map<Integer, String> WORK_LOAD_TYPE_NAME_MAP = new DualHashBidiMap();

    static {
        WORK_LOAD_TYPE_NAME_MAP.put(41, "Pod");
        WORK_LOAD_TYPE_NAME_MAP.put(42, "ReplicaSet");
        WORK_LOAD_TYPE_NAME_MAP.put(43, "Deployment");
        WORK_LOAD_TYPE_NAME_MAP.put(44, "Service");
        WORK_LOAD_TYPE_NAME_MAP.put(45, "Node");
        WORK_LOAD_TYPE_NAME_MAP.put(46, "Cluster");
        WORK_LOAD_TYPE_NAME_MAP.put(47, "Job");
        WORK_LOAD_TYPE_NAME_MAP.put(48, "CronJob");
        WORK_LOAD_TYPE_NAME_MAP.put(49, "DaemonSet");
        WORK_LOAD_TYPE_NAME_MAP.put(50, "StatefulSet");
        WORK_LOAD_TYPE_NAME_MAP.put(51, "Namespace");
    }
}
