package com.databuff.dao.mysql;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.databuff.dao.mysql.provider.DcDatabuffIssueLogSqlProvider;
import com.databuff.entity.DcDatabuffIssueLog;
import org.apache.ibatis.annotations.*;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@Mapper
public interface DcDatabuffIssueLogMapper extends BaseMapper<DcDatabuffIssueLog> {

    @Select("SELECT l.alarm_id FROM dc_databuff_issue_log l " +
            "INNER JOIN dc_databuff_issue_detail detail ON l.issue_id = detail.id " +
            "WHERE detail.problemId = #{problemId}")
    List<String> selectAlarmIdsByProblemId(@Param("problemId") String problemId);

    @Select("SELECT * FROM dc_databuff_issue_log WHERE issue_id = #{issueId}")
    List<DcDatabuffIssueLog> selectById(String issueId);

    @Select("SELECT * FROM dc_databuff_issue_log")
    List<DcDatabuffIssueLog> selectAll();

    @InsertProvider(type = DcDatabuffIssueLogSqlProvider.class, method = "insert")
    int insert(DcDatabuffIssueLog issueLog);

    @Update("UPDATE dc_databuff_issue_log SET alarm_id=#{alarmId}, policy_id=#{policyId}, source=#{source}, " +
            "create_time=#{createTime}, description=#{description} WHERE issue_id=#{issueId}")
    void update(DcDatabuffIssueLog issueLog);

    @Delete("DELETE FROM dc_databuff_issue_log WHERE issue_id=#{issueId}")
    void delete(String issueId);

    @Select("SELECT issue_id FROM dc_databuff_issue_log WHERE alarm_id = #{alarmId}")
    List<String> selectIssueIdByAlarmId(String alarmId);

    /**
     * 根据报警ID集合查询对应最新的问题记录
     *
     * @param alarmIds 需要查询的报警ID集合
     * @return 包含报警信息的Map列表，每个Map包含alarm_id、issueId、problemId、influenceServiceCount和analyseEndTime字段
     */
    @Select({
            "<script>",
            "SELECT",
            "    ddil.alarm_id," +
                    "    ddid.id AS issueId," +
                    "    ddid.problemId," +
                    "    dp.problemService," +
                    "    dp.influenceServiceCount," +
                    "    dp.analyseEndTime",
            "FROM dc_databuff_issue_log ddil",
            "INNER JOIN dc_databuff_issue_detail ddid ON ddid.id = ddil.issue_id",
            "INNER JOIN dc_databuff_problem dp ON ddid.problemId = dp.id",
            "WHERE ddil.alarm_id IN",
            "    <foreach item='alarmId' collection='alarmIds' open='(' separator=',' close=')'>",
            "        #{alarmId}",
            "    </foreach>",
            "AND (ddil.alarm_id, dp.analyseEndTime) IN (",
            "    <!-- 获取每个报警ID对应的最大分析结束时间 -->",
            "    SELECT",
            "        sub.alarm_id," +
                    "        MAX(sub.analyseEndTime) AS max_time",
            "    FROM (",
            "        <!-- 子查询获取原始数据 -->",
            "        SELECT",
            "            ddil.alarm_id," +
                    "            dp.analyseEndTime",
            "        FROM dc_databuff_issue_log ddil",
            "        INNER JOIN dc_databuff_issue_detail ddid ON ddid.id = ddil.issue_id",
            "        INNER JOIN dc_databuff_problem dp ON ddid.problemId = dp.id",
            "        WHERE ddil.alarm_id IN",
            "            <foreach item='alarmId' collection='alarmIds' open='(' separator=',' close=')'>",
            "                #{alarmId}",
            "            </foreach>",
            "    ) AS sub",
            "    GROUP BY sub.alarm_id",
            ")",
            "</script>"
    })
    List<Map<String, Object>> selectLatestRecordsByAlarmIds(@Param("alarmIds") Collection<String> alarmIds);

}