package com.databuff.entity.dto;

import java.io.Serializable;

/**
 * @description 对应表dc_databuff_plugin_dashboard 
 * <AUTHOR>
 * @date 2022-01-13 
 */
public class DatabuffPluginDashboard implements Serializable {
	private static final long serialVersionUID =  7340847221029561764L;

	/**
	 * 对应表字段 id
	 */
	private Long id;

	/**
	 * 对应表字段 api_key
	 */
	private String apiKey;

	/**
	 * 插件id；对应表字段 plugin_dic_id
	 */
	private Long pluginDicId;

	/**
	 * 仪表盘id；对应表字段 dashboard_id
	 */
	private Long dashboardId;

	public DatabuffPluginDashboard(){}
	public DatabuffPluginDashboard(String apiKey,Long pluginDicId,Long dashboardId){
		this.apiKey = apiKey;
		this.pluginDicId = pluginDicId;
		this.dashboardId = dashboardId;
	}

	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getApiKey() {
		return this.apiKey;
	}

	public void setApiKey(String apiKey) {
		this.apiKey = apiKey;
	}

	public Long getPluginDicId() {
		return this.pluginDicId;
	}

	public void setPluginDicId(Long pluginDicId) {
		this.pluginDicId = pluginDicId;
	}

	public Long getDashboardId() {
		return this.dashboardId;
	}

	public void setDashboardId(Long dashboardId) {
		this.dashboardId = dashboardId;
	}

}
