package com.databuff.common.exception;

/**
 * 自定义异常(CustomException)
 * <AUTHOR>
 * @date 2019/3/30 13:59
 */
public class CustomException extends RuntimeException {


    public CustomException() {
    }

    public CustomException(String message) {
        super(message);
    }

    public CustomException(String message, Throwable cause) {
        super(message, cause);
    }

    public CustomException(Throwable cause) {
        super(cause);
    }

    public CustomException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
