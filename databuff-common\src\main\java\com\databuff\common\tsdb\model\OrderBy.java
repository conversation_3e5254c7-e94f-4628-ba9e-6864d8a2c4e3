package com.databuff.common.tsdb.model;

import java.io.Serializable;

public class OrderBy implements Serializable {
    private String field;
    private boolean isAsc;

    public OrderBy(String field, boolean isAsc) {
        this.field = field;
        this.isAsc = isAsc;
    }

    public String getField() {
        return field;
    }

    public boolean isAsc() {
        return isAsc;
    }

    /**
     * 升序
     * @param field
     * @return
     */
    public static OrderBy asc(String field) {
        return new OrderBy(field, true);
    }

    /**
     * 降序
     * @param field
     * @return
     */
    public static OrderBy desc(String field) {
        return new OrderBy(field, false);
    }

    // 新增拷贝构造函数
    public OrderBy(OrderBy other) {
        this.field = other.field;
        this.isAsc = other.isAsc;
    }
    // 移除 clone() 方法，改为提供 copy() 方法
    public OrderBy copy() {
        return new OrderBy(this);
    }
}
