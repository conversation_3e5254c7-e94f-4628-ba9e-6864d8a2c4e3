package com.databuff.entity.rum;

import lombok.Data;

import java.util.List;

/**
 * @author:TianMing
 * @date: 2024/6/27
 * @time: 15:31
 */
@Data
public class Span {
    private String traceId;
    private String spanId;
    private String parentSpanId = "0";
    private String name;
    private int kind;
    private String startTimeUnixNano;
    private String endTimeUnixNano;
    private List<Attribute> attributes;
    private int droppedAttributesCount;
    private List<Event> events;
    private int droppedEventsCount;
    private Status status;
    private List<Object> links; // Change Object to the actual type if you know it
    private int droppedLinksCount;
}
