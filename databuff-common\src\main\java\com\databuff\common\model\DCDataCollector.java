package com.databuff.common.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class DCDataCollector implements Serializable {

    private static final long serialVersionUID = 1L;

    @JSONField(name = "hashcode")
    private Integer hashcode;

    @JSO<PERSON>ield(name = "tag")
    private String tag;

    @JSONField(name = "error")
    private Integer error;

    @JSONField(name = "hours")
    private Long hours;

    @JSONField(name = "minutes")
    private Long minutes;

    @JSONField(name = "startTime")
    private String startTime;

    @JSONField(name = "is_parent")
    private Integer is_parent;

    @JSONField(name = "trace_id")
    private String trace_id;

    @JSONField(name = "span_id")
    private String span_id;

    @JSONField(name = "meta.http.status_code")
    private Integer meta_http_status_code;

    @JSONField(name = "duration")
    private Long duration;

    @JSONField(name = "hostName")
    private String hostName;

    @JSONField(name = "type")
    private String type;

    @JSONField(name = "serviceId")
    private String serviceId;

    @J<PERSON><PERSON>ield(name = "serviceInstance")
    private String serviceInstance;

    @JSONField(name = "resource")
    private String resource;

    @JSONField(name = "meta.http.method")
    private String meta_http_method;

    @JSONField(name = "meta.error.type")
    private String meta_error_type;

    @JSONField(name = "envTag1")
    private String envTag1 = "";

    @JSONField(name = "envTag2")
    private String envTag2 = "";
}
