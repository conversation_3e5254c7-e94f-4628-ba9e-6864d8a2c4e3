package com.databuff.common.constants;

import java.util.HashMap;
import java.util.Map;

import static com.databuff.common.constants.Constant.Kafka.TRACE_TOPIC;
import static com.databuff.common.constants.Constant.Kafka.TRACE_TO_FILL_TOPIC;

public class MetricName {

    /**
     * metric
     */
    public static final String METRIC_KAFKA_FETCH = "metric.kafka.fetch";
    public static final String METRIC_KAFKA_FETCH_FAIL = "metric.kafka.fetch.fail";
    public static final String METRIC_KAFKA_CONSUME = "metric.kafka.consume";
    public static final String METRIC_KAFKA_DELAY = "metric.kafka.delay";
    public static final String METRIC_DATA_ENTRY_WORKER_COST = "metric.data.entry.worker.cost";
    public static final String METRIC_ORIGINAL_TIME = "metric.original.time";
    public static final String METRIC_PUBLISH = "metric.publish";
    public static final String METRIC_DISCARD = "metric.discard";
    public static final String METRIC_SUCCESS = "metric.success";
    public static final String METRIC_PAYLOAD = "metric.payload";
    public static final String METRIC_DEBUG_HIT = "metric.debug.hit";
    public static final String METRIC_NAME_SUCCESS = "metric.name.success";
    public static final String METRIC_NAME_DISCARD = "metric.name.discard";
    public static final String METRIC_TAG_COVER = "metric.tag.cover";


    /**
     * hostInfo
     */
    public static final String HOST_INFO_DISCARD = "hostInfo.discard";
    public static final String HOST_INFO_SUCCESS = "hostInfo.success";

    /**
     * k8s
     */
    public static final String K8S_DISCARD = "k8s.discard";
    public static final String K8S_SUCCESS = "k8s.success";
    public static final String K8S_PAYLOAD = "k8s.payload";

    /**
     * trace
     */
    public static final String TRACE_DISCARD = "trace.discard";
    public static final String TRACE_SUCCESS = "trace.success";
    public static final String TRACE_PAYLOAD = "trace.payload";
    public static final String PROFILING_CPU_DECOMPRESS_NULL = "profiling.cpu.decompress.null";

    public static final String PROFILING_CPU_SINK = "profiling.cpu.sink";
    public static final String PROFILING_CPU_CONF = "profiling.cpu.conf";
    public static final String PROFILING_CPU_SINK_CNT = "profiling.cpu.sink.cnt";

    /**
     * npm
     */
    public static final String NPM_STATS_DISCARD = "npm.stats.discard";
    public static final String NPM_STATS_SUCCESS = "npm.stats.success";
    public static final String NPM_STATS_PAYLOAD = "npm.stats.payload";
    public static final String NPM_DETAIL_DISCARD = "npm.detail.discard";
    public static final String NPM_DETAIL_SUCCESS = "npm.detail.success";
    public static final String NPM_DETAIL_PAYLOAD = "npm.detail.payload";

    /**
     * log to es
     */
    public static final String LOG_CONSUME = "log.consume";
    public static final String LOG_DELAY = "log.delay";
    public static final String LOG_COST = "log.cost";
    public static final String LOG_TO_SR = "log.to.sr";

    /**
     * log
     */
    public static final String LOG_DISCARD = "log.discard";
    public static final String LOG_SUCCESS = "log.success";

    /**
     * kafka
     */
    public static final String KAFKA_SEND_SUCCESS_COST = "kafka.send.success.cost";
    public static final String KAFKA_SEND_SUCCESS_COUNT = "kafka.send.success.count";

    public static final String TRACE_SPAN_RESOURCE_TOO_LONG = "trace.span.resource.too.long";
    public static final String TRACE_SPAN_RESOURCE_STANDARD = "trace.span.resource.standard";
    public static final String TRACE_METRIC_TAG_VALUE_TOO_LONG = "metric.tag.value.too.long";

    /**
     * redis
     */
    public static final String REDIS_COMMAND = "redis.command";


    public static final String DATA_DELAY = "data.delay";
    public static final String OLAP_RECEIVE_COUNT = "olap.receive.count";
    public static final String OLAP_RECEIVE_PAYLOAD = "olap.receive.payload";

    public static final String JOB_NAME_ALARM_FORWARD_DISCARD = "alarm.forward.discard";
    public static final String JOB_NAME_ALARM_FORWARD_COUNT = "alarm.forward.count";
    public static final String JOB_NAME_ALARM_FORWARD_FAIL_COUNT = "alarm.forward.fail.count";
    public static final String JOB_NAME_ALARM_FORWARD_FAIL = "alarm.forward.fail";

    public static final String CMDB_FROM_KAFKA = "cmdb.from.kafka.count";
    public static final String CMDB_FROM_KAFKA_2MYSQL = "cmdb.from.kafka.2mysql.count";

    public static final String CMDB_TASK_SYNC_ALL_SVC_COUNTS = "cmdb.sync.all.svc.counts";
    public static final String CMDB_TASK_SYNC_CMDB_SVC_COUNTS = "cmdb.sync.cmdb.svc.counts";
    public static final String CMDB_TASK_SYNC_CMDB_ERR_COUNTS = "cmdb.sync.cmdb.err.counts";
    public static final String CMDB_TASK_SYNC_CMDB_SUCCESS_COUNTS = "cmdb.sync.cmdb.success.counts";

    /**
     * ringbuffer
     */
    public static final String RINGBUFFER_PARALLEL = "ringbuffer.parallel";
    public static final String RINGBUFFER_FILL_RATIO = "ringbuffer.fillRatio";
    public static final String RINGBUFFER_MINIMUM_GATING_SEQUENCE = "ringbuffer.minimumGatingSequence";
    public static final String RINGBUFFER_FULL = "ringbuffer.full";
    public static final String RINGBUFFER_OLAP_FLUSH_ROWS = "ringbuffer.olap.flush.rows";

    /**
     * moredb
     */
    public static final String TSDB_SEND_POINTS = "moredb.send.points";
    public static final String TSDB_SEND_COST = "moredb.send.cost";
    public static final String TSDB_SEND_FAILED = "moredb.send.failed";
    public static final String TSDB_QUERY_COST = "moredb.query.cost";
    public static final String TSDB_QUERY_FAILED = "moredb.query.failed";
    public static final String TSDB_CREATE_COST = "moredb.create.db.cost";
    public static final String TSDB_CREATE_FAILED = "moredb.create.db.failed";
    public static final String TSDB_CONNECTION_COST = "moredb.connection.cost";

    /**
     * open ai
     */
    public static final String QUERY_OPEN_AI_COST = "ai.query.cost";

    /**
     * webapp
     */
    public static final String SQL_COST = "sql.cost";

    /**
     * task-executor
     */
    public static final String TASK_EXECUTOR_SUCCESS_COUNT = "tsdb.query.success.series.count";
    public static final String TASK_EXECUTOR_COST_SUM = "tsdb.query.success.cost";

    /**
     * StarRocks
     */
    public static final String OLAP_SINK_FUNCTION_FLUSH_COST = "OlapSinkFunction.flush.cost";
    public static final String OLAP_SINK_FUNCTION_FLUSH_PAYLOAD = "OlapSinkFunction.flush.payload";
    public static final String OLAP_SINK_FUNCTION_FLUSH_PENDING = "OlapSinkFunction.flush.pending";

    /**
     * 这边统计的是调用dts trace web接口算一次
     */
    public static final String RUM_TRACE_REQUEST = "rum.trace.request";
    public static final String RUM_TRACE_DISCARD = "rum.trace.discard";
    public static final String RUM_TRACE_DISCARD_PAGE_METRICS = "rum.trace.discard.page.metrics";
    public static final String RUM_TRACE_DISCARD_DOMAIN = "rum.trace.discard.domain";
    public static final String RUM_TRACE_DISCARD_ROBOT = "rum.trace.discard.robot";
    /**
     * rum 不同的page与action 等数据 会定时放在一起上报 这边统计的是span个数
     */
    public static final String RUM_TRACE_SUCCESS = "rum.trace.success";
    public static final String RUM_TRACE_PAYLOAD = "rum.trace.payload";
    public static final String RUM_LOG_REQUEST = "rum.log.request";
    public static final String RUM_LOG_DISCARD = "rum.log.discard";
    public static final String RUM_LOG_DISCARD_DOMAIN = "rum.log.discard.domain";
    public static final String RUM_LOG_DISCARD_ROBOT = "rum.log.discard.robot";
    public static final String RUM_LOG_SUCCESS = "rum.log.success";
    public static final String RUM_LOG_PAYLOAD = "rum.log.payload";


    /**
     * ios
     */
    public static final String RUM_IOS_TRACE_REQUEST = "rum.ios.trace.request";
    public static final String RUM_IOS_TRACE_DISCARD = "rum.ios.trace.discard";
    public static final String RUM_IOS_TRACE_SUCCESS = "rum.ios.trace.success";
    public static final String RUM_IOS_TRACE_PAYLOAD = "rum.ios.trace.payload";
    public static final String RUM_IOS_LOG_REQUEST = "rum.ios.log.request";
    public static final String RUM_IOS_LOG_DISCARD = "rum.ios.log.discard";
    public static final String RUM_IOS_LOG_SUCCESS = "rum.ios.log.success";
    public static final String RUM_IOS_LOG_PAYLOAD = "rum.ios.log.payload";


    /**
     * android
     */
    public static final String RUM_ANDROID_TRACE_REQUEST = "rum.android.trace.request";
    public static final String RUM_ANDROID_TRACE_DISCARD = "rum.android.trace.discard";
    public static final String RUM_ANDROID_TRACE_SUCCESS = "rum.android.trace.success";
    public static final String RUM_ANDROID_TRACE_PAYLOAD = "rum.android.trace.payload";
    public static final String RUM_ANDROID_LOG_REQUEST = "rum.android.log.request";
    public static final String RUM_ANDROID_LOG_DISCARD = "rum.android.log.discard";
    public static final String RUM_ANDROID_LOG_SUCCESS = "rum.android.log.success";
    public static final String RUM_ANDROID_LOG_PAYLOAD = "rum.android.log.payload";
    public static final String TAG_KEY_TOPIC = "topic";

    public static final Map<String, String> TRACE_TOPIC_TAGS = initTags(TAG_KEY_TOPIC, TRACE_TOPIC);
    public static final Map<String, String> TRACE_TO_FILL_TOPIC_TAGS = initTags(TAG_KEY_TOPIC, TRACE_TO_FILL_TOPIC);

    public static Map<String, String> initTags(String key, String value) {
        Map<String, String> tags = new HashMap<>();
        tags.put(key, value);
        return tags;
    }

}
