package com.databuff.dao.starrocks;

import com.databuff.entity.dto.TimeValue;
import com.databuff.entity.rum.web.BaseRumWebRequestSearchCriteria;
import com.databuff.entity.rum.web.RequestUVDto;
import com.databuff.entity.rum.web.RumWebRequestListSearchCriteria;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Mapper
@Repository
public interface RumWebRequestUVMapper {

    List<TimeValue> getRequestErrorUVTrend(
            @Param("appId") int appId,
            @Param("fromTime") Date fromTime,
            @Param("toTime") Date toTime,
            @Param("timeBucket") String timeBucket,
            @Param("criteria") BaseRumWebRequestSearchCriteria criteria,
            @Param("processedHttpUrls") List<String> processedHttpUrls
    );


    List<RequestUVDto> getRequestUVWithErrorUVForUrls(
            @Param("appId") int appId,
            @Param("fromTime") Date fromTime,
            @Param("toTime") Date toTime,
            @Param("criteria") RumWebRequestListSearchCriteria criteria,
            @Param("processedHttpUrls") List<String> processedHttpUrls
    );


    List<RequestUVDto> getRequestUVWithErrorUV(
            @Param("appId") int appId,
            @Param("fromTime") Date fromTime,
            @Param("toTime") Date toTime,
            @Param("criteria") RumWebRequestListSearchCriteria criteria,
            @Param("offset") int offset,
            @Param("limit") int limit
    );

    long countRequestUVWithErrorUV(
            @Param("appId") int appId,
            @Param("fromTime") Date fromTime,
            @Param("toTime") Date toTime,
            @Param("criteria") RumWebRequestListSearchCriteria criteria
    );

}
