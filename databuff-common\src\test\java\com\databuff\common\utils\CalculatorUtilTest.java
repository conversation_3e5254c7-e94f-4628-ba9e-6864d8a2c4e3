package com.databuff.common.utils;

import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * Test class for CalculatorUtil
 */
public class CalculatorUtilTest {

    /**
     * Helper method to create a test map with variable values
     */
    private Map<String, Map<Object, Double>> createTestValMap() {
        Map<String, Map<Object, Double>> valMap = new HashMap<>();

        // Create a map for variable A
        Map<Object, Double> aMap = new TreeMap<>();
        aMap.put("1", 10.0);
        aMap.put("2", 20.0);
        valMap.put("A", aMap);

        // Create a map for variable B
        Map<Object, Double> bMap = new TreeMap<>();
        bMap.put("1", 5.0);
        bMap.put("2", 10.0);
        valMap.put("B", bMap);

        return valMap;
    }

    /**
     * Test for expressions with the same variable on both sides of an operation
     */
    @Test
    public void testSameVariableOperations() {
        Map<String, Map<Object, Double>> valMap = createTestValMap();

        // Test A+A (should be 2*A)
        Map<Object, Double> result = CalculatorUtil.executeExprOfHistogram("A+A=", valMap);
        assertEquals(20.0, result.get("1"), 0.001, "A+A should equal 2*A for key 1");
        assertEquals(40.0, result.get("2"), 0.001, "A+A should equal 2*A for key 2");

        // Reset valMap for next test
        valMap = createTestValMap();

        // Test A-A (should be 0)
        result = CalculatorUtil.executeExprOfHistogram("A-A=", valMap);
        assertEquals(0.0, result.get("1"), 0.001, "A-A should equal 0 for key 1");
        assertEquals(0.0, result.get("2"), 0.001, "A-A should equal 0 for key 2");

        // Reset valMap for next test
        valMap = createTestValMap();

        // Test A*A (should be A^2)
        result = CalculatorUtil.executeExprOfHistogram("A*A=", valMap);
        assertEquals(100.0, result.get("1"), 0.001, "A*A should equal A^2 for key 1");
        assertEquals(400.0, result.get("2"), 0.001, "A*A should equal A^2 for key 2");

        // Reset valMap for next test
        valMap = createTestValMap();

        // Test A/A (should be 1)
        result = CalculatorUtil.executeExprOfHistogram("A/A=", valMap);
        assertEquals(1.0, result.get("1"), 0.001, "A/A should equal 1 for key 1");
        assertEquals(1.0, result.get("2"), 0.001, "A/A should equal 1 for key 2");
    }

    /**
     * Test for expressions with variable and number operations
     */
    @Test
    public void testVariableNumberOperations() {
        Map<String, Map<Object, Double>> valMap = createTestValMap();

        // Test A*10+A (should be 11*A)
        Map<Object, Double> result = CalculatorUtil.executeExprOfHistogram("A*10+A=", valMap);
        assertEquals(110.0, result.get("1"), 0.001, "A*10+A should equal 11*A for key 1");
        assertEquals(220.0, result.get("2"), 0.001, "A*10+A should equal 11*A for key 2");

        // Reset valMap for next test
        valMap = createTestValMap();

        // Test A*2+A (should be 3*A)
        result = CalculatorUtil.executeExprOfHistogram("A*2+A=", valMap);
        assertEquals(30.0, result.get("1"), 0.001, "A*2+A should equal 3*A for key 1");
        assertEquals(60.0, result.get("2"), 0.001, "A*2+A should equal 3*A for key 2");

        // Reset valMap for next test
        valMap = createTestValMap();

        // Test A*2-A (should be A)
        result = CalculatorUtil.executeExprOfHistogram("A*2-A=", valMap);
        assertEquals(10.0, result.get("1"), 0.001, "A*2-A should equal A for key 1");
        assertEquals(20.0, result.get("2"), 0.001, "A*2-A should equal A for key 2");
    }

    /**
     * Test for expressions with different variables
     */
    @Test
    public void testDifferentVariableOperations() {
        Map<String, Map<Object, Double>> valMap = createTestValMap();

        // Test A+B
        Map<Object, Double> result = CalculatorUtil.executeExprOfHistogram("A+B=", valMap);
        assertEquals(15.0, result.get("1"), 0.001, "A+B should equal 15 for key 1");
        assertEquals(30.0, result.get("2"), 0.001, "A+B should equal 30 for key 2");

        // Reset valMap for next test
        valMap = createTestValMap();

        // Test A-B
        result = CalculatorUtil.executeExprOfHistogram("A-B=", valMap);
        assertEquals(5.0, result.get("1"), 0.001, "A-B should equal 5 for key 1");
        assertEquals(10.0, result.get("2"), 0.001, "A-B should equal 10 for key 2");

        // Reset valMap for next test
        valMap = createTestValMap();

        // Test A*B
        result = CalculatorUtil.executeExprOfHistogram("A*B=", valMap);
        assertEquals(50.0, result.get("1"), 0.001, "A*B should equal 50 for key 1");
        assertEquals(200.0, result.get("2"), 0.001, "A*B should equal 200 for key 2");

        // Reset valMap for next test
        valMap = createTestValMap();

        // Test A/B
        result = CalculatorUtil.executeExprOfHistogram("A/B=", valMap);
        assertEquals(2.0, result.get("1"), 0.001, "A/B should equal 2 for key 1");
        assertEquals(2.0, result.get("2"), 0.001, "A/B should equal 2 for key 2");
    }

    /**
     * Test for complex expressions
     */
    @Test
    public void testComplexExpressions() {
        Map<String, Map<Object, Double>> valMap = createTestValMap();

        // Test (A+B)*2
        Map<Object, Double> result = CalculatorUtil.executeExprOfHistogram("(A+B)*2=", valMap);
        assertEquals(30.0, result.get("1"), 0.001, "(A+B)*2 should equal 30 for key 1");
        assertEquals(60.0, result.get("2"), 0.001, "(A+B)*2 should equal 60 for key 2");

        // Reset valMap for next test
        valMap = createTestValMap();

        // Test A*2+B*3
        result = CalculatorUtil.executeExprOfHistogram("A*2+B*3=", valMap);
        assertEquals(35.0, result.get("1"), 0.001, "A*2+B*3 should equal 35 for key 1");
        assertEquals(70.0, result.get("2"), 0.001, "A*2+B*3 should equal 70 for key 2");
    }

    /**
     * 测试修复的问题：变量值在计算过程中被覆盖导致的错误
     */
    @Test
    public void testFixedBugs() {
        Map<String, Map<Object, Double>> valMap = createTestValMap();

        // 测试 A*10+A，之前错误结果是 A*10*2，现在应该是 A*10+A = 11*A
        Map<Object, Double> result = CalculatorUtil.executeExprOfHistogram("A*10+A=", valMap);
        assertEquals(110.0, result.get("1"), 0.001, "A*10+A should equal 11*A for key 1");
        assertEquals(220.0, result.get("2"), 0.001, "A*10+A should equal 11*A for key 2");

        // Reset valMap for next test
        valMap = createTestValMap();

        // 测试 A*2+A，之前错误结果是 A*2*2，现在应该是 A*2+A = 3*A
        result = CalculatorUtil.executeExprOfHistogram("A*2+A=", valMap);
        assertEquals(30.0, result.get("1"), 0.001, "A*2+A should equal 3*A for key 1");
        assertEquals(60.0, result.get("2"), 0.001, "A*2+A should equal 3*A for key 2");

        // Reset valMap for next test
        valMap = createTestValMap();

        // 测试 A*2-A，之前错误结果是 A*2-A*2=0，现在应该是 A*2-A = A
        result = CalculatorUtil.executeExprOfHistogram("A*2-A=", valMap);
        assertEquals(10.0, result.get("1"), 0.001, "A*2-A should equal A for key 1");
        assertEquals(20.0, result.get("2"), 0.001, "A*2-A should equal A for key 2");
    }

    /**
     * 测试更复杂的表达式
     */
    @Test
    public void testMoreComplexExpressions() {
        Map<String, Map<Object, Double>> valMap = createTestValMap();

        // 测试 10*A+A = 11*A
        Map<Object, Double> result = CalculatorUtil.executeExprOfHistogram("10*A+A=", valMap);
        assertEquals(110.0, result.get("1"), 0.001, "10*A+A should equal 11*A for key 1");
        assertEquals(220.0, result.get("2"), 0.001, "10*A+A should equal 11*A for key 2");

        // Reset valMap for next test
        valMap = createTestValMap();

        // 测试 1+10*A+10+A = 1+10*A+10+A = 11+11*A
        result = CalculatorUtil.executeExprOfHistogram("1+10*A+10+A=", valMap);
        assertEquals(121.0, result.get("1"), 0.001, "1+10*A+10+A should equal 11+11*A for key 1");
        assertEquals(231.0, result.get("2"), 0.001, "1+10*A+10+A should equal 11+11*A for key 2");

        // Reset valMap for next test
        valMap = createTestValMap();

        // 测试 1+10*A+10+2*A = 11+12*A
        result = CalculatorUtil.executeExprOfHistogram("1+10*A+10+2*A=", valMap);
        assertEquals(131.0, result.get("1"), 0.001, "1+10*A+10+2*A should equal 11+12*A for key 1");
        assertEquals(251.0, result.get("2"), 0.001, "1+10*A+10+2*A should equal 11+12*A for key 2");

        // Reset valMap for next test
        valMap = createTestValMap();

        // 测试带括号的复杂表达式 (A+B)*2+A = 2*(A+B)+A = 3*A+2*B
        result = CalculatorUtil.executeExprOfHistogram("(A+B)*2+A=", valMap);
        assertEquals(40.0, result.get("1"), 0.001, "(A+B)*2+A should equal 3*A+2*B for key 1");
        assertEquals(80.0, result.get("2"), 0.001, "(A+B)*2+A should equal 3*A+2*B for key 2");

        // Reset valMap for next test
        valMap = createTestValMap();

        // 测试带括号的复杂表达式 A*(B+2)+B = A*B+2*A+B = A*B+B+2*A
        result = CalculatorUtil.executeExprOfHistogram("A*(B+2)+B=", valMap);
        assertEquals(75.0, result.get("1"), 0.001, "A*(B+2)+B should equal A*B+2*A+B for key 1");
        assertEquals(250.0, result.get("2"), 0.001, "A*(B+2)+B should equal A*B+2*A+B for key 2");
    }

    /**
     * 测试更多复杂的表达式场景
     */
    @Test
    public void testAdvancedComplexExpressions() {
        Map<String, Map<Object, Double>> valMap = createTestValMap();

        // Case 1: A加减乘除B加减乘除常数
        // 测试 A+B*5 = A+5*B
        Map<Object, Double> result = CalculatorUtil.executeExprOfHistogram("A+B*5=", valMap);
        assertEquals(35.0, result.get("1"), 0.001, "A+B*5 should equal A+5*B for key 1");
        assertEquals(70.0, result.get("2"), 0.001, "A+B*5 should equal A+5*B for key 2");

        // Reset valMap for next test
        valMap = createTestValMap();

        // 测试 A-B/2 = A-B/2
        result = CalculatorUtil.executeExprOfHistogram("A-B/2=", valMap);
        assertEquals(7.5, result.get("1"), 0.001, "A-B/2 should equal A-B/2 for key 1");
        assertEquals(15.0, result.get("2"), 0.001, "A-B/2 should equal A-B/2 for key 2");

        // Reset valMap for next test
        valMap = createTestValMap();

        // 测试 A*B+3 = A*B+3
        result = CalculatorUtil.executeExprOfHistogram("A*B+3=", valMap);
        assertEquals(53.0, result.get("1"), 0.001, "A*B+3 should equal A*B+3 for key 1");
        assertEquals(203.0, result.get("2"), 0.001, "A*B+3 should equal A*B+3 for key 2");

        // Reset valMap for next test
        valMap = createTestValMap();

        // Case 2: A加减乘除B加减乘除常数 加减乘除 A加减乘除B加减乘除
        // 测试 (A+B*2)+(A*B+5) = A+2*B+A*B+5 = 2*A+2*B+A*B+5
        result = CalculatorUtil.executeExprOfHistogram("(A+B*2)+(A*B+5)=", valMap);
        assertEquals(75, result.get("1"), 0.001, "(A+B*2)+(A*B+5) should equal 2*A+2*B+A*B+5 for key 1");
        assertEquals(245, result.get("2"), 0.001, "(A+B*2)+(A*B+5) should equal 2*A+2*B+A*B+5 for key 2");

        // Reset valMap for next test
        valMap = createTestValMap();

        // 测试 (A-B/2)*(A+B*3) = (A-B/2)*(A+3*B)
        result = CalculatorUtil.executeExprOfHistogram("(A-B/2)*(A+B*3)=", valMap);
        assertEquals(187.5, result.get("1"), 0.001, "(A-B/2)*(A+B*3) should equal (A-B/2)*(A+3*B) for key 1");
        assertEquals(750.0, result.get("2"), 0.001, "(A-B/2)*(A+B*3) should equal (A-B/2)*(A+3*B) for key 2");

        // Reset valMap for next test
        valMap = createTestValMap();

        // Case 3: 常数加减乘除常数 +- A加减乘除B加减乘除常数
        // 测试 5+3+(A*B+2) = 8+A*B+2 = 10+A*B
        result = CalculatorUtil.executeExprOfHistogram("5+3+(A*B+2)=", valMap);
        assertEquals(60.0, result.get("1"), 0.001, "5+3+(A*B+2) should equal 10+A*B for key 1");
        assertEquals(210.0, result.get("2"), 0.001, "5+3+(A*B+2) should equal 10+A*B for key 2");

        // Reset valMap for next test
        valMap = createTestValMap();

        // 测试 10-2-(A+B/4) = 8-(A+B/4) = 8-A-B/4
        result = CalculatorUtil.executeExprOfHistogram("10-2-(A+B/4)=", valMap);
        assertEquals(-3.25, result.get("1"), 0.001, "10-2-(A+B/4) should equal 8-A-B/4 for key 1");
        assertEquals(-14.5, result.get("2"), 0.001, "10-2-(A+B/4) should equal 8-A-B/4 for key 2");

        // Reset valMap for next test
        valMap = createTestValMap();

        // 测试 2*4+A*B/2 = 8+A*B/2
        result = CalculatorUtil.executeExprOfHistogram("2*4+A*B/2=", valMap);
        assertEquals(33.0, result.get("1"), 0.001, "2*4+A*B/2 should equal 8+A*B/2 for key 1");
        assertEquals(108.0, result.get("2"), 0.001, "2*4+A*B/2 should equal 8+A*B/2 for key 2");
    }
}
