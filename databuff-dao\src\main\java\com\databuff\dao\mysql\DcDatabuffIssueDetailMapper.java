package com.databuff.dao.mysql;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.databuff.entity.DcDatabuffIssueDetail;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

@Mapper
public interface DcDatabuffIssueDetailMapper extends BaseMapper<DcDatabuffIssueDetail> {

    @Insert("INSERT INTO dc_databuff_issue_detail(id, root_cause_node, root_cause_type, status, start_time, end_time, create_time, update_time, root_analyse, gid) " +
            "VALUES(#{id}, #{rootCauseNode}, #{rootCauseType}, #{status}, #{startTime}, #{endTime}, #{createTime}, #{updateTime}, #{rootAnalyse}, #{gid})")
    int insert(DcDatabuffIssueDetail detail);

    @Insert("INSERT INTO dc_databuff_issue_detail(id, root_cause_node, root_cause_type, source, status, start_time, end_time, create_time, update_time, root_analyse, problemId, gid) " +
            "VALUES(#{id}, #{rootCauseNode}, #{rootCauseType}, #{source}, #{status}, #{startTime}, #{endTime}, #{createTime}, #{updateTime}, #{rootAnalyse}, #{problemId}, #{gid}) " +
            "ON DUPLICATE KEY UPDATE " +
            "root_cause_node = VALUES(root_cause_node), " +
            "root_cause_type = VALUES(root_cause_type), " +
            "source = VALUES(source), " +
            "status = VALUES(status), " +
            "start_time = VALUES(start_time), " +
            "end_time = VALUES(end_time), " +
            "create_time = VALUES(create_time), " +
            "update_time = VALUES(update_time), " +
            "root_analyse = VALUES(root_analyse), " +
            "problemId = VALUES(problemId)")
    int upsert(DcDatabuffIssueDetail issueDetail);

    @Update({
            "<script>",
            "UPDATE dc_databuff_issue_detail",
            "<set>",
            "<if test='rootCauseNode != null'>root_cause_node = #{rootCauseNode},</if>",
            "<if test='rootCauseType != null'>root_cause_type = #{rootCauseType},</if>",
            "<if test='status != null'>status = #{status},</if>",
            "<if test='startTime != null'>start_time = #{startTime},</if>",
            "<if test='endTime != null'>end_time = #{endTime},</if>",
            "<if test='updateTime != null'>update_time = #{updateTime},</if>",
            "<if test='rootAnalyse != null'>root_analyse = #{rootAnalyse},</if>",
            "<if test='problemId != null'>problemId = #{problemId},</if>",
            "</set>",
            "WHERE id = #{id}",
            "</script>"
    })
    int update(DcDatabuffIssueDetail detail);

    @Select("SELECT EXISTS(SELECT 1 FROM dc_databuff_issue_detail d WHERE id = #{id} FOR UPDATE)")
    boolean lockById(String id);

    @Select("SELECT EXISTS(SELECT 1 FROM dc_databuff_issue_detail d WHERE id = #{id})")
    boolean exist(String id);

    @Delete("DELETE FROM dc_databuff_issue_detail WHERE id = #{id}")
    int deleteById(String id);

    @Select("SELECT d.`id`,d.`root_cause_node`,d.`root_cause_type`,d.`status`,d.`start_time`,d.`end_time`,d.`create_time`,d.`update_time`, d.`root_analyse`,p.suggest,p.suggestStatus FROM dc_databuff_issue_detail d left join dc_databuff_problem p on d.problemId=p.id WHERE d.id = #{id}")
    DcDatabuffIssueDetail findById(String id);

    @Select("SELECT d.`id`,d.`start_time`,d.`end_time`,d.`problemId`,d.`create_time`,d.`update_time`, d.`root_analyse` FROM dc_databuff_issue_detail d WHERE id = #{id} AND root_analyse IS NOT NULL")
    @Results({
            @Result(property = "rootAnalyse", column = "root_analyse", javaType = JSONArray.class)
    })
    DcDatabuffIssueDetail findExistRootIdById(String id);

    @Select("SELECT * FROM dc_databuff_issue_detail d")
    List<DcDatabuffIssueDetail> findAll();

    @Select({
            "<script>",
            "SELECT d.id, d.root_cause_node, d.root_cause_type, d.status, d.start_time, d.end_time, d.create_time, d.update_time ",
            "FROM dc_databuff_issue_detail d",
            "<where>",
            "<if test='ew != null'>",
            "${ew.sqlSegment}",
            "</if>",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "rootCauseNode", column = "root_cause_node"),
            @Result(property = "rootCauseType", column = "root_cause_type"),
            @Result(property = "status", column = "status"),
            @Result(property = "startTime", column = "start_time"),
            @Result(property = "endTime", column = "end_time"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time"),
            @Result(property = "services", column = "service", javaType = List.class, many = @Many(select = "selectServicesByIssueId"))
    })
    List<DcDatabuffIssueDetail> searchIssues(@Param("ew") QueryWrapper<DcDatabuffIssueDetail> queryWrapper);

    @Select({
            "<script>",
            "SELECT d.root_cause_node as rootCauseNode, COUNT(*) as cnt",
            "FROM dc_databuff_issue_detail d",
            "LEFT JOIN dc_databuff_issue_service s ON d.id = s.issue_id",
            "<where>",
            "<if test='ew != null'>",
            "${ew.sqlSegment}",
            "</if>",
            "</where>",
            "GROUP BY d.root_cause_node",
            "ORDER BY cnt DESC",
            "LIMIT #{topN}",
            "</script>"
    })
    List<Map<String, Object>> chartIssuesByRootCauseNode(@Param("ew") QueryWrapper<DcDatabuffIssueDetail> queryWrapper, @Param("topN") int topN);

    @Select({
            "<script>",
            "SELECT d.root_cause_type as rootCauseType, COUNT(*) as cnt",
            "FROM dc_databuff_issue_detail d",
            "LEFT JOIN dc_databuff_issue_service s ON d.id = s.issue_id",
            "<where>",
            "<if test='ew != null'>",
            "${ew.sqlSegment}",
            "</if>",
            "</where>",
            "GROUP BY d.root_cause_type",
            "ORDER BY cnt DESC",
            "LIMIT #{topN}",
            "</script>"
    })
    List<Map<String, Object>> chartIssuesByRootCauseType(@Param("ew") QueryWrapper<DcDatabuffIssueDetail> queryWrapper, @Param("topN") int topN);


    @Select({
            "<script>",
            "SELECT ${ew.sqlSelect}",
            "FROM dc_databuff_issue_detail d",
            "LEFT JOIN dc_databuff_issue_service s ON d.id = s.issue_id",
            "<where>",
            "<if test='ew != null'>",
            "${ew.sqlSegment}",
            "</if>",
            "</where>",
            "</script>"
    })
    List<Object> selectObjs(@Param("ew") QueryWrapper<DcDatabuffIssueDetail> queryWrapper);
}