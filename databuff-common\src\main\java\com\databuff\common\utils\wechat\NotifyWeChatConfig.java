package com.databuff.common.utils.wechat;

import lombok.Data;

import java.io.Serializable;

@Data
public class NotifyWeChatConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;

    /**
     * api_key
     */
    private String apiKey;

    /**
     * 是否开启，默认1开启，0不开启
     */
    private Integer tenantEnable;
    /**
     * 机器人通知是否开启，默认1开启，0不开启
     */
    private Integer robotEnable;
    /**
     * 是否开启，默认1开启，0不开启
     */
    private Integer enable;

    /**
     * dingtalk，wechat，sms，mail
     */
    private String notifyType;
    /**
     * 微信企业ID
     */
    private String corpid;

    /**
     * 微信企业密匙
     */
    private String corpsecret;
    /**
     * 微信应用ID
     */
    private Long wechatAgentId ;
    /**
     * 微信群机器人webhook调用地址
     */
    private String wechatWebhook;

    //总发送条数
    private Long totalNum;

    //当月发送条数
    private Long theMonthNum;
}
