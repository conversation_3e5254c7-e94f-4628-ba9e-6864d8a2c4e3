package com.databuff.common.tsdb.builder;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.tsdb.model.AggFun;
import com.databuff.common.tsdb.model.Aggregation;
import com.databuff.common.tsdb.model.OrderBy;
import com.databuff.common.tsdb.model.Where;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public abstract class AbstractQueryBuilder {
    protected QueryBuilder queryBuilder;

    public AbstractQueryBuilder(QueryBuilder queryBuilder) {
        this.queryBuilder = queryBuilder;
    }

    public String buildSelect(AggFun tsAgg, AggFun valAgg, AggFun topAgg, List<String> parentGroupBy){
        StringBuilder select = new StringBuilder("SELECT ");
        // 通用的查询构建逻辑
        appendAggregations(select);
        return select.toString() ;
    }

    public String buildWhere(){
        StringBuilder where = new StringBuilder(" WHERE ");
        if (queryBuilder.getWheres() != null && !queryBuilder.getWheres().isEmpty()) {
            appendSpecificWheres(where);
        }
        if (StringUtils.isBlank(where.toString().replace(" WHERE ", "").trim())) {
            return "";
        }
        return where.toString();
    }

    public String buildShowTagValueQuery() {
        //检验
        queryBuilder.validate();
        StringBuilder query = new StringBuilder("SELECT ");
        // 通用的查询构建逻辑
        appendAggregations(query);
        // 添加测量值
        appendMeasurement(query);
        // 添加 WHERE
        query.append(buildWhere());
        // 添加 GROUP BY
        appendGroupBy(query);
        // 时间间隔
        appendSpecificInterval(query);
        // ORDER BY
        appendOrderBy(query);
        // LIMIT / OFFSET
        appendLimitAndOffset(query);
        return query.toString();
    }

    public Map<String, String> buildShowTagValueQueryMap() {
        //检验
        queryBuilder.validate();
        Map<String, String> sqlMap = new HashMap<>();
        List<String> groupBy = queryBuilder.getGroupBy();
        if (groupBy == null || groupBy.isEmpty()) {
            return sqlMap;
        }
        for (String by : groupBy) {
            StringBuilder query = new StringBuilder("SELECT ");
            // 通用的查询构建逻辑
            appendAggregations(query);
            // 添加测量值
            appendMeasurement(query);
            // 添加 WHERE
            query.append(buildWhere());
            // 添加 GROUP BY
            query.append(" GROUP BY ").append("\"" + by.trim() + "\"");
            // 时间间隔
            appendSpecificInterval(query);
            // ORDER BY
            appendOrderBy(query);
            // LIMIT / OFFSET
            appendLimitAndOffset(query);
            sqlMap.put(by, query.toString());
        }
        return sqlMap;
    }

    public String buildQuery() {
        //检验
        queryBuilder.validate();
        StringBuilder query = new StringBuilder("SELECT ");
        // 通用的查询构建逻辑
        appendAggregations(query);
        // 添加测量值
        appendMeasurement(query);
        // 添加 WHERE
        query.append(buildWhere());
        // 添加 GROUP BY
        appendGroupBy(query);
        // 时间间隔
        appendSpecificInterval(query);
        // ORDER BY
        appendOrderBy(query);
        // LIMIT / OFFSET
        appendLimitAndOffset(query);
        return query.toString();
    }

    // 添加特殊处理函数
    public StringBuilder buildDiffAggSpecificSelect(AggFun tsAgg, AggFun valAgg, AggFun topAgg, JSONObject otherParam) {
        StringBuilder query = new StringBuilder("SELECT ");

        if (queryBuilder.getAggregations() == null || queryBuilder.getAggregations().isEmpty()) {
            query.append("*");
            return query;
        }
        appendDatabaseSpecificLogic(query,tsAgg, valAgg, topAgg, otherParam);
        return query;
    }

    // 子类实现此方法来添加数据库特定的逻辑
    public abstract void appendDatabaseSpecificLogic(StringBuilder query,AggFun tsAgg, AggFun valAgg, AggFun topAgg, JSONObject otherParam);
    public abstract String getSpecificAggregationFunction(Aggregation aggregation);
    // 添加 WHERE 条件
    public abstract void  appendSpecificWheres(StringBuilder query);

    // 添加时间间隔（如时间窗）
    public abstract void  appendSpecificInterval(StringBuilder query);

    // 添加聚合函数
    public void appendAggregations(StringBuilder query) {
        if (queryBuilder.getAggregations() == null || queryBuilder.getAggregations().isEmpty()) {
            query.append("*");
            return;
        }
        query.append(queryBuilder.getAggregations().stream()
                .map(this::getSpecificAggregationFunction)
                .filter(s -> s != null)
                .collect(Collectors.joining(", ")));
    }

    // 添加测量值
    public void appendMeasurement(StringBuilder query) {
        if (StringUtils.isBlank(queryBuilder.getMeasurement())) {
            throw new IllegalStateException("Measurement 不能为空");
        }
        query.append(" FROM \"").append(queryBuilder.getMeasurement()).append("\"");
    }

    // 添加GROUP BY
    public void appendGroupBy(StringBuilder query) {
        List<String> groupBy = queryBuilder.getGroupBy();
        if (groupBy != null && !groupBy.isEmpty()) {
            query.append(" GROUP BY ")
                    .append(groupBy.stream()
                            .map(field -> "\"" + field.trim() + "\"")  // 去掉前后空格并加双引号
                            .collect(Collectors.joining(", ")));  // 用逗号连接
        }
    }
    // 添加ORDER BY
    public void appendOrderBy(StringBuilder query) {
        List<OrderBy> orderBy = queryBuilder.getOrderBy();
        if (orderBy != null && !orderBy.isEmpty()) {
            query.append(" ORDER BY ");
            query.append(queryBuilder.getOrderBy().stream()
                    .map(order -> "\"" + order.getField().trim() + "\" " + (order.isAsc() ? "ASC" : "DESC"))
                    .collect(Collectors.joining(", ")));
        }
    }

    // 添加LIMIT 和 OFFSET
    public void appendLimitAndOffset(StringBuilder query) {
        if (queryBuilder.getLimit() != null && queryBuilder.getLimit() > 0) {
            query.append(" LIMIT ").append(queryBuilder.getLimit());
        }
        if (queryBuilder.getOffset() != null && queryBuilder.getOffset() > 0) {
            query.append(" OFFSET ").append(queryBuilder.getOffset());
        }
    }

    /**
     * 在查询中添加时间范围条件
     * 提取查询构建器中的时间条件，并将其添加到SQL查询中
     *
     * @param sql 要添加时间条件的SQL查询字符串构建器
     * @param builder 查询构建器，包含时间条件
     */
    public static void appendTimeRangeConditions(StringBuilder sql, QueryBuilder builder) {
        // 提取时间范围条件
        List<Where> timeConditions = new ArrayList<>();
        for (Where where : builder.getWheres()) {
            if (where.getSubConditions() != null && !where.getSubConditions().isEmpty()) {
                for (Where subWhere : where.getSubConditions()) {
                    if ("time".equals(subWhere.getField())) {
                        timeConditions.add(subWhere);
                    }
                }
            } else if ("time".equals(where.getField())) {
                timeConditions.add(where);
            }
        }

        if (!timeConditions.isEmpty()) {
            sql.append(" WHERE ");
            boolean first = true;

            for (Where where : timeConditions) {
                if (!first) {
                    sql.append(" AND ");
                }

                sql.append("time ");

                switch (where.getOperator()) {
                    case EQ:
                        sql.append("= ");
                        break;
                    case NEQ:
                        sql.append("!= ");
                        break;
                    case GT:
                        sql.append("> ");
                        break;
                    case GTE:
                        sql.append(">= ");
                        break;
                    case LT:
                        sql.append("< ");
                        break;
                    case LTE:
                        sql.append("<= ");
                        break;
                    default:
                        throw new IllegalArgumentException("Unsupported operator for time condition: " + where.getOperator());
                }

                // 处理时间值，确保是纳秒级别
                Object value = where.getValue();
                if (value instanceof Long) {
                    long timeValue = (Long) value;
                    // 如果时间值小于10^15，则认为是毫秒，需要转换为纳秒
                    if (timeValue < 1_000_000_000_000_000L) {
                        timeValue = timeValue * 1_000_000; // 毫秒转纳秒
                    }
                    sql.append(timeValue);
                } else {
                    sql.append(value);
                }

                first = false;
            }
        }
    }
}

