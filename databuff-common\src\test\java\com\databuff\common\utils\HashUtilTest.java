package com.databuff.common.utils;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 *     private static final int iterations = 5000000;
 *     private static final int stringLength = 500;
 * <p>
 *    不同大小string表现不同
 * md5 Hash64 byte[] performance test took 2772 ms for 5000000 iterations
 * CityHash128 performance test took 670 ms for 5000000 iterations
 * md5 Hash64 performance test took 2821 ms for 5000000 iterations
 * CityHash128 byte[] performance test took 506 ms for 5000000 iterations
 * CityHash64 byte[] performance test took 646 ms for 5000000 iterations
 * CityHash64 performance test took 707 ms for 5000000 iterations
 * SipHash64 performance test took 957 ms for 5000000 iterations
 * Murmur3_128 performance test took 930 ms for 5000000 iterations
 * XXH3Long performance test took 1101 ms for 5000000 iterations
 * XXH3Long byte performance test took 429 ms for 5000000 iterations
 * <AUTHOR>
 * @date 2025/03/09
 */
public class HashUtilTest {

    private static final int ITERATIONS = 5000000;
    private static final int STRING_LENGTH = 500;
    private static List<String> dataList = null;
    private static List<byte[]> dataByteList = null;


    @BeforeAll
    public static void init(){
        // 在计时前预生成所有测试数据
        System.out.println("HashTest");
        dataList = generateRandomData(ITERATIONS);
        dataByteList = generateRandomByteData(ITERATIONS);
    }




    // 正确性验证
    @Test
    public void testCityHash64Correctness() {
        String input = "test";
        byte[] bytes = input.getBytes(StandardCharsets.UTF_8);
        long expectedHash = -742803963401426510L; // 预先计算的哈希值
        long actualHash = HashUtil.cityHash64(bytes);
        assertEquals(expectedHash, actualHash, "CityHash64 correctness test failed");
    }

    @Test
    public void testSipHash64Correctness() {
        String input = "test";
        long expectedHash = 2771506328522617498L; // 预先计算的哈希值
        long actualHash = HashUtil.sipHash64(input);
        assertEquals(expectedHash, actualHash, "CityHash64 correctness test failed");
    }

    @Test
    public void testMurmur3_128HashCorrectness() {
        String input = "test";
        String expectedHash = "9de1bd74cc287dac824dbdf93182129a"; // 预先计算的哈希值
        String actualHash = HashUtil.murmur3_128Hash(input);
        assertEquals(expectedHash, actualHash, "Murmur3_128 correctness test failed");
    }

    // 性能测试 Murmur3_128
    @Test
    public void testMurmur3_128Performance() {
        // 开始计时，只测试哈希计算
        long startTime = System.nanoTime();
        for (String data : dataList) {
            HashUtil.murmur3_128Hash(data);
        }
        long endTime = System.nanoTime();
        Assertions.assertTrue(dataList.size()>1);

        // 输出结果
        long duration = (endTime - startTime) / 1000000; // 转换为毫秒
        System.out.println("Murmur3_128 performance test took " + duration + " ms for " + ITERATIONS + " iterations");
    }

    // 性能测试 SipHash64
    @Test
    public void testSipHash64Performance() {
        // 开始计时，只测试哈希计算
        long startTime = System.nanoTime();
        for (String data : dataList) {
            HashUtil.sipHash64(data);
        }
        long endTime = System.nanoTime();
        Assertions.assertTrue(dataList.size()>1);

        // 输出结果
        long duration = (endTime - startTime) / 1000000; // 转换为毫秒
        System.out.println("SipHash64 performance test took " + duration + " ms for " + ITERATIONS + " iterations");
    }

    @Test
    public void testCityHash64StringCorrectness() {
        String input = "test";
        long expectedHash = -742803963401426510L; // 预先计算的哈希值
        long actualHash = HashUtil.cityHash64(input);
        assertEquals(expectedHash, actualHash, "CityHash64 correctness test failed");
    }

    @Test
    public void testCityHash128Correctness() {
        String input = "test";
        byte[] bytes = input.getBytes(StandardCharsets.UTF_8);
        long[] expectedHash = new long[]{ -446027196093499767L, -7866015623825524919L }; // 预先计算的哈希值
        long[] actualHash = HashUtil.cityHash128(bytes, 0, bytes.length);
        assertEquals(expectedHash[0], actualHash[0], "CityHash128 correctness test failed for first part");
        assertEquals(expectedHash[1], actualHash[1], "CityHash128 correctness test failed for second part");
    }
    // 性能测试 CityHash64
    @Test
    public void testCityHash64Performance() {
        // 开始计时，只测试哈希计算
        long startTime = System.nanoTime();
        for (String data : dataList) {
            HashUtil.cityHash64(data);
        }
        long endTime = System.nanoTime();
        Assertions.assertTrue(dataList.size()>1);

        // 输出结果
        long duration = (endTime - startTime) / 1000000; // 转换为毫秒
        System.out.println("CityHash64 performance test took " + duration + " ms for " + ITERATIONS + " iterations");
    }

    @Test
    public void testCityHash64BytePerformance() {
        // 开始计时，只测试哈希计算
        long startTime = System.nanoTime();
        for (byte[] data : dataByteList) {
            HashUtil.cityHash64(data);
        }
        long endTime = System.nanoTime();
        Assertions.assertTrue(dataList.size()>1);

        // 输出结果
        long duration = (endTime - startTime) / 1000000; // 转换为毫秒
        System.out.println("CityHash64 byte[] performance test took " + duration + " ms for " + ITERATIONS + " iterations");
    }

    @Test
    public void testMd5Performance() {
        // 开始计时，只测试哈希计算
        long startTime = System.nanoTime();
        for (String data : dataList) {
            HashUtil.getMD5Long(data);
        }
        long endTime = System.nanoTime();
        Assertions.assertTrue(dataList.size()>1);

        // 输出结果
        long duration = (endTime - startTime) / 1000000; // 转换为毫秒
        System.out.println("md5 Hash64 performance test took " + duration + " ms for " + ITERATIONS + " iterations");
    }

    @Test
    public void testMd5BytePerformance() {
        // 开始计时，只测试哈希计算
        long startTime = System.nanoTime();
        for (byte[] data : dataByteList) {
            HashUtil.getMD5Long(data);
        }
        long endTime = System.nanoTime();
        Assertions.assertTrue(dataList.size()>1);

        // 输出结果
        long duration = (endTime - startTime) / 1000000; // 转换为毫秒
        System.out.println("md5 Hash64 byte[] performance test took " + duration + " ms for " + ITERATIONS + " iterations");
    }

    // 性能测试 CityHash128
    @Test
    public void testCityHash128Performance() {
        // 开始计时，只测试哈希计算
        long startTime = System.nanoTime();
        for (String data : dataList) {
            HashUtil.cityHash128(data);
        }
        long endTime = System.nanoTime();
        Assertions.assertTrue(dataList.size()>1);

        // 输出结果
        long duration = (endTime - startTime) / 1000000; // 转换为毫秒
        System.out.println("CityHash128 performance test took " + duration + " ms for " + ITERATIONS + " iterations");
    }

    // 性能测试 CityHash128
    @Test
    public void testCityHash128BytePerformance() {
        // 开始计时，只测试哈希计算
        long startTime = System.nanoTime();
        for (byte[] data : dataByteList) {
            HashUtil.cityHash128(data);
        }
        long endTime = System.nanoTime();
        Assertions.assertTrue(dataList.size()>1);

        // 输出结果
        long duration = (endTime - startTime) / 1000000; // 转换为毫秒
        System.out.println("CityHash128 byte[] performance test took " + duration + " ms for " + ITERATIONS + " iterations");
    }

    // 生成随机数据
    private static List<String> generateRandomData(int count) {
        List<String> dataList = new ArrayList<>(count);
        Random random = new Random();
        for (int i = 0; i < count; i++) {
            int length = random.nextInt(STRING_LENGTH); // 随机长度
            String input = generateRandomString(length);
            dataList.add(input);
        }
        return dataList;
    }

    private static List<byte[]> generateRandomByteData(int count) {
        List<byte[]> dataList = new ArrayList<>(count);
        Random random = new Random();
        for (int i = 0; i < count; i++) {
            int length = random.nextInt(STRING_LENGTH); // 随机长度
            String input = generateRandomString(length);
            dataList.add(input.getBytes());
        }
        return dataList;
    }

    // 生成随机字符串
    private static String generateRandomString(int length) {
        StringBuilder sb = new StringBuilder(length);
        Random random = new Random();
        for (int i = 0; i < length; i++) {
            sb.append((char) (random.nextInt(26) + 'a')); // 生成小写字母
        }
        return sb.toString();
    }


}