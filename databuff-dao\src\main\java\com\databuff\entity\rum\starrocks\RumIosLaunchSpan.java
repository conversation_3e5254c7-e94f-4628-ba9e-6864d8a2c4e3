package com.databuff.entity.rum.starrocks;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("dc_rum_ios_launch_span")
public class RumIosLaunchSpan extends BaseRumIosSpan {
    @ApiModelProperty(value = "启动id")
    @JSONField(name = "launch_id")
    private Long launchId;
}
