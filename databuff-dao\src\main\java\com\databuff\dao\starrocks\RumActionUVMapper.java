package com.databuff.dao.starrocks;

import com.databuff.entity.rum.web.ActionUVDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Mapper
@Repository
public interface RumActionUVMapper {

    List<ActionUVDto> getUVForApps(
            @Param("fromTime") Date fromTime,
            @Param("toTime") Date toTime
    );

    List<ActionUVDto> getUVForAppAction(
            @Param("appId") Integer appId,
            @Param("actionList") List<String> actionList,
            @Param("fromTime") Date fromTime,
            @Param("toTime") Date toTime
    );
}
