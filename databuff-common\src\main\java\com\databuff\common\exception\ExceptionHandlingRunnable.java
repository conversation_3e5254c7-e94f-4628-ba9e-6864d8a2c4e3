package com.databuff.common.exception;

import com.databuff.common.utils.OtelMetricUtil;

public class ExceptionHandlingRunnable implements Runnable {

    private Runnable delegate;

    public ExceptionHandlingRunnable(Runnable delegate) {
        this.delegate = delegate;
    }

    @Override
    public void run() {
        try {
            delegate.run();
        } catch (Exception e) {
            OtelMetricUtil.logException(delegate.getClass().getName(), e);
        }
    }
}