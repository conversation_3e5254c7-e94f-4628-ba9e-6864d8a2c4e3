package com.databuff.dao.starrocks;

import com.databuff.entity.dto.DCEventDto;
import com.databuff.handler.MultiDetectQueryRequestHandler;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@Mapper
@Repository
public interface DcEventMapper {

    /**
     * 根据告警ID查询事件列表
     *
     * @param alertId 告警ID
     * @return 事件DTO列表
     */
    @Select("<script>" +
            "SELECT dc_event.* FROM dc_event JOIN " +
            "(SELECT id, unnest FROM dc_alarm, unnest(eventId) AS unnest WHERE id=#{alertId}) a " +
            "ON dc_event.id = a.unnest " +
            "<where>" +
            "  <if test='createDts != null and createDts.size() > 0'>" +
            "    AND dc_event.createDt IN " +
            "    <foreach collection='createDts' item='dt' open='(' separator=',' close=')'>" +
            "      #{dt}" +
            "    </foreach>" +
            "  </if>" +
            "</where>" +
            "ORDER BY triggerTime DESC" +
            "</script>")
    @Results(id = "DCEventDtoResultMap", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "apiKey", column = "apiKey"),
            @Result(property = "monitorId", column = "monitorId"),
            @Result(property = "value", column = "value"),
            @Result(property = "duration", column = "duration"),
            @Result(property = "silence", column = "silence"),
            @Result(property = "level", column = "level"),
            @Result(property = "read", column = "read"),
            @Result(property = "triggerTime", column = "triggerTime"),
            @Result(property = "createTime", column = "createTime"),
            @Result(property = "creatorId", column = "creatorId"),
            @Result(property = "editorId", column = "editorId"),
            @Result(property = "source", column = "source"),
            @Result(property = "classification", column = "classification"),
            @Result(property = "type", column = "type"),
            @Result(property = "trgTrd", column = "trgTrd"),
            @Result(property = "triggerObjType", column = "triggerObjType"),
            @Result(property = "group", column = "`group`"),
            @Result(property = "ruleName", column = "ruleName"),
            @Result(property = "message", column = "message"),
            @Result(property = "eventStatus", column = "eventStatus"),
            @Result(property = "trigger", column = "trigger", typeHandler = com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler.class),
            @Result(property = "tags", column = "tags", typeHandler = com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler.class),
            @Result(property = "query", column = "query", typeHandler = MultiDetectQueryRequestHandler.class),
            @Result(property = "thresholds", column = "thresholds", typeHandler = com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler.class),
            @Result(property = "multithresholds", column = "multithresholds", typeHandler = com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler.class),
            @Result(property = "metric", column = "metric", javaType = java.util.List.class, typeHandler = com.databuff.handler.StringArrayToListTypeHandler.class),
            @Result(property = "metrics", column = "metrics", javaType = java.util.List.class, typeHandler = com.databuff.handler.StringArrayToListTypeHandler.class),
            @Result(property = "busName", column = "busName", javaType = java.util.List.class, typeHandler = com.databuff.handler.StringArrayToListTypeHandler.class),
            @Result(property = "host", column = "host", javaType = java.util.List.class, typeHandler = com.databuff.handler.StringArrayToListTypeHandler.class),
            @Result(property = "serviceId", column = "serviceId", javaType = java.util.List.class, typeHandler = com.databuff.handler.StringArrayToListTypeHandler.class),
            @Result(property = "serviceInstance", column = "serviceInstance", javaType = java.util.List.class, typeHandler = com.databuff.handler.StringArrayToListTypeHandler.class),
            @Result(property = "deviceName", column = "deviceName", javaType = java.util.List.class, typeHandler = com.databuff.handler.StringArrayToListTypeHandler.class)
    })
    List<DCEventDto> findEventListByAlarmId(@Param("alertId") String alertId, @Param("createDts") Collection<String> createDts);

    /**
     * 根据告警ID查询事件时间
     *
     * @param alertId 告警ID
     * @param allEntityPermission 是否有所有实体权限
     * @param gids 实体ID集合
     * @return 事件时间的Map列表
     */
    @Select("<script>" +
            "SELECT COUNT(*) as cnt, triggerTime FROM dc_event JOIN " +
            "(SELECT id, unnest FROM dc_alarm, unnest(eventId) AS unnest WHERE id=#{alertId}) a " +
            "ON dc_event.id = a.unnest " +
            "<if test='!allEntityPermission'> " +
            "<if test='gids != null and gids.size() > 0'>AND (gid IN <foreach item='gid' collection='gids' separator=',' open='(' close=')'>#{gid}</foreach>)</if>" +
            "<if test='gids == null or gids.size() == 0'>AND 1=0</if>" +
            "</if>" +
            "GROUP BY triggerTime " +
            "ORDER BY triggerTime DESC" +
            "</script>")
    List<Map<String, Object>> findEventTimeByAlarmId(@Param("alertId") String alertId, @Param("allEntityPermission") boolean allEntityPermission, @Param("gids") Collection<String> gids);

    /**
     * 根据事件ID查询事件
     *
     * @param eventId 事件ID
     * @param createDt 创建时间
     * @param allEntityPermission 是否有所有实体权限
     * @param gids 实体ID集合
     * @return 事件DTO
     */
    @Select("SELECT * FROM dc_event WHERE id = #{eventId} AND createDt = #{createDt}")
    @ResultMap("DCEventDtoResultMap")
    DCEventDto findEventByEventId(@Param("eventId") String eventId, @Param("createDt") String createDt, @Param("allEntityPermission") boolean allEntityPermission, @Param("gids") Collection<String> gids);

    /**
     * 检索符合所提供条件的 DCEventDto 对象列表。
     *
     * @param createDt 创建时间
     * @param apiKey    用于身份验证的 API 密钥。
     * @param startTime 要查询的事件周期的开始时间（自纪元以来以毫秒为单位）。
     * @param endTime   要查询的事件周期的结束时间（自纪元以来以毫秒为单位）。
     * @param condition 用于事件搜索的条件。
     * @param allEntityPermission 是否有所有实体权限
     * @param gids 实体ID集合
     * @return 符合所提供条件的 DCEventDto 对象列表。
     */
    @Select("<script>" +
            "SELECT id, monitorId, `level`, metrics, message, ruleName, classification, `group`, triggerTime, createTime, `tags` " +
            "FROM dc_event " +
            "WHERE createDt = #{createDt} " +
            "<if test='startTime != null and startTime != 0'> AND createTime &gt;= #{startTime} </if> " +
            "<if test='endTime != null and endTime != 0'> AND createTime &lt; #{endTime} </if> " +
            "<if test='condition != null and condition != \"\"'> AND (${condition}) </if> " +
            "<if test='!allEntityPermission'> " +
            "<if test='gids != null and gids.size() > 0'>AND (gid IN <foreach item='gid' collection='gids' separator=',' open='(' close=')'>#{gid}</foreach>)</if>" +
            "<if test='gids == null or gids.size() == 0'>AND 1=0</if>" +
            "</if>" +
            "</script>")
    @ResultMap("DCEventDtoResultMap")
    List<DCEventDto> findEventsByCondition(@Param("createDt") String createDt, @Param("apiKey") String apiKey, @Param("startTime") long startTime, @Param("endTime") long endTime, @Param("condition") String condition, @Param("allEntityPermission") boolean allEntityPermission, @Param("gids") Collection<String> gids);
}