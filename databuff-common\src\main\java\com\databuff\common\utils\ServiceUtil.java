package com.databuff.common.utils;

import com.alibaba.fastjson.JSONObject;

import java.util.Map;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.util.HashMap;
import java.util.Map;

import static com.databuff.common.constants.Constant.Trace.*;

public class ServiceUtil {
    /**
     * @param source                OpenTelemetry,SkyWalking,Databuff
     * @param service               {
     *                              "OpenTelemetry": {
     *                              "prefix": "otel_",
     *                              "suffix": "_otel"
     *                              },
     *                              "SkyWalking": {
     *                              "prefix": "sky_",
     *                              "suffix": "_sky"
     *                              }
     *                              }
     * @param svcNameAppendIdentity
     * @return
     */
    public static String serviceNameAppendSource(String source, String service, JSONObject svcNameAppendIdentity) {
        if (svcNameAppendIdentity == null || svcNameAppendIdentity.size() == 0 || StringUtils.isBlank(source)) {
            return service;
        }
        JSONObject config = svcNameAppendIdentity.getJSONObject(source);
        if (config == null || config.size() == 0) {
            return service;
        }
        String prefix = config.getString("prefix");
        String suffix = config.getString("suffix");
        if (StringUtils.isNotBlank(prefix) && !service.startsWith(prefix)) {
            service = prefix + service;
        }
        if (StringUtils.isNotBlank(suffix) && !service.endsWith(suffix)) {
            service = service + suffix;
        }
        return service;
    }

    public static String joinApiKeyAndService(String apiKey, String service) {
        return apiKey + ":" + service;
    }

    public static String generateServiceId(String apiKeyAndService) {
        return DigestUtils.md5Hex(apiKeyAndService).substring(8, 24);
    }

    public static String generateOldServiceIdBase64(String apiKeyAndService) {
        String serviceId = "";
        try {
            serviceId = Base64ConvertUtil.encode(apiKeyAndService);
        } catch (UnsupportedEncodingException e) {
        }
        return serviceId;
    }

    public static String generateServiceId(String apiKey, String service) {
        return generateServiceId(joinApiKeyAndService(apiKey, service));
    }

    public static String getVirtualServiceValue(boolean virtualService) {
        return Boolean.TRUE.equals(virtualService) ? "1" : "0";
    }


    public static String[] getRemotelySvc(Map<String, String> meta, String name) {
        String peerHostname = meta.get(PEER_HOSTNAME);
        String peerPort = meta.get(PEER_PORT);
        String type = REMOTE_TYPE_METRIC_NAME_MAP.get(name) == null ? "Unknown" : REMOTE_TYPE_METRIC_NAME_MAP.get(name);
        if (type == REMOTE_TYPE_HTTP) {
            //http远程调用
            String httpUrl = meta.get(HTTP_URL);
            if (StringUtils.isNotEmpty(peerHostname) && StringUtils.isEmpty(peerPort) && StringUtils.isNotEmpty(httpUrl)) {
                //如果peer.hostname不为空 peer.port 为空，根据url判断http还是https，增加端口80或者443
                if (httpUrl.startsWith("https")) {
                    peerPort = "443";
                } else if (httpUrl.startsWith("http")) {
                    peerPort = "80";
                }
            } else if (StringUtils.isEmpty(peerHostname) && StringUtils.isEmpty(peerPort) && StringUtils.isNotEmpty(httpUrl)) {
                try {
                    URI uri = new URI(httpUrl);
                    if (uri.getHost() != null) {
                        peerHostname = uri.getHost();
                    }
                    if (uri.getPort() != -1) {
                        peerPort = String.valueOf(uri.getPort());
                    } else {
                        if (httpUrl.startsWith("https")) {
                            peerPort = "443";
                        } else if (httpUrl.startsWith("http")) {
                            peerPort = "80";
                        }
                    }
                } catch (Exception e) {
                }
            }
        }
        //如果是服务端调用，peer.hostname和peer.port 为空
        if (StringUtils.isEmpty(peerHostname) && StringUtils.isEmpty(peerPort)) {
            return null;
        }

        if ("localhost".equals(peerHostname) || "127.0.0.1".equals(peerHostname)) {
            return null;
        }

        //这里需要
        String[] remoteService = new String[3];
        remoteService[0] = type;
        remoteService[1] = peerHostname;
        remoteService[2] = peerPort;
        return remoteService;
    }

    public static Map<String, String> initDefaultComponentNameInstance(String componentServiceName, String dbInstance, String peerHost, String peerPort, String reportSvc) {
        Map<String, String> svcNameInstance = new HashMap<>();
        String service;
        String serviceInstance;
        if (StringUtils.isNotEmpty(dbInstance)) {
            service = String.format("[%s]%s", componentServiceName, dbInstance);
            serviceInstance = StringUtils.defaultIfEmpty(peerHost, service);
        } else if (StringUtils.isNotEmpty(peerHost)) {
            service = String.format("[%s]%s%s", componentServiceName, peerHost, StringUtils.isNotEmpty(peerPort) ? ":" + peerPort : "");
            serviceInstance = peerHost;
        } else {
            service = String.format("[%s]%s", componentServiceName, reportSvc);
            serviceInstance = service;
        }
        svcNameInstance.put(SERVICE, service);
        svcNameInstance.put(SERVICE_INSTANCE, serviceInstance);
        return svcNameInstance;
    }
}
