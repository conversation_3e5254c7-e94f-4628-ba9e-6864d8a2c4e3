package com.databuff.common.utils.webhook;

import lombok.Data;

import java.io.Serializable;

@Data
public class NotifyWebhookConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * api_key
     */
    private String apiKey;

    /**
     * 是否开启，默认1开启，0不开启
     */
    private Integer tenantEnable;

    /**
     * 是否开启，默认1开启，0不开启
     */
    private Integer enable;

    /**
     * dingtalk，wechat，sms，mail, webhook
     */
    private String notifyType;

    /**
     * webhook HTTP 请求方式
     */
    private String webhookMethod;

    /**
     * webhook Url
     */
    private String webhookUrl;
    /**
     * webhook header
     */
    private String webhookHeader;

    /**
     * 总发送条数
     */
    private Long totalNum;
    /**
     * 当月发送条数
      */
    private Long theMonthNum;
}
