package com.databuff.common.tsdb.model;

public class TSDBDatabaseInfo {
    private String databaseName;
    private String userName;
    private String password;
    private int shard;
    private int replication;
    private int keepDay;
    private int interval;
    public TSDBDatabaseInfo(){

    }
    public TSDBDatabaseInfo(String databaseName, String userName, String password, int shard, int replication, int keepDay, int interval) {
        this.databaseName = databaseName;
        this.userName = userName;
        this.password = password;
        this.shard = shard;
        this.replication = replication;
        this.keepDay = keepDay;
        this.interval = interval;
    }

    public String getDatabaseName() {
        return databaseName;
    }

    public void setDatabaseName(String databaseName) {
        this.databaseName = databaseName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public int getShard() {
        return shard;
    }

    public void setShard(int shard) {
        this.shard = shard;
    }

    public int getReplication() {
        return replication;
    }

    public void setReplication(int replication) {
        this.replication = replication;
    }

    public int getKeepDay() {
        return keepDay;
    }

    public void setKeepDay(int keepDay) {
        this.keepDay = keepDay;
    }

    public int getInterval() {
        return interval;
    }

    public void setInterval(int interval) {
        this.interval = interval;
    }
}
