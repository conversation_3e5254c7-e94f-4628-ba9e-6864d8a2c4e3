package com.databuff.common.utils.dingtalk;

import lombok.Data;

import java.io.Serializable;

@Data
public class NotifyDingTalkConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;

    /**
     * api_key
     */
    private String apiKey;

    /**
     * 是否开启，默认1开启，0不开启
     */
    private Integer tenantEnable;
    /**
     * 机器人通知是否开启，默认1开启，0不开启
     */
    private Integer robotEnable;
    /**
     * 是否开启，默认1开启，0不开启
     */
    private Integer enable;

    /**
     * dingtalk，wechat，sms，mail
     */
    private String notifyType;
    /**
     * 应用的唯一标识key
     */
    private String appkey;
    /**
     * 应用的密钥
     */
    private String appsecret;
    /**
     * 发送消息时使用的微应用的AgentID
     */
    private Long dingAgentId ;
    /**
     * 钉钉群机器人webhook调用地址
     */
    private String dingWebhook;

    /**
     * 签名秘钥（安全设置使用 加签 的方式）
     */
    private String dingSecret;

    //总发送条数
    private Long totalNum;

    //当月发送条数
    private Long theMonthNum;
}
