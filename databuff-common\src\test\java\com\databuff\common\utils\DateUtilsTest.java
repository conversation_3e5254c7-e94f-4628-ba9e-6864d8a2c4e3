package com.databuff.common.utils;

import org.junit.jupiter.api.Test;

import java.util.Calendar;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

class DateUtilsTest {


    @Test
    void testGetHourFromDate() {
        // Test case 1: Regular date
        Calendar cal = Calendar.getInstance();
        cal.set(2024, Calendar.JANUARY, 15, 10, 30, 45);
        Date testDate = cal.getTime();
        assertEquals(2024011510, DateUtils.getHourFromDate(testDate));

        // Test case 2: Date at month boundary
        cal.set(2023, Calendar.DECEMBER, 31, 23, 59, 59);
        testDate = cal.getTime();
        assertEquals(2023123123, DateUtils.getHourFromDate(testDate));

        // Test case 3: Date at year boundary
        cal.set(2024, Calendar.JANUARY, 1, 0, 0, 0);
        testDate = cal.getTime();
        assertEquals(2024010100, DateUtils.getHourFromDate(testDate));

        // Test case 4: Null input
        assertNull(DateUtils.getHourFromDate(null));
    }

    @Test
    void testGetHourFromDateWithSpecificTimes() {
        // Test specific times that might be problematic
        Calendar cal = Calendar.getInstance();

        // Test single digit month
        cal.set(2024, Calendar.MARCH, 5, 8, 0, 0);
        assertEquals(2024030508, DateUtils.getHourFromDate(cal.getTime()));

        // Test single digit day
        cal.set(2024, Calendar.OCTOBER, 7, 15, 0, 0);
        assertEquals(2024100715, DateUtils.getHourFromDate(cal.getTime()));

        // Test single digit hour
        cal.set(2024, Calendar.DECEMBER, 25, 5, 0, 0);
        assertEquals(2024122505, DateUtils.getHourFromDate(cal.getTime()));
    }


    @Test
    void testGetMinuteFromDate() {
        // Test case 1: Regular date
        Calendar cal = Calendar.getInstance();
        cal.set(2024, Calendar.JANUARY, 15, 10, 30, 45);
        Date testDate = cal.getTime();
        assertEquals(202401151030L, DateUtils.getMinuteFromDate(testDate));

        // Test case 2: Date at month boundary
        cal.set(2023, Calendar.DECEMBER, 31, 23, 59, 59);
        testDate = cal.getTime();
        assertEquals(202312312359L, DateUtils.getMinuteFromDate(testDate));

        // Test case 3: Date at year boundary
        cal.set(2024, Calendar.JANUARY, 1, 0, 0, 0);
        testDate = cal.getTime();
        assertEquals(202401010000L, DateUtils.getMinuteFromDate(testDate));

        // Test case 4: Null input
        assertNull(DateUtils.getMinuteFromDate(null));
    }

}