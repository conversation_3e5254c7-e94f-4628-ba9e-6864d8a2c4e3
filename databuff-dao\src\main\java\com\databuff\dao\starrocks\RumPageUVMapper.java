package com.databuff.dao.starrocks;

import com.databuff.entity.dto.TimeValue;
import com.databuff.entity.rum.web.PageUVDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Mapper
@Repository
public interface RumPageUVMapper {

    List<PageUVDto> getUVForApps(
            @Param("fromTime") Date fromTime,
            @Param("toTime") Date toTime
    );

    List<PageUVDto> getUVForPages(
            @Param("appId") Integer appId,
            @Param("processedLocationHrefList") List<String> processedLocationHrefList,
            @Param("isSlowPage") Boolean isSlowPage,
            @Param("fromTime") Date fromTime,
            @Param("toTime") Date toTime
    );

    List<TimeValue> getUVTrend(
            @Param("appId") Integer appId,
            @Param("fromTime") Date fromTime,
            @Param("toTime") Date toTime,
            @Param("timeBucket") String timeBucket
    );

}
