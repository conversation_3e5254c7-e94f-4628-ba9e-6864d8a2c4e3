package com.databuff.dao.starrocks;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.databuff.entity.dto.WebSearchCriteria;
import com.databuff.entity.rum.starrocks.RumAction;
import com.databuff.entity.rum.starrocks.RumActionSpan;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface RumActionMapper extends BaseMapper<RumAction> {

    @Select("<script>" +
            "SELECT * FROM dc_rum_action WHERE app_id = #{appId} AND action_id = #{actionId} " +
            "<if test='fromTime != null'> AND startTime &gt;= #{fromTime}</if>" +
            "<if test='toTime != null'> AND startTime &lt; #{toTime}</if>" +
            "ORDER BY startTime DESC limit 1" +
            "</script>")
    RumAction selectRumActionById(WebSearchCriteria search);

    @Select("<script>" +
            "SELECT " +
            "startTime,action_id,action_name,action_duration,action_request_duration,action_service_duration,app_id,user_id,ip,isp,session_id,region " +
            "FROM dc_rum_action WHERE 1=1" +
            "<if test='appId != null'> AND app_id = #{appId}</if>" +
            "<if test='actionName != null'> AND action_name = #{actionName}</if>" +
            "<if test='actionNameLike != null'> AND action_name LIKE CONCAT('%', #{actionNameLike}, '%')</if>" +
            "<if test='userId != null'> AND user_id = #{userId}</if>" +
            "<if test='ip != null'>" +
            "    <choose>" +
            "        <when test='actionName != null'>" +
            "            AND ip LIKE CONCAT('%', #{ip}, '%')" +
            "        </when>" +
            "        <otherwise>" +
            "            AND ip = #{ip}" +
            "        </otherwise>" +
            "    </choose>" +
            "</if>" +
            "<if test='sessionId != null'> AND session_id = #{sessionId}</if>" +
            "<if test='fromTime != null'> AND startTime &gt;= #{fromTime}</if>" +
            "<if test='toTime != null'> AND startTime &lt; #{toTime}</if>" +
            "</script>")
    List<RumAction> searchRumActionsWithFilters(WebSearchCriteria searchCriteria);

    @Select("<script>" +
            "SELECT " +
            "action_id,trace_id,span_id,parent_id,startTime,start,end,duration,http_url,service " +
            "FROM dc_rum_action_span WHERE app_id = #{appId} AND action_id = #{actionId}" +
            "<if test='fromTime != null'> AND startTime &gt;= #{fromTime}</if>" +
            "<if test='toTime != null'> AND startTime &lt; #{toTime}</if>" +
            "</script>")
    List<RumActionSpan> getActionSpan(WebSearchCriteria searchCriteria);
}