package com.databuff.common.tsdb.dto.detect;

import com.databuff.common.tsdb.dto.explore.QueryRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Collection;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试 MultiDetectQueryRequest 类的功能，特别是新增的 findXXX 和 setXXX 方法。
 */
public class MultiDetectQueryRequestTest {

    private MultiDetectQueryRequest multiRequest;
    private DetectQueryRequest detectQueryA;
    private DetectQueryRequest detectQueryB;
    private QueryRequest queryA1;
    private QueryRequest queryA2;
    private QueryRequest queryB1;

    @BeforeEach
    public void setUp() {
        // 创建测试对象
        multiRequest = new MultiDetectQueryRequest();

        // 创建 DetectQueryRequest 对象
        detectQueryA = new DetectQueryRequest();
        detectQueryB = new DetectQueryRequest();

        // 创建 QueryRequest 对象
        queryA1 = new QueryRequest();
        queryA1.setMetric("metric1");
        queryA2 = new QueryRequest();
        queryA2.setMetric("metric2");
        queryB1 = new QueryRequest();
        queryB1.setMetric("metric3");

        // 设置 DetectQueryRequest 的属性
        detectQueryA.setA(queryA1);
        detectQueryA.setB(queryA2);
        detectQueryA.setTimeAggregator("avg");
        detectQueryA.setComparison(">");
        detectQueryA.setUnit("percent");
        detectQueryA.setViewUnit("%");
        detectQueryA.setContinuous(true);
        detectQueryA.setRequireFullWindow(false);

        detectQueryB.setA(queryB1);
        detectQueryB.setTimeAggregator("max");
        detectQueryB.setComparison("<");
        detectQueryB.setUnit("bytes");
        detectQueryB.setViewUnit("MB");
        detectQueryB.setContinuous(false);
        detectQueryB.setRequireFullWindow(true);

        // 设置 MultiDetectQueryRequest 的属性
        multiRequest.setA(detectQueryA);
        multiRequest.setB(detectQueryB);
    }

    @Test
    public void testBuildNonNullQueries() {
        // 测试 buildNonNullQueries 方法
        Collection<DetectQueryRequest> nonNullQueries = multiRequest.buildNonNullQueries();
        assertEquals(2, nonNullQueries.size());
        assertTrue(nonNullQueries.contains(detectQueryA));
        assertTrue(nonNullQueries.contains(detectQueryB));
    }

    @Test
    public void testFindTimeAggregators() {
        // 测试 findTimeAggregators 方法
        Collection<String> timeAggregators = multiRequest.findTimeAggregators();
        assertEquals(2, timeAggregators.size());
        assertTrue(timeAggregators.contains("avg"));
        assertTrue(timeAggregators.contains("max"));
    }

    @Test
    public void testFindComparisons() {
        // 测试 findComparisons 方法
        Collection<String> comparisons = multiRequest.findComparisons();
        assertEquals(2, comparisons.size());
        assertTrue(comparisons.contains(">"));
        assertTrue(comparisons.contains("<"));
    }

    @Test
    public void testFindUnits() {
        // 测试 findUnits 方法
        Collection<String> units = multiRequest.findUnits();
        assertEquals(2, units.size());
        assertTrue(units.contains("percent"));
        assertTrue(units.contains("bytes"));
    }

    @Test
    public void testFindViewUnits() {
        // 测试 findViewUnits 方法
        Collection<String> viewUnits = multiRequest.findViewUnits();
        assertEquals(2, viewUnits.size());
        assertTrue(viewUnits.contains("%"));
        assertTrue(viewUnits.contains("MB"));
    }

    @Test
    public void testFindContinuous() {
        // 测试 findContinuous 方法
        Collection<Boolean> continuous = multiRequest.findContinuous();
        assertEquals(2, continuous.size());
        assertTrue(continuous.contains(true));
        assertTrue(continuous.contains(false));
    }

    @Test
    public void testFindRequireFullWindow() {
        // 测试 findRequireFullWindow 方法
        Collection<Boolean> requireFullWindow = multiRequest.findRequireFullWindow();
        assertEquals(2, requireFullWindow.size());
        assertTrue(requireFullWindow.contains(true));
        assertTrue(requireFullWindow.contains(false));
    }

    @Test
    public void testSetTimeAggregator() {
        // 测试 setTimeAggregator 方法
        multiRequest.setTimeAggregator("sum");

        // 验证所有 DetectQueryRequest 对象的 timeAggregator 属性都被设置为 "sum"
        for (DetectQueryRequest detectQuery : multiRequest.buildNonNullQueries()) {
            assertEquals("sum", detectQuery.getTimeAggregator());
        }
    }

    @Test
    public void testSetComparison() {
        // 测试 setComparison 方法
        multiRequest.setComparison(">=");

        // 验证所有 DetectQueryRequest 对象的 comparison 属性都被设置为 ">="
        for (DetectQueryRequest detectQuery : multiRequest.buildNonNullQueries()) {
            assertEquals(">=", detectQuery.getComparison());
        }
    }

    @Test
    public void testSetUnit() {
        // 测试 setUnit 方法
        multiRequest.setUnit("count");

        // 验证所有 DetectQueryRequest 对象的 unit 属性都被设置为 "count"
        for (DetectQueryRequest detectQuery : multiRequest.buildNonNullQueries()) {
            assertEquals("count", detectQuery.getUnit());
        }
    }

    @Test
    public void testSetViewUnit() {
        // 测试 setViewUnit 方法
        multiRequest.setViewUnit("个");

        // 验证所有 DetectQueryRequest 对象的 viewUnit 属性都被设置为 "个"
        for (DetectQueryRequest detectQuery : multiRequest.buildNonNullQueries()) {
            assertEquals("个", detectQuery.getViewUnit());
        }
    }

    @Test
    public void testSetContinuous() {
        // 测试 setContinuous 方法
        multiRequest.setContinuous(true);

        // 验证所有 DetectQueryRequest 对象的 continuous 属性都被设置为 true
        for (DetectQueryRequest detectQuery : multiRequest.buildNonNullQueries()) {
            assertTrue(detectQuery.getContinuous());
        }
    }

    @Test
    public void testSetRequireFullWindow() {
        // 测试 setRequireFullWindow 方法
        multiRequest.setRequireFullWindow(false);

        // 验证所有 DetectQueryRequest 对象的 requireFullWindow 属性都被设置为 false
        for (DetectQueryRequest detectQuery : multiRequest.buildNonNullQueries()) {
            assertFalse(detectQuery.getRequireFullWindow());
        }
    }

    @Test
    public void testSetPropertyOnAllDetectQueries() {
        // 测试 setPropertyOnAllDetectQueries 方法
        multiRequest.setPropertyOnAllDetectQueries("comparison", "!=");

        // 验证所有 DetectQueryRequest 对象的 comparison 属性都被设置为 "!="
        for (DetectQueryRequest detectQuery : multiRequest.buildNonNullQueries()) {
            assertEquals("!=", detectQuery.getComparison());
        }
    }

    @Test
    public void testSetPropertyOnAllQueries() {
        // 测试 setPropertyOnAllQueries 方法
        multiRequest.setPropertyOnAllQueries("interval", 60);

        // 验证所有嵌套的 QueryRequest 对象的 interval 属性都被设置为 60
        for (DetectQueryRequest detectQuery : multiRequest.buildNonNullQueries()) {
            for (QueryRequest query : detectQuery.getNonNullQueries()) {
                assertEquals(60, query.getInterval());
            }
        }
    }

    @Test
    public void testSetPropertyOnAllQueriesWithInvalidProperty() {
        // 测试使用不存在的属性调用 setPropertyOnAllQueries 方法
        Exception exception = assertThrows(RuntimeException.class, () -> {
            multiRequest.setPropertyOnAllQueries("nonExistentProperty", "value");
        });

        // 验证异常消息包含预期的错误信息
        assertTrue(exception.getMessage().contains("property nonExistentProperty on queries"));
    }

    @Test
    public void testSetPropertyOnAllDetectQueriesWithInvalidProperty() {
        // 测试使用不存在的属性调用 setPropertyOnAllDetectQueries 方法
        Exception exception = assertThrows(RuntimeException.class, () -> {
            multiRequest.setPropertyOnAllDetectQueries("nonExistentProperty", "value");
        });

        // 验证异常消息包含预期的错误信息
        assertTrue(exception.getMessage().contains("property nonExistentProperty on detect queries"));
    }
}
