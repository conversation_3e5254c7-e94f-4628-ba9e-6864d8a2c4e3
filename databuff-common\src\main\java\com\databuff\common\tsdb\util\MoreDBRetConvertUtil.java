package com.databuff.common.tsdb.util;

import com.databuff.common.tsdb.model.TSDBResult;
import com.databuff.common.tsdb.model.TSDBResultSet;
import com.databuff.common.tsdb.model.TSDBSeries;
import com.databuff.moredb.model.query.GroupResult;
import com.databuff.moredb.model.query.QueryResult;
import com.databuff.moredb.model.query.SelectResult;
import com.databuff.moredb.model.util.StringUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.databuff.moredb.proto.Statement.Stmt.StmtCase.*;

public class MoreDBRetConvertUtil {

    public static TSDBResultSet formatResult(TSDBResult result,QueryResult queryResult, String epoch, AtomicBoolean hasCountFunction) {
        TSDBResultSet resultSet = new TSDBResultSet();
        List<TSDBResult> results = new ArrayList<>();
        results.add(result);
        List<TSDBSeries> series = new ArrayList<>();
        result.setSeries(series);

        String queryType = queryResult.getQueryType();
        if (QUERY.name().equals(queryType)) {
            buildQuery(series, (SelectResult) queryResult.getResult(), epoch, hasCountFunction);
        } else if (SHOWMETRIC.name().equals(queryType)) {
            buildShowMetric(series, (Map) queryResult.getResult());
        } else if (SHOWTAGKEYS.name().equals(queryType)) {
            buildShowTagKeys(series, (Map) queryResult.getResult());
        } else if (SHOWTAGVALUES.name().equals(queryType)) {
            buildShowTagValues(series, (Map) queryResult.getResult());
        } else if (SHOWFIELDKEYS.name().equals(queryType)) {
            buildShowFieldKeys(series, (Map) queryResult.getResult());
        }
        resultSet.setResults(results);
        return resultSet;
    }

    private static void buildShowFieldKeys(List<TSDBSeries> seriesList, Map result) {
        TSDBSeries series = new TSDBSeries();
        series.setName((String) result.get("measurement"));
        series.setColumns(Arrays.asList("fieldKey", "fieldType"));
        Map<String, String> values = (Map<String, String>) result.get("data");
        if (values != null && values.size() > 0) {
            for (Map.Entry<String, String> entry : values.entrySet()) {
                series.addValue(Arrays.asList(entry.getKey(), entry.getValue()));
            }
            seriesList.add(series);
        }
    }

    private static void buildShowTagValues(List<TSDBSeries> seriesList, Map result) {
        TSDBSeries series = new TSDBSeries();
        series.setName((String) result.get("measurement"));
        series.setColumns(Arrays.asList("key", "value"));
        Collection<String> values = (Collection<String>) result.get("data");
        String tagKey = (String) result.get("tagKey");
        if (values != null && values.size() > 0) {
            TreeSet<String> treeSetValues = new TreeSet<>(values);
            for (String value : treeSetValues) {
                if (StringUtils.isNotEmpty(value)) {
                    series.addValue(Arrays.asList(tagKey, value));
                }
            }
            seriesList.add(series);
        }
    }

    private static void buildShowTagKeys(List<TSDBSeries> seriesList, Map result) {
        TSDBSeries series = new TSDBSeries();
        series.setName((String) result.get("measurement"));
        series.setColumns(Arrays.asList("tagKey"));
        initMetaData(result, series);
        seriesList.add(series);
    }

    private static void buildShowMetric(List<TSDBSeries> seriesList, Map result) {
        TSDBSeries series = new TSDBSeries();
        series.setName("measurements");
        series.setColumns(Arrays.asList("name"));
        initMetaData(result, series);
        seriesList.add(series);
    }

    private static void buildQuery(List<TSDBSeries> seriesList, SelectResult result, String epoch, AtomicBoolean hasCountFunction) {
        if (result == null) {
            return;
        }
        List<GroupResult> groupResults = result.getGroups();
        if (groupResults == null || groupResults.size() == 0) {
            return;
        }
        List<String> columns = null;
        long rate = 1;
        if ("s".equals(epoch)) {
            rate = 1000;
        }
        long[] times = new long[result.getPointCount()];
        for (int i = 0; i < result.getPointCount(); i++) {
            times[i] = (result.getStartTime() + result.getInterval() * i) / rate;
        }
        TSDBSeries countSeries = null;
        String fieldKey = null;
        if (groupResults != null && groupResults.size() > 0) {
            for (int i = 0; i < groupResults.size(); i++) {
                GroupResult groupResult = groupResults.get(i);
                if (columns == null) {
                    columns = new ArrayList<>();
                    columns.add("time");
                    if (hasCountFunction.get()) {
                        columns.add("count");
                    } else {
                        for (String column : groupResult.getFields().keySet()) {
                            columns.add(formatColumn(column));
                        }
                    }
                }
                if (hasCountFunction.get()) {
                    if (countSeries == null) {
                        countSeries = new TSDBSeries();
                        countSeries.setName(result.getMeasurementName());
                        countSeries.setTags(new HashMap<>());
                        countSeries.setColumns(columns);
                        for (int j = 0; j < result.getPointCount(); j++) {
                            List<Object> values = new ArrayList<>(columns.size());
                            values.add(times[j]);
                            values.add(null);
                            countSeries.addValue(values);
                        }
                        seriesList.add(countSeries);
                        List<String> fields = new ArrayList<>(groupResult.getFields().keySet());
                        fieldKey = fields.get(0);
                    }
                    List<List<Object>> list = countSeries.getValues();
                    for (int j = 0; j < result.getPointCount(); j++) {
                        List<Number> numbers = groupResult.getFields().get(fieldKey);
                        Number number = numbers.get(j);
                        if (number != null) {
                            List<Object> columnValues = list.get(j);
                            Number countValue = ((Number) columnValues.get(1));
                            if (countValue == null) {
                                list.get(j).set(1, 1);
                            } else {
                                columnValues.set(1, countValue.intValue() + 1);
                            }
                        }
                    }
                } else {
                    TSDBSeries series = new TSDBSeries();
                    series.setName(result.getMeasurementName());
                    Map<String, String> group = groupResult.getGroup();
                    if (group != null && group.size() > 0) {
                        series.setTags(group);
                    }
                    series.setColumns(columns);
                    for (int j = 0; j < result.getPointCount(); j++) {
                        List<Object> values = new ArrayList<>(columns.size());
                        values.add(times[j]);
                        for (Map.Entry<String, List<Number>> entry : groupResult.getFields().entrySet()) {
                            if (entry.getValue() == null) {
                                values.add(null);
                            } else {
                                Number val = entry.getValue().get(j);
                                values.add(val);
                            }
                        }
                        series.addValue(values);
                    }
                    seriesList.add(series);
                }
            }
        }
    }

    private static String formatColumn(String column) {
        int first = column.indexOf("(");
        if (first > 0) {
            column = column.substring(0, first);
        }
        return column;
    }

    public static String formatInfluxSql(String sql, AtomicBoolean hasCountFunction) {
        //如果是show measurement语句
        String lowerCaseSql = sql.toLowerCase();
        if (lowerCaseSql.contains("show")) {
            boolean showMeasurement = false;
            if (lowerCaseSql.contains("measurements") || lowerCaseSql.contains("tag keys")) {
                showMeasurement = true;
                sql = removeWhere(sql);
                sql = sql.replaceAll("/", "").trim();
                sql = sql.replaceAll("~", "").trim();
            }
            sql = sql.replaceAll("\\\\", "").trim();
            sql = sql.replaceAll("\\(\\?i\\)", "").trim();
            if (showMeasurement) {
                lowerCaseSql = sql.toLowerCase();
                int start = sql.indexOf("=");
                if (start > 0) {
                    int end = lowerCaseSql.indexOf("limit");
                    if (end < 0) {
                        end = sql.length();
                    }
                    if (end < start) {
                        end = start;
                    }
                    String prefix = sql.substring(start + 1, end).trim();
                    sql = sql.substring(0, start + 1) + " '" + prefix + "' " + sql.substring(end, sql.length());
                }
            }
        }
        String countFun = "count(";
        int index = sql.indexOf(countFun);
        if (index > 0) {
            hasCountFunction.set(true);
            int countEnd = sql.indexOf(")", index);
            sql = sql.substring(0, index) + sql.substring(index + countFun.length(), countEnd) + sql.substring(countEnd + 1);
        }
        return sql;
    }

    private static void initMetaData(Map result, TSDBSeries series) {
        Object data = result.get("data");
        if (data != null) {
            Collection<String> listData = (Collection<String>) data;
            for (String metricName : listData) {
                if (StringUtils.isNotEmpty(metricName)) {
                    series.addValue(Arrays.asList(metricName));
                }
            }
        }
    }

    private static String removeWhere(String sql) {
        int whereIndex = sql.indexOf("WHERE");
        if (whereIndex > 0) {
            sql = sql.substring(0, whereIndex).trim();
        } else {
            whereIndex = sql.indexOf("where");
            if (whereIndex > 0) {
                sql = sql.substring(0, whereIndex).trim();
            }
        }
        return sql;
    }
}
