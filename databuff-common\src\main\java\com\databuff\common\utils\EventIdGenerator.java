package com.databuff.common.utils;

import com.databuff.common.threadLocal.NowTimeThreadLocal;

import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * 生成唯一的事件 ID。
 */
public class EventIdGenerator {

    /**
     * 根据提供的源、类型和监视器 ID 生成唯一的事件 ID。
     *
     * @param source 事件的来源。这表示为两位数。
     * @param type   事件的类型。这被表示为一个字符。
     * @return 唯一的事件ID。
     */
    public static String generateEventId(int source, String type, String id) {
        StringBuilder eventId = new StringBuilder();

        // 1. Add 'E' for Event
        eventId.append('E');

        // 2. Add source
        eventId.append(String.format("%02d", source));

        // 3. Add type
        eventId.append(getFirstLettersInUpperCase(type));

        // 4. Add date (yyyyMMdd)
        LocalDate date = LocalDate.now();
        eventId.append(date.format(DateTimeFormatter.ofPattern("yyyyMMdd")));

        // 5. Generate a unique ID based on the monitorId and a random UUID
//        final String id = "M" + monitorId;
        eventId.append(id);

        return eventId.toString();
    }

    public static String generateEventId(IdGenerator idGenerator) {
        LocalDate date = LocalDate.now();
        final Long nowTime = NowTimeThreadLocal.getNowTime();
        if (nowTime != null) {
            Instant instant = Instant.ofEpochMilli(nowTime);
            date = instant.atZone(ZoneId.of("Asia/Shanghai")).toLocalDate();
        }

        return idGenerator.generate(date.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
    }

    public static LocalDate convertLongToLocalDate(Long epochSeconds) {
        LocalDateTime dateTime = LocalDateTime.ofEpochSecond(epochSeconds, 0, ZoneOffset.UTC);
        return dateTime.toLocalDate();
    }

    /**
     * 根据提供的源、类型和监视器 ID 生成唯一的事件 ID。
     *
     * @param type      事件的类型。这被表示为一个字符。
     * @param monitorId 监视器的 ID。这用于确保事件 ID 的唯一性。
     * @return 唯一的事件ID。
     */
    public static String generateEventId(String type, String id) {
        // 10 代表databuff
        return generateEventId(10, type, id);
    }

    public static String getFirstLettersInUpperCase(String input) {
        if (input.length() > 1) {
            return input.substring(0, 1).toUpperCase();
        } else {
            return input.toUpperCase();
        }
    }

    /**
     * 从给定的事件 ID 中提取日期。
     * <p>
     * 事件 ID 的格式应为“E”+ 两位数来源 + 类型的第一个大写字母 + 日期 (yyyyMMdd) + ID。
     * 因此，日期始终位于事件 ID 的第 5 个和第 12 个字符之间。
     *
     * @param eventId 要从中提取日期的事件 ID。
     * @return 从事件 ID 中提取的日期，以整数形式，格式为 yyyyMMdd。
     * @throws NumberFormatException 如果事件 ID 的日期部分不是有效的整数。
     */
    public static int extractDateFromEventId(String eventId) {
        String dateStr = eventId.substring(4, 12);
        return Integer.parseInt(dateStr);
    }

    public static String extractStrDateFromEventId(String eventId) {
        String dateStr = eventId.substring(4, 12);
        LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
        LocalDateTime dateTime = date.atStartOfDay();
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    private static SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

    public static String extractStrDateFrom(Long startTime) {
        return dateFormat.format(new Date(startTime));
    }
}