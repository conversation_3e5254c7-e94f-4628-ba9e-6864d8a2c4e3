package com.databuff.common.tsdb.builder;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.tsdb.model.AggFun;
import com.databuff.common.tsdb.model.Aggregation;
import com.databuff.common.tsdb.model.Where;
import com.databuff.common.tsdb.model.WhereOp;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

// 具体的 MoreDB 查询构建类
public class MoreDBQueryBuilder extends AbstractQueryBuilder {


    // MoreDB 聚合函数映射
    private static final Map<AggFun, String> FUNCTIONS = new HashMap<>();
    // MoreDB WHERE 操作符映射
    private static final Map<WhereOp, String> OPERATORS = new HashMap<>();
    // MoreDB TOP聚合函数映射
    private static final Map<AggFun, String> TOP_FUNCTIONS = new HashMap<>();
    static {
        TOP_FUNCTIONS.put(AggFun.MEAN, "t_mean");
        TOP_FUNCTIONS.put(AggFun.SUM, "t_sum");
        TOP_FUNCTIONS.put(AggFun.MAX, "t_max");
        TOP_FUNCTIONS.put(AggFun.MIN, "t_min");
        TOP_FUNCTIONS.put(AggFun.LAST, "t_last");

        FUNCTIONS.put(AggFun.MEAN, "mean");
        FUNCTIONS.put(AggFun.AVG, "avg");
        FUNCTIONS.put(AggFun.COUNT, "count");
        FUNCTIONS.put(AggFun.SUM, "sum");
        FUNCTIONS.put(AggFun.PERCENTILE, "upper");
        FUNCTIONS.put(AggFun.MAX, "max");
        FUNCTIONS.put(AggFun.MIN, "min");
        FUNCTIONS.put(AggFun.LAST, "last");

        // WHERE 操作符映射
        OPERATORS.put(WhereOp.EQ, "=");
        OPERATORS.put(WhereOp.NEQ, "!=");
        OPERATORS.put(WhereOp.LT, "<");
        OPERATORS.put(WhereOp.GT, ">");
        OPERATORS.put(WhereOp.LTE, "<=");
        OPERATORS.put(WhereOp.GTE, ">=");
    }

    /**
     * 用于正则表达式转义的模式，包含所有需要转义的特殊字符：
     * \ - 反斜杠
     * " - 双引号
     * ! - 感叹号
     * # - 井号
     * $ - 美元符号
     * % - 百分号
     * & - 和号
     * ' - 单引号
     * ( - 左括号
     * ) - 右括号
     * * - 星号
     * + - 加号
     * , - 逗号
     * - - 减号/连字符
     * . - 点号
     * / - 斜杠
     * : - 冒号
     * ; - 分号
     * < - 小于号
     * = - 等号
     * > - 大于号
     * ? - 问号
     * @ - 艾特符号
     * [ - 左方括号
     * ] - 右方括号
     * ^ - 脱字符
     * _ - 下划线
     * ` - 反引号
     * { - 左花括号
     * | - 竖线
     * } - 右花括号
     * ~ - 波浪号
     */
    // 用于正则表达式转义的模式，包含所有需要转义的特殊字符
    private static final String REGEX_SPECIAL_CHARS = "[\\\\\"!#$%&'()*+,-./:;<=>?@\\[\\]^_`{|}~]";
    private static final String REPLACEMENT = "\\\\$0";

    public MoreDBQueryBuilder(QueryBuilder queryBuilder) {
        super(queryBuilder);
    }

    @Override
    public void appendDatabaseSpecificLogic(StringBuilder query, AggFun tsAgg, AggFun valAgg, AggFun topAgg, JSONObject otherParam) {
        // 添加 MoreDB 特定的 SQL 语法逻辑
        List<Aggregation> aggregations = queryBuilder.getAggregations();
        if (aggregations == null || aggregations.isEmpty()) {
            return;
        }
        StringBuilder selectFields = new StringBuilder();
        for (Aggregation aggregation : aggregations) {
            String field = aggregation.getField();
            String alias = aggregation.getAlias();
            if (field == null || field.trim().isEmpty()) {
                throw new IllegalArgumentException("Field name cannot be null or empty.");
            }
            //todo 是否有些特殊字段需要转义
            String aggField =  field ;
            if (tsAgg != null) {
                String upperFunction =FUNCTIONS.getOrDefault(tsAgg, tsAgg.name());
                aggField = upperFunction + "(" + aggField + ")";
            }
            if (valAgg != null) {
                String upperFunction =FUNCTIONS.getOrDefault(valAgg, valAgg.name());
                aggField = upperFunction + "(" + aggField + ")";
            }
            if (topAgg != null) {
                String upperFunction =TOP_FUNCTIONS.getOrDefault(topAgg, topAgg.name());
                aggField = upperFunction + "(" + aggField + ")";
            }
            if (alias == null || alias.trim().isEmpty()) {
                selectFields.append(aggField).append(",");
            }else{
                // 避免别名与 SQL 关键字冲突
                selectFields.append(aggField).append(" AS \"").append(alias).append("\",");
            }
        }
        query.append(selectFields.substring(0, selectFields.length() - 1));

    }
    @Override
    public String getSpecificAggregationFunction(Aggregation aggregation) {
        AggFun function = aggregation.getFunction();
        if (function == null) {
            return  aggregation.getField() +  aggregation.formatAlias("") ;
        }
        if (function == AggFun.PERCENTILE) {
            //百分位的特殊处理 不需要要加“”
            return FUNCTIONS.getOrDefault(function, function.name()) + "(" + aggregation.getField() + ")" + aggregation.formatAlias("") ;
        }
        return aggregation.getAggregationFunction(FUNCTIONS.getOrDefault(function, function.name()));
    }

    @Override
    public void appendSpecificWheres(StringBuilder query) {
        if (queryBuilder.getWheres() != null && !queryBuilder.getWheres().isEmpty()) {
            query.append(queryBuilder.getWheres().stream()
                    .map(this::convertWhereConditions)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.joining(" AND ")));
        }
    }

    @Override
    public void appendSpecificInterval(StringBuilder query) {
        Integer interval = queryBuilder.getInterval();
        String intervalUnit = queryBuilder.getIntervalUnit();
        if (interval != null && interval != 0) {
            List<String> groupBy = queryBuilder.getGroupBy();
            if (groupBy != null && !groupBy.isEmpty()) {
                query.append(",time(").append(interval).append(intervalUnit).append(") ");
            }else{
                query.append(" GROUP BY time(").append(interval).append(intervalUnit).append(") ");
            }
        }
    }
    private String convertWhereConditions(Where condition) {
        if (condition == null) {
            return "";
        }
        // 处理嵌套 WHERE 语句
        if (CollectionUtils.isNotEmpty(condition.getSubConditions())) {
            String operator = Optional.ofNullable(condition.getLogicalOperator())
                    .map(Enum::name)
                    .orElse("AND");
            List<String> parts = condition.getSubConditions().stream()
                    .map(this::convertWhereConditions)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(parts)) {
                return "";
            }
            return "(" + String.join(" " + operator + " ", parts) + ")";
        }
        if (StringUtils.isBlank(condition.getField())) {
            return "";
        }
        WhereOp whereOp = condition.getOperator();
        final Boolean caseInsensitive = condition.getCaseInsensitive();
        final String caseInsensitiveLIKEStr = (caseInsensitive != null && caseInsensitive) ? "(?i)" : "";
        final String caseInsensitiveEQStr = (caseInsensitive != null && caseInsensitive) ? "*(?i)" : "";
        String operator = OPERATORS.getOrDefault(whereOp, condition.getOperator().name());
        operator = operator.trim();
        String field = condition.getField();
        Object value = condition.getValue();
        if (value == null) {
            value =  "NULL"; // 处理 null 值，防止 SQL 语法错误
        }else if (value instanceof Boolean) {
            value = String.format("%s", (Boolean) value ? 1 : 0);
        }
        switch (whereOp) {
            case LIKE:
                return "\"" + field + "\" = " + dealSpecialChar("*.*" + caseInsensitiveLIKEStr + escapeSpecialChar(value.toString()) + ".*");
            case NOT_LIKE:
                return "\"" + field + "\" != " + dealSpecialChar("*.*" + caseInsensitiveLIKEStr + escapeSpecialChar(value.toString()) + ".*");
            case START_WITH:
                return "\"" + field + "\" = " + dealSpecialChar(  escapeSpecialChar(value.toString()) + "*");
            case END_WITH:
                return "\"" + field + "\" = " + dealSpecialChar("*.*" + caseInsensitiveLIKEStr + escapeSpecialChar(value.toString()));
            case REGEX:
                return "\"" + field + "\" = " + dealSpecialChar("*" + caseInsensitiveLIKEStr + escapeSpecialChar(value.toString()));
            case IS:
                return "\""+field + "\" = '' " ;
            case IS_NOT:
                return "\""+field + "\" != '' " ;
            case IN:
            case NOT_IN:
                return formatInCondition(field, whereOp, value, caseInsensitiveEQStr);
            default:
                if ("TIME".equals(field.toUpperCase())) {
                    return field+ " " + operator + " " + value;
                }
                return "\"" + field + "\" " + operator + " " + formatValueForSQL(value.toString(), caseInsensitiveEQStr);
        }
    }

    private String formatInCondition(String field, WhereOp whereOp, Object value, String caseInsensitiveStr) {
        String inOperator = whereOp == WhereOp.IN ? "IN" : "NOT IN";

        if (value == null) {
            return "\"" + field + "\" " + inOperator + " (NULL)";
        }

        List<String> values;
        if (value instanceof String) {
            values = Arrays.asList(((String) value).split(","));
        } else if (value instanceof Collection<?>) {
            // 尝试转换 List<?> 为 List<String>
            values = ((Collection<?>) value).stream()
                    .map(obj -> {
                        if (obj instanceof String || obj instanceof Integer || obj instanceof Double) {
                            return String.valueOf(obj);
                        } else {
                            throw new IllegalArgumentException("IN 条件的值必须是 String、Collection<String>  或 Collection<Integer> 或 Collection<Double>");
                        }
                    })
                    .collect(Collectors.toList());
        } else {
            throw new IllegalArgumentException("IN 条件的值必须是 String、Collection<String>  或 Collection<Integer> 或 Collection<Double>");
        }
        if (values.isEmpty()) {
            // 如果 IN 条件的值为空，则直接返回空字符串
            return "";
        }
        // moredb in语法不支持正则表达式，所以不能使用caseInsensitiveStr
        return "\"" + field + "\" " + inOperator + " (" +
                values.stream().map(v -> formatValueForSQL(v, "")).collect(Collectors.joining(", ")) + ")";
    }

    //处理单双引号问题
    private String dealSpecialChar(String value) {
        // 如果包含 `"`，则用单引号 `'` 包裹，否则用双引号 `"`
        if (value.contains("\"")) {
            return "'" + value + "'";
        } else {
            return "\"" + value + "\"";
        }
    }

    /**
     * 转义特殊字符处理
     * 根据参数控制是否只转义正则表达式中的特殊字符。
     * 如果特殊字符已经被转义（前面有反斜杠），则不会再次转义。
     *
     * 当 regexOnly=true 时，仅转义以下正则表达式特殊字符：
     * \ $ ^ . * + ? | ( ) [ ] { }
     *
     * 当 regexOnly=false 时，额外转义 MoreDB 特定字符：
     * * (星号)
     *
     * 转义示例：
     *
     * regexOnly=true 的情况:
     * 输入: "user.name+filter(value)"
     * 输出: "user\.name\+filter\(value\)"
     *
     * 输入: "select \* from table"（已转义的星号）
     * 输出: "select \* from table"（保持不变）
     *
     * regexOnly=false 的情况:
     * 输入: "select * from table"（未转义的星号）
     * 输出: "select \* from table"（星号被转义）
     *
     * 已转义字符不会再次转义:
     * 输入: "value\[0-9\]"
     * 输出: "value\[0-9\]"（保持不变）
     *
     * @param input 需要转义的输入字符串
     * @param regexOnly 如果为true，则仅转义正则表达式中的特殊字符；如果为false，则额外转义MoreDB特定字符
     * @return 转义后的字符串
     */
    public String escapeSpecialChar(String input, boolean regexOnly) {
        if (StringUtils.isBlank(input)) {
            return input;
        }

        // 创建结果字符串构建器
        StringBuilder result = new StringBuilder(input.length() * 2);

        // 逐字符遍历输入字符串
        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);

            // 检查当前字符是否是反斜杠，如果是则检查下一个字符
            if (c == '\\' && i + 1 < input.length()) {
                char nextChar = input.charAt(i + 1);
                // 检查下一个字符是否是特殊字符
                if ((regexOnly && isSpecialChar(nextChar)) ||
                        (!regexOnly && ismoreDBSpecialChar(nextChar))) {
                    // 如果是已转义的特殊字符，保留原样
                    result.append(c).append(nextChar);
                    i++; // 跳过下一个字符，因为已经处理过了
                    continue;
                }
            }

            // 如果当前字符是特殊字符但没有被转义，则进行转义
            if ((regexOnly && isSpecialChar(c)) ||
                    (!regexOnly && ismoreDBSpecialChar(c))) {
                result.append('\\');
            }

            // 添加当前字符
            result.append(c);
        }

        return result.toString();
    }

    public String escapeSpecialChar(String input) {
        // 默认只转义正则表达式特殊字符
        return escapeSpecialChar(input, true);
    }

    /**
     * 检查字符是否是需要转义的特殊字符
     *
     * @param c 要检查的字符
     * @return 如果是特殊字符返回true，否则返回false
     */
    private boolean isSpecialChar(char c) {
        // 只对正则表达式中的特殊字符返回true
        return c == '\\' || c == '$' || c == '^' || c == '.' || c == '*' ||
                c == '+' || c == '?' || c == '|' || c == '(' || c == ')' ||
                c == '[' || c == ']' || c == '{' || c == '}';
    }

    /**
     * 检查指定字符是否为数据库特殊字符'*'.
     *
     * @param c 需要检查的字符
     * @return 如果字符是'*'则返回true，否则返回false
     */
    private boolean ismoreDBSpecialChar(char c) {
        return c == '*';
    }

    private String formatValueForSQL(String value, String caseInsensitiveStr) {
        // 如果包含 `"`，则用单引号 `'` 包裹，否则用双引号 `"`
        return StringUtils.isBlank(caseInsensitiveStr) ? dealSpecialChar(escapeSpecialChar(value, false)) : dealSpecialChar(caseInsensitiveStr + escapeSpecialChar(value));
    }
}