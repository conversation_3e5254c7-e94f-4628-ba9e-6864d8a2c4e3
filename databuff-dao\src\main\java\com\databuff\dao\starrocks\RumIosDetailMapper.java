package com.databuff.dao.starrocks;

import com.databuff.entity.rum.starrocks.*;
import com.databuff.entity.rum.web.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface RumIosDetailMapper {

    RumIosLaunchDto selectLaunchById(@Param("request") IosLaunchDetailRequest request);

    List<RumIosLifecycleMethod> selectLifecycleMethods(
            @Param("associatedId") Long associatedId,
            @Param("type") Integer type,
            @Param("request") IosBaseDetailRequest request);

    List<RumIosLaunchSpanDto> selectLaunchSpans(@Param("request") IosLaunchDetailRequest request);

    RumIosPageDto selectPageById(@Param("request") IosPageDetailRequest request);

    List<RumIosPageSpanDto> selectPageSpans(@Param("request") IosPageDetailRequest request);

    RumIosActionDto selectActionById(@Param("request") IosActionDetailRequest request);

    List<RumIosActionSpanDto> selectActionSpans(@Param("request") IosActionDetailRequest request);

    RumIosAnrDto selectAnrById(@Param("request") IosAnrDetailRequest request);

    RumIosAnrDto selectAnrStack(@Param("request") IosAnrDetailRequest request);

    RumIosCrashDto selectCrashById(@Param("request") IosCrashDetailRequest request);

    RumIosCrashDto selectCrashStack(@Param("request") IosCrashDetailRequest request);

    List<RumIosActionDto> selectExceptionTrace(
            @Param("startTime") Date startTime,
            @Param("appId") Integer appId,
            @Param("userId") String userId);

    Long selectLaunchStart(@Param("request") IosLaunchDetailRequest request);

    Long selectPageStart(@Param("request") IosPageDetailRequest request);

    Long selectActionStart(@Param("request") IosActionDetailRequest request);
}
