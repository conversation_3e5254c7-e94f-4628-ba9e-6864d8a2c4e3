package com.databuff.dao.mysql;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.databuff.entity.*;
import com.databuff.entity.dto.LicenseModelEntry;
import com.databuff.entity.dto.LicenseProductEntry;
import com.databuff.entity.extend.DataSource;
import com.databuff.entity.extend.SaasOrg;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * UserMapper
 * <AUTHOR>
 * @date 2019/3/19 14:43
 */
@Repository
@Mapper
public interface UserMapper extends BaseMapper {
    /**
     * 判断用户是否存在，如果存在将密码返回给jwt校验
     * @param account
     * @return
     */
    User selectOne(@Param("account") String account);

    /**
     * 查询用户信息
     * @param account
     * @return
     */
    @Select("<script>\n" +
            "select id,account,nickName,mobile,remark,responsible from dc_user \n" +
            "<if test='account != null'>\n" +
            "where account=#{account}\n" +
            "</if>\n" +
            "<if test='account == null'>\n" +
            "where account='error'\n" +
            "</if>\n" +
            "</script>")
    User findUserInfoByToken(@Param("account") String account);

    /**
     * 根据用户名返回拥有的目录+权限
     * @param account
     * @return
     */
    @Select("select r.id,r.name,r.url,r.parent_id,r.hidden,r.module_function,r.icon,r.leaf,r.path,r.`order` from dc_user u \n" +
            "LEFT JOIN dc_user_role ur on u.id=ur.user_id LEFT JOIN dc_role_resources rr on rr.role_id=ur.role_id \n" +
            "RIGHT JOIN dc_resources r ON rr.resources_id=r.id where u.account=#{account} and hidden=1 order by `order` \n")
    List<Map<String,Object>> findMenuByAccount(@Param("account") String account);

    /**
     * 创建用户
     * @param user
     * @return
     */
    @Insert("INSERT INTO dc_user (account,nick_name, password, responsible,company,remark,mobile,member_phone,email_addr,create_time,lockexpire_time,update_time,passwd_tactics,locked_duration,c_id,member_tenant,huawei_imc_id) \n" +
            "VALUES (#{account},#{nickName}, #{password}, #{responsible},#{company},#{remark},#{mobile},#{memberPhone},#{emailAddr},#{createTime},#{lockexpireTime},#{updateTime}" +
            ",#{passwdTactics},#{passwdTactics},#{cId},#{memberTenant},#{huaweiImcId})")
    int createUser(User user);

    /**
     * 根据用户id删除用户权限
     * @param useId
     * @return
     */
    @Delete("delete from dc_user_role where user_id = #{userId}")
    int deleteUserAllPermis(@Param("userId") String useId);

    /**
     * 修改用户信息
     * @param user
     * @return
     */
    @Update("update dc_user set update_time=#{updateTime},responsible=#{responsible},remark=#{remark},mobile=#{mobile},email_addr=#{emailAddr} where id=#{id}")
    int updateUserInfo(User user);



    /**
     * 根据企业管理员id 获取到一个用户 得到apikey
     * @param tenant
     * @return
     */
    User selectOneImcUsre(@Param("tenant") String tenant);
    /**
     * 修改用户信息
     * @param user
     * @return
     */
    int updateImcUserInfo(User user);
    /**
     * 修改租户信息
     * @param user
     * @return
     */
    int updateTenantInfo(User user);
    /**
     * 修改huaweiImcId
     * @param account
     * @param huaweiImcId
     * @return
     */
    @Update("update dc_user set huawei_imc_id=#{huaweiImcId},company=#{company},email_addr=#{email} where account=#{account}")
    int updateImcUserId(@Param("account") String account, @Param("huaweiImcId") String huaweiImcId, @Param("company") String company, @Param("email") String email);

    /**
     * 根据华为用户信息id删除用户
     * @param huaweiImcId
     * @return
     */
    @Delete("delete from dc_user where huawei_imc_id = #{huaweiImcId}")
    int deleteUserByImcId(@Param("huaweiImcId") String huaweiImcId);
    /**
     * 修改密码
     * @param user
     * @return
     */
    @Update("update dc_user set password=#{password} where account=#{account}")
    int updateUserPass(User user);

    /**
     * 重置密码
     * @param user
     * @return
     */
    @Update("update dc_user set password=#{password} where id=#{id}")
    int resetUserPassByUserId(User user);

    /**
     * 删除用户
     * @param id
     * @return
     */
    @Delete("delete  from dc_user  where id=#{id}")
    int deleteUserByUserId(@Param("id") String id);

    /**
     * 查询用户统计
     * @return
     */
    @Select("select count(1) as count from dc_user")
    int selectUserCounter();

    /**
     * 查找统计授权产品
     */
    @Select("select count(*) from dc_license_product where license_product_serialnum = #{machineCodes} and license_product_endtime > #{endDate}")
    int findLicenseCount(@Param(value = "endDate") long endDate, @Param(value = "machineCodes") String machineCodes);

    @Select("select license_product_serialnum from dc_license_product order by id desc limit 1")
    String findOldLicenseSerialnum();

    /**
     * 根据用户查找用户信息
     * @param account
     * @return
     */

    User getUserInfo(@Param(value = "account") String account);

    /**
     * 根据角色名查询用户列表
     * @return
     */
    @Select("SELECT DISTINCT du.account  FROM dc_user du left join dc_user_role dur  on du.id =dur.user_id left join dc_role dr on dur.role_id = dr.id where dr.role_name = #{roleName}")
    List<String> getAccountsByRole(@Param(value = "roleName") String roleName);

    /**
     * 根据用户id查找用户信息
     */
    @Select("select account,responsible,mobile,company from dc_user where id=#{id} ")
    User getUserById(@Param(value = "id") String id);

    /**
     * 修改用户联系方式
     * @param account
     * @param mobile
     * @return
     */
    @Update("update dc_user set mobile=#{mobile} where account=#{account}")
    int updateUserMobile(@Param("account") String account, @Param("mobile") String mobile);

    /**
     * 创建角色
     * @param role
     */
    void createRole(Role role);

    /**
     * 根据角色名称查找角色id
     * @param roleName
     * @return
     */
    @Select("select id,role_name as `name`,available,description,pid  from dc_role where role_name=#{role_name} and available=0")
    Role findRoleIdByRoleName(@Param("role_name") String roleName);


    /**
     * 根据角色id查找角色
     * @param roleId
     * @return
     */
    @Select("select id,role_name as `name`,available,description,pid from dc_role where id=#{roleId} and available=0")
    Role findRoleByRoleId(@Param("roleId") Integer roleId);

    /**
     * 创建角色权限
     * @param rolePermissionList
     */
    @Insert("<script>\n" +
            "insert into dc_role_resources (role_id,resources_id,create_time,update_time) Values \n" +
            "<foreach collection=\"rolePermissionList\" item=\"item\" index=\"index\" separator=\",\">\n" +
            "(#{item.roleId},#{item.permissionId},#{item.createTime},#{item.updateTime})\n" +
            "</foreach>\n" +
            "</script>")
    void createRolePermis(@Param("rolePermissionList") List<RolePermission> rolePermissionList);

    /**
     * 查询所有角色
     * @param keyword
     * @param defineType
     * @return
     */
    @Select("<script>\n" +
            "select id,role_name,description,define_type,create_time,update_time,available,pid from dc_role where available=0 \n" +
            "<if test=\"defineType != null\">\n" +
            "and define_type = #{defineType}\n" +
            "</if>\n" +
            "<if test=\"keyword != null\">\n" +
            "and role_name LIKE binary CONCAT('%',#{keyword},'%') \n" +
            "</if>\n" +
            "</script>")
    @Results(id = "roleMap",value = {
            @Result(id = true,column = "id",property = "id"),
            @Result(column = "role_name",property = "name"),
            @Result(column = "define_type",property = "defineType"),
            @Result(column = "create_time",property = "createTime"),
            @Result(column = "update_time",property = "updateTime"),
            @Result(column = "pid",property = "pid"),
    })
    List<Role> findAllRole(@Param("keyword") String keyword, @Param("defineType") String defineType);


    List<RoleRet> findRoles(@Param("pid") Integer pid,@Param("keyword") String keyword, @Param("defineType") String defineType);

    /**
     * 根据用户名称查找角色
     * @param account
     * @return
     */
    List<RoleRet> findRoleByUserName(@Param(value = "account") String account);
    /**
     * 创建用户角色关系
     * @param userRole
     */
    @Insert("insert into dc_user_role (user_id,role_id,create_time,update_time) values (#{userId},#{roleId},#{createTime},#{updateTime})")
    void createUserRole(UserRole userRole);

    /**
     * 根据角色ID查找权限资源
     * @param role_id
     * @return
     */
    @Select("select resources_id from dc_role_resources where role_id=#{role_id}")
    List<Integer> getPermisByRoleId(@Param("role_id") String role_id);

    /**
     * 更新角色
     * @param role
     */
    @Update("<script>\n" +
    "update dc_role set role_name=#{name},description=#{description},define_type=#{defineType},update_time=#{updateTime}\n" +
    "<if test=\"pid != null\">\n" +
    ", pid = #{pid}\n" +
    "</if>\n" +
    "where id=#{id}\n" +
    "</script>"
    )
    void updateRole(Role role);

    /**
     * 删除角色，更新为不可用
     * @param role
     */
    @Update("update dc_role set available=1,update_time=#{updateTime} where id=#{id}")
    void delRoleById(Role role);

    /**
     * 删除角色权限
     * @param role_id
     */
    @Delete("delete from dc_role_resources where role_id=#{role_id}")
    void deleteRoleAllPermis(@Param("role_id") String role_id);

    /**
     * 删除角色权限
     * @param role_id
     */
    @Delete("delete from dc_role_resources where role_id=#{role_id} and resources_id=#{resources_id}")
    void deleteRolePermis(@Param("role_id") Integer role_id,@Param("resources_id") Integer resources_id);

    /**
     * 删除用户角色关系,根据角色id
     * @param role_id
     */
    @Delete("delete from dc_user_role where role_id=#{role_id}")
    void deleteRoleUserByRoleId(@Param("role_id") String role_id);

    /**
     * 删除用户角色关系,根据用户id
     * @param user_id
     */
    @Delete("delete from dc_user_role where user_id=#{user_id}")
    void deleteRoleUserByUserId(String user_id);

    /**
     * 查询任一用户的属性
     * @return
     */
    @Select("select passwd_tactics passwdTactics,locked_duration lockedDuration from dc_user limit 1")
    User findOneUser();

    /**
     * 更新累计密码错误次数及锁定时长
     * @param account
     * @param pdErrors
     * @param lockTime
     */
    @Update("<script>\n" +
            "update dc_user \n" +
            "<set>\n" +
            "<if test=\"pdErrors!=null\">\n" +
            "passwd_errors = #{pdErrors},\n" +
            "</if>\n" +
            "<if test=\"lockTime!=null\">\n" +
            "lockexpire_time = #{lockTime},\n" +
            "</if>\n" +
            "</set>\n" +
            "where account=#{account}" +
            "</script>")
    void updateUserOfPDErrorsAndLockTime(@Param("account") String account, @Param("pdErrors") long pdErrors, @Param("lockTime") Date lockTime);

    /**
     * 返回用户列表
     * @param user
     * @return
     */
    List<User> selectAllUserList(User user);

    /**
     * 获取token过期时长
     * @return
     */
    @Select("SELECT page_time_out FROM dc_sysconfig_base limit 1")
    int selectPageOutTime();

    /**
     * 查询授权产品
     *
     * @return
     */
    @Select("select * from dc_license_product order by id desc limit 1")
    @Results(id = "licenseProduct", value = {
            @Result(id = true, column = "id", property = "id"),
            @Result(column = "license_product_name", property = "licenseProductName"),
            @Result(column = "license_product_version", property = "licenseProductVersion"),
            @Result(column = "license_product_serialnum", property = "licenseProductSerialnum"),
            @Result(column = "license_product_status", property = "licenseProductStatus"),
            @Result(column = "license_product_starttime", property = "licenseProductStarttime"),
            @Result(column = "license_product_endtime", property = "licenseProductEndtime"),
            @Result(column = "license_product_path", property = "licenseProductPath"),
            @Result(column = "expire_limit", property = "expireLimit"),
            @Result(column = "create_time", property = "createTime"),
            @Result(column = "update_time", property = "updateTime")
    })
    LicenseProductEntry findLicenseProduct();

    /**
     * 查询授权模块
     *
     * @param id 授权产品ID
     * @return
     */
    @Select("select * from dc_license_model where license_product_id=#{id}")
    @Results(id = "licenseModel", value = {
            @Result(id = true, column = "id", property = "id"),
            @Result(column = "license_product_id", property = "licenseProductId"),
            @Result(column = "license_model_name", property = "licenseModelName"),
            @Result(column = "license_model_identify", property = "licenseModelIdentify"),
            @Result(column = "license_model_starttime", property = "licenseModelStarttime"),
            @Result(column = "license_model_endtime", property = "licenseModelEndtime"),
            @Result(column = "create_time", property = "createTime"),
            @Result(column = "update_time", property = "updateTime")
    })
    List<LicenseModelEntry> findAllLicenseModelByLicenseProductId(Integer id);

    /**
     * 根据机器码与uuid查询统计
     *
     * @param serialNum 机器码
     * @param uuid
     * @return
     */
    @Select("select count(*) from dc_license_log where license_log_serialnum=#{serialNum} and license_log_identify=#{uuid}")
    int getLicenseFileLog(@Param(value = "serialNum") String serialNum, @Param(value = "uuid") String uuid);


    /**
     * 添加license日志
     *
     * @param serialNum 机器码
     * @param uuid  uuid
     */
    @Insert("insert into dc_license_log(license_log_serialnum,license_log_identify) values(#{serialNum},#{uuid})")
    void saveLicenseFileLog(@Param(value = "serialNum") String serialNum, @Param(value = "uuid") String uuid);


    /**
     * 删除授权产品
     */
    @Delete("delete from dc_license_product where license_product_serialnum = #{licenseProductSerialnum}")
    void delLicenseProductBySerialnum(LicenseProductEntry productEntry);
    /**
     * 将授权产品的状态改为2删除 0未授权 1已授权 2已删除
     */
    @Delete("update dc_license_product set license_product_status = 2 where license_product_name = #{licenseProductName}")
    void updateDelLicenseProduct(LicenseProductEntry productEntry);

    /**
     * 保存授权产品
     */
    void saveLicenseProduct(LicenseProductEntry productEntry);

    /**
     * 删除授权模块
     */
    @Delete("delete from dc_license_model where license_product_id in (select id from dc_license_product where license_product_name = #{licenseProductName})")
    void delLicenseModelEntries(LicenseProductEntry productEntry);

    /**
     * 保存授权模块
     */
    @Insert("<script>" +
            "insert into dc_license_model(license_product_id,license_model_name,license_model_identify,license_model_starttime,license_model_endtime,license_module_function) values" +
            "<foreach collection=\"modelEntries\" item=\"item\" index=\"index\" separator=\",\" > " +
            "(#{item.licenseProductId},#{item.licenseModelName},#{item.licenseModelIdentify},#{item.licenseModelStarttime},#{item.licenseModelEndtime},#{item.licenseModelFunction})" +
            "</foreach>\n" +
            "</script>")
    void saveModelEntries(@Param(value = "modelEntries") List<LicenseModelEntry> modelEntries);

    /**
     * 修改资源权限，即授权开发资源权限
     */
    @Update("<script>" +
            "update dc_resources set hidden=0,module_function=''; " +
            "<foreach collection=\"modelEntries\" item=\"item\" index=\"index\" separator=\";\" > " +
            "update dc_resources set hidden=1,module_function=#{item.licenseModelFunction} where path = #{item.licenseModelIdentify}\n" +
            "</foreach>\n" +
            "</script>")
    void updateResources(@Param(value = "modelEntries") List<LicenseModelEntry> modelEntries);


    /**
     * 查询校验码
     *
     * @param machinecode
     * @return
     */
    @Select("select qr_code_num from dc_qr_code where machine_code=#{machinecode}")
    Integer getCounter(String machinecode);

    /**
     * 保存校验码
     *
     * @param machinecode
     * @param num
     */
    @Insert("insert into dc_qr_code(machine_code,qr_code_num) values(#{machinecode},#{num})")
    void addCounter(@Param(value = "machinecode") String machinecode, @Param(value = "num") int num);


    /**
     * 修改校验码
     */
    @Update("update dc_qr_code set qr_code_num=#{num} where machine_code=#{machinecode}")
    void updateCounter(@Param(value = "machinecode") String machinecode, @Param(value = "num") int num);

    /**
     * 添加租户
     * @param saasTenant
     */
    void addTenant(SaasTenant saasTenant);

    /**
     * 根据apiKey修改租户的lic到期时间
     * @param apiKey
     * @param endTime
     */
    void updateTenantLic(@Param("apiKey") String apiKey, @Param("endTime") Date endTime);

    /**
     * 增加org id记录
     * @param org
     */
    void addOrg(SaasOrg org);

    /**
     * 增加仪表盘数据源
     * @param dataSource
     */
    void addDataSource(DataSource dataSource);
    /**
     * 插入仪表盘
     * @param dashboard
     */
    void addDashboard(SaasDashboard dashboard);

    /**
     * 根据apiKey
     * @param apiKey
     * @return
     */
    DataSource getGraDataSourceByApikey(@Param("apiKey") String apiKey);


    List<DataSource> getGraDataSourcesByApikey(@Param("apiKey") String apiKey);

    /**
     * 根据org id 查询首页仪表盘id
     * @param orgId
     * @return
     */
    SaasDashboard getFontDashboardIdByOrgId(@Param("orgId") Long orgId,@Param("title") String title);
    /**
     * 根据apiKey查找orgId
     * @param apiKey
     * @return
     */
    @Select("select id from `org` where `name`=#{apiKey}")
    Long getOrgIdByApiKey(@Param("apiKey") String apiKey);


    /**
     * 统计平台账户
     * @return
     */
    @Select("select count(id) from dc_platform_user")
    int selectPlatformUserCounter();

    /**
     * 查找平台侧Admin账户
     * @param account
     * @return
     */
    @Select("select id from dc_platform_user where `account` = #{account}")
    User selectPlatformUser(@Param("account") String account);

    /**
     * 插入其他限制信息
     * @param otherConfine
     */
    void addSaasOtherConfine(SaasOtherConfine otherConfine);

    /**
     * 根据apiKey查询限制
     * @param apiKey
     * @return
     */
    SaasOtherConfine getConfineByApiKey(@Param("apiKey") String apiKey);


    void insertAuthPlugins(MetaConfigEntity metaConfig);

    void updateAuthPlugins(MetaConfigEntity metaConfig);

    MetaConfigEntity getAuthPluginsConfig(@Param("code") String code);

    void deleteAllAAU(String apiKey);

    /**
     * 根据apiKey, orgId, type1, database删除DataSource数据
     * @param orgId 组织ID
     * @param type1 类型1
     * @param database 数据库名称
     * @return 删除的记录数
     */
    @Delete("DELETE FROM data_source WHERE org_id = #{orgId} AND name = #{type1} AND `database` = #{database}")
    int deleteDataSourceByType(@Param("orgId") Long orgId, @Param("type1") String type1, @Param("database") String database);
}