package com.databuff.dao.mysql;

import com.databuff.entity.rum.mysql.AndroidSymbolFile;
import com.databuff.entity.rum.web.AndroidSymbolSearchCriteria;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface RumAndroidSymbolFileMapper {
    void insert(AndroidSymbolFile symbolFile);

    List<AndroidSymbolFile> getSymbolFiles(AndroidSymbolSearchCriteria search);

    int deleteSymbolFiles(@Param("ids") List<Long> ids);

    List<AndroidSymbolFile> getSymbolFilesByIds(@Param("ids") List<Long> ids);

    AndroidSymbolFile getSymbolFileById(@Param("id") Long id);

    AndroidSymbolFile getByAppVersionAndBuild(@Param("appId") Integer appId,
                                              @Param("versionCode") String versionCode,
                                              @Param("versionName") String versionName);


    int updateByVersionCodeAndName(AndroidSymbolFile symbolFile);


}
