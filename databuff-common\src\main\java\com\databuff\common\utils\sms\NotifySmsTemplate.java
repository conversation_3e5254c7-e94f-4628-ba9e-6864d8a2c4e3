package com.databuff.common.utils.sms;

import lombok.Data;

import java.io.Serializable;

@Data
public class NotifySmsTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;
    /**
     * api_key
     */
    private String apiKey;
    /**
     * notify_config_id
     */
    private Integer notifyConfigId;

    /**
     * 通知类别
     */
    private String smsNotifyType;

    /**
     * 短信模板id
     */
    private String smsTemplateId;

    /**
     * 短信模板内容
     */
    private String smsTemplateContent;


    /**
     * 短信签名名称
     */
    private String smsSignName;
}
