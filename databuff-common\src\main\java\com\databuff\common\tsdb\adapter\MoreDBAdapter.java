package com.databuff.common.tsdb.adapter;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.metric.dto.Query;
import com.databuff.common.tsdb.builder.MoreDBQueryBuilder;
import com.databuff.common.tsdb.builder.QueryBuilder;
import com.databuff.common.tsdb.model.*;
import com.databuff.common.tsdb.pool.TSDBConnectPool;
import com.databuff.common.tsdb.wrapper.DatabaseWrapper;
import com.databuff.common.tsdb.wrapper.MoreDBWrapper;
import com.databuff.common.utils.TSDBPointUtil;
import com.databuff.moredb.proto.Common;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
public class MoreDBAdapter implements DatabaseAdapter {

    protected TSDBConnectPool pool;

    public MoreDBAdapter(TSDBConnectPool pool) {
        this.pool = pool;
    }

    @Override
    public TSDBResultSet executeQuery(DatabaseWrapper dbClient, String sql, String databaseName, QueryBuilder builder) {
        try {
            MoreDBWrapper moreDB = (MoreDBWrapper) dbClient;
            return moreDB.query(new Query(sql, databaseName), TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error("查询数据失败,databaseName:{},sql:{},error:", databaseName, sql, e);
            throw new RuntimeException("查询数据失败", e);
        }
    }

    /**
     * 执行查询，返回标签值集合
     *
     * @param dbClient
     * @param query        moredb格式的SHOW TAG VALUES查询语句
     * @param databaseName 目标数据库名称
     * @return 标签名称到其对应值集合的映射，键为标签名，值为该标签的所有可能值集合
     */
    @Override
    public Map<String, Set<String>> executeShowTagValues(DatabaseWrapper dbClient, String query, String databaseName) {
        Map<String, Set<String>> result = new HashMap<>();

        try {
            // 获取数据库连接并记录连接耗时
            MoreDBWrapper moreDB = (MoreDBWrapper) dbClient;
            if (moreDB == null) {
                throw new RuntimeException("无法获取数据库连接");
            }
            // 执行查询并获取结果集
            log.debug("MoreDB executing show tag values query: {}, database: {}", query, databaseName);
            TSDBResultSet tsdbResultSet = moreDB.query(new Query(query, databaseName), TimeUnit.MILLISECONDS);

            // 验证结果集有效性
            if (tsdbResultSet == null || tsdbResultSet.hasError() || tsdbResultSet.getResults() == null) {
                log.warn("查询标签值失败: {}", tsdbResultSet != null ? tsdbResultSet.getError() : "结果为空");
                return result;
            }

            // 遍历结果集提取标签值
            for (TSDBResult tsdbResult : tsdbResultSet.getResults()) {
                if (tsdbResult.hasError() || CollectionUtils.isEmpty(tsdbResult.getSeries())) {
                    continue;
                }

                for (TSDBSeries series : tsdbResult.getSeries()) {
                    if (series == null || CollectionUtils.isEmpty(series.getValues())) {
                        continue;
                    }

                    Map<String, String> seriesTags = series.getTags();
                    if (seriesTags == null) {
                        continue;
                    }

                    // 将标签键值对合并到结果映射
                    for (Map.Entry<String, String> entry : seriesTags.entrySet()) {
                        final String key = entry.getKey();
                        final String value = entry.getValue();
                        if (StringUtils.isNotEmpty(value)) {
                            result.computeIfAbsent(key, k -> new HashSet<>()).add(value);
                        }
                    }
                }
            }

            return result;
        } catch (Exception e) {
            // 异常处理与指标记录
            log.error("查询标签值失败,databaseName:{},query:{},error:", databaseName, query, e);
            throw new RuntimeException("查询标签值失败", e);
        }
    }


    @Override
    public Collection<String> executeShowTagKeys(DatabaseWrapper dbClient, String sql, String databaseName) {
        try {
            MoreDBWrapper moreDB = (MoreDBWrapper) dbClient;
            if (moreDB == null) {
                throw new RuntimeException("无法获取数据库连接");
            }
            return moreDB.showTagKeys(new Query(sql, databaseName));
        } catch (Exception e) {
            log.error("查询数据失败,databaseName:{},sql:{}, error: {}", databaseName, sql, e);
            throw new RuntimeException("查询数据失败", e);
        }
    }

    @Override
    public boolean createDatabase(DatabaseWrapper dbClient, TSDBDatabaseInfo databaseInfo) {
        if (databaseInfo == null || StringUtils.isBlank(databaseInfo.getDatabaseName())) {
            throw new IllegalArgumentException("数据库信息不能为空，且 databaseName 不能为空");
        }
        try {
            MoreDBWrapper moreDB = (MoreDBWrapper) dbClient;
            if (!moreDB.databaseExists(databaseInfo.getDatabaseName())) {
                log.info("创建数据库: {}", databaseInfo.getDatabaseName());
                moreDB.createDatabase(
                        databaseInfo.getDatabaseName(),
                        databaseInfo.getUserName(),
                        databaseInfo.getPassword(),
                        databaseInfo.getShard(),
                        databaseInfo.getReplication(),
                        databaseInfo.getKeepDay(),
                        databaseInfo.getInterval()
                );
                log.info("数据库创建成功: {}", databaseInfo.getDatabaseName());
            } else {
                log.info("数据库 {} 已存在", databaseInfo.getDatabaseName());
            }
        } catch (Exception e) {
            log.error("创建MoreDB数据库失败", e);
            return false;
        }
        return true;
    }

    @Override
    public boolean writePoints(DatabaseWrapper dbClient, String databaseName, List<TSDBPoint> tsdbPoints) {

        List<Common.Point> points = new ArrayList<>();
        for (TSDBPoint tsdbPoint : tsdbPoints) {
            if (StringUtils.isBlank(tsdbPoint.getMeasurement())) {
                throw new RuntimeException("No measurement defined");
            }
            points.addAll(toPointList(tsdbPoint));
        }
        if (CollectionUtils.isEmpty(points)) {
            return true;
        }
        try {
            MoreDBWrapper moreDB = (MoreDBWrapper) dbClient;
            long getClientTime = System.currentTimeMillis();
            moreDB.write(databaseName, points);
        } catch (Throwable e) {
            log.error("插入数据失败", e);
            return false;
        }
        return true;
    }

    @Override
    public String buildFullSql(QueryBuilder builder) {
        return new MoreDBQueryBuilder(builder).buildQuery();
    }

    /**
     * 构建显示标签值的SQL语句。
     *
     * @param dbClient
     * @param builder  查询构建器，包含测量名、数据库名、分组字段和聚合参数
     * @return 生成的SQL语句字符串，若失败返回null
     */
    @Override
    public Map<String, String> buildShowTagValueSql(DatabaseWrapper dbClient, QueryBuilder builder) {
        try {
            // 获取测量名并创建查询标签键的SQL语句
            final String measurement = builder.getMeasurement();
            String tagSql = createShowStatement("tag keys", measurement);
            final String databaseName = builder.getDatabaseName();
            Collection<String> tagKeys = this.executeShowTagKeys(dbClient, tagSql, databaseName);
            if (tagKeys == null) {
                throw new RuntimeException("查询tag keys失败");
            }

            // 处理分组字段参数，确保其有效性
            List<String> groupBy = builder.getGroupBy();
            if (groupBy == null || groupBy.isEmpty()) {
                groupBy = new ArrayList<>(tagKeys);
            }

            Set<String> tagKeysSet = new HashSet<>(tagKeys);
            Set<String> validGroupSet = new HashSet<>(groupBy);
            validGroupSet.retainAll(tagKeysSet);
            builder.setGroupBy(new ArrayList<>(validGroupSet));

            // 处理聚合参数的默认值
            List<Aggregation> aggregations = builder.getAggregations();
            if (CollectionUtils.isEmpty(aggregations)) {
                String fieldSql = createShowStatement("field keys", measurement);
                Collection<String> fieldKeys = this.executeShowTagKeys(dbClient, fieldSql, databaseName);
                if (fieldKeys == null) {
                    throw new RuntimeException("查询field keys失败");
                }

                aggregations = fieldKeys.stream()
                        .map(key -> new Aggregation(AggFun.LAST, key))
                        .collect(Collectors.toList());
                builder.setAggregations(aggregations);
            }

            // 设置查询参数并构建最终SQL语句
            builder.setInterval(null);
            builder.setLimit(1000);
            return new MoreDBQueryBuilder(builder).buildShowTagValueQueryMap();
        } catch (Exception e) {
            log.error("创建show语句失败", e);
            return null;
        }
    }

    private String createShowStatement(String type, String measurement) {
        // 对measurement进行转义处理（如替换单引号）
        String escapedMeasurement = measurement.replace("'", "''");
        return new StringBuilder("show ").append(type).append(" from '")
                .append(escapedMeasurement)
                .append("' limit 200").toString();
    }

    @Override
    public String buildWhereSql(QueryBuilder builder) {
        return new MoreDBQueryBuilder(builder).buildWhere();
    }

    @Override
    public Map<String, Object> getConfigs() {
        MoreDBWrapper moreDB = null;
        try {
            moreDB = (MoreDBWrapper) pool.getTSDBClient();
            return moreDB.getConfigs();
        } catch (Exception e) {
            log.error("获取tsdb config map error:", e);
            throw new RuntimeException("获取tsdb config map erro", e);
        } finally {
            if (moreDB != null) {
                pool.releaseConnection(moreDB);
            }
        }
    }

    @Override
    public void close() throws Exception {
//        pool.close();
    }

    @Override
    public TSDBResultSet executeQuery(DatabaseWrapper dbClient, QueryBuilder builder, AggFun tsAgg, AggFun valAgg, AggFun topAgg, JSONObject otherParam) throws Exception {
        if (builder == null) {
            log.error("builder is null");
            return null;
        }
        //检验
        builder.validate();
        MoreDBQueryBuilder moreDBQueryBuilder = new MoreDBQueryBuilder(builder);
        StringBuilder sqlBuilder = moreDBQueryBuilder.buildDiffAggSpecificSelect(tsAgg, valAgg, topAgg, otherParam);
        moreDBQueryBuilder.appendMeasurement(sqlBuilder);
        sqlBuilder.append(moreDBQueryBuilder.buildWhere());
        // 添加 GROUP BY
        moreDBQueryBuilder.appendGroupBy(sqlBuilder);
        // 时间间隔
        moreDBQueryBuilder.appendSpecificInterval(sqlBuilder);
        // ORDER BY
        moreDBQueryBuilder.appendOrderBy(sqlBuilder);
        // LIMIT / OFFSET
        moreDBQueryBuilder.appendLimitAndOffset(sqlBuilder);

        return executeQuery(dbClient, sqlBuilder.toString(), builder.getDatabaseName(), builder);
    }

    @Override
    public TSDBResultSet apmPercentageLatency(DatabaseWrapper dbClient, QueryBuilder builder) {
        //检验
        builder.validate();
        String sql = new MoreDBQueryBuilder(builder).buildQuery();
        return executeQuery(dbClient, sql, builder.getDatabaseName(), builder);
    }

    public List<Common.Point> toPointList(TSDBPoint tsdbPoint) {
        Common.Point.Builder builder = Common.Point.newBuilder();
        builder.setMeasurement(tsdbPoint.getMeasurement());
        builder.setTime(tsdbPoint.getTimestamp());

        Map<String, String> tags = tsdbPoint.getTags();
        if (tags != null) {
            tags.forEach((key, value) -> {
                if (key != null && value != null) {
                    builder.putTags(key, value);
                }
            });
        }

        Map<String, Object> fields = tsdbPoint.getFields();
        Map<String, Common.FieldType> fieldTypes = tsdbPoint.getFieldTypes();
        if (fields != null) {
            fields.forEach((key, value) -> {
                if (key != null && value != null) {
                    TSDBPointUtil.addField(builder, key, value, fieldTypes != null ? fieldTypes.get(key) : null);
                }
            });
        }

        return Arrays.asList(builder.build());
    }
}