package com.databuff.entity.enums;

import lombok.Getter;

/**
 * 指标模型枚举
 * 用于描述 metricsCore.fields 中的 metric_model 字段
 */
@Getter
public enum MetricModelEnum {

    /** 求和模型 */
    SUM("SUM", "求和"),
    /** 仪表模型 (Gauge) */
    GAUGE("GAUGE", "仪表"),
    /** 直方图模型 */
    HISTOGRAM("HISTOGRAM", "直方图");

    private final String code;
    private final String name;

    MetricModelEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据编码获取枚举实例
     *
     * @param code 枚举编码
     * @return 对应的 MetricModelEnum，找不到时返回 null
     */
    public static MetricModelEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (MetricModelEnum m : values()) {
            if (m.getCode().equals(code)) {
                return m;
            }
        }
        return null;
    }

    /**
     * 根据名称获取枚举实例
     *
     * @param name 中文名称
     * @return 对应的 MetricModelEnum，找不到时返回 null
     */
    public static MetricModelEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        for (MetricModelEnum m : values()) {
            if (m.getName().equals(name)) {
                return m;
            }
        }
        return null;
    }
}
