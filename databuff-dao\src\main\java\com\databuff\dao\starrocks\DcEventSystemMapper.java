package com.databuff.dao.starrocks;

import com.databuff.entity.dto.DCEventDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface DcEventSystemMapper {

    /**
     * 根据告警id查询事件列表
     *
     * @param alertId
     * @return
     */
    List<DCEventDto> findMonitorEventV2(@Param("alertId") Long alertId);

    /**
     * 根据事件id列表查询所有的事件ID
     *
     * @param eventIds
     * @return
     */
    List<DCEventDto> findEventListByEventIds(@Param("eventIds") String[] eventIds);

    /**
     * 根据事件id列表查询所有的事件时间
     *
     * @param eventIds
     * @return
     */
    List<DCEventDto> findEventTimeByEventIds(@Param("eventIds") String[] eventIds);

    /**
     * 根据事件ID查询事件
     *
     * @param eventId
     * @return
     */
    DCEventDto findEventByEventId(@Param("eventId") String eventId);

    /**
     * 检索符合所提供条件的 DCEventDto 对象列表。
     *
     * @param apiKey    用于身份验证的 API 密钥。
     * @param startTime 要查询的事件周期的开始时间（自纪元以来以毫秒为单位）。
     * @param endTime   要查询的事件周期的结束时间（自纪元以来以毫秒为单位）。
     * @param condition 用于事件搜索的条件。
     * @return 符合所提供条件的 DCEventDto 对象列表。
     */
    List<DCEventDto> findEventsByCondition(@Param("apiKey") String apiKey, @Param("startTime") long startTime, @Param("endTime") long endTime, @Param("condition") String condition);

    @Select({"<script>",
            "SELECT es.*,ess.`readTime`,ess.`read` " +
            "FROM dc_event_system es ",
            "LEFT JOIN dc_event_system_status ess ",
            "ON es.id = ess.id ",
            "WHERE 1 = 1 ",
            "<if test='apiKey != null'> AND apiKey = #{apiKey} </if>",
            "<if test='monitorId != null'> AND monitorId = #{monitorId} </if>",
            "<if test='classification != null and classification != \"\"'> AND classification = #{classification} </if>",
            "<if test='type != null and type != \"\"'> AND type = #{type} </if>",
            "<if test='triggerTimeStart != null and triggerTimeEnd != null'> AND triggerTime BETWEEN #{triggerTimeStart} AND #{triggerTimeEnd} </if>",
            "<if test='durationMin != null and durationMax != null'> AND duration BETWEEN #{durationMin} AND #{durationMax} </if>",
            "<if test='message != null and message != \"\"'> AND message LIKE CONCAT('%', #{message}, '%') </if>",
            "<if test='ruleName != null and ruleName != \"\"'> AND ruleName LIKE CONCAT('%', #{ruleName}, '%') </if>",
            "<if test='levels != null and levels.size() > 0'> AND level IN <foreach item='item' index='index' collection='levels' open='(' separator=',' close=')'>#{item}</foreach> </if>",
            "</script>"})
    List<DCEventDto> findEventsByMultipleConditions(@Param("apiKey") String apiKey,
                                                    @Param("monitorId") String monitorId,
                                                    @Param("classification") String classification,
                                                    @Param("type") String type,
                                                    @Param("triggerTimeStart") Long triggerTimeStart,
                                                    @Param("triggerTimeEnd") Long triggerTimeEnd,
                                                    @Param("durationMin") Double durationMin,
                                                    @Param("durationMax") Double durationMax,
                                                    @Param("message") String message,
                                                    @Param("ruleName") String ruleName,
                                                    @Param("levels") List<Integer> levels);

}
