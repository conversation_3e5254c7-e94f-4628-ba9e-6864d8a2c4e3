package com.databuff.dao.starrocks;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.databuff.dao.starrocks.provider.AlertSearchSqlProvider;
import com.databuff.dao.starrocks.provider.DcAlertSqlProvider;
import com.databuff.entity.dto.*;
import com.databuff.handler.StringArrayToSetTypeHandler;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@Mapper
@Repository
public interface DcAlertMapper {

    /**
     * 根据告警id查询告警详情
     *
     * @param id 告警id
     * @param createDt 创建日期
     * @param apiKey API密钥
     * @param allEntityPermission 是否有所有实体权限
     * @param gids 实体ID集合
     * @return 告警详情对象
     */
    @Select("<script>" +
            "SELECT apiKey,id,timestamp,startTriggerTime,endTriggerTime,lastTriggerTime,updateTime,duration,description,type," +
            "alarmStatus,eventCnt," +
            "monitorId,classification,metrics,busName,host,serviceId,serviceInstance,deviceName," +
            "tags, trigger,remark,status,remark,level,policyId,gid " +
            "FROM dc_alarm " +
            "WHERE id = #{id} AND apiKey = #{apiKey} " +
            "<if test='createDt != null'>AND createDt = #{createDt}</if>" +
            "<if test='!allEntityPermission'> " +
            "<if test='gids != null and gids.size() > 0'>AND (gid IN <foreach item='gid' collection='gids' separator=',' open='(' close=')'>#{gid}</foreach>)</if>" +
            "<if test='gids == null or gids.size() == 0'>AND 1=0</if>" +
            "</if>" +
            "</script>")
    @Results({
            @Result(column = "monitorId", property = "monitorId", typeHandler = StringArrayToSetTypeHandler.class),
            @Result(column = "classification", property = "classification", typeHandler = StringArrayToSetTypeHandler.class),
            @Result(column = "metrics", property = "metrics", typeHandler = StringArrayToSetTypeHandler.class),
            @Result(column = "busName", property = "busName", typeHandler = StringArrayToSetTypeHandler.class),
            @Result(column = "host", property = "host", typeHandler = StringArrayToSetTypeHandler.class),
            @Result(column = "serviceId", property = "serviceId", typeHandler = StringArrayToSetTypeHandler.class),
            @Result(column = "serviceInstance", property = "serviceInstance", typeHandler = StringArrayToSetTypeHandler.class),
            @Result(column = "deviceName", property = "deviceName", typeHandler = StringArrayToSetTypeHandler.class),
            @Result(column = "tags", property = "tags", typeHandler = FastjsonTypeHandler.class),
            @Result(column = "trigger", property = "trigger", typeHandler = FastjsonTypeHandler.class),
            @Result(column = "remark", property = "remark", typeHandler = FastjsonTypeHandler.class)
    })
    DCAlarmDto findAlertDetailsById(@Param("id") String id, @Param("createDt") String createDt, @Param("apiKey") String apiKey, @Param("allEntityPermission") boolean allEntityPermission, @Param("gids") Collection<String> gids);

    /**
     * 获取所有告警信息，包括告警详情
     * 一般用于信息回填，更新。
     *
     * @param id 告警id
     * @param apiKey API密钥
     * @param allEntityPermission 是否有所有实体权限
     * @param gids 实体ID集合
     * @return 告警信息对象
     */
    @Select("<script>" +
            "SELECT * FROM dc_alarm WHERE id = #{id} and apiKey=#{apiKey} " +
            "<if test='!allEntityPermission'> " +
            "<if test='gids != null and gids.size() > 0'>AND (gid IN <foreach item='gid' collection='gids' separator=',' open='(' close=')'>#{gid}</foreach>)</if>" +
            "<if test='gids == null or gids.size() == 0'>AND 1=0</if>" +
            "</if>" +
            "</script>")
    @Results({
            @Result(column = "eventId", property = "eventId", typeHandler = StringArrayToSetTypeHandler.class),
            @Result(column = "monitorId", property = "monitorId", typeHandler = StringArrayToSetTypeHandler.class),
            @Result(column = "classification", property = "classification", typeHandler = StringArrayToSetTypeHandler.class),
            @Result(column = "metrics", property = "metrics", typeHandler = StringArrayToSetTypeHandler.class),
            @Result(column = "busName", property = "busName", typeHandler = StringArrayToSetTypeHandler.class),
            @Result(column = "host", property = "host", typeHandler = StringArrayToSetTypeHandler.class),
            @Result(column = "serviceId", property = "serviceId", typeHandler = StringArrayToSetTypeHandler.class),
            @Result(column = "serviceInstance", property = "serviceInstance", typeHandler = StringArrayToSetTypeHandler.class),
            @Result(column = "deviceName", property = "deviceName", typeHandler = StringArrayToSetTypeHandler.class),
            @Result(column = "tags", property = "tags", typeHandler = FastjsonTypeHandler.class),
            @Result(column = "trigger", property = "trigger", typeHandler = FastjsonTypeHandler.class),
            @Result(column = "remark", property = "remark", typeHandler = FastjsonTypeHandler.class)
    })
    DCAlarmDto findAlertAllDetailsById(@Param("id") String id, @Param("apiKey") String apiKey, @Param("allEntityPermission") boolean allEntityPermission, @Param("gids") Collection<String> gids);

    /**
     * 根据时间间隔获取事件数量
     *
     * @param apiKey              API密钥
     * @param id                  告警id
     * @param time                时间
     * @param interval            时间间隔
     * @param allEntityPermission 是否有所有实体权限
     * @param gids                实体ID集合
     * @return 事件数量列表
     */
    @Select("<script>" +
            "SELECT CAST(UNIX_TIMESTAMP(time_slice(`time`, INTERVAL 1 minute)) / #{interval} AS UNSIGNED) * #{interval} * 1000 as `time`, sum(eventCnt) as eventCnt " +
            "FROM dc_alarm_aggregate " +
            "WHERE apiKey = #{apiKey} AND id = #{id} " +
            "<if test='time != null'>AND time >= #{time}</if>" +
            "<if test='!allEntityPermission'> " +
            "<if test='gids != null and gids.size() > 0'>AND (gid IN <foreach item='gid' collection='gids' separator=',' open='(' close=')'>#{gid}</foreach>)</if>" +
            "<if test='gids == null or gids.size() == 0'>AND 1=0</if>" +
            "</if>" +
            "GROUP BY CAST(UNIX_TIMESTAMP(time_slice(`time`, INTERVAL 1 minute)) / #{interval} AS UNSIGNED) * #{interval} * 1000 " +
            "ORDER BY CAST(UNIX_TIMESTAMP(time_slice(`time`, INTERVAL 1 minute)) / #{interval} AS UNSIGNED) * #{interval} * 1000 ASC" +
            "</script>")
    @Results({
            @Result(column = "eventCnt", property = "eventCnt", javaType = Integer.class)
    })
    List<DcAlarmAggregate> getEventCountByTime(@Param("apiKey") String apiKey, @Param("id") String id, @Param("time") String time, @Param("interval") Integer interval, @Param("allEntityPermission") boolean allEntityPermission, @Param("gids") Collection<String> gids);

    /**
     * 根据服务ID和状态统计告警数量
     *
     * @param id                   告警ID
     * @param allEntityPermission  是否具有所有实体的权限
     * @param gids                 实体ID集合
     * @param idLike               类似ID的字符串
     * @param apiKey               API密钥
     * @param fromTime             开始时间
     * @param toTime               结束时间
     * @param descriptionLowerCase 描述的字符串（小写）
     * @param trigger              触发条件的JSON对象
     * @param queryWrapper         查询条件包装器
     * @return 告警触发ID状态统计列表
     */
    @SelectProvider(type = AlertSearchSqlProvider.class, method = "countByServiceIdStatus")
    List<AlarmTriggerIdStatusCount> countByServiceIdStatus(@Param("id") String id,
                                                           @Param("allEntityPermission") boolean allEntityPermission,
                                                           @Param("gids") Collection<String> gids,
                                                           @Param("idLike") String idLike,
                                                           @Param("apiKey") String apiKey,
                                                           @Param("fromTime") String fromTime,
                                                           @Param("toTime") String toTime,
                                                           @Param("descriptionLowerCase") String descriptionLowerCase,
                                                           @Param("trigger") JSONObject trigger,
                                                           @Param(Constants.WRAPPER) Wrapper<DCAlarmDto> queryWrapper);

    /**
     * 根据主机状态统计告警数量
     *
     * @param id                   告警ID
     * @param allEntityPermission  是否具有所有实体的权限
     * @param gids                 实体ID集合
     * @param idLike               类似ID的字符串
     * @param apiKey               API密钥
     * @param fromTime             开始时间
     * @param toTime               结束时间
     * @param descriptionLowerCase 描述的字符串（小写）
     * @param trigger              触发条件的JSON对象
     * @param queryWrapper         查询条件包装器
     * @return 告警触发ID状态统计列表
     */
    @SelectProvider(type = AlertSearchSqlProvider.class, method = "countByHostStatus")
    List<AlarmTriggerIdStatusCount> countByHostStatus(@Param("id") String id,
                                                      @Param("allEntityPermission") boolean allEntityPermission,
                                                      @Param("gids") Collection<String> gids,
                                                      @Param("idLike") String idLike,
                                                      @Param("apiKey") String apiKey,
                                                      @Param("fromTime") String fromTime,
                                                      @Param("toTime") String toTime,
                                                      @Param("descriptionLowerCase") String descriptionLowerCase,
                                                      @Param("trigger") JSONObject trigger,
                                                      @Param(Constants.WRAPPER) Wrapper<DCAlarmDto> queryWrapper);

    /**
     * 根据服务ID统计告警数量
     *
     * @param id                   告警ID
     * @param allEntityPermission  是否具有所有实体的权限
     * @param gids                 实体ID集合
     * @param idLike               类似ID的字符串
     * @param apiKey               API密钥
     * @param fromTime             开始时间
     * @param toTime               结束时间
     * @param descriptionLowerCase 描述的字符串（小写）
     * @param trigger              触发条件的JSON对象
     * @param queryWrapper         查询条件包装器
     * @return 告警触发ID状态统计列表
     */
    @SelectProvider(type = AlertSearchSqlProvider.class, method = "countByServiceId")
    List<AlarmTriggerIdStatusCount> countByServiceId(@Param("id") String id,
                                                     @Param("allEntityPermission") boolean allEntityPermission,
                                                     @Param("gids") Collection<String> gids,
                                                     @Param("idLike") String idLike,
                                                     @Param("apiKey") String apiKey,
                                                     @Param("fromTime") String fromTime,
                                                     @Param("toTime") String toTime,
                                                     @Param("descriptionLowerCase") String descriptionLowerCase,
                                                     @Param("trigger") JSONObject trigger,
                                                     @Param(Constants.WRAPPER) Wrapper<DCAlarmDto> queryWrapper);

    @SelectProvider(type = AlertSearchSqlProvider.class, method = "countByServiceInstance")
    List<AlarmTriggerIdStatusCount> countByServiceInstance(@Param("id") String id,
                                                           @Param("allEntityPermission") boolean allEntityPermission,
                                                           @Param("gids") Collection<String> gids,
                                                           @Param("idLike") String idLike,
                                                           @Param("apiKey") String apiKey,
                                                           @Param("fromTime") String fromTime,
                                                           @Param("toTime") String toTime,
                                                           @Param("descriptionLowerCase") String descriptionLowerCase,
                                                           @Param("trigger") JSONObject trigger,
                                                           @Param(Constants.WRAPPER) Wrapper<DCAlarmDto> queryWrapper);

    @SelectProvider(type = AlertSearchSqlProvider.class, method = "countByHost")
    List<HostCount> countByHost(@Param("id") String id,
                                @Param("allEntityPermission") boolean allEntityPermission,
                                @Param("gids") Collection<String> gids,
                                @Param("idLike") String idLike,
                                @Param("apiKey") String apiKey,
                                @Param("fromTime") String fromTime,
                                @Param("toTime") String toTime,
                                @Param("descriptionLowerCase") String descriptionLowerCase,
                                @Param("trigger") JSONObject trigger,
                                @Param(Constants.WRAPPER) Wrapper<DCAlarmDto> queryWrapper);

    /**
     * 根据指定字段分组统计告警数量
     *
     * @param id                   告警ID
     * @param allEntityPermission  是否具有所有实体的权限
     * @param gids                 实体ID集合
     * @param idLike               类似ID的字符串
     * @param apiKey               API密钥
     * @param fromTime             开始时间
     * @param toTime               结束时间
     * @param descriptionLowerCase 描述的字符串（小写）
     * @param trigger              触发条件的JSON对象
     * @param queryWrapper         查询条件包装器
     * @param groupFields          分组字段列表
     * @return 分组统计结果
     */
    @SelectProvider(type = AlertSearchSqlProvider.class, method = "countByFields")
    List<Map<String, Object>> countByFields(@Param("id") String id,
                                            @Param("allEntityPermission") boolean allEntityPermission,
                                            @Param("domainManagerStatusOpen") boolean domainManagerStatusOpen,
                                            @Param("gids") Collection<String> gids,
                                            @Param("idLike") String idLike,
                                            @Param("apiKey") String apiKey,
                                            @Param("fromTime") String fromTime,
                                            @Param("toTime") String toTime,
                                            @Param("descriptionLowerCase") String descriptionLowerCase,
                                            @Param("trigger") JSONObject trigger,
                                            @Param(Constants.WRAPPER) Wrapper<DCAlarmDto> queryWrapper,
                                            @Param("groupTriggerFields") List<String> groupTriggerFields,
                                            @Param("groupFields") List<String> groupFields);

    @SelectProvider(type = AlertSearchSqlProvider.class, method = "count")
    Integer count(@Param("id") String id,
                  @Param("allEntityPermission") boolean allEntityPermission,
                  @Param("gids") Collection<String> gids,
                  @Param("idLike") String idLike,
                  @Param("apiKey") String apiKey,
                  @Param("fromTime") String fromTime,
                  @Param("toTime") String toTime,
                  @Param("descriptionLowerCase") String descriptionLowerCase,
                  @Param("trigger") JSONObject trigger,
                  @Param(Constants.WRAPPER) Wrapper<DCAlarmDto> queryWrapper);


    /**
     * 搜索告警
     * SQL查询包括apiKey、startTriggerTime、时间戳、描述、serviceId、serviceInstances、busNames、hosts和deviceNames的条件。
     * 该方法使用MyBatis＜if＞和＜foreach＞标记来处理参数为列表的条件。
     * SQL查询按时间戳降序排列结果，并根据偏移量和限制参数限制结果数。
     *
     * @param apiKey           要在dc_alarm表中匹配的apiKey。
     * @param fromTime         在dc_alarm表中匹配的最大startTriggerTime。
     * @param toTime           在dc_alarm表中匹配的最小时间戳。
     * @param descriptionLowerCase      要在dc_alarm表中匹配的描述。SQL查询使用LIKE运算符来匹配此参数。
     * @param serviceIds       dc_alarm表中要匹配的serviceId列表。SQL查询使用array_contents函数来匹配此参数。
     * @param serviceInstances dc_alarm表中要匹配的serviceInstances列表。SQL查询使用array_contents函数来匹配此参数。
     * @param busNames         要在dc_alarm表中匹配的busNames列表。SQL查询使用array_contents函数来匹配此参数。
     * @param hosts            dc_alarm表中要匹配的主机列表。SQL查询使用array_contents函数来匹配此参数。
     * @param deviceNames      dc_alarm表中要匹配的设备名称列表。SQL查询使用array_contents函数来匹配此参数。
     * @return符合搜索条件的DCAlarmD对象列表。
     */
    @SelectProvider(type = AlertSearchSqlProvider.class, method = "selectList")
    List<String> searchAlarmsV2(@Param("id") String id,
                                    @Param("allEntityPermission") boolean allEntityPermission,
                                    @Param("idLike") String idLike,
                                    @Param("apiKey") String apiKey,
                                    @Param("fromTime") String fromTime,
                                    @Param("toTime") String toTime,
                                    @Param("descriptionLowerCase") String descriptionLowerCase,
                                    @Param("trigger") JSONObject trigger,
                                    @Param(Constants.WRAPPER) Wrapper<DCAlarmDto> queryWrapper,
                                    @Param("gids") Collection<String> gids);

    @Results({
            @Result(column = "trigger", property = "trigger", typeHandler = FastjsonTypeHandler.class),
            @Result(column = "tags", property = "tags", typeHandler = FastjsonTypeHandler.class)
    })
    @SelectProvider(type = AlertSearchSqlProvider.class, method = "searchParams")
    List<DCAlarmDto> searchAlarmParams(@Param("id") String id,
                                       @Param("allEntityPermission") boolean allEntityPermission,
                                       @Param("gids") Collection<String> gids,
                                       @Param("idLike") String idLike,
                                       @Param("apiKey") String apiKey,
                                       @Param("fromTime") String fromTime,
                                       @Param("toTime") String toTime,
                                       @Param("descriptionLowerCase") String descriptionLowerCase,
                                       @Param("trigger") JSONObject trigger,
                                       @Param(Constants.WRAPPER) Wrapper<DCAlarmDto> queryWrapper);

    @SelectProvider(type = AlertSearchSqlProvider.class, method = "getAlarmCntTrend")
    List<Map<String, Object>> getAlarmCntTrend(@Param("allEntityPermission") boolean allEntityPermission,
                                               @Param("apiKey") String apiKey,
                                               @Param("interval") Integer interval,
                                               @Param("fromTime") String fromTime,
                                               @Param("toTime") String toTime,
                                               @Param("idLike") String idLike,
                                               @Param("descriptionLowerCase") String descriptionLowerCase,
                                               @Param("trigger") JSONObject trigger,
                                               @Param(Constants.WRAPPER) Wrapper<DCAlarmDto> queryWrapper,
                                               @Param("gids") Collection<String> gids);

    @SelectProvider(type = AlertSearchSqlProvider.class, method = "countByBusName")
    @Results({
            @Result(column = "names", property = "names", typeHandler = StringArrayToSetTypeHandler.class)
    })
    List<BusNameCount> countByBusName(@Param("id") String id,
                                      @Param("allEntityPermission") boolean allEntityPermission,
                                      @Param("gids") Collection<String> gids,
                                      @Param("idLike") String idLike,
                                      @Param("apiKey") String apiKey,
                                      @Param("fromTime") String fromTime,
                                      @Param("toTime") String toTime,
                                      @Param("descriptionLowerCase") String descriptionLowerCase,
                                      @Param("trigger") JSONObject trigger,
                                      @Param(Constants.WRAPPER) Wrapper<DCAlarmDto> queryWrapper);


    /**
     * 从告警聚合表中动态查询指标趋势。
     *
     * @param interval      时间分桶粒度（秒）
     * @param fromTime      查询开始时间 (格式: yyyy-MM-dd HH:mm:ss)
     * @param toTime        查询结束时间 (格式: yyyy-MM-dd HH:mm:ss)
     * @param groupByFields 动态分组字段列表 (已通过白名单校验)
     * @param queryWrapper  包含所有过滤条件的MyBatis Plus Wrapper
     * @return 聚合后的趋势数据
     */
    @SelectProvider(type = DcAlertSqlProvider.class, method = "getMetricTrendFromAggregate")
    List<Map<String, Object>> getMetricTrendFromAggregate(
            @Param("interval") Integer interval,
            @Param("fromTime") String fromTime,
            @Param("toTime") String toTime,
            @Param("groupByFields") Collection<String> groupByFields,
            @Param(Constants.WRAPPER) Wrapper<DCAlarmDto> queryWrapper
    );

    /**
     * 【新增方法】
     * 使用UNION ALL一次性查询出所有维度的、以JSON数组字符串形式存在的值。
     *
     * @param fromTimeMillis 查询开始时间 (毫秒)
     * @param toTimeMillis   查询结束时间 (毫秒)
     * @param queryWrapper   包含apiKey等过滤条件的Wrapper
     * @return 一个Map列表，每个Map包含 "tag_type" 和 "tag_value" (JSON数组字符串)
     */
    @SelectProvider(type = DcAlertSqlProvider.class, method = "getTagValuesAsStringArrays")
    List<Map<String, Object>> getTagValuesAsStringArrays(
            @Param("fromTimeMillis") Long fromTimeMillis,
            @Param("toTimeMillis") Long toTimeMillis,
            @Param(Constants.WRAPPER) Wrapper<DCAlarmDto> queryWrapper
    );

}