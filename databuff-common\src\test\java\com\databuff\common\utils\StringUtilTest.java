package com.databuff.common.utils;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class StringUtilTest {

    @Test
    void truncateString() {
        assertEquals("Hello", StringUtil.truncateString("Hello, World!", 5));
        assertEquals("Hello, World!", StringUtil.truncateString("Hello, World!", 20));
        assertNull(StringUtil.truncateString(null, 5));
        assertEquals("", StringUtil.truncateString("", 5));
        assertEquals("你好", StringUtil.truncateString("你好，世界！", 2));
    }

    @Test
    void truncateToByteLength() {
        assertEquals("Hello", StringUtil.truncateToByteLength("Hello, World!", 5));
        assertEquals("Hello, World!", StringUtil.truncateToByteLength("Hello, World!", 20));
        assertNull(StringUtil.truncateToByteLength(null, 5));
        assertEquals("", StringUtil.truncateToByteLength("", 5));
        assertEquals("你好", StringUtil.truncateToByteLength("你好，世界！", 6));
        assertEquals("你好，", StringUtil.truncateToByteLength("你好，世界！", 9));
        assertEquals("你好,", StringUtil.truncateToByteLength("你好,世界！", 8));
        assertEquals("你好,", StringUtil.truncateToByteLength("你好,世界！", 7));
        assertEquals("你", StringUtil.truncateToByteLength("你好，世界！", 3));
    }
}
