package com.databuff.common.threadLocal;

import com.alibaba.ttl.TransmittableThreadLocal;

import java.util.Collection;

/**
 * TransmittableThreadLocal 是阿里巴巴开源的扩展，解决了 InheritableThreadLocal 在使用线程池等场景下，线程复用导致的线程本地变量丢失问题。
 * 它不仅支持父子线程之间的变量传递，还支持线程池中线程复用时的变量传递。
 * 适用于复杂的多线程环境，特别是使用线程池的场景。
 */
public class TransmittableThreadLocalUtil {
    private static final ThreadLocal<ThreadLocalEntity> THREAD_LOCAL =  new TransmittableThreadLocal<>();

    public static void setThreadLocal(ThreadLocalEntity value) {
        THREAD_LOCAL.set(value);
    }
    public static ThreadLocalEntity getThreadLocal() {
        return  THREAD_LOCAL.get();
    }
    public static void removeThreadLocal() {
        THREAD_LOCAL.remove();
    }

    public static Collection<String> getGid() {
        return THREAD_LOCAL.get()==null?null:THREAD_LOCAL.get().getAgis();
    }
    public static String getAccount() {
        return THREAD_LOCAL.get()==null?null:THREAD_LOCAL.get().getAccount();
    }

    public static boolean hasAnnotate() {
        final ThreadLocalEntity threadLocalEntity = THREAD_LOCAL.get();
        return threadLocalEntity == null ? false : threadLocalEntity.isHasAnnotate();
    }

    protected static void setAnnotate(boolean hasAnnotate) {
        final ThreadLocalEntity threadLocal = getThreadLocal();
        if (threadLocal != null) {
            threadLocal.setHasAnnotate(hasAnnotate);
            threadLocal.setUpdateFlag(true);
        } else {
            setThreadLocal(ThreadLocalEntity.builder()
                    .hasAnnotate(hasAnnotate)
                    .updateFlag(false)
                    .build());
        }
    }
}
