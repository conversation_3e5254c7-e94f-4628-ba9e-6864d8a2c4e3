package com.databuff.common.tsdb.dto.detect;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.databuff.common.tsdb.dto.explore.QueryRequest;
import com.databuff.common.tsdb.model.LogicalOperator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

@Data
@JsonDeserialize(using = MultiDetectQueryRequestDeserializer.class) // 启用反序列化器
@JsonSerialize(using = MultiDetectQueryRequestSerializer.class)    // 启用序列化器
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MultiDetectQueryRequest {

    public String getJsonField() {
        return JSONObject.toJSONString(this, SerializerFeature.DisableCircularReferenceDetect);
    }

    @JsonProperty("1")
    @JSONField(name = "1")
    private DetectQueryRequest A;

    @JsonProperty("2")
    @JSONField(name = "2")
    private DetectQueryRequest B;

    @JsonProperty("3")
    @JSONField(name = "3")
    private DetectQueryRequest C;

    @JsonProperty("4")
    @JSONField(name = "4")
    private DetectQueryRequest D;

    @JsonProperty("5")
    @JSONField(name = "5")
    private DetectQueryRequest E;

    private LogicalOperator critical;
    private LogicalOperator warning;
    private LogicalOperator noData;

    private Boolean hasAnnotate;

    public void setHasAnnotate(Boolean hasAnnotate) {
        this.hasAnnotate = hasAnnotate;
        if (hasAnnotate == null) {
            return;
        }
        // 同步起始值到所有非空查询请求
        for (DetectQueryRequest nonNullQuery : this.buildNonNullQueries()) {
            nonNullQuery.setHasAnnotate(hasAnnotate);
        }
    }

    private String apiKey;

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
        if (apiKey == null) {
            return;
        }
        // 同步起始值到所有非空查询请求
        for (DetectQueryRequest nonNullQuery : this.buildNonNullQueries()) {
            nonNullQuery.setApiKey(apiKey);
        }
    }

    @ApiModelProperty(value = "起始时间戳，单位为毫秒", example = "1609459200000")
    private Long start;

    /**
     * 设置起始值，并同步到所有非空的查询请求中。
     *
     * @param start 要设置的起始值
     */
    public void setStart(Long start) {
        this.start = start;
        if (start == null) {
            return;
        }
        // 同步起始值到所有非空查询请求
        for (DetectQueryRequest nonNullQuery : this.buildNonNullQueries()) {
            nonNullQuery.setStart(start);
        }
    }

    @ApiModelProperty(value = "结束时间戳，单位为毫秒", example = "1609462800000")
    private Long end;

    /**
     * 设置结束时间并同步更新所有非空查询请求的结束时间。
     *
     * @param end 新的结束时间值（可能为null）
     */
    public void setEnd(Long end) {
        this.end = end;
        if (end == null) {
            return;
        }
        // 同步更新所有非空查询请求的结束时间
        for (DetectQueryRequest nonNullQuery : this.buildNonNullQueries()) {
            nonNullQuery.setEnd(end);
        }
    }

    @ApiModelProperty(value = "查询数据的时间间隔，单位为秒", example = "60")
    private Integer interval;

    /**
     * 设置查询时间间隔（单位为秒），并同步到所有非空的查询请求中。
     *
     * @param interval 新的时间间隔值（单位为秒，可能为null）
     */
    public void setInterval(Integer interval) {
        this.interval = interval;
        if (interval == null) {
            return;
        }
        // 同步时间间隔到所有非空查询请求（转换为Long类型）
        for (DetectQueryRequest nonNullQuery : this.buildNonNullQueries()) {
            nonNullQuery.setInterval(interval);
        }
    }

    /**
     * 获取所有非空的 A-E 查询对象列表。
     */
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false) // Fastjson 的注解
    public Collection<DetectQueryRequest> buildNonNullQueries() {
        return Arrays.stream(new DetectQueryRequest[]{A, B, C, D, E})
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 获取包含非空DetectQueryRequest对象的映射表。
     * 该方法遍历A到E五个DetectQueryRequest对象，将非空的对象按指定键("1"至"5")存入Map中。
     * 每个键对应如下：
     * "1" -> A
     * "2" -> B
     * "3" -> C
     * "4" -> D
     * "5" -> E
     *
     * @return 包含非空请求对象的Map，键为字符串编号，值为对应的DetectQueryRequest实例
     */
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false) // Fastjson 的注解
    public Map<String, DetectQueryRequest> buildNonNullQueriesMap() {
        Map<String, DetectQueryRequest> map = new HashMap<>();
        if (A != null) map.put("1", A);
        if (B != null) map.put("2", B);
        if (C != null) map.put("3", C);
        if (D != null) map.put("4", D);
        if (E != null) map.put("5", E);
        return map;
    }


    /**
     * 收集所有非空查询请求中的查询条件并返回结果集合。
     *
     * @return 包含所有查询条件字符串的集合
     */
    public Collection<String> findBy() {
        Collection<String> result = new HashSet<>();

        // 遍历所有非空的DetectQueryRequest对象
        for (DetectQueryRequest detectQueryRequest : this.buildNonNullQueries()) {
            if (detectQueryRequest == null) {
                continue;
            }

            // 遍历MultiQueryDTO中的非空QueryRequest对象
            for (QueryRequest queryRequest : detectQueryRequest.getNonNullQueries()) {
                if (queryRequest == null) {
                    continue;
                }

                // 将当前QueryRequest的查询条件添加到结果集合
                result.addAll(queryRequest.getBy());
            }
        }
        return result;
    }

    public Collection<String> findWay() {
        return buildNonNullQueries().stream()
                .map(DetectQueryRequest::getWay)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }


    /**
     * 收集所有检测查询请求中定义的指标名称。
     * <p>
     * 该方法遍历所有非空的检测查询请求及其嵌套的查询请求，提取每个查询请求中的指标名称，
     * 并返回所有唯一指标名称的集合。
     *
     * @return 包含所有唯一指标名称的集合
     */
    public Collection<String> findMetrics() {
        Collection<String> result = new HashSet<>();
        // 遍历所有非空的检测查询请求
        for (DetectQueryRequest detectQueryRequest : this.buildNonNullQueries()) {
            if (detectQueryRequest == null) {
                continue;
            }
            // 新增对getNonNullQueries()返回值的空指针检查
            Collection<QueryRequest> queries = detectQueryRequest.getNonNullQueries();
            if (queries == null) {
                continue;
            }
            for (QueryRequest queryRequest : queries) {
                if (queryRequest == null) {
                    continue;
                }
                result.add(queryRequest.getMetric());
            }
        }
        return result;
    }

    /**
     * 设置所有查询请求的时间偏移值。
     * @param timeOffset 需要设置的时间偏移量（单位由具体实现定义）
     * @return 无返回值
     */
    public void setTimeOffset(int timeOffset) {
        // 遍历所有非空的DetectQueryRequest对象
        for (DetectQueryRequest detectQueryRequest : this.buildNonNullQueries()) {
            if (detectQueryRequest == null) {
                continue;
            }
            // 遍历当前DetectQueryRequest中的每个非空QueryRequest并设置时间偏移
            for (QueryRequest queryRequest : detectQueryRequest.getNonNullQueries()) {
                if (queryRequest == null) {
                    continue;
                }
                queryRequest.setTimeOffset(timeOffset);
            }
        }
    }

    /**
     * 通用方法，用于在所有非空的DetectQueryRequest对象上设置指定属性值。
     * 该方法使用反射机制，根据属性名查找并调用对应的setter方法。
     *
     * @param propertyName 要设置的属性名称
     * @param value 要设置的属性值
     */
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    public void setPropertyOnAllDetectQueries(String propertyName, Object value) {
        if (propertyName == null || value == null) {
            return;
        }

        // 构造setter方法名
        String setterMethodName = "set" + propertyName.substring(0, 1).toUpperCase() + propertyName.substring(1);

        try {
            // 获取DetectQueryRequest类中对应属性类型的setter方法
            Method setterMethod = null;
            for (Method method : DetectQueryRequest.class.getMethods()) {
                if (method.getName().equals(setterMethodName) && method.getParameterCount() == 1) {
                    setterMethod = method;
                    break;
                }
            }

            if (setterMethod != null) {
                // 在所有非空查询请求上调用setter方法
                for (DetectQueryRequest detectQuery : this.buildNonNullQueries()) {
                    if (detectQuery != null) {
                        setterMethod.invoke(detectQuery, value);
                    }
                }
            } else {
                // 如果没有找到对应的setter方法，抛出异常
                throw new RuntimeException("Property '" + propertyName + "' not found in DetectQueryRequest class");
            }
        } catch (Exception e) {
            // 记录异常信息或者抛出自定义异常
            throw new RuntimeException("Failed to set property " + propertyName + " on detect queries", e);
        }
    }

    /**
     * 通用方法，用于在所有非空的DetectQueryRequest对象的所有非空QueryRequest对象上设置指定属性值。
     * 该方法使用反射机制，根据属性名查找并调用对应的setter方法。
     *
     * @param propertyName 要设置的属性名称
     * @param value 要设置的属性值
     */
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    public void setPropertyOnAllQueries(String propertyName, Object value) {
        if (propertyName == null || value == null) {
            return;
        }

        // 构造setter方法名
        String setterMethodName = "set" + propertyName.substring(0, 1).toUpperCase() + propertyName.substring(1);

        try {
            // 获取QueryRequest类中对应属性类型的setter方法
            Method setterMethod = null;
            for (Method method : QueryRequest.class.getMethods()) {
                if (method.getName().equals(setterMethodName) && method.getParameterCount() == 1) {
                    setterMethod = method;
                    break;
                }
            }

            if (setterMethod != null) {
                // 遍历所有非空的DetectQueryRequest对象
                for (DetectQueryRequest detectQuery : this.buildNonNullQueries()) {
                    if (detectQuery == null) {
                        continue;
                    }
                    // 遍历当前DetectQueryRequest中的每个非空QueryRequest并设置属性
                    for (QueryRequest query : detectQuery.getNonNullQueries()) {
                        if (query != null) {
                            setterMethod.invoke(query, value);
                        }
                    }
                }
            } else {
                // 如果没有找到对应的setter方法，抛出异常
                throw new RuntimeException("Property '" + propertyName + "' not found in QueryRequest class");
            }
        } catch (Exception e) {
            // 记录异常信息或者抛出自定义异常
            throw new RuntimeException("Failed to set property " + propertyName + " on queries", e);
        }
    }

    /**
     * 设置所有非空查询请求的时间聚合器。
     *
     * @param timeAggregator 要设置的时间聚合器值
     */
    public void setTimeAggregator(String timeAggregator) {
        if (timeAggregator == null) {
            return;
        }
        // 同步时间聚合器到所有非空查询请求
        for (DetectQueryRequest nonNullQuery : this.buildNonNullQueries()) {
            nonNullQuery.setTimeAggregator(timeAggregator);
        }
    }

    /**
     * 设置所有非空查询请求的比较运算符。
     *
     * @param comparison 要设置的比较运算符值
     */
    public void setComparison(String comparison) {
        if (comparison == null) {
            return;
        }
        // 同步比较运算符到所有非空查询请求
        for (DetectQueryRequest nonNullQuery : this.buildNonNullQueries()) {
            nonNullQuery.setComparison(comparison);
        }
    }

    /**
     * 设置所有非空查询请求的单位。
     *
     * @param unit 要设置的单位值
     */
    public void setUnit(String unit) {
        if (unit == null) {
            return;
        }
        // 同步单位到所有非空查询请求
        for (DetectQueryRequest nonNullQuery : this.buildNonNullQueries()) {
            nonNullQuery.setUnit(unit);
        }
    }

    /**
     * 设置所有非空查询请求的显示单位。
     *
     * @param viewUnit 要设置的显示单位值
     */
    public void setViewUnit(String viewUnit) {
        if (viewUnit == null) {
            return;
        }
        // 同步显示单位到所有非空查询请求
        for (DetectQueryRequest nonNullQuery : this.buildNonNullQueries()) {
            nonNullQuery.setViewUnit(viewUnit);
        }
    }

    /**
     * 设置所有非空查询请求的连续检测标志。
     *
     * @param continuous 要设置的连续检测标志值
     */
    public void setContinuous(Boolean continuous) {
        if (continuous == null) {
            return;
        }
        // 同步连续检测标志到所有非空查询请求
        for (DetectQueryRequest nonNullQuery : this.buildNonNullQueries()) {
            nonNullQuery.setContinuous(continuous);
        }
    }

    /**
     * 设置所有非空查询请求的完整窗口要求标志。
     *
     * @param requireFullWindow 要设置的完整窗口要求标志值
     */
    public void setRequireFullWindow(Boolean requireFullWindow) {
        if (requireFullWindow == null) {
            return;
        }
        // 同步完整窗口要求标志到所有非空查询请求
        for (DetectQueryRequest nonNullQuery : this.buildNonNullQueries()) {
            nonNullQuery.setRequireFullWindow(requireFullWindow);
        }
    }

    /**
     * 收集所有非空查询请求中的时间聚合器并返回结果集合。
     *
     * @return 包含所有时间聚合器的集合
     */
    public Collection<String> findTimeAggregators() {
        return buildNonNullQueries().stream()
                .map(DetectQueryRequest::getTimeAggregator)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    /**
     * 收集所有非空查询请求中的比较运算符并返回结果集合。
     *
     * @return 包含所有比较运算符的集合
     */
    public Collection<String> findComparisons() {
        return buildNonNullQueries().stream()
                .map(DetectQueryRequest::getComparison)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    /**
     * 收集所有非空查询请求中的单位并返回结果集合。
     *
     * @return 包含所有单位的集合
     */
    public Collection<String> findUnits() {
        return buildNonNullQueries().stream()
                .map(DetectQueryRequest::getUnit)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    /**
     * 收集所有非空查询请求中的显示单位并返回结果集合。
     *
     * @return 包含所有显示单位的集合
     */
    public Collection<String> findViewUnits() {
        return buildNonNullQueries().stream()
                .map(DetectQueryRequest::getViewUnit)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    /**
     * 收集所有非空查询请求中的连续检测标志并返回结果集合。
     *
     * @return 包含所有连续检测标志的集合
     */
    public Collection<Boolean> findContinuous() {
        return buildNonNullQueries().stream()
                .map(DetectQueryRequest::getContinuous)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    /**
     * 收集所有非空查询请求中的完整窗口要求标志并返回结果集合。
     *
     * @return 包含所有完整窗口要求标志的集合
     */
    public Collection<Boolean> findRequireFullWindow() {
        return buildNonNullQueries().stream()
                .map(DetectQueryRequest::getRequireFullWindow)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }
}
