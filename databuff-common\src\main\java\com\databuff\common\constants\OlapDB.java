package com.databuff.common.constants;

import com.google.common.collect.Maps;

import java.util.Map;

import static com.databuff.common.constants.Constant.Trace.*;
import static com.databuff.common.constants.TSDBIndex.*;

public class OlapDB {

    public static final String OLAP_DATABASE = "databuff";
    public static final String DC_SPAN = "dc_span";
    public static final String DC_DATA_COLLECT = "dc_data_collect";
    public static final String DC_ALARM = "dc_alarm";
    public static final String DC_LOGS = "dc_logs";
    public static final String DC_AGG_ALARM = "dc_alarm_aggregate";

    public static final String DC_K8S_LATEST_CLUSTER = "dc_k8s_latest_cluster";
    public static final String DC_K8S_LATEST_NAMESPACE = "dc_k8s_latest_namespace";
    public static final String DC_K8S_LATEST_NODE = "dc_k8s_latest_node";
    public static final String DC_K8S_LATEST_POD = "dc_k8s_latest_pod";
    public static final String DC_K8S_LATEST_SERVICE = "dc_k8s_latest_service";
    public static final String DC_K8S_LATEST_WORKLOAD = "dc_k8s_latest_workload";
    public static final String DC_LATEST_CONTAINERS = "dc_latest_containers";
    public static final String DC_LATEST_ALL_PROCESS = "dc_latest_all_process";
    public static final String DC_RUM_PAGE_SPAN = "dc_rum_page_span";
    public static final String DC_RUM_ACTION_SPAN = "dc_rum_action_span";
    public static final String DC_RUM_PAGE = "dc_rum_page";
    public static final String DC_RUM_PAGE_UV = "dc_rum_page_uv";
    public static final String DC_RUM_ACTION = "dc_rum_action";
    public static final String DC_RUM_ACTION_UV = "dc_rum_action_uv";
    public static final String DC_RUM_ERROR_LOGS = "dc_rum_error_logs";
    public static final String DC_RUM_ERROR_LOGS_UV = "dc_rum_error_logs_uv";
    public static final String DC_RUM_WEB_REQUEST_SPAN = "dc_rum_web_request_span";
    public static final String DC_RUM_WEB_REQUEST_UV = "dc_rum_web_request_uv";
    public static final String DC_RUM_WEB_SESSION = "dc_rum_web_session";

    // iOS RUM related tables
    public static final String DC_RUM_IOS_LAUNCH = "dc_rum_ios_launch";
    public static final String DC_RUM_IOS_ACTION = "dc_rum_ios_action";
    public static final String DC_RUM_IOS_PAGE = "dc_rum_ios_page";
    public static final String DC_RUM_IOS_ANR = "dc_rum_ios_anr";
    public static final String DC_RUM_IOS_CRASH = "dc_rum_ios_crash";
    public static final String DC_RUM_IOS_LAUNCH_SPAN = "dc_rum_ios_launch_span";
    public static final String DC_RUM_IOS_ACTION_SPAN = "dc_rum_ios_action_span";
    public static final String DC_RUM_IOS_PAGE_SPAN = "dc_rum_ios_page_span";
    public static final String DC_RUM_IOS_LIFECYCLE_METHOD = "dc_rum_ios_lifecycle_method";
    public static final String DC_RUM_IOS_DEVICE_MINUTE = "dc_rum_ios_device_minute";
    public static final String DC_RUM_IOS_SESSION = "dc_rum_ios_session";


    // android RUM related tables
    public static final String DC_RUM_ANDROID_LAUNCH = "dc_rum_android_launch";
    public static final String DC_RUM_ANDROID_ACTION = "dc_rum_android_action";
    public static final String DC_RUM_ANDROID_PAGE = "dc_rum_android_page";
    public static final String DC_RUM_ANDROID_ANR = "dc_rum_android_anr";
    public static final String DC_RUM_ANDROID_CRASH = "dc_rum_android_crash";
    public static final String DC_RUM_ANDROID_LAUNCH_SPAN = "dc_rum_android_launch_span";
    public static final String DC_RUM_ANDROID_ACTION_SPAN = "dc_rum_android_action_span";
    public static final String DC_RUM_ANDROID_PAGE_SPAN = "dc_rum_android_page_span";
    public static final String DC_RUM_ANDROID_LIFECYCLE_METHOD = "dc_rum_android_lifecycle_method";
    public static final String DC_RUM_ANDROID_DEVICE_MINUTE = "dc_rum_android_device_minute";
    public static final String DC_RUM_ANDROID_SESSION = "dc_rum_android_session";


    public static final Map<String, String> META_INDEPENDENT_FIELDS = Maps.newConcurrentMap();
    public static final Map<String, String> META_INDEPENDENT_FIELDS_FLIP = Maps.newConcurrentMap();

    static {
        META_INDEPENDENT_FIELDS.put(HTTP_STATUS_CODE, "meta.http.status_code");
        META_INDEPENDENT_FIELDS.put(ERROR_TYPE, "meta.error.type");
        META_INDEPENDENT_FIELDS.put(PEER_HOSTNAME, "meta.peer.hostname");
        META_INDEPENDENT_FIELDS.put("http.method", "meta.http.method");
        META_INDEPENDENT_FIELDS.put("http.url", "meta.http.url");

        META_INDEPENDENT_FIELDS.put("db.type", "meta.db.type");
        META_INDEPENDENT_FIELDS.put("db.instance", "meta.db.instance");
        META_INDEPENDENT_FIELDS.put(DB_OPERATION, "meta.db.operation");

        META_INDEPENDENT_FIELDS.put("topic", "meta.mq.topic");
        META_INDEPENDENT_FIELDS.put("group", "meta.mq.group");
        META_INDEPENDENT_FIELDS.put("broker", "meta.mq.broker");
        META_INDEPENDENT_FIELDS.put("root.resource", "meta.root.resource");
        META_INDEPENDENT_FIELDS.put("indices", "meta.indices");
        META_INDEPENDENT_FIELDS.put("partition", "metrics.mq.partition");


        for (Map.Entry<String, String> entry : META_INDEPENDENT_FIELDS.entrySet()) {
            META_INDEPENDENT_FIELDS_FLIP.put(entry.getValue(), entry.getKey());
        }
    }


    public static final Map<String, String> METRIC_INDEPENDENT_FIELDS = Maps.newConcurrentMap();
    public static final Map<String, String> METRIC_INDEPENDENT_FIELDS_FLIP = Maps.newConcurrentMap();

    static {
        METRIC_INDEPENDENT_FIELDS.put("partition", "metrics.mq.partition");
        METRIC_INDEPENDENT_FIELDS.put("path.id", "path.id");
        METRIC_INDEPENDENT_FIELDS.put("parent.path.id", "parent.path.id");

        for (Map.Entry<String, String> entry : METRIC_INDEPENDENT_FIELDS.entrySet()) {
            METRIC_INDEPENDENT_FIELDS_FLIP.put(entry.getValue(), entry.getKey());
        }
    }


    public static final Map<String, String> TSDB_ALL_QUERY_FIELDS = Maps.newConcurrentMap();
    public static final Map<String, String> TSDB_HTTP_QUERY_FIELDS = Maps.newConcurrentMap();
    public static final Map<String, String> TSDB_RPC_QUERY_FIELDS = Maps.newConcurrentMap();
    public static final Map<String, String> TSDB_MQ_QUERY_FIELDS = Maps.newConcurrentMap();
    public static final Map<String, String> TSDB_ES_QUERY_FIELDS = Maps.newConcurrentMap();
    public static final Map<String, String> TSDB_DB_QUERY_FIELDS = Maps.newConcurrentMap();
    public static final Map<String, String> TSDB_REDIS_QUERY_FIELDS = Maps.newConcurrentMap();
    public static final Map<String, String> TSDB_OTHER_QUERY_FIELDS = Maps.newConcurrentMap();
    public static final Map<String, String> TSDB_TRACE_QUERY_FIELDS = Maps.newConcurrentMap();

    public static final Map<String, Map<String, String>> TSDB_QUERY_TYPES = Maps.newConcurrentMap();

    static {
        TSDB_ALL_QUERY_FIELDS.put("resource", "");
        TSDB_ALL_QUERY_FIELDS.put("service", "1");
        TSDB_ALL_QUERY_FIELDS.put("serviceInstance", "");
        TSDB_ALL_QUERY_FIELDS.put("srcService", "");
        TSDB_ALL_QUERY_FIELDS.put("srcServiceInstance", "");

        TSDB_HTTP_QUERY_FIELDS.putAll(TSDB_ALL_QUERY_FIELDS);
        TSDB_HTTP_QUERY_FIELDS.put("httpCode", "");
        TSDB_HTTP_QUERY_FIELDS.put("httpMethod", "");
        TSDB_HTTP_QUERY_FIELDS.put("url", "");

        TSDB_RPC_QUERY_FIELDS.putAll(TSDB_ALL_QUERY_FIELDS);
//        TSDB_RPC_QUERY_FIELDS.put("type", "");

        TSDB_MQ_QUERY_FIELDS.putAll(TSDB_ALL_QUERY_FIELDS);
        TSDB_MQ_QUERY_FIELDS.put("broker", "");
        TSDB_MQ_QUERY_FIELDS.put("group", "");
        TSDB_MQ_QUERY_FIELDS.put("partition", "");
        TSDB_MQ_QUERY_FIELDS.put("topic", "");
//        TSDB_MQ_QUERY_FIELDS.put("type", "");


        TSDB_ES_QUERY_FIELDS.putAll(TSDB_ALL_QUERY_FIELDS);
        TSDB_ES_QUERY_FIELDS.put("url", "");

        TSDB_DB_QUERY_FIELDS.putAll(TSDB_ALL_QUERY_FIELDS);
        TSDB_DB_QUERY_FIELDS.put("dbType", "");
        TSDB_DB_QUERY_FIELDS.put("sqlDatabase", "");
        TSDB_DB_QUERY_FIELDS.put("sqlOperation", "");

        TSDB_REDIS_QUERY_FIELDS.putAll(TSDB_ALL_QUERY_FIELDS);

        TSDB_OTHER_QUERY_FIELDS.putAll(TSDB_ALL_QUERY_FIELDS);

        TSDB_TRACE_QUERY_FIELDS.put("resource", "");
        TSDB_TRACE_QUERY_FIELDS.put("service", "1");
        TSDB_TRACE_QUERY_FIELDS.put("serviceInstance", "");
        TSDB_TRACE_QUERY_FIELDS.put("errorType", "1");
        TSDB_TRACE_QUERY_FIELDS.put("hostName", "");
        TSDB_TRACE_QUERY_FIELDS.put("httpStatusCode", "");
        TSDB_TRACE_QUERY_FIELDS.put("httpMethod", "");


        TSDB_QUERY_TYPES.put(TSDB_METRIC_SERVICE_TRACE, TSDB_TRACE_QUERY_FIELDS);
        TSDB_QUERY_TYPES.put(TSDB_METRIC_OTHER_TABLE_NAME, TSDB_OTHER_QUERY_FIELDS);
        TSDB_QUERY_TYPES.put(TSDB_METRIC_REDIS_TABLE_NAME, TSDB_REDIS_QUERY_FIELDS);
        TSDB_QUERY_TYPES.put(TSDB_METRIC_DB_TABLE_NAME, TSDB_DB_QUERY_FIELDS);
        TSDB_QUERY_TYPES.put(TSDB_METRIC_ES_TABLE_NAME, TSDB_ES_QUERY_FIELDS);
        TSDB_QUERY_TYPES.put(TSDB_METRIC_MQ_TABLE_NAME, TSDB_MQ_QUERY_FIELDS);
        TSDB_QUERY_TYPES.put(TSDB_METRIC_RPC_TABLE_NAME, TSDB_RPC_QUERY_FIELDS);
        TSDB_QUERY_TYPES.put(TSDB_METRIC_HTTP_TABLE_NAME, TSDB_HTTP_QUERY_FIELDS);
    }

}
