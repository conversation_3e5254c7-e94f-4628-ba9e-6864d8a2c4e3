package com.databuff.dao.starrocks;

import com.databuff.entity.K8sEntity;
import com.databuff.entity.K8sParam;
import com.databuff.entity.K8sPodEntity;
import com.databuff.entity.dto.TimeValue;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author:TianMing
 * @date: 2024/1/23
 * @time: 14:41
 */
@Mapper
@Repository
public interface K8sSrMapper extends DataCleanupMapper {
    Long K8sClusterCount(K8sParam k8sParam);

    List<K8sEntity> K8sClusterList(K8sParam k8sParam);

    Long K8sNamespaceCount(K8sParam k8sParam);

    List<K8sEntity> K8sNamespaceList(K8sParam k8sParam);

    Long K8sNodeCount(K8sParam k8sParam);

    List<K8sEntity> K8sNodeList(K8sParam k8sParam);

    Long K8sServiceCount(K8sParam k8sParam);

    List<K8sEntity> K8sServiceList(K8sParam k8sParam);

    Long K8sWorkloadCount(K8sParam k8sParam);

    List<K8sEntity> K8sWorkloadList(K8sParam k8sParam);

    Long podCount(K8sParam k8sParam);

    List<K8sPodEntity> K8sPodList(K8sParam k8sParam);

    List<TimeValue> countNsByCluster(K8sParam k8sParam);

    List<TimeValue> countWlByCluster(K8sParam k8sParam);
    List<TimeValue> countPodByCluster(K8sParam k8sParam);
    List<TimeValue> countPodByNsClusterStatus(K8sParam k8sParam);
    List<TimeValue> countPodByWLClusterStatus(K8sParam k8sParam);
    List<TimeValue> countPodByNodeClusterStatus(K8sParam k8sParam);
    List<TimeValue> countPodByNsCluster(K8sParam k8sParam);
    List<TimeValue> countSvcByCluster(K8sParam k8sParam);
    List<TimeValue> countWlByNsCluster(K8sParam k8sParam);

    List<TimeValue> countSvcByNsCluster(K8sParam k8sParam);



}
