package com.databuff.common.utils;

import lombok.extern.slf4j.Slf4j;

/**
 * 字段验证工具类，用于验证对象字段是否为空并生成相应的错误日志
 *
 * <p>主要功能：
 * <ul>
 *   <li>支持链式调用验证多个字段</li>
 *   <li>自动收集空值字段并生成错误信息</li>
 *   <li>提供RUM专用和通用两种验证器 rum多了上报moredb</li>
 * </ul>
 *
 * <p>使用示例:
 * <pre>
 * // RUM验证器使用示例
 * boolean hasErrors = FieldValidator.createRumValidator("UserService", "createUser")
 *     .validate("userId", user.getId())
 *     .validate("userName", user.getName())
 *     .hasErrors();
 *
 * // 通用验证器使用示例
 * boolean hasErrors = FieldValidator.createSimpleValidator("用户验证")
 *     .validate("email", user.getEmail())
 *     .validate("phone", user.getPhone())
 *     .hasErrors();
 * </pre>
 */
@Slf4j
public class FieldValidator {

    private final StringBuilder missingFields = new StringBuilder();
    private final LogHandler logHandler;

    /**
     * 日志处理接口，用于自定义错误日志输出方式
     */
    public interface LogHandler {
        /**
         * 处理错误信息
         *
         * @param missingFields 收集到的所有空值字段名称
         */
        void handleError(String missingFields);
    }

    private FieldValidator(LogHandler logHandler) {
        this.logHandler = logHandler;
    }

    /**
     * 创建RUM专用的验证器实例
     *
     * @param className  类名
     * @param methodName 方法名
     * @return FieldValidator实例
     */
    public static FieldValidator createRumValidator(String className, String methodName) {
        String prefix = "rum: " + className + " " + methodName;
        return new FieldValidator(
                (missingFields) -> {
                    log.error("{} validation failed. Missing required fields: {}",
                            className + " " + methodName, missingFields);
                    OtelMetricUtil.logException(prefix, new NullPointerException("Missing fields: " + missingFields));
                }
        );
    }

    /**
     * 创建通用验证器实例
     *
     * @param logPrefix 日志前缀
     * @return FieldValidator实例
     */
    public static FieldValidator createSimpleValidator(String logPrefix) {
        return new FieldValidator(
                (missingFields) -> log.error("{} validation failed: {}", logPrefix, missingFields)
        );
    }

    /**
     * 验证字段值是否为空
     *
     * @param fieldName 字段名称
     * @param value     字段值
     * @return 当前验证器实例，支持链式调用
     */
    public FieldValidator validate(String fieldName, Object value) {
        if (value == null) {
            missingFields.append(fieldName).append(", ");
        }
        return this;
    }

    /**
     * 检查是否存在验证错误
     *
     * @return true:存在空值字段 false:所有字段验证通过
     */
    public boolean hasErrors() {
        if (missingFields.length() > 0) {
            missingFields.setLength(missingFields.length() - 2);
            logHandler.handleError(missingFields.toString());
            return true;
        }
        return false;
    }
}
