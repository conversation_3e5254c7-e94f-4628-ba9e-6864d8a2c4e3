package com.databuff.common.tsdb.dto.detect;

import com.alibaba.fastjson.serializer.JSONSerializer;
import com.alibaba.fastjson.serializer.ObjectSerializer;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;

public class MultiDetectQueryRequestSerializerFastjson implements ObjectSerializer {

    public void write(JSONSerializer serializer, Object object, Object fieldName, Type fieldType, int features) throws IOException {

        MultiDetectQueryRequest request = (MultiDetectQueryRequest) object;
        Map<String, Object> jsonMap = new HashMap<>();

        // 序列化 "1" -> A、"2" -> B 等字段
        if (request.getA() != null) {
            jsonMap.put("1", request.getA());
        }
        if (request.getB() != null) {
            jsonMap.put("2", request.getB());
        }
        if (request.getC() != null) {
            jsonMap.put("3", request.getC());
        }
        if (request.getD() != null) {
            jsonMap.put("4", request.getD());
        }
        if (request.getE() != null) {
            jsonMap.put("5", request.getE());
        }

        // 序列化枚举字段（如 critical）
        jsonMap.put("critical", request.getCritical().name());
        jsonMap.put("warning", request.getWarning().name());
        jsonMap.put("noData", request.getNoData().name());

        // 写入 JSON
        serializer.write(jsonMap);
    }
    
}
