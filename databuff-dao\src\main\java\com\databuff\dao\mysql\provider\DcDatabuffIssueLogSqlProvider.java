package com.databuff.dao.mysql.provider;

import com.databuff.entity.DcDatabuffIssueLog;
import org.apache.ibatis.builder.annotation.ProviderMethodResolver;
import org.apache.ibatis.jdbc.SQL;

public class DcDatabuffIssueLogSqlProvider implements ProviderMethodResolver {

    public String insert(DcDatabuffIssueLog issueLog) {
        preprocessIssueLog(issueLog);
        return new SQL() {{
            INSERT_INTO("dc_databuff_issue_log");
            VALUES("issue_id", "#{issueId}");
            VALUES("alarm_id", "#{alarmId}");
            VALUES("policy_id", "#{policyId}");
            VALUES("source", "#{source}");
            VALUES("create_time", "#{createTime}");
            VALUES("description", "#{description}");
        }}.toString();
    }

    private void preprocessIssueLog(DcDatabuffIssueLog issueLog) {
        // 预处理逻辑，例如限制 description 的长度
        final String description = issueLog.getDescription();
        if (description != null && description.length() > 255) {
            issueLog.setDescription(description.substring(0, 255));
        }
    }
}