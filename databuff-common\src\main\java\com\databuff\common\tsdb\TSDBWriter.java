package com.databuff.common.tsdb;

import com.databuff.common.tsdb.model.TSDBDatabaseInfo;
import com.databuff.common.tsdb.model.TSDBPoint;

import java.util.List;

/**
 * TSDB写入操作接口
 * 定义所有与TSDB写入相关的操作
 */
public interface TSDBWriter extends TSDBOperator {

    /**
     * 创建数据库
     *
     * @param databaseInfo 数据库信息
     * @return 是否创建成功
     */
    boolean createDatabase(TSDBDatabaseInfo databaseInfo);

    /**
     * 创建数据库（简化版）
     * 只需要提供数据库名称，其他参数从tsdbWriters中获取
     * 适用于在已有连接配置的情况下快速创建数据库
     *
     * @param databaseName 数据库名称
     * @return 是否成功创建数据库
     */
    boolean createDatabase(String databaseName);

    /**
     * 创建数据库（简化版）
     * 只需要提供数据库名称，其他参数从tsdbWriters中获取
     * 适用于在已有连接配置的情况下快速创建数据库
     *
     * @param databaseName 数据库名称
     * @param interval 数据点间隔（秒）
     * @return 是否成功创建数据库
     */
    boolean createDatabase(String databaseName, Integer interval);

    /**
     * 写入单个数据点
     *
     * @param databaseName 数据库名称
     * @param point 数据点
     * @return 是否写入成功
     */
    boolean writePoint(String databaseName, TSDBPoint point);

    /**
     * 批量写入数据点
     *
     * @param databaseName 数据库名称
     * @param points 数据点列表
     * @return 是否写入成功
     */
    boolean writePoints(String databaseName, List<TSDBPoint> points);
}
