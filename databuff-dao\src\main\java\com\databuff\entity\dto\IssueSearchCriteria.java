package com.databuff.entity.dto;

import com.databuff.entity.DcDatabuffIssueDetail;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class IssueSearchCriteria {
    private String description;
    private List<String> services;
    private String rootCauseNode;
    private String rootCauseType;
    private DcDatabuffIssueDetail.Source source;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date fromTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date toTime;
    private Integer page = 0;
    private Integer size = 50;
    private Integer topN = 10;
    private String sortField;
    private String sortOrder;
}