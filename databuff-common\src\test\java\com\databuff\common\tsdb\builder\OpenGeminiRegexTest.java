package com.databuff.common.tsdb.builder;

import com.databuff.common.tsdb.model.AggFun;
import com.databuff.common.tsdb.model.Aggregation;
import com.databuff.common.tsdb.model.Where;
import com.databuff.common.tsdb.model.WhereOp;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * OpenGemini正则表达式查询测试类
 * 主要测试包含特殊字符的正则表达式查询
 */
public class OpenGeminiRegexTest {

    @Test
    @DisplayName("测试包含特殊字符的正则表达式查询")
    public void testRegexWithSpecialChars() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("service.trace");
        builder.setMeasurement("service.trace");
        builder.addAgg(new Aggregation(AggFun.MAX, "maxDuration"));
        builder.addAgg(new Aggregation(AggFun.SUM, "cnt"));

        // 添加包含特殊字符的正则表达式条件
        builder.addWhere(new Where("resource", WhereOp.REGEX, "select * from xxl_job_lock where lock_name=? for update"));

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        String sql = queryBuilder.buildQuery();

        // 打印完整SQL
        System.out.println("\n===== testRegexWithSpecialChars =====\n" + sql + "\n=====================\n");

        // 验证结果
        assertNotNull(sql);
        // 验证特殊字符是否被正确转义
        assertTrue(sql.contains("\"resource\"::tag =~ /select \\* from xxl_job_lock where lock_name=\\? for update/"));
    }

    @Test
    @DisplayName("测试LIKE操作符包含特殊字符的查询")
    public void testLikeWithSpecialChars() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("service.trace");
        builder.setMeasurement("service.trace");
        builder.addAgg(new Aggregation(AggFun.MAX, "maxDuration"));
        builder.addAgg(new Aggregation(AggFun.SUM, "cnt"));

        // 添加包含特殊字符的LIKE条件
        builder.addWhere(new Where("resource", WhereOp.LIKE, "select * from xxl_job_lock where lock_name=? for update"));

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        String sql = queryBuilder.buildQuery();

        // 打印完整SQL
        System.out.println("\n===== testLikeWithSpecialChars =====\n" + sql + "\n=====================\n");

        // 验证结果
        assertNotNull(sql);
        // 验证特殊字符是否被正确转义
        assertTrue(sql.contains("\"resource\"::tag =~ /.*select \\* from xxl_job_lock where lock_name=\\? for update.*/"));
    }

    @Test
    @DisplayName("测试包含分号的正则表达式查询")
    public void testRegexWithSemicolon() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("service.trace");
        builder.setMeasurement("service.trace");
        builder.addAgg(new Aggregation(AggFun.MAX, "maxDuration"));
        builder.addAgg(new Aggregation(AggFun.SUM, "cnt"));

        // 添加包含分号的正则表达式条件
        builder.addWhere(new Where("resource", WhereOp.REGEX, "select * from table; drop table users;"));

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        String sql = queryBuilder.buildQuery();

        // 打印完整SQL
        System.out.println("\n===== testRegexWithSemicolon =====\n" + sql + "\n=====================\n");

        // 验证结果
        assertNotNull(sql);
        // 验证分号是否被正确转义
        assertTrue(sql.contains("\"resource\"::tag =~ /select \\* from table\\; drop table users\\;/"));
    }

    @Test
    @DisplayName("测试包含分号的LIKE查询")
    public void testLikeWithSemicolon() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("service.trace");
        builder.setMeasurement("service.trace");
        builder.addAgg(new Aggregation(AggFun.MAX, "maxDuration"));
        builder.addAgg(new Aggregation(AggFun.SUM, "cnt"));

        // 添加包含分号的LIKE条件
        builder.addWhere(new Where("resource", WhereOp.LIKE, "select * from table; drop table users;"));

        // 创建OpenGeminiQueryBuilder
        OpenGeminiQueryBuilder queryBuilder = new OpenGeminiQueryBuilder(builder);

        // 执行测试
        String sql = queryBuilder.buildQuery();

        // 打印完整SQL
        System.out.println("\n===== testLikeWithSemicolon =====\n" + sql + "\n=====================\n");

        // 验证结果
        assertNotNull(sql);
        // 验证分号是否被正确转义
        assertTrue(sql.contains("\"resource\"::tag =~ /.*select \\* from table\\; drop table users\\;.*/"));
    }
}
