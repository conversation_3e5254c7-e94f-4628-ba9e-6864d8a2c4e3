package com.databuff.common.tsdb.adapter;

import com.databuff.common.metric.dto.Query;
import com.databuff.common.tsdb.model.*;
import com.databuff.common.tsdb.pool.TSDBConnectPool;
import com.databuff.common.tsdb.wrapper.MoreDBWrapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class MoreDBAdapterTest {

    @Mock
    private TSDBConnectPool mockPool;

    @Mock
    private MoreDBWrapper mockMoreDBWrapper;

    private MoreDBAdapter adapter;

    @BeforeEach
    void setUp() throws Exception {
        when(mockPool.getTSDBClient()).thenReturn(mockMoreDBWrapper);
        adapter = new MoreDBAdapter(mockPool);
    }

    @Test
    void testExecuteQuery() throws Exception {
        // Arrange
        String sql = "SELECT * FROM measurement";
        String databaseName = "testDB";
        TSDBResultSet expectedResult = new TSDBResultSet();

        when(mockMoreDBWrapper.query(any(), any())).thenReturn(expectedResult);

        // Act
        TSDBResultSet result = adapter.executeQuery(mockPool.getTSDBClient(),sql, databaseName, null);

        // Assert
        assertNotNull(result);
        assertEquals(expectedResult, result);
        verify(mockPool).getTSDBClient();
        verify(mockMoreDBWrapper).query(any(), any());
        verify(mockPool).releaseConnection(mockMoreDBWrapper);
    }

    @Test
    void testCreateDatabase() throws Exception {
        // Arrange
        TSDBDatabaseInfo databaseInfo = new TSDBDatabaseInfo();
        databaseInfo.setDatabaseName("testDB");
        databaseInfo.setUserName("user");
        databaseInfo.setPassword("password");
        databaseInfo.setShard(2);
        databaseInfo.setReplication(1);
        databaseInfo.setKeepDay(30);
        databaseInfo.setInterval(60);

        when(mockMoreDBWrapper.databaseExists(anyString())).thenReturn(false);
        doNothing().when(mockMoreDBWrapper).createDatabase(
                anyString(), anyString(), anyString(), anyInt(), anyInt(), anyInt(), anyInt());

        // Act
        boolean result = adapter.createDatabase(mockPool.getTSDBClient(),databaseInfo);

        // Assert
        assertTrue(result);
        verify(mockPool).getTSDBClient();
        verify(mockMoreDBWrapper).databaseExists(databaseInfo.getDatabaseName());
        verify(mockMoreDBWrapper).createDatabase(
                databaseInfo.getDatabaseName(),
                databaseInfo.getUserName(),
                databaseInfo.getPassword(),
                databaseInfo.getShard(),
                databaseInfo.getReplication(),
                databaseInfo.getKeepDay(),
                databaseInfo.getInterval()
        );
        verify(mockPool).releaseConnection(mockMoreDBWrapper);
    }

    @Test
    void testWritePoints() throws Exception {
        // Arrange
        String databaseName = "testDB";
        List<TSDBPoint> points = new ArrayList<>();

        // 使用正确的构造函数创建TSDBPoint
        String measurement = "cpu";
        long timestamp = System.currentTimeMillis();
        Map<String, String> tags = new HashMap<>();
        tags.put("host", "server01");
        Map<String, Object> fields = new HashMap<>();
        fields.put("usage", 0.8);

        TSDBPoint point = new TSDBPoint(measurement, timestamp, tags, fields);
        points.add(point);

        doNothing().when(mockMoreDBWrapper).write(anyString(), anyList());

        // Act
        boolean result = adapter.writePoints(mockPool.getTSDBClient(),databaseName, points);

        // Assert
        assertTrue(result);
        verify(mockPool).getTSDBClient();
        verify(mockMoreDBWrapper).write(eq(databaseName), anyList());
        verify(mockPool).releaseConnection(mockMoreDBWrapper);
    }

    @Test
    void testGetConfigs() throws Exception {
        // Arrange
        Map<String, Object> expectedConfigs = new HashMap<>();
        expectedConfigs.put("url", "localhost:8086");
        expectedConfigs.put("user", "admin");

        when(mockMoreDBWrapper.getConfigs()).thenReturn(expectedConfigs);

        // Act
        Map<String, Object> result = adapter.getConfigs();

        // Assert
        assertNotNull(result);
        assertEquals(expectedConfigs, result);
        verify(mockPool).getTSDBClient();
        verify(mockMoreDBWrapper).getConfigs();
        verify(mockPool).releaseConnection(mockMoreDBWrapper);
    }

    @Test
    void testClose() throws Exception {
        // Act
        adapter.close();

        // Assert
        verify(mockPool).close();
    }

    @Test
    void testAutoCloseable() {
        // Test that the adapter implements AutoCloseable and can be used with try-with-resources
        assertDoesNotThrow(() -> {
            try (MoreDBAdapter testAdapter = new MoreDBAdapter(mockPool)) {
                // Do something with the adapter
                assertNotNull(testAdapter);
            }
        });

        verify(mockPool).close();
    }

    @Test
    void testExecuteShowTagValues() throws Exception {
        // 准备测试数据
        String query = "SHOW TAG VALUES FROM \"cpu\" WITH KEY = \"host\"";
        String databaseName = "testdb";

        // 创建模拟的查询结果
        TSDBResultSet mockResultSet = new TSDBResultSet();
        List<TSDBResult> results = new ArrayList<>();
        TSDBResult result = new TSDBResult();
        result.setQueryType("SHOW TAG VALUES");

        List<TSDBSeries> seriesList = new ArrayList<>();
        TSDBSeries series = new TSDBSeries();
        series.setName("cpu");

        // 设置标签和值
        Map<String, String> seriesTags = new HashMap<>();
        seriesTags.put("host", "server01");
        seriesTags.put("region", "us-west");
        series.setTags(seriesTags);

        // 需要设置一些值以使得空值检查通过
        List<List<Object>> values = new ArrayList<>();
        values.add(Arrays.asList("dummy"));
        series.setValues(values);

        seriesList.add(series);

        // 添加第二个系列来测试多个值
        TSDBSeries series2 = new TSDBSeries();
        series2.setName("cpu");
        Map<String, String> seriesTags2 = new HashMap<>();
        seriesTags2.put("host", "server02");
        seriesTags2.put("region", "us-east");
        series2.setTags(seriesTags2);
        series2.setValues(values); // 使用相同的值
        seriesList.add(series2);

        // 添加第三个系列
        TSDBSeries series3 = new TSDBSeries();
        series3.setName("cpu");
        Map<String, String> seriesTags3 = new HashMap<>();
        seriesTags3.put("host", "server03");
        seriesTags3.put("region", "eu-west");
        series3.setTags(seriesTags3);
        series3.setValues(values); // 使用相同的值
        seriesList.add(series3);

        result.setSeries(seriesList);
        results.add(result);
        mockResultSet.setResults(results);

        // 设置模拟行为
        when(mockMoreDBWrapper.query(any(Query.class), eq(TimeUnit.MILLISECONDS)))
            .thenReturn(mockResultSet);

        // 执行测试
        Map<String, Set<String>> tagValues = adapter.executeShowTagValues(mockPool.getTSDBClient(), query, databaseName);

        // 验证结果
        assertNotNull(tagValues);
        assertEquals(2, tagValues.size()); // 应该有host和region两个标签

        // 验证host标签的值
        assertTrue(tagValues.containsKey("host"));
        assertEquals(3, tagValues.get("host").size());
        assertTrue(tagValues.get("host").contains("server01"));
        assertTrue(tagValues.get("host").contains("server02"));
        assertTrue(tagValues.get("host").contains("server03"));

        // 验证region标签的值
        assertTrue(tagValues.containsKey("region"));
        assertEquals(3, tagValues.get("region").size());
        assertTrue(tagValues.get("region").contains("us-west"));
        assertTrue(tagValues.get("region").contains("us-east"));
        assertTrue(tagValues.get("region").contains("eu-west"));

        // 验证方法调用
        verify(mockMoreDBWrapper).query(any(Query.class), eq(TimeUnit.MILLISECONDS));
    }

    @Test
    void testExecuteShowTagValuesWithError() throws Exception {
        // 准备测试数据
        String query = "SHOW TAG VALUES FROM \"cpu\" WITH KEY = \"host\"";
        String databaseName = "testdb";

        // 创建带有错误的模拟查询结果
        TSDBResultSet mockResultSet = new TSDBResultSet();
        mockResultSet.setError("Database not found");

        // 设置模拟行为
        when(mockMoreDBWrapper.query(any(Query.class), eq(TimeUnit.MILLISECONDS)))
            .thenReturn(mockResultSet);

        // 执行测试
        Map<String, Set<String>> tagValues = adapter.executeShowTagValues(mockPool.getTSDBClient(), query, databaseName);

        // 验证结果 - 应该返回空映射
        assertNotNull(tagValues);
        assertTrue(tagValues.isEmpty());

        // 验证方法调用
        verify(mockMoreDBWrapper).query(any(Query.class), eq(TimeUnit.MILLISECONDS));
    }

    @Test
    void testExecuteShowTagValuesWithEmptySeries() throws Exception {
        // 准备测试数据
        String query = "SHOW TAG VALUES FROM \"cpu\" WITH KEY = \"host\"";
        String databaseName = "testdb";

        // 创建模拟的查询结果，但没有系列数据
        TSDBResultSet mockResultSet = new TSDBResultSet();
        List<TSDBResult> results = new ArrayList<>();
        TSDBResult result = new TSDBResult();
        result.setQueryType("SHOW TAG VALUES");
        result.setSeries(new ArrayList<>()); // 空系列列表
        results.add(result);
        mockResultSet.setResults(results);

        // 设置模拟行为
        when(mockMoreDBWrapper.query(any(Query.class), eq(TimeUnit.MILLISECONDS)))
            .thenReturn(mockResultSet);

        // 执行测试
        Map<String, Set<String>> tagValues = adapter.executeShowTagValues(mockPool.getTSDBClient(), query, databaseName);

        // 验证结果 - 应该返回空映射
        assertNotNull(tagValues);
        assertTrue(tagValues.isEmpty());

        // 验证方法调用
        verify(mockMoreDBWrapper).query(any(Query.class), eq(TimeUnit.MILLISECONDS));
    }

    @Test
    void testExecuteShowTagValuesWithException() throws Exception {
        // 准备测试数据
        String query = "SHOW TAG VALUES FROM \"cpu\" WITH KEY = \"host\"";
        String databaseName = "testdb";

        // 设置模拟行为 - 抛出异常
        when(mockMoreDBWrapper.query(any(Query.class), eq(TimeUnit.MILLISECONDS)))
            .thenThrow(new RuntimeException("Connection failed"));

        // 执行测试并验证异常
        Exception exception = assertThrows(RuntimeException.class, () -> {
            adapter.executeShowTagValues(mockPool.getTSDBClient(), query, databaseName);
        });

        // 验证异常消息
        assertTrue(exception.getMessage().contains("查询标签值失败"));

        // 验证方法调用
        verify(mockMoreDBWrapper).query(any(Query.class), eq(TimeUnit.MILLISECONDS));
    }
}
