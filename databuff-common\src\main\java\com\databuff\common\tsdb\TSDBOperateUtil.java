package com.databuff.common.tsdb;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.tsdb.builder.QueryBuilder;
import com.databuff.common.tsdb.config.TSDBRefreshScopeConfig;
import com.databuff.common.tsdb.factory.TSDBOperatorFactory;
import com.databuff.common.tsdb.impl.TSDBReaderImpl;
import com.databuff.common.tsdb.impl.TSDBWriterImpl;
import com.databuff.common.tsdb.model.*;
import com.databuff.common.tsdb.pool.TSDBConnectPool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Slf4j
@Component
public class TSDBOperateUtil implements TSDBReader, TSDBWriter {
    private TSDBReader tsdbReader;
    private TSDBWriter tsdbWriter;

    /**
     * 默认构造函数
     * 创建一个没有配置读写器的工具类实例
     */
    public TSDBOperateUtil() {
    }

    /**
     * 使用连接池构造函数
     * 通过TSDB连接池创建读写器
     *
     * @param tsdbConnectPool TSDB连接池，用于创建读写器
     */
    @Autowired(required = false)
    public TSDBOperateUtil(TSDBConnectPool tsdbConnectPool) {
        if (tsdbConnectPool != null) {
            this.tsdbReader = new TSDBReaderImpl(tsdbConnectPool, null);
            this.tsdbWriter = new TSDBWriterImpl(tsdbConnectPool);
            log.info("添加默认写入器成功");
        }
    }

    /**
     * 使用连接池构造函数
     * 通过TSDB连接池创建读写器
     *
     * @param tsdbConnectPool TSDB连接池，用于创建读写器
     */
    @Autowired(required = false)
    public TSDBOperateUtil(TSDBConnectPool tsdbConnectPool, TSDBRefreshScopeConfig tsdbRefreshScopeConfig) {
        if (tsdbConnectPool != null) {
            this.tsdbReader = new TSDBReaderImpl(tsdbConnectPool, tsdbRefreshScopeConfig);
            this.tsdbWriter = new TSDBWriterImpl(tsdbConnectPool);
            log.info("添加默认写入器成功");
        }
    }

    /**
     * 使用工厂构造函数
     * 通过TSDB操作工厂创建读写器
     *
     * @param factory TSDB操作工厂，用于创建读写器
     */
    @Autowired(required = false)
    public TSDBOperateUtil(TSDBOperatorFactory factory) {
        if (factory != null) {
            try {
                this.tsdbReader = factory.createReader();
                this.tsdbWriter = factory.createWriter();
            } catch (Exception e) {
                log.error("Failed to create TSDB reader or writer: {}", e.getMessage());
            }
        }
    }

    // TSDBReader 接口方法实现

    /**
     * 构建完整的SQL查询语句
     *
     * @param builder 查询构建器
     * @return 构建的完整SQL查询语句
     */
    @Override
    public String buildFullSql(QueryBuilder builder) {
        return tsdbReader.buildFullSql(builder);
    }

    /**
     * 构建SQL的WHERE子句
     *
     * @param builder 查询构建器
     * @return 构建的WHERE子句
     */
    @Override
    public String buildWhereSql(QueryBuilder builder) {
        return tsdbReader.buildWhereSql(builder);
    }

    /**
     * 执行查询操作
     *
     * @param builder 查询构建器
     * @return 查询结果集
     */
    @Override
    public TSDBResultSet executeQuery(QueryBuilder builder) {
        return tsdbReader.executeQuery(builder);
    }

    @Override
    public List<TSDBSeries> executeQueryForAllGroups(QueryBuilder builder) {
        return tsdbReader.executeQueryForAllGroups(builder);
    }

    @Override
    public TSDBSeries executeQueryForOneGroup(QueryBuilder builder) {
        return tsdbReader.executeQueryForOneGroup(builder);
    }


    @Override
    public TSDBResultSet apmPercentageLatency(QueryBuilder builder) {
        return tsdbReader.apmPercentageLatency(builder);
    }


    /**
     * 获取标签值
     *
     * @param builder 查询构建器
     * @return 标签名到标签值集合的映射
     */
    @Override
    public Map<String, Set<String>> showTagValues(QueryBuilder builder) {
        return tsdbReader.showTagValues(builder);
    }

    /**
     * 直接使用查询语句和数据库名称获取标签值
     *
     * @param query        查询语句
     * @param databaseName 数据库名称
     * @return 标签名到标签值集合的映射
     */
    @Override
    public Map<String, Set<String>> showTagValues(String query, String databaseName) {
        return tsdbReader.showTagValues(query, databaseName);
    }

    /**
     * 获取标签值
     *
     * @param field        字段名
     * @param databaseName 数据库名
     * @param measurement  测量名
     * @param wheres       条件列表
     * @return 标签名到标签值集合的映射
     */
    @Override
    public Map<String, Set<String>> showTagValues(String field, String databaseName, String measurement, List<Where> wheres) {
        return tsdbReader.showTagValues(field, databaseName, measurement, wheres);
    }

    /**
     * 获取标签值
     *
     * @param field        字段名
     * @param databaseName 数据库名
     * @param measurement  测量名
     * @param wheres       条件列表
     * @param keys         标签键集合
     * @return 标签名到标签值集合的映射
     */
    @Override
    public Map<String, Set<String>> showTagValues(String field, String databaseName, String measurement, List<Where> wheres, Collection<String> keys) {
        return tsdbReader.showTagValues(field, databaseName, measurement, wheres, keys);
    }

    /**
     * 获取指定时间范围内的标签值
     *
     * @param field        字段名
     * @param databaseName 数据库名
     * @param measurement  测量名
     * @param wheres       条件列表
     * @param keys         标签键集合
     * @param start        开始时间戳
     * @param end          结束时间戳
     * @return 标签名到标签值集合的映射
     */
    @Override
    public Map<String, Set<String>> showTagValues(String field, String databaseName, String measurement, List<Where> wheres, Collection<String> keys, Long start, Long end) {
        return tsdbReader.showTagValues(field, databaseName, measurement, wheres, keys, start, end);
    }

    /**
     * 获取指定周期内的标签值
     *
     * @param field        字段名
     * @param databaseName 数据库名
     * @param measurement  测量名
     * @param wheres       条件列表
     * @param keys         标签键集合
     * @param period       周期（秒）
     * @param timeOffset   时间偏移量
     * @return 标签名到标签值集合的映射
     */
    @Override
    public Map<String, Set<String>> showPeriodTagValues(String field, String databaseName, String measurement, List<Where> wheres, Collection<String> keys, Integer period, Integer timeOffset) {
        return tsdbReader.showPeriodTagValues(field, databaseName, measurement, wheres, keys, period, timeOffset);
    }

    /**
     * 获取指定周期内的标签值
     *
     * @param field        字段名
     * @param databaseName 数据库名
     * @param measurement  测量名
     * @param wheres       条件列表
     * @param keys         标签键集合
     * @param period       周期（秒）
     * @return 标签名到标签值集合的映射
     */
    @Override
    public Map<String, Set<String>> showPeriodTagValues(String field, String databaseName, String measurement, List<Where> wheres, Collection<String> keys, Integer period) {
        return tsdbReader.showPeriodTagValues(field, databaseName, measurement, wheres, keys, period);
    }

    /**
     * 执行带聚合函数的查询操作
     *
     * @param builder    查询构建器
     * @param tsAgg      时间聚合函数
     * @param valAgg     值聚合函数
     * @param topAgg     顶层聚合函数
     * @param otherParam 其他参数
     * @return 查询结果集
     * @throws Exception 查询异常
     */
    @Override
    public TSDBResultSet executeQuery(QueryBuilder builder, AggFun tsAgg, AggFun valAgg, AggFun topAgg, JSONObject otherParam) throws Exception {
        return tsdbReader.executeQuery(builder, tsAgg, valAgg, topAgg, otherParam);
    }

    // TSDBWriter 接口方法实现

    /**
     * 创建数据库
     * 并行向所有配置的写入器发送创建数据库请求
     * 只要有一个写入器成功创建，即返回成功
     *
     * @param databaseInfo 数据库信息
     * @return 是否成功创建数据库
     */
    @Override
    public boolean createDatabase(TSDBDatabaseInfo databaseInfo) {
        return tsdbWriter.createDatabase(databaseInfo);
    }


    public static int getKeepDay(String keepDay) {
        try {
            return Integer.parseInt(keepDay.substring(0, keepDay.length() - 1));
        } catch (Throwable e) {
        }
        return 7;
    }

    /**
     * 创建指定名称的数据库。
     *
     * @param databaseName 要创建的数据库名称
     * @return 创建成功返回true，否则返回false
     */
    @Override
    public boolean createDatabase(String databaseName) {
        return createDatabase(databaseName, null);
    }

    /**
     * 创建数据库（简化版）
     * 只需要提供数据库名称，其他参数从tsdbWriters中获取
     * 适用于在已有连接配置的情况下快速创建数据库
     *
     * @param databaseName 数据库名称
     * @return 是否成功创建数据库
     */
    @Override
    public boolean createDatabase(String databaseName, Integer interval) {
        return tsdbWriter.createDatabase(databaseName, interval);
    }

    /**
     * 写入单个数据点
     * 并行向所有配置的写入器发送写入请求
     * 只要有一个写入器成功写入，即返回成功
     *
     * @param databaseName 数据库名
     * @param point        数据点
     * @return 是否成功写入数据点
     */
    @Override
    public boolean writePoint(String databaseName, TSDBPoint point) {
        if (point == null) {
            log.debug("No points to write");
            return false;
        }
        return tsdbWriter.writePoint(databaseName, point);
    }

    /**
     * 批量写入多个数据点
     * 并行向所有配置的写入器发送批量写入请求
     * 只要有一个写入器成功写入，即返回成功
     *
     * @param databaseName 数据库名
     * @param points       数据点列表
     * @return 是否成功写入数据点
     */
    @Override
    public boolean writePoints(String databaseName, List<TSDBPoint> points) {
        if (CollectionUtils.isEmpty(points)) {
            log.debug("No points to write");
            return false;
        }
        return tsdbWriter.writePoints(databaseName, points);
    }

    /**
     * 关闭所有资源
     * 关闭读取器、所有写入器和异步执行线程池
     *
     * @throws Exception 关闭过程中的异常
     */
    @Override
    public void close() throws Exception {
        if (tsdbReader != null) {
            tsdbReader.close();
        }

        if (tsdbWriter != null) {
            tsdbWriter.close();
        }
    }

    /**
     * 获取配置信息
     * 优先返回读取器的配置，如果读取器不存在则返回第一个写入器的配置
     *
     * @return 配置信息映射
     */
    @Override
    public Map<String, Object> getConfigs() {
        if (tsdbReader != null) {
            return tsdbReader.getConfigs();
        } else if (tsdbWriter != null) {
            return tsdbWriter.getConfigs();
        } else {
            return Collections.emptyMap();
        }
    }


}
