package com.databuff.common.utils.wechat;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.databuff.common.constants.Constant;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author:TianMing
 * @date: 2021/12/15
 * @time: 17:15
 */
@Slf4j
public class WeChatTest {

    public static void main(String[] args) {

       //String token = WeChatUtils.getToken("wwa0821cdc86e2e510", "IYQaafw8hP1ZzFIX1R3UaljovSfN5-nLenyO2g6zrDU");
        String  token = "kHWdXh1C8OdtG3pW1YgkMnRLe_zpyKPW1Pu0KxwAzz8koF2AdPyHQZ0Ug0wnjNwttQaxFEHY86kUTy-DaVNEDpwfHffORa0mKAweA284Q0j0ZRerdmjDEKyydBRjPo00WZw6OCDtXRQNvpbe3fN86UqXytCUHrpYnJCtsTaRJT4XHzsFwqxRg4bkJx-CNNtW0tYhES9uUyyhM_dL5Kmhfg";
        System.out.println(token);

//        System.out.println(WeChatUtils.getUserId(token, "ujzKg6JNu7yxXrmP5kt7HDWtTbr9hU_Ad0BNstmFAhQ"));

        System.out.println(WeChatUtils.getUserInfo(token, "fangm"));
    }
}
