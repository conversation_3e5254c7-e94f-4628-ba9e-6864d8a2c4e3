package com.databuff.common.utils;

import com.databuff.common.tsdb.builder.QueryBuilder;
import com.databuff.common.tsdb.model.Where;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.databuff.common.constants.Constant.ENV_TAG1;
import static com.databuff.common.constants.Constant.ENV_TAG2;

public class SqlUtil {

    public static void whereAndGroupByTime(QueryBuilder queryBuilder, long fromTimeMs, long toTimeMs, String groupBys, Integer intervalSecond) {
        queryBuilder.addWhere(Where.gte("time", fromTimeMs));
        queryBuilder.addWhere(Where.lt("time", toTimeMs));
        if (StringUtils.isEmpty(groupBys) && intervalSecond == null) {
            return;
        }
        queryBuilder.addAllGroupBy(Arrays.asList(groupBys.split(",")));
        if (intervalSecond != null) {
            queryBuilder.setInterval(intervalSecond);
        }
    }


    public static void addEnvTagsWhere(List<Where> wheres, String tagKey, List<String> envTags) {
        if (CollectionUtils.isNotEmpty(envTags)) {
            wheres.add(Where.in(tagKey, envTags));
        }
    }

    public static void addEnvTagsWhere(List<Where> wheres, String tagKey1, List<String> envTag1s, String tagKey2, List<String> envTag2s) {
        addEnvTagsWhere(wheres,tagKey1,envTag1s);
        addEnvTagsWhere(wheres,tagKey2,envTag2s);
    }

    public static void addEnvTagsWhere(QueryBuilder queryBuilder, String tagKey1, List<String> envTag1s, String tagKey2, List<String> envTag2s) {
        addEnvTagsWhere(queryBuilder,tagKey1,envTag1s);
        addEnvTagsWhere(queryBuilder,tagKey2,envTag2s);
    }

    public  static void addEnvTagsWhere(QueryBuilder queryBuilder, List<String> envTag1s, List<String> envTag2s) {
        addEnvTagsWhere(queryBuilder,ENV_TAG1,envTag1s);
        addEnvTagsWhere(queryBuilder,ENV_TAG2,envTag2s);
    }
    public  static void addEnvTagsWhere(QueryBuilder queryBuilder, String tagKey, List<String> envTags) {
        if (CollectionUtils.isNotEmpty(envTags)) {
            queryBuilder.addWhere(Where.in(tagKey, envTags));
        }
    }
}
