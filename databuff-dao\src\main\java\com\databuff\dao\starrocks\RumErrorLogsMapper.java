package com.databuff.dao.starrocks;

import com.databuff.entity.dto.TimeValue;
import com.databuff.entity.rum.starrocks.RumErrorLogs;
import com.databuff.entity.rum.web.BrowserErrorStats;
import com.databuff.entity.rum.web.RumErrorLogsAggregateDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Mapper
@Repository
public interface RumErrorLogsMapper {
    List<RumErrorLogsAggregateDto> getErrorLogsList(
            @Param("appId") int appId,
            @Param("errorMessage") String errorMessage,
            @Param("offset") int offset,
            @Param("pageSize") int pageSize,
            @Param("fromTime") Date fromTime,
            @Param("toTime") Date toTime,
            @Param("sortField") String sortField,
            @Param("sortOrder") Sort.Direction sortOrder
    );


    List<RumErrorLogsAggregateDto> getErrorLogsListExcludingMessages(
            @Param("appId") int appId,
            @Param("excludeErrorMessages") List<String> excludeErrorMessages,
            @Param("errorMessage") String errorMessage,
            @Param("offset") int offset,
            @Param("pageSize") int pageSize,
            @Param("fromTime") Date fromTime,
            @Param("toTime") Date toTime,
            @Param("sortField") String sortField,
            @Param("sortOrder") Sort.Direction sortOrder
    );



    List<RumErrorLogsAggregateDto> getErrorLogsListByErrorMessages(
            @Param("appId") int appId,
            @Param("errorMessages") List<String> errorMessages,
            @Param("offset") int offset,
            @Param("pageSize") int pageSize,
            @Param("fromTime") Date fromTime,
            @Param("toTime") Date toTime,
            @Param("sortField") String sortField,
            @Param("sortOrder") Sort.Direction sortOrder
    );

    long countErrorLogs(@Param("appId") int appId, @Param("fromTime") Date fromTime, @Param("toTime") Date toTime, @Param("errorMessage") String errorMessage);

    long countErrorLogsExcludingMessages(@Param("appId") int appId, @Param("excludeErrorMessages") List<String> excludeErrorMessages, @Param("fromTime") Date fromTime, @Param("toTime") Date toTime, @Param("errorMessage") String errorMessage);


    List<RumErrorLogs> getJsErrorTrackingList(
            @Param("appId") int appId,
            @Param("userId") String userId,
            @Param("errorMessage") String errorMessage,
            @Param("ip") String ip,
            @Param("sessionId") String sessionId,
            @Param("offset") int offset,
            @Param("pageSize") int pageSize,
            @Param("fromTime") Date fromTime,
            @Param("toTime") Date toTime,
            @Param("sortField") String sortField,
            @Param("sortOrder") Sort.Direction sortOrder
    );

    long countJsErrorTrackingList(
            @Param("appId") int appId,
            @Param("userId") String userId,
            @Param("errorMessage") String errorMessage,
            @Param("ip") String ip,
            @Param("sessionId") String sessionId,
            @Param("fromTime") Date fromTime,
            @Param("toTime") Date toTime
    );

    RumErrorLogs getJsErrorTrackingDetail(
            @Param("appId") int appId,
            @Param("errorId") long errorId,
            @Param("errorTime") Date errorTime
    );


    List<TimeValue> getUVTrend(
            @Param("appId") int appId,
            @Param("fromTime") Date fromTime,
            @Param("toTime") Date toTime,
            @Param("timeBucket") String timeBucket
    );

    List<TimeValue> getJSErrorCountTrend(
            @Param("appId") int appId,
            @Param("fromTime") Date fromTime,
            @Param("toTime") Date toTime,
            @Param("timeBucket") String timeBucket
    );


    List<TimeValue> getTop5ErrorMessageTrend(
            @Param("appId") int appId,
            @Param("fromTime") Date fromTime,
            @Param("toTime") Date toTime,
            @Param("timeBucket") String timeBucket
    );


    List<BrowserErrorStats> getTop10JSErrorBrowsers(
            @Param("appId") int appId,
            @Param("fromTime") Date fromTime,
            @Param("toTime") Date toTime
    );


}
