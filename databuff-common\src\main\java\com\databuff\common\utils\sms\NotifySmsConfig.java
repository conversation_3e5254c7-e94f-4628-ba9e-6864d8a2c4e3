package com.databuff.common.utils.sms;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class NotifySmsConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;

    /**
     * api_key
     */
    private String apiKey;

    /**
     * 是否开启，默认1开启，0不开启
     */
    private Integer tenantEnable;
    /**
     * 是否开启，默认1开启，0不开启
     */
    private Integer enable;

    /**
     * dingtalk，wechat，sms，mail
     */
    private String notifyType;

    /**
     * 短信访问秘钥id
     */
    private String smsKeyId;

    /**
     * 短信访问秘钥
     */
    private String smsKeySecret;

    //总发送条数
    private Long totalNum;

    //当月发送条数
    private Long theMonthNum;
    /**
     * 短信模板
     */
    List<NotifySmsTemplate> templates ;
}
