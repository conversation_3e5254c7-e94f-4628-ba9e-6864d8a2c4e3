package com.databuff.entity.extend;

import com.databuff.entity.SearchParamSetter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class MetricSearch implements SearchParamSetter {

    private String apiKey;
    private String database;
    private String metric;
    private String fromTime;
    private String toTime;
    private int interval;
    private Map<String, String> filters;
    private List<String> groupBys;
    private List<String> fields;
    @ApiModelProperty("环境标签1")
    private List<String> envTag1s;
    @ApiModelProperty("环境标签2")
    private List<String> envTag2s;
}
