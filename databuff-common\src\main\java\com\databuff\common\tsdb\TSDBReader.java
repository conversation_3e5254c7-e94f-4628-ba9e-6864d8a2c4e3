package com.databuff.common.tsdb;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.tsdb.builder.QueryBuilder;
import com.databuff.common.tsdb.model.AggFun;
import com.databuff.common.tsdb.model.TSDBResultSet;
import com.databuff.common.tsdb.model.TSDBSeries;
import com.databuff.common.tsdb.model.Where;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * TSDB读取操作接口
 * 定义所有与TSDB读取相关的操作
 */
public interface TSDBReader extends TSDBOperator {

    /**
     * 构建完整的SQL查询语句
     *
     * @param builder 查询构建器
     * @return 完整的SQL查询语句
     */
    String buildFullSql(QueryBuilder builder);

    /**
     * 构建WHERE条件SQL
     *
     * @param builder 查询构建器
     * @return WHERE条件SQL
     */
    String buildWhereSql(QueryBuilder builder);

    /**
     * 执行查询
     *
     * @param builder 查询构建器
     * @return 查询结果集
     */
    TSDBResultSet executeQuery(QueryBuilder builder);

    /**
     * 根据QueryBuilder构建并执行查询，返回标签名称到其对应值集合的映射
     *
     * @param builder 查询构建器，包含数据库名称和标签过滤条件等参数
     * @return 标签名称到其所有可能值的集合映射
     */
    Map<String, Set<String>> showTagValues(QueryBuilder builder);

    /**
     * 直接使用查询语句和数据库名称获取标签值
     *
     * @param query 查询语句
     * @param databaseName 数据库名称
     * @return 标签名到标签值集合的映射
     */
    Map<String, Set<String>> showTagValues(String query, String databaseName);

    /**
     * 根据条件查询指定字段的标签值，并按分组键聚合结果
     *
     * @param field        需要查询的字段名称，不能为空
     * @param databaseName 数据库名称，不能为空
     * @param measurement  测量名称，不能为空
     * @param wheres       额外的查询条件列表，可为空
     * @return 分组键到标签值集合的映射，键为分组键名称，值为该分组下的所有标签值
     */
    Map<String, Set<String>> showTagValues(String field, String databaseName, String measurement, List<Where> wheres);

    /**
     * 根据条件查询指定字段的标签值，并按分组键聚合结果
     *
     * @param field        需要查询的字段名称，不能为空
     * @param databaseName 数据库名称，不能为空
     * @param measurement  测量名称，不能为空
     * @param wheres       额外的查询条件列表，可为空
     * @param keys         分组键列表，用于结果聚合，可为空
     * @return 分组键到标签值集合的映射，键为分组键名称，值为该分组下的所有标签值
     */
    Map<String, Set<String>> showTagValues(String field, String databaseName, String measurement, List<Where> wheres, Collection<String> keys);

    /**
     * 根据条件查询指定字段的标签值，并按分组键聚合结果
     *
     * @param field        需要查询的字段名称，不能为空
     * @param databaseName 数据库名称，不能为空
     * @param measurement  测量名称，不能为空
     * @param wheres       额外的查询条件列表，可为空
     * @param keys         分组键列表，用于结果聚合，可为空
     * @param start        时间范围起始时间戳（毫秒），可为空
     * @param end          时间范围结束时间戳（毫秒），可为空
     * @return 分组键到标签值集合的映射，键为分组键名称，值为该分组下的所有标签值
     */
    Map<String, Set<String>> showTagValues(String field, String databaseName, String measurement, List<Where> wheres, Collection<String> keys, Long start, Long end);

    /**
     * 根据指定的时间周期和偏移量，查询指定测量在时间范围内的标签值。
     *
     * @param field        字段名
     * @param databaseName 数据库名称
     * @param measurement  测量名称
     * @param wheres       过滤条件列表
     * @param keys         需要查询的标签键集合
     * @param period       时间周期（单位毫秒，必须为非负数）
     * @param timeOffset   时间偏移量（单位毫秒，允许为null，默认为0）
     * @return 包含标签键到标签值集合的映射，键为标签键，值为对应的标签值集合
     * @throws IllegalArgumentException 参数校验失败或时间计算溢出时抛出
     */
    Map<String, Set<String>> showPeriodTagValues(String field, String databaseName, String measurement, List<Where> wheres, Collection<String> keys, Integer period, Integer timeOffset);

    /**
     * 根据指定的时间周期和偏移量，查询指定测量在时间范围内的标签值。
     *
     * @param field        字段名
     * @param databaseName 数据库名称
     * @param measurement  测量名称
     * @param wheres       过滤条件列表
     * @param keys         需要查询的标签键集合
     * @param period       时间周期（单位毫秒，必须为非负数）
     * @return 包含标签键到标签值集合的映射，键为标签键，值为对应的标签值集合
     * @throws IllegalArgumentException 参数校验失败或时间计算溢出时抛出
     */
    Map<String, Set<String>> showPeriodTagValues(String field, String databaseName, String measurement, List<Where> wheres, Collection<String> keys, Integer period);

    /**
     * 执行高级查询
     *
     * @param builder    查询构建器
     * @param tsAgg      时间段的聚合方式
     * @param valAgg     不同分组值的聚合方式
     * @param topAgg     topN的聚合方式
     * @param otherParam 其他参数
     * @return 查询结果集
     * @throws Exception 查询异常
     */
    TSDBResultSet executeQuery(QueryBuilder builder, AggFun tsAgg, AggFun valAgg, AggFun topAgg, JSONObject otherParam) throws Exception;

    /**
     * 执行针对所有分组的查询
     * 该方法将查询所有满足条件的分组数据，并返回每个分组的结果
     *
     * @param builder    查询构建器，包含数据库名称、测量名称、聚合函数等参数
     * @return 查询结果集，包含所有分组的数据
     */
    List<TSDBSeries> executeQueryForAllGroups(QueryBuilder builder);

    /**
     * 执行针对特定分组的查询
     * 该方法将查询指定分组标签值组合的数据
     *
     * @param builder    查询构建器，包含数据库名称、测量名称、聚合函数等参数
     * @return 查询结果集，仅包含指定分组的数据
     */
    TSDBSeries executeQueryForOneGroup(QueryBuilder builder);

    /**
     * apm 百分位查询特殊接口
     * @param builder
     * @return
     */
    TSDBResultSet apmPercentageLatency(QueryBuilder builder);
}
