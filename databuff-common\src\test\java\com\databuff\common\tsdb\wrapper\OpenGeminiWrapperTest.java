package com.databuff.common.tsdb.wrapper;

import com.databuff.common.metric.dto.Query;
import com.databuff.common.tsdb.builder.QueryBuilder;
import com.databuff.common.tsdb.model.*;
import com.databuff.common.tsdb.util.OpenGeminiRetConvertUtil;
import org.influxdb.InfluxDB;
import org.influxdb.InfluxDBFactory;
import org.influxdb.dto.BatchPoints;
import org.influxdb.dto.Pong;
import org.influxdb.dto.QueryResult;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({InfluxDBFactory.class})
@PowerMockIgnore({"javax.management.*", "javax.net.ssl.*", "javax.crypto.*"})
public class OpenGeminiWrapperTest {

    @Mock
    private InfluxDB mockInfluxDB;

    private OpenGeminiWrapper wrapper;
    private Map<String, Object> config;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);

        // 设置配置
        config = new HashMap<>();
        config.put("url", "localhost:8086");
        config.put("user", "admin");
        config.put("password", "admin");
        config.put("queryTimeout", "5000");

        // 模拟InfluxDB工厂
        PowerMockito.mockStatic(InfluxDBFactory.class);
        PowerMockito.when(InfluxDBFactory.connect(anyString(), anyString(), anyString())).thenReturn(mockInfluxDB);

        // 模拟ping响应
        Pong pong = mock(Pong.class);
        when(pong.getVersion()).thenReturn("1.0.0");
        when(mockInfluxDB.ping()).thenReturn(pong);

        // 启用批处理
        doNothing().when(mockInfluxDB).enableBatch();

        // 模拟单点写入方法
        doNothing().when(mockInfluxDB).write(anyString(), anyString(), any(org.influxdb.dto.Point.class));

        // 创建wrapper
        wrapper = new OpenGeminiWrapper(config, DatabaseType.OPENGEMINI);
    }

    @Test
    public void testQuery() {
        // 准备测试数据
        Query query = new Query("SELECT * FROM cpu", "testdb");

        // 模拟查询结果
        QueryResult queryResult = new QueryResult();
        List<QueryResult.Result> results = new ArrayList<>();
        QueryResult.Result result = new QueryResult.Result();
        List<QueryResult.Series> seriesList = new ArrayList<>();
        QueryResult.Series series = new QueryResult.Series();
        series.setName("cpu");
        series.setColumns(Arrays.asList("time", "usage"));

        List<List<Object>> values = new ArrayList<>();
        values.add(Arrays.asList("2025-04-16T02:27:00Z", 0.85));
        values.add(Arrays.asList("2025-04-16T02:28:00Z", 0.75));
        series.setValues(values);

        seriesList.add(series);
        result.setSeries(seriesList);
        results.add(result);
        queryResult.setResults(results);

        when(mockInfluxDB.query(any(org.influxdb.dto.Query.class))).thenReturn(queryResult);

        // 执行测试
        TSDBResultSet resultSet = wrapper.query(query, TimeUnit.MILLISECONDS, null);

        // 验证结果
        assertNotNull(resultSet);
        assertFalse(resultSet.hasError());
        assertEquals(1, resultSet.getResults().size());

        TSDBResult tsdbResult = resultSet.getResults().get(0);
        assertFalse(tsdbResult.hasError());
        assertEquals(1, tsdbResult.getSeries().size());

        TSDBSeries tsdbSeries = tsdbResult.getSeries().get(0);
        assertEquals("cpu", tsdbSeries.getName());
        assertEquals(Arrays.asList("time", "usage"), tsdbSeries.getColumns());
        assertEquals(2, tsdbSeries.getValues().size());

        // 验证时间戳已转换为毫秒
        assertTrue(tsdbSeries.getValues().get(0).get(0) instanceof Long);
        assertEquals(Instant.parse("2025-04-16T02:27:00Z").toEpochMilli(), tsdbSeries.getValues().get(0).get(0));
        assertEquals(0.85, tsdbSeries.getValues().get(0).get(1));

        verify(mockInfluxDB).query(any(org.influxdb.dto.Query.class));
    }

    @Test
    public void testWrite() {
        // 准备测试数据
        String databaseName = "testdb";
        List<TSDBPoint> points = new ArrayList<>();

        Map<String, String> tags = new HashMap<>();
        tags.put("host", "server01");

        Map<String, Object> fields = new HashMap<>();
        fields.put("usage", 0.85);

        TSDBPoint point = new TSDBPoint("cpu", System.currentTimeMillis(), tags, fields);
        points.add(point);

        // 模拟 BatchPoints 和 write 方法
        doNothing().when(mockInfluxDB).write(any(BatchPoints.class));

        // 执行测试
        wrapper.write(databaseName, points);

        // 验证结果
        verify(mockInfluxDB).write(any(BatchPoints.class));
    }

    /**
     * 测试写入包含特殊字符的标签值
     * 验证特殊字符是否按照InfluxDB Line Protocol正确转义
     */
    @Test
    public void testWriteWithSpecialCharactersInTagValues() {
        // 准备测试数据
        String databaseName = "testdb";
        List<TSDBPoint> points = new ArrayList<>();
        long timestamp = 1625097600000L; // 使用固定时间戳便于验证

        // 1. 测试普通标签值
        Map<String, String> normalTags = new HashMap<>();
        normalTags.put("host", "server01");
        normalTags.put("region", "us-west");

        // 2. 测试包含特殊字符的标签值
        Map<String, String> specialCharTags = new HashMap<>();
        specialCharTags.put("location", "New York, USA");  // 包含逗号
        specialCharTags.put("status", "up=running");      // 包含等号
        specialCharTags.put("description", "Test Server"); // 包含空格

        // 3. 测试已经包含转义字符的标签值
        Map<String, String> escapedTags = new HashMap<>();
        escapedTags.put("path", "C:\\Windows");          // 包含反斜杠
        escapedTags.put("command", "grep \\, search");   // 已转义的逗号
        escapedTags.put("query", "key\\=value");        // 已转义的等号
        escapedTags.put("text", "hello\\ world");       // 已转义的空格

        // 4. 测试混合情况
        Map<String, String> mixedTags = new HashMap<>();
        mixedTags.put("complex", "a\\=b, c=d, e\\ f"); // 混合已转义和未转义的特殊字符

        // 创建测试点
        Map<String, Object> fields = new HashMap<>();
        fields.put("value", 1.0);

        points.add(new TSDBPoint("test_normal", timestamp, normalTags, fields));
        points.add(new TSDBPoint("test_special", timestamp, specialCharTags, fields));
        points.add(new TSDBPoint("test_escaped", timestamp, escapedTags, fields));
        points.add(new TSDBPoint("test_mixed", timestamp, mixedTags, fields));

        // 捕获写入的BatchPoints以验证生成的Line Protocol
        org.mockito.ArgumentCaptor<String> databaseCaptor = org.mockito.ArgumentCaptor.forClass(String.class);
        org.mockito.ArgumentCaptor<String> rpCaptor = org.mockito.ArgumentCaptor.forClass(String.class);
        org.mockito.ArgumentCaptor<org.influxdb.dto.Point> pointCaptor = org.mockito.ArgumentCaptor.forClass(org.influxdb.dto.Point.class);

        // 执行测试
        wrapper.write(databaseName, points);

        // 验证调用并捕获参数
        verify(mockInfluxDB, times(4)).write(databaseCaptor.capture(), rpCaptor.capture(), pointCaptor.capture());

        // 获取捕获的Point对象并打印其Line Protocol格式
        List<org.influxdb.dto.Point> capturedPoints = pointCaptor.getAllValues();
        System.out.println("\n===== Line Protocol Format for Points with Special Characters =====\n");

        // 验证普通标签值
        org.influxdb.dto.Point normalPoint = capturedPoints.get(0);
        String normalLineProtocol = normalPoint.lineProtocol();
        System.out.println("Normal Tags Line Protocol: " + normalLineProtocol);
        assertTrue(normalLineProtocol.contains("host=server01"));
        assertTrue(normalLineProtocol.contains("region=us-west"));

        // 验证特殊字符标签值
        org.influxdb.dto.Point specialPoint = capturedPoints.get(1);
        String specialLineProtocol = specialPoint.lineProtocol();
        System.out.println("Special Chars Line Protocol: " + specialLineProtocol);
        assertTrue(specialLineProtocol.contains("location=New\\ York\\,\\ USA"));
        assertTrue(specialLineProtocol.contains("status=up\\=running"));
        assertTrue(specialLineProtocol.contains("description=Test\\ Server"));

        // 验证已转义字符标签值
        org.influxdb.dto.Point escapedPoint = capturedPoints.get(2);
        String escapedLineProtocol = escapedPoint.lineProtocol();
        System.out.println("Escaped Chars Line Protocol: " + escapedLineProtocol);
        assertTrue(escapedLineProtocol.contains("path=C\\\\Windows"));
        assertTrue(escapedLineProtocol.contains("command=grep\\ \\\\\\,\\ search") ||
                escapedLineProtocol.contains("command=grep\\ \\\\,\\ search"));
        assertTrue(escapedLineProtocol.contains("query=key\\\\\\=value") ||
                escapedLineProtocol.contains("query=key\\\\=value"));
        assertTrue(escapedLineProtocol.contains("text=hello\\\\\\ world") ||
                escapedLineProtocol.contains("text=hello\\\\ world"));

        // 验证混合情况标签值
        org.influxdb.dto.Point mixedPoint = capturedPoints.get(3);
        String mixedLineProtocol = mixedPoint.lineProtocol();
        System.out.println("Mixed Chars Line Protocol: " + mixedLineProtocol);
        // 由于混合情况复杂，这里只打印出来，不做具体断言

        System.out.println("\n===== End of Line Protocol Format =====\n");
    }

    /**
     * 测试formatTagValue方法（通过反射访问私有方法）
     * 直接测试各种特殊字符的转义情况
     */
    @Test
    public void testFormatTagValue() throws Exception {
        // 使用反射访问私有方法
        java.lang.reflect.Method method = OpenGeminiWrapper.class.getDeclaredMethod(
                "formatTagValue",
                String.class
        );
        method.setAccessible(true);

        // 测试用例
        Map<String, String> testCases = new HashMap<>();
        testCases.put("normal", "normal");                      // 普通字符串
        testCases.put("with,comma", "with\\,comma");           // 包含逗号
        testCases.put("with=equals", "with\\=equals");         // 包含等号
        testCases.put("with space", "with\\ space");           // 包含空格
        testCases.put("with\\backslash", "with\\\\backslash"); // 包含反斜杠
        testCases.put("C:\\Windows", "C:\\\\Windows");         // Windows路径
        testCases.put("already\\,escaped", "already\\,escaped"); // 已转义的逗号
        testCases.put("already\\=escaped", "already\\=escaped"); // 已转义的等号
        testCases.put("already\\ escaped", "already\\ escaped"); // 已转义的空格
        testCases.put("mixed\\=a,b c", "mixed\\=a\\,b\\ c");   // 混合情况
        testCases.put("multi  space", "multi\\ space");         // 多个空格
        testCases.put("with\nline\rbreaks", "with\\ line\\ breaks"); // 换行符

        System.out.println("\n===== Testing formatTagValue Method =====\n");

        // 执行测试并验证结果
        for (Map.Entry<String, String> entry : testCases.entrySet()) {
            String input = entry.getKey();
            String expectedOutput = entry.getValue();
            String actualOutput = (String) method.invoke(wrapper, input);

            System.out.println("Input:          " + input);
            System.out.println("Expected Output: " + expectedOutput);
            System.out.println("Actual Output:   " + actualOutput);
            System.out.println();

            // 对于已经包含转义字符的情况，我们期望不会发生双重转义
            if (input.contains("\\")) {
                // 检查是否没有发生双重转义
                assertFalse(actualOutput.contains("\\\\\\"));
            }

            // 验证特殊字符已正确转义
            if (input.contains(",") && !input.contains("\\,")) {
                assertTrue(actualOutput.contains("\\,"));
            }
            if (input.contains("=") && !input.contains("\\=")) {
                assertTrue(actualOutput.contains("\\="));
            }
            if (input.contains(" ") && !input.contains("\\ ")) {
                assertTrue(actualOutput.contains("\\ "));
            }
        }

        System.out.println("===== End of formatTagValue Testing =====\n");
    }

    @Test
    public void testDatabaseExists() {
        // 准备测试数据
        String databaseName = "testdb";

        // 模拟查询结果
        QueryResult queryResult = new QueryResult();
        List<QueryResult.Result> results = new ArrayList<>();
        QueryResult.Result result = new QueryResult.Result();
        List<QueryResult.Series> seriesList = new ArrayList<>();
        QueryResult.Series series = new QueryResult.Series();

        List<List<Object>> values = new ArrayList<>();
        List<Object> value = new ArrayList<>();
        value.add("testdb");
        values.add(value);

        series.setValues(values);
        seriesList.add(series);
        result.setSeries(seriesList);
        results.add(result);
        queryResult.setResults(results);

        when(mockInfluxDB.query(any(org.influxdb.dto.Query.class))).thenReturn(queryResult);

        // 执行测试
        boolean exists = wrapper.databaseExists(databaseName);

        // 验证结果
        assertTrue(exists);
        verify(mockInfluxDB).query(any(org.influxdb.dto.Query.class));
    }

    @Test
    public void testCreateDatabase() {
        // 准备测试数据
        String databaseName = "testdb";
        String userName = "admin";
        String password = "admin";
        int shard = 2;
        int replication = 1;
        int keepDay = 30;
        int interval = 10;

        // 模拟查询结果
        QueryResult queryResult = new QueryResult();
        when(mockInfluxDB.query(any(org.influxdb.dto.Query.class))).thenReturn(queryResult);

        // 模拟单点写入方法
        doNothing().when(mockInfluxDB).write(anyString(), anyString(), any(org.influxdb.dto.Point.class));

        // 执行测试
        wrapper.createDatabase(databaseName, userName, password, shard, replication, keepDay, interval);

        // 验证结果
        // 捕获查询参数以验证SQL生成是否正确
        org.mockito.ArgumentCaptor<org.influxdb.dto.Query> queryCaptor = org.mockito.ArgumentCaptor.forClass(org.influxdb.dto.Query.class);
        verify(mockInfluxDB).query(queryCaptor.capture());

        // 检查生成的SQL是否符合预期
        String expectedSql = "CREATE DATABASE \"testdb\" WITH DURATION 30d REPLICATION 1 SHARD DURATION 2h INDEX DURATION 10h NAME \"autogen\"";
        System.out.println("Expected SQL: " + expectedSql);
        System.out.println("Actual SQL: " + queryCaptor.getValue().getCommand());
        assertEquals(expectedSql, queryCaptor.getValue().getCommand());
    }

    /**
     * 测试生成创建数据库的SQL语句
     * 使用反射访问私有方法
     */
    @Test
    public void testGenerateCreateDatabaseSql() throws Exception {
        try {
            // 使用反射访问私有方法
            java.lang.reflect.Method method = OpenGeminiWrapper.class.getDeclaredMethod(
                "generateCreateDatabaseSql",
                String.class, int.class, int.class, int.class, int.class
            );
            method.setAccessible(true);

            // 测试基本数据库创建（无附加参数）
            String sql1 = (String) method.invoke(wrapper, "test_db", 0, 0, 0, 0);
            System.out.println("Test 1 - Basic SQL: " + sql1);
            assertEquals("CREATE DATABASE \"test_db\"", sql1);

            // 测试带所有参数的数据库创建
            String sql2 = (String) method.invoke(wrapper, "test_db", 24, 2, 30, 7);
            System.out.println("Test 2 - Full SQL: " + sql2);
            String expected2 = "CREATE DATABASE \"test_db\" WITH DURATION 30d REPLICATION 2 SHARD DURATION 24h INDEX DURATION 7h NAME \"autogen\"";
            System.out.println("Test 2 - Expected: " + expected2);

            // 比较字符串长度
            System.out.println("Expected length: " + expected2.length());
            System.out.println("Actual length: " + sql2.length());

            // 比较字符串内容，按字符打印出来
            for (int i = 0; i < Math.min(expected2.length(), sql2.length()); i++) {
                if (expected2.charAt(i) != sql2.charAt(i)) {
                    System.out.println("Difference at position " + i + ": expected '" + expected2.charAt(i) +
                                       "', got '" + sql2.charAt(i) + "'");
                }
            }

            assertEquals(expected2, sql2);

            // 测试只带部分参数的数据库创建
            String sql3 = (String) method.invoke(wrapper, "test_db", 0, 2, 30, 0);
            System.out.println("Test 3 - Partial SQL: " + sql3);
            String expected3 = "CREATE DATABASE \"test_db\" WITH DURATION 30d REPLICATION 2 NAME \"autogen\"";
            System.out.println("Test 3 - Expected: " + expected3);
            assertEquals(expected3, sql3);
        } catch (Exception e) {
            System.out.println("Exception in test: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    @Test
    public void testClose() {
        // 模拟 close 方法
        doNothing().when(mockInfluxDB).close();

        // 执行测试
        wrapper.close();

        // 验证结果
        verify(mockInfluxDB).close();
    }

    /**
     * 测试OpenGeminiRetConvertUtil的formatResult方法
     */
    @Test
    public void testOpenGeminiRetConvertUtil_formatResult() {
        // 创建测试数据
        TSDBResult tsdbResult = new TSDBResult();
        QueryResult queryResult = new QueryResult();
        List<QueryResult.Result> results = new ArrayList<>();
        QueryResult.Result result = new QueryResult.Result();
        List<QueryResult.Series> seriesList = new ArrayList<>();
        QueryResult.Series series = new QueryResult.Series();

        series.setName("cpu");
        series.setColumns(Arrays.asList("time", "usage"));

        // 添加ISO格式时间戳和数值时间戳的混合数据
        List<List<Object>> values = new ArrayList<>();
        values.add(Arrays.asList("2025-04-16T02:27:00Z", 0.85));
        values.add(Arrays.asList(1625097600000L, 0.75)); // 毫秒时间戳
        series.setValues(values);

        // 添加标签
        Map<String, String> tags = new HashMap<>();
        tags.put("host", "server01");
        series.setTags(tags);

        seriesList.add(series);
        result.setSeries(seriesList);
        results.add(result);
        queryResult.setResults(results);

        // 测试毫秒时间单位
        AtomicBoolean hasCountFunction = new AtomicBoolean(false);
        TSDBResultSet resultSetMs = OpenGeminiRetConvertUtil.formatResult(tsdbResult, queryResult, "ms", hasCountFunction, QueryBuilder.QueryType.STANDARD);

        // 验证结果
        assertNotNull(resultSetMs);
        assertFalse(resultSetMs.hasError());
        assertEquals(1, resultSetMs.getResults().size());

        TSDBResult tsdbResultMs = resultSetMs.getResults().get(0);
        assertEquals(1, tsdbResultMs.getSeries().size());

        TSDBSeries tsdbSeriesMs = tsdbResultMs.getSeries().get(0);
        assertEquals("cpu", tsdbSeriesMs.getName());
        assertEquals(Arrays.asList("time", "usage"), tsdbSeriesMs.getColumns());
        assertEquals(2, tsdbSeriesMs.getValues().size());
        assertEquals("server01", tsdbSeriesMs.getTags().get("host"));

        // 验证ISO时间戳已转换为毫秒
        assertEquals(Instant.parse("2025-04-16T02:27:00Z").toEpochMilli(), tsdbSeriesMs.getValues().get(0).get(0));
        assertEquals(0.85, tsdbSeriesMs.getValues().get(0).get(1));

        // 验证数值时间戳保持不变
        assertEquals(1625097600000L, tsdbSeriesMs.getValues().get(1).get(0));

        // 测试秒时间单位
        TSDBResult tsdbResultForSeconds = new TSDBResult();
        TSDBResultSet resultSetSec = OpenGeminiRetConvertUtil.formatResult(tsdbResultForSeconds, queryResult, "s", hasCountFunction, QueryBuilder.QueryType.STANDARD);

        TSDBSeries tsdbSeriesSec = resultSetSec.getResults().get(0).getSeries().get(0);
        // 验证ISO时间戳已转换为秒
        assertEquals(Instant.parse("2025-04-16T02:27:00Z").toEpochMilli() / 1000, tsdbSeriesSec.getValues().get(0).get(0));
        // 验证数值时间戳已转换为秒
        assertEquals(1625097600000L / 1000, tsdbSeriesSec.getValues().get(1).get(0));
    }

    /**
     * 测试OpenGeminiRetConvertUtil的formatInfluxSql方法
     */
    @Test
    public void testOpenGeminiRetConvertUtil_formatInfluxSql() {
        // 测试普通查询
        AtomicBoolean hasCountFunction = new AtomicBoolean(false);
        String sql1 = "SELECT * FROM cpu WHERE time > now() - 1h";
        String result1 = OpenGeminiRetConvertUtil.formatInfluxSql(sql1, hasCountFunction);
        assertEquals(sql1, result1);
        assertFalse(hasCountFunction.get());

        // 测试count函数
        hasCountFunction.set(false);
        String sql2 = "SELECT count(usage) FROM cpu WHERE time > now() - 1h";
        String result2 = OpenGeminiRetConvertUtil.formatInfluxSql(sql2, hasCountFunction);
        assertEquals("SELECT usage FROM cpu WHERE time > now() - 1h", result2);
        assertTrue(hasCountFunction.get());

        // 测试show measurements
        hasCountFunction.set(false);
        String sql3 = "SHOW MEASUREMENTS WHERE name =~ /cpu/";
        String result3 = OpenGeminiRetConvertUtil.formatInfluxSql(sql3, hasCountFunction);
        assertEquals("SHOW MEASUREMENTS", result3);
        assertFalse(hasCountFunction.get());

        // 测试show tag keys
        hasCountFunction.set(false);
        String sql4 = "SHOW TAG KEYS FROM cpu WHERE time > now() - 1h";
        String result4 = OpenGeminiRetConvertUtil.formatInfluxSql(sql4, hasCountFunction);
        assertEquals("SHOW TAG KEYS FROM cpu", result4);
        assertFalse(hasCountFunction.get());

        // 测试show field keys
        hasCountFunction.set(false);
        String sql5 = "SHOW FIELD KEYS FROM cpu";
        String result5 = OpenGeminiRetConvertUtil.formatInfluxSql(sql5, hasCountFunction);
        assertEquals("SHOW FIELD KEYS FROM cpu", result5);
        assertFalse(hasCountFunction.get());
    }

    /**
     * 测试查询方法使用OpenGeminiRetConvertUtil处理count函数
     */
    @Test
    public void testQueryWithCountFunction() {
        // 准备测试数据
        Query query = new Query("SELECT count(usage) FROM cpu", "testdb");

        // 模拟查询结果
        QueryResult queryResult = new QueryResult();
        List<QueryResult.Result> results = new ArrayList<>();
        QueryResult.Result result = new QueryResult.Result();
        List<QueryResult.Series> seriesList = new ArrayList<>();
        QueryResult.Series series = new QueryResult.Series();
        series.setName("cpu");
        series.setColumns(Arrays.asList("time", "usage"));

        List<List<Object>> values = new ArrayList<>();
        values.add(Arrays.asList("2025-04-16T02:27:00Z", 10));
        values.add(Arrays.asList("2025-04-16T02:28:00Z", 15));
        series.setValues(values);

        seriesList.add(series);
        result.setSeries(seriesList);
        results.add(result);
        queryResult.setResults(results);

        // 捕获SQL查询
        org.mockito.ArgumentCaptor<org.influxdb.dto.Query> queryCaptor =
            org.mockito.ArgumentCaptor.forClass(org.influxdb.dto.Query.class);
        when(mockInfluxDB.query(queryCaptor.capture())).thenReturn(queryResult);

        // 执行测试
        TSDBResultSet resultSet = wrapper.query(query, TimeUnit.MILLISECONDS, null);

        // 验证结果
        assertNotNull(resultSet);
        // 验证SQL已被转换
        assertEquals("SELECT usage FROM cpu", queryCaptor.getValue().getCommand());

        // 验证结果集
        assertFalse(resultSet.hasError());
        assertEquals(1, resultSet.getResults().size());

        TSDBResult tsdbResult = resultSet.getResults().get(0);
        assertFalse(tsdbResult.hasError());
        assertEquals(1, tsdbResult.getSeries().size());

        TSDBSeries tsdbSeries = tsdbResult.getSeries().get(0);
        assertEquals("cpu", tsdbSeries.getName());
        assertEquals(Arrays.asList("time", "usage"), tsdbSeries.getColumns());
        assertEquals(2, tsdbSeries.getValues().size());
    }

    /**
     * 测试查询方法使用OpenGeminiRetConvertUtil处理时间单位转换
     */
    @Test
    public void testQueryWithTimeUnitConversion() {
        // 准备测试数据
        Query query = new Query("SELECT * FROM cpu", "testdb");

        // 模拟查询结果
        QueryResult queryResult = new QueryResult();
        List<QueryResult.Result> results = new ArrayList<>();
        QueryResult.Result result = new QueryResult.Result();
        List<QueryResult.Series> seriesList = new ArrayList<>();
        QueryResult.Series series = new QueryResult.Series();
        series.setName("cpu");
        series.setColumns(Arrays.asList("time", "usage"));

        List<List<Object>> values = new ArrayList<>();
        values.add(Arrays.asList("2025-04-16T02:27:00Z", 0.85));
        series.setValues(values);

        seriesList.add(series);
        result.setSeries(seriesList);
        results.add(result);
        queryResult.setResults(results);

        when(mockInfluxDB.query(any(org.influxdb.dto.Query.class))).thenReturn(queryResult);

        // 测试毫秒时间单位
        TSDBResultSet resultSetMs = wrapper.query(query, TimeUnit.MILLISECONDS, null);
        TSDBSeries tsdbSeriesMs = resultSetMs.getResults().get(0).getSeries().get(0);
        assertEquals(Instant.parse("2025-04-16T02:27:00Z").toEpochMilli(), tsdbSeriesMs.getValues().get(0).get(0));
        assertEquals("QUERY", resultSetMs.getResults().get(0).getQueryType());

        // 测试秒时间单位
        TSDBResultSet resultSetSec = wrapper.query(query, TimeUnit.SECONDS, null);
        TSDBSeries tsdbSeriesSec = resultSetSec.getResults().get(0).getSeries().get(0);
        assertEquals(Instant.parse("2025-04-16T02:27:00Z").toEpochMilli() / 1000, tsdbSeriesSec.getValues().get(0).get(0));
        assertEquals("QUERY", resultSetSec.getResults().get(0).getQueryType());
    }

    /**
     * 测试查询方法处理元数据查询
     */
    @Test
    public void testQueryWithMetadataQueries() {
        // 测试SHOW MEASUREMENTS
        Query measurementsQuery = new Query("SHOW MEASUREMENTS", "testdb");
        QueryResult measurementsResult = createMockQueryResult("measurements", Arrays.asList("name"),
                Arrays.asList(Arrays.asList("cpu"), Arrays.asList("memory")));
        when(mockInfluxDB.query(any(org.influxdb.dto.Query.class))).thenReturn(measurementsResult);
        TSDBResultSet measurementsResultSet = wrapper.query(measurementsQuery, TimeUnit.MILLISECONDS, null);
        assertEquals("SHOW MEASUREMENTS", measurementsResultSet.getResults().get(0).getQueryType());

        // 测试SHOW TAG KEYS
        Query tagKeysQuery = new Query("SHOW TAG KEYS FROM cpu", "testdb");
        QueryResult tagKeysResult = createMockQueryResult("cpu", Arrays.asList("tagKey"),
                Arrays.asList(Arrays.asList("host"), Arrays.asList("region")));
        when(mockInfluxDB.query(any(org.influxdb.dto.Query.class))).thenReturn(tagKeysResult);
        TSDBResultSet tagKeysResultSet = wrapper.query(tagKeysQuery, TimeUnit.MILLISECONDS, null);
        assertEquals("SHOW TAG KEYS", tagKeysResultSet.getResults().get(0).getQueryType());

        // 测试SHOW TAG VALUES
        Query tagValuesQuery = new Query("SHOW TAG VALUES FROM cpu WITH KEY = host", "testdb");
        QueryResult tagValuesResult = createMockQueryResult("cpu", Arrays.asList("key", "value"),
                Arrays.asList(Arrays.asList("host", "server01"), Arrays.asList("host", "server02")));
        when(mockInfluxDB.query(any(org.influxdb.dto.Query.class))).thenReturn(tagValuesResult);
        TSDBResultSet tagValuesResultSet = wrapper.query(tagValuesQuery, TimeUnit.MILLISECONDS, null);
        assertEquals("SHOW TAG VALUES", tagValuesResultSet.getResults().get(0).getQueryType());

        // 测试SHOW FIELD KEYS
        Query fieldKeysQuery = new Query("SHOW FIELD KEYS FROM cpu", "testdb");
        QueryResult fieldKeysResult = createMockQueryResult("cpu", Arrays.asList("fieldKey", "fieldType"),
                Arrays.asList(Arrays.asList("usage", "float"), Arrays.asList("status", "integer")));
        when(mockInfluxDB.query(any(org.influxdb.dto.Query.class))).thenReturn(fieldKeysResult);
        TSDBResultSet fieldKeysResultSet = wrapper.query(fieldKeysQuery, TimeUnit.MILLISECONDS, null);
        assertEquals("SHOW FIELD KEYS", fieldKeysResultSet.getResults().get(0).getQueryType());
    }

    /**
     * 创建模拟的QueryResult对象
     */
    private QueryResult createMockQueryResult(String name, List<String> columns, List<List<Object>> values) {
        QueryResult queryResult = new QueryResult();
        List<QueryResult.Result> results = new ArrayList<>();
        QueryResult.Result result = new QueryResult.Result();
        List<QueryResult.Series> seriesList = new ArrayList<>();
        QueryResult.Series series = new QueryResult.Series();

        series.setName(name);
        series.setColumns(columns);
        series.setValues(values);

        seriesList.add(series);
        result.setSeries(seriesList);
        results.add(result);
        queryResult.setResults(results);

        return queryResult;
    }
}
