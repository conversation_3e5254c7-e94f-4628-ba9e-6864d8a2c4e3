package com.databuff.common.metric;

/**
 * 定义 Flink 窗口聚合操作的类型
 */
public enum FlinkAggregationType {
    /**
     * 对窗口内所有值求和 (适用于数值类型)
     */
    SUM,

    /**
     * 取窗口内按事件时间戳排序的第一个值
     */
    FIRST,

    /**
     * 取窗口内按事件时间戳排序的最后一个值
     */
    LAST,

    /**
     * 计算窗口内所有值的平均值 (适用于数值类型) - 可选，如果需要
     */
    AVG,

    /**
     * 取窗口内所有值的最小值 (适用于可比较类型) - 可选
     */
    MIN,

    /**
     * 取窗口内所有值的最大值 (适用于可比较类型) - 可选
     */
    MAX,

    /**
     * 计算窗口内记录的数量 (通常由 Flink 窗口 API 隐式提供，但如果需要明确传递也可添加) - 可选
     */
    COUNT,

    /**
     * 计算窗口内最大值与最小值的差值 (适用于数值类型)
     */
    RANGE,

    /**
     * 表示不聚合，直接透传最后的值（类似 GAUGE）- 可选，如果需要明确区分
     */
    // GAUGE_LAST // 或者直接使用 LAST
}