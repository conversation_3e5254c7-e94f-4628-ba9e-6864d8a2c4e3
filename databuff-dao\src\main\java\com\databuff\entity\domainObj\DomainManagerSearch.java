package com.databuff.entity.domainObj;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class DomainManagerSearch{

    private String apiKey;
    /**
     * 实体类型 service,host,business,k8s_cluster,k8s_namespace,k8s_workload
     */
    private String objType ;
    /**
     * 查询条件
     */
    private String query;

    private Integer businessType ;

    /**
     * 服务id列
     */
    private List<String> svcIds;

}
