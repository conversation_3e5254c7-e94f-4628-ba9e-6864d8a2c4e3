package com.databuff.common.tsdb.model;

import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;
import org.influxdb.dto.QueryResult;

import java.util.*;

public class TSDBSeries {
    @Getter @Setter
    private List<String> units;
    @Getter @Setter
    private List<String> describeCns;
    @Getter @Setter
    private String name;
    @Getter @Setter
    private Map<String, String> tags;

    private List<String> columns;
    private Map<String, Integer> columnIndexMap;
    @Setter
    @Getter
    private List<List<Object>> values = new ArrayList<>();
    @Getter @Setter
    private List<RootDetail> rootDetails;

    public List<String> getColumns() {
        return columns != null ? Collections.unmodifiableList(columns) : null;
    }

    public void setColumns(List<String> columns) {
        this.columns = columns;
        updateColumnIndexMap();
    }

    public void addColumn(String column) {
        if (this.columns == null) {
            this.columns = Lists.newArrayList();
        }
        this.columns.add(column);
        if (this.columnIndexMap == null) {
            this.columnIndexMap = new HashMap<>();
        }
        this.columnIndexMap.put(column, this.columns.size() - 1);
    }

    private void updateColumnIndexMap() {
        if (this.columns == null) {
            this.columnIndexMap = null;
            return;
        }
        this.columnIndexMap = new HashMap<>();
        for (int i = 0; i < this.columns.size(); i++) {
            this.columnIndexMap.put(this.columns.get(i), i);
        }
    }

    public Integer getColumnIndex(String column) {
        if (column == null || this.columnIndexMap == null) {
            return null;
        }
        return this.columnIndexMap.get(column);
    }

    /**
     * 从QueryResult.Series和tagValues创建TSDBSeries对象
     *
     * @param series QueryResult.Series对象
     * @param tagValues 标签值列表
     * @param timeUnit 时间单位，"s"表示秒，"ms"表示毫秒
     * @return 创建的TSDBSeries对象
     */
    public static TSDBSeries fromSeriesAndTagValues(QueryResult.Series series, List<Object> tagValues, String timeUnit) {
        if (series == null || tagValues == null) {
            return null;
        }

        // 校验 timeUnit 参数
        if (!"s".equals(timeUnit) && !"ms".equals(timeUnit)) {
            throw new IllegalArgumentException("timeUnit must be 's' or 'ms'");
        }

        TSDBSeries tsdbSeries = new TSDBSeries();
        tsdbSeries.setName(series.getName());
        tsdbSeries.setTags(new HashMap<>());
        tsdbSeries.setValues(new ArrayList<>());

        // 防御性拷贝 tagValues，避免修改外部数据
        List<Object> safeTagValues = new ArrayList<>(tagValues);
        tsdbSeries.getValues().add(safeTagValues);

        final List<String> columns = new ArrayList<>(series.getColumns());

        // 收集需要删除的索引
        List<Integer> indicesToRemove = new ArrayList<>();

        for (int i = 1; i < columns.size(); i++) {
            if (i < safeTagValues.size()) {
                final Object tagValue = safeTagValues.get(i);
                if (tagValue instanceof String) {
                    tsdbSeries.getTags().put(columns.get(i), (String) tagValue);
                    indicesToRemove.add(i);
                }
            }
        }

        // 倒序删除，避免索引错乱
        for (int i = indicesToRemove.size() - 1; i >= 0; i--) {
            int idx = indicesToRemove.get(i);
            columns.remove(idx);
            safeTagValues.remove(idx);
        }

        tsdbSeries.setColumns(columns);

        // 处理时间戳格式转换
        List<List<Object>> convertedValues = convertTimestampFormat(tsdbSeries.getValues(), columns, timeUnit);
        tsdbSeries.setValues(convertedValues);

        return tsdbSeries;
    }

    /**
     * 将ISO 8601格式的时间戳转换为毫秒或秒时间戳
     *
     * @param values 原始数据值列表
     * @param columns 列名列表
     * @param timeUnit 时间单位，"s"表示秒，"ms"表示毫秒
     * @return 转换后的数据值列表
     */
    private static List<List<Object>> convertTimestampFormat(List<List<Object>> values, List<String> columns, String timeUnit) {
        if (values == null || values.isEmpty() || columns == null || columns.isEmpty()) {
            return values;
        }

        // 查找time列的索引
        int timeIndex = columns.indexOf("time");
        if (timeIndex < 0) {
            return values;
        }

        List<List<Object>> convertedValues = new ArrayList<>(values.size());
        long divisor = "s".equals(timeUnit) ? 1000 : 1; // 如果是秒，需要除以1000

        for (List<Object> row : values) {
            if (row == null || row.size() <= timeIndex) {
                convertedValues.add(row);
                continue;
            }

            Object timeValue = row.get(timeIndex);
            if (timeValue == null) {
                convertedValues.add(row);
                continue;
            }

            String timeStr = timeValue.toString();
            // 检查是否是ISO 8601格式的时间戳 (例如: "2025-04-16T02:27:00Z")
            if (timeStr.matches("\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}Z")) {
                try {
                    // 将ISO 8601格式转换为时间戳
                    long timestamp = java.time.Instant.parse(timeStr).toEpochMilli();
                    if (divisor > 1) {
                        timestamp = timestamp / divisor;
                    }

                    // 创建新的行并替换时间戳
                    List<Object> newRow = new ArrayList<>(row);
                    newRow.set(timeIndex, timestamp);
                    convertedValues.add(newRow);
                } catch (java.time.format.DateTimeParseException e) {
                    // 如果解析失败，保留原始值
                    convertedValues.add(row);
                }
            } else if (timeValue instanceof Number) {
                // 如果已经是数字格式，检查是否需要转换单位
                Number numValue = (Number) timeValue;
                if (divisor > 1) {
                    // 创建新的行并替换时间戳
                    List<Object> newRow = new ArrayList<>(row);
                    newRow.set(timeIndex, numValue.longValue() / divisor);
                    convertedValues.add(newRow);
                } else {
                    convertedValues.add(row);
                }
            } else {
                // 如果不是ISO格式或数字，保留原始值
                convertedValues.add(row);
            }
        }

        return convertedValues;
    }

    public void addValue(List<Object> value) {
        this.values.add(value);
    }
}