package com.databuff.dao.mysql;

import com.databuff.entity.rum.mysql.RumAppSettings;
import com.databuff.entity.rum.web.AppListRequest;
import com.databuff.entity.rum.web.ApplicationSearchCriteria;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface RumAppV2Mapper {
    RumAppSettings getAppSettingsById(int id);

    RumAppSettings getAppSettingsByName(@Param("appName") String appName);

    int updateAppStatus(@Param("id") int id, @Param("status") int status);

    List<RumAppSettings> getAllAppSettings();

    List<RumAppSettings> getAppSettingsByKeyword(@Param("keyword") String keyword);

    List<RumAppSettings> getAppSettingsPage(ApplicationSearchCriteria criteria);

    List<RumAppSettings> getResourceAppSettingsPage(AppListRequest criteria);

    int deleteApplication(@Param("id") int id);

    boolean existsByAppName(@Param("appName") String appName);

    void insertApplication(RumAppSettings application);

    int updateApplicationName(@Param("id") int id, @Param("newName") String newName);

    int updateCustomIpRules(@Param("id") int id, @Param("customIpRules") String customIpRules);

    int toggleCustomIpRules(@Param("id") int id, @Param("useCustomIpRules") boolean useCustomIpRules);

    int updateScoreSettings(@Param("id") int id, @Param("webScoreSettings") String webScoreSettings);

    int updateSecuritySettings(@Param("id") int id, @Param("securitySettings") String securitySettings);

    int updateThresholdSettings(@Param("id") int id, @Param("thresholdSettings") String thresholdSettings);

    int updateUrlAggregationSettings(@Param("id") int id, @Param("urlAggregationSettings") String urlAggregationSettings);

    List<String> getAllDistinctAppNames();

}
