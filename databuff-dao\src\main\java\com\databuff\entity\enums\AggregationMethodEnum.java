package com.databuff.entity.enums;

import lombok.Getter;

/**
 * 聚合方式枚举
 * 平均，求和，最大值，75分位等
 */
@Getter
public enum AggregationMethodEnum {
    
    // 基础聚合方法
    AVG("AVG", "平均值"),                // 所有值的算术平均
    SUM("SUM", "求和"),                  // 所有值的总和
    COUNT("COUNT", "计数"),              // 数据点的数量
    MIN("MIN", "最小值"),                // 最小值
    MAX("MAX", "最大值"),                // 最大值
    MEDIAN("MEDIAN", "中位数"),          // 数据的中位数(50分位)
    
    // 分位数聚合
    P50("P50", "50分位"),                // 50分位数(中位数)
    P75("P75", "75分位"),                // 75分位数
    P90("P90", "90分位"),                // 90分位数
    P95("P95", "95分位"),                // 95分位数
    P99("P99", "99分位"),                // 99分位数
    P999("P999", "99.9分位"),            // 99.9分位数

    
    // 特殊聚合
    LAST("LAST", "最新值");              // 最后一个值
    
    private final String code;
    private final String name;
    
    AggregationMethodEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    /**
     * 根据编码获取枚举
     */
    public static AggregationMethodEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (AggregationMethodEnum method : values()) {
            if (method.getCode().equals(code)) {
                return method;
            }
        }
        return null;
    }
    
    /**
     * 根据名称获取枚举
     */
    public static AggregationMethodEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        for (AggregationMethodEnum method : values()) {
            if (method.getName().equals(name)) {
                return method;
            }
        }
        return null;
    }
    
    /**
     * 根据聚合类型获取枚举
     */
    public static AggregationMethodEnum fromAggregatorType(String aggregatorType) {
        if (aggregatorType == null) {
            return null;
        }
        
        switch (aggregatorType.toLowerCase()) {
            case "avg":
                return AVG;
            case "sum":
                return SUM;
            case "max":
                return MAX;
            case "min":
                return MIN;
            case "median":
                return MEDIAN;
            case "p50":
                return P50;
            case "p75":
                return P75;
            case "p90":
                return P90;
            case "p95":
                return P95;
            case "p99":
                return P99;
            case "p999":
                return P999;
            case "count":
                return COUNT;
            case "last":
                return LAST;
            default:
                // 尝试直接匹配code
                return getByCode(aggregatorType.toUpperCase());
        }
    }
}
