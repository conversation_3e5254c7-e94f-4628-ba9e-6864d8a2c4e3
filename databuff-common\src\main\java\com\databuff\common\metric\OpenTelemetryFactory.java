package com.databuff.common.metric;

import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.common.Attributes;
import io.opentelemetry.sdk.OpenTelemetrySdk;
import io.opentelemetry.sdk.metrics.InstrumentSelector;
import io.opentelemetry.sdk.metrics.SdkMeterProvider;
import io.opentelemetry.sdk.metrics.View;
import io.opentelemetry.sdk.metrics.ViewBuilder;
import io.opentelemetry.sdk.metrics.export.MetricExporter;
import io.opentelemetry.sdk.metrics.export.PeriodicMetricReader;
import io.opentelemetry.sdk.metrics.internal.SdkMeterProviderUtil;
import io.opentelemetry.sdk.resources.Resource;
import io.opentelemetry.semconv.resource.attributes.ResourceAttributes;

import java.util.concurrent.TimeUnit;

import static com.databuff.common.constants.MetricName.*;

public class OpenTelemetryFactory {

    public static OpenTelemetry init(String service, MetricExporter metricExporter, int interval) {
        Resource resource = Resource.getDefault()
                .merge(Resource.create(Attributes.of(ResourceAttributes.SERVICE_NAME, service)))
                .merge(Resource.create(Attributes.of(ResourceAttributes.SERVICE_NAMESPACE, "self-monitor")))
                .merge(Resource.create(Attributes.of(ResourceAttributes.SERVICE_VERSION, "1.0.0")))
                .merge(Resource.create(Attributes.of(ResourceAttributes.HOST_NAME, "host-name")));


        ViewBuilder viewBuilder = View.builder();
        SdkMeterProviderUtil.setCardinalityLimit(viewBuilder, 1000000);

        SdkMeterProvider sdkMeterProvider = SdkMeterProvider.builder()
                .registerMetricReader(PeriodicMetricReader.builder(metricExporter).setInterval(interval, TimeUnit.SECONDS).build())
                .registerView(InstrumentSelector.builder().setName(METRIC_PUBLISH).build(), viewBuilder.build())
                .registerView(InstrumentSelector.builder().setName(METRIC_NAME_DISCARD).build(), viewBuilder.build())
                .setResource(resource)
                .build();

        OpenTelemetry openTelemetry = OpenTelemetrySdk.builder()
                .setMeterProvider(sdkMeterProvider)
                .buildAndRegisterGlobal();

        return openTelemetry;
    }
}
