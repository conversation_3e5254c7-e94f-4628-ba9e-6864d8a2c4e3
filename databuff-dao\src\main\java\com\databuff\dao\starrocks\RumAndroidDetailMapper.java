package com.databuff.dao.starrocks;

import com.databuff.entity.rum.starrocks.RumAndroidLifecycleMethod;
import com.databuff.entity.rum.web.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface RumAndroidDetailMapper {

    RumAndroidLaunchDto selectLaunchById(@Param("request") AndroidLaunchDetailRequest request);

    List<RumAndroidLifecycleMethod> selectLifecycleMethods(
            @Param("associatedId") Long associatedId,
            @Param("type") Integer type,
            @Param("request") AndroidBaseDetailRequest request);

    List<RumAndroidLaunchSpanDto> selectLaunchSpans(@Param("request") AndroidLaunchDetailRequest request);

    RumAndroidPageDto selectPageById(@Param("request") AndroidPageDetailRequest request);

    List<RumAndroidPageSpanDto> selectPageSpans(@Param("request") AndroidPageDetailRequest request);

    RumAndroidActionDto selectActionById(@Param("request") AndroidActionDetailRequest request);

    List<RumAndroidActionSpanDto> selectActionSpans(@Param("request") AndroidActionDetailRequest request);

    RumAndroidAnrDto selectAnrById(@Param("request") AndroidAnrDetailRequest request);

    RumAndroidAnrDto selectAnrStack(@Param("request") AndroidAnrDetailRequest request);

    RumAndroidCrashDto selectCrashById(@Param("request") AndroidCrashDetailRequest request);

    RumAndroidCrashDto selectCrashStack(@Param("request") AndroidCrashDetailRequest request);

    List<RumAndroidActionDto> selectExceptionTrace(
            @Param("startTime") Date startTime,
            @Param("appId") Integer appId,
            @Param("userId") String userId);

    Long selectLaunchStart(@Param("request") AndroidLaunchDetailRequest request);

    Long selectPageStart(@Param("request") AndroidPageDetailRequest request);

    Long selectActionStart(@Param("request") AndroidActionDetailRequest request);
}
