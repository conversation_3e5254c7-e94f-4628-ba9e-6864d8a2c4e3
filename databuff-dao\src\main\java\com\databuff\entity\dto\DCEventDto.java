package com.databuff.entity.dto;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.databuff.common.tsdb.dto.detect.MultiDetectQueryRequest;
import com.databuff.entity.DcDatabuffProblem;
import com.databuff.handler.MultiDetectQueryRequestHandler;
import com.databuff.metric.RuleTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 根据数据库表字段建立实体类
 */

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class DCEventDto {
    /**
     * 事件常量
     */
    public static final String KEY_MESSAGE = "message";
    public static final String KEY_DURATION = "duration";
    public static final String KEY_CLASSIFICATION = "classification";
    public static final String KEY_LEVEL = "level";
    public static final String KEY_QUERY = "query";
    public static final String BUS_NAME = "busName";
    public static final String BUS_ID = "busId";

    // API密钥
    private String apiKey;

    // 事件ID
    private String id;

    // 域ID
    private String gid;

    // 问题ID
    private String issueId;

    // 影响面ID
    private String problemId;

    /**
     * 问题
     */
    private DcDatabuffProblem problem;

    // 创建日期
    private String createDt;

    // 监控规则ID
    private String monitorId;

    // 值
    private Double value;

    // 持续时间
    private Double duration;

    // 是否静默
    private boolean silence;

    // 事件最终等级
    private Integer level;

    // 是否已读
    private Boolean read;

    // 本次触发时间(异常点)
    private Long triggerTime;

    // 事件创建时间
    private Long createTime;

    // 链路开始时间
    private Long start;

    // 链路结束时间
    private Long end;

    // 规则创建者ID
    private String creatorId;

    // 规则修改者ID
    private String editorId;

    // 事件来源
    private String source;

    // 分类
    private RuleTypeEnum classification;

    // 检测方法
    private String type;

    // 触发消息
    private String trgTrd;

    // 触发字段
    private String triggerObjType;

    // 触发分组
    private String group;

    // 规则名称
    private String ruleName;

    // 事件消息
    private String message;

    // 度量
    private List<String> metric;

    // 度量标准
    private List<String> metrics;

    private List<String> by;

    // 业务系统列表
    private List<String> busName;

    // 主机列表
    private List<String> host;

    // 服务ID列表
    private List<String> serviceId;

    // 服务实例列表
    private List<String> serviceInstance;

    // 磁盘分区列表
    private List<String> deviceName;

    // 事件状态
    private String eventStatus;

    private String version;

    // 触发对象
    @TableField(value = "trigger", typeHandler = FastjsonTypeHandler.class)
    private JSONObject trigger;

    // 标签
    @TableField(value = "tags", typeHandler = FastjsonTypeHandler.class)
    private JSONObject tags;

    // 规则查询参数
    @TableField(value = "query", typeHandler = MultiDetectQueryRequestHandler.class)
    private MultiDetectQueryRequest query;

    // 单指标阈值
    @TableField(value = "thresholds", typeHandler = FastjsonTypeHandler.class)
    private JSONObject thresholds;

    // 多指标阈值
    @TableField(value = "multithresholds", typeHandler = FastjsonTypeHandler.class)
    private JSONObject multithresholds;

    public Boolean getRead() {
        return this.read;
    }

    public void setRead(Boolean read) {
        this.read = read;
    }
}
