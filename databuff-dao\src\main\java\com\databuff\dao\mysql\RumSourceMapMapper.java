package com.databuff.dao.mysql;

import com.databuff.entity.rum.mysql.RumSourceMap;
import com.databuff.entity.rum.web.SourceMapSearchCriteria;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface RumSourceMapMapper {

    void insert(RumSourceMap rumSourceMap);

    void update(RumSourceMap rumSourceMap);

    RumSourceMap getByAppIdAndFileName(@Param("appId") Integer appId, @Param("fileName") String fileName);

    List<RumSourceMap> getSourceMaps(SourceMapSearchCriteria search);


    List<RumSourceMap> getBatchByAppIdAndFileNames(@Param("appId") Integer appId, @Param("fileNames") List<String> fileNames);

    int deleteSourceMaps(@Param("ids") List<Integer> ids);

    List<RumSourceMap> getSourceMapsByIds(@Param("ids") List<Integer> ids);

}
