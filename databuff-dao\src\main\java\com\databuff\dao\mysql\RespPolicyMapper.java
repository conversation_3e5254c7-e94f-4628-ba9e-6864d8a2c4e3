package com.databuff.dao.mysql;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.databuff.entity.RespPolicy;
import com.databuff.entity.RespPolicyParams;
import com.databuff.entity.RespPolicyRet;
import com.databuff.handler.FastjsonListTypeHandler;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * 响应策略映射器接口
 * 提供对响应策略的数据库操作方法
 * @author:TianMing
 * @date: 2023/12/13
 * @time: 9:34
 */
@Mapper
@Repository
public interface RespPolicyMapper extends BaseMapper<RespPolicy> {

    /**
     * 查询由管理员创建的响应策略
     *
     * 该方法从数据库中检索所有由管理员（角色名为'Administrator'）创建的响应策略。
     * 查询条件包括：
     * 1. 用户必须拥有'Administrator'角色。
     * 2. 响应策略的gid字段为NULL。
     *
     * SQL查询逻辑：
     * - 主表：dc_databuff_resp_policy t0
     * - 子查询：dc_user cu、dc_user_role cur 和 dc_role cr 进行内连接，确保用户角色为'Administrator'
     * - 条件：cu.id = t0.creator_id 并且 cr.role_name = 'Administrator'
     * - 其他条件：t0.gid IS NULL
     *
     * @return 返回由管理员创建的响应策略列表
     */
    @Select(
            "SELECT t0.* " +
                    "FROM dc_databuff_resp_policy t0 " +
                    "WHERE EXISTS (" +
                    "   SELECT 1 FROM dc_user cu " +
                    "   INNER JOIN dc_user_role cur ON cu.id = cur.user_id " +
                    "   INNER JOIN dc_role cr ON cur.role_id = cr.id " +
                    "   WHERE cu.id = t0.creator_id " +
                    "   AND cr.role_name = 'Administrator'" +
                    ") " +
                    "AND t0.gid IS NULL"
    )
    @Results({
            @Result(column = "filter_conditions", property = "filterConditions", typeHandler = FastjsonListTypeHandler.class),
            @Result(column = "resp_conditions", property = "respConditions", typeHandler = FastjsonListTypeHandler.class),
            @Result(column = "resp_actions", property = "respActions", typeHandler = FastjsonListTypeHandler.class)
    })
    List<RespPolicyRet> findByCreatorAdmin();

    @Select(
            "<script>select t0.*,cu.account as creator,eu.account as editor from dc_databuff_resp_policy t0 " +
                    " left join dc_user cu on t0.creator_id = cu.id and t0.api_key = cu.c_id left join dc_user eu on t0.editor_id  = eu.id and t0.api_key = cu.c_id " +
                    "where 1=1" +
                    "<when test='id!=null and id!=&apos;&apos; '> and t0.id=#{id}</when> " +
                    "<when test='policyName!=null and policyName!=&apos;&apos; '> and t0.policy_name like binary CONCAT('%', #{policyName},'%')</when> " +
                    "<when test='enabled!=null'> and t0.enabled=#{enabled}</when> " +
                    "<when test='actionType!=null and actionType!=&apos;&apos; '> and t0.action_type=#{actionType}</when> " +
                    "<when test='creatorId!=null and creatorId!=&apos;&apos; '> and t0.creator_id=#{creatorId}</when> " +
                    "<when test='editorId!=null and editorId!=&apos;&apos; '> and t0.editor_id=#{editorId}</when> " +
                    "<when test='apiKey!=null and apiKey!=&apos;&apos; '> and t0.api_key=#{apiKey}</when> " +
                    "<when test='ids!=null and ids.size()>0 '> " +
                    "AND t0.id IN" +
                    "<foreach collection='ids' item='id' open='(' separator=',' close=')'>"+
                    "#{id}" +
                    "</foreach>" +
                    "</when>" +
                    "<if test='!allEntityPermission'> " +
                    "<if test='gids != null and gids.size() > 0'> AND (t0.gid IN <foreach item='gid' collection='gids' separator=',' open='(' close=')'>#{gid}</foreach>)</if> " +
                    "<if test='gids == null or gids.size() == 0'> AND (t0.gid IS NULL)</if> " +
                    "</if> " +
                    "<if test='!domainManagerStatus'> AND t0.gid IS NULL </if> " +
                    "<if test='domainManagerStatus'> AND t0.gid IS NOT NULL </if> " +
                    "<when test='sortField!=null and sortField!=&apos;&apos; '>\n" +
                    " order by ${sortField} ${sortOrder}\n" +
                    "</when>" +
                    "</script>")
    @Results({
            @Result(column = "filter_conditions", property = "filterConditions", typeHandler = FastjsonListTypeHandler.class),
            @Result(column = "resp_conditions", property = "respConditions", typeHandler = FastjsonListTypeHandler.class),
            @Result(column = "resp_actions", property = "respActions", typeHandler = FastjsonListTypeHandler.class)
    })
    List<RespPolicyRet> pageAll(RespPolicyParams queryParamDTO);

    @Select(
            "<script>select t0.*,cu.account as creator,eu.account as editor from dc_databuff_resp_policy t0 " +
                    " left join dc_user cu on t0.creator_id = cu.id and t0.api_key = cu.c_id " +
                    " left join dc_user eu on t0.editor_id = eu.id and t0.api_key = cu.c_id " +
                    " where 1=1 " +
                    "<when test='id != null and id != &apos;&apos;'> and t0.id=#{id}</when> " +
                    "<when test='policyName != null and policyName != &apos;&apos;'> and t0.policy_name like binary CONCAT('%', #{policyName}, '%')</when> " +
                    "<when test='enabled != null'> and t0.enabled=#{enabled}</when> " +
                    "<when test='actionType != null and actionType != &apos;&apos;'> and t0.action_type=#{actionType}</when> " +
                    "<when test='creatorId != null and creatorId != &apos;&apos;'> and t0.creator_id=#{creatorId}</when> " +
                    "<when test='editorId != null and editorId != &apos;&apos;'> and t0.editor_id=#{editorId}</when> " +
                    "<when test='apiKey != null and apiKey != &apos;&apos;'> and t0.api_key=#{apiKey}</when> " +
                    "<when test='ids != null and ids.size() > 0'> " +
                    " AND t0.id IN " +
                    "<foreach collection='ids' item='id' open='(' separator=',' close=')'>#{id}</foreach>" +
                    "</when> " +
                    "<if test='!allEntityPermission'> " +
                    "<if test='gids != null and gids.size() > 0'> AND (t0.gid IN <foreach item='gid' collection='gids' separator=',' open='(' close=')'>#{gid}</foreach>)</if> " +
                    "<if test='gids == null or gids.size() == 0'> AND (t0.gid IS NULL)</if> " +
                    "</if> " +
                    "<if test='!domainManagerStatus'> AND t0.gid IS NULL </if> " +
                    "<if test='domainManagerStatus'> AND t0.gid IS NOT NULL </if> " +
                    "<when test='sortField != null and sortField != &apos;&apos;'> " +
                    " order by " +
                    "<choose> " +
                    "<when test='@com.databuff.util.SqlUtils@isValidSortField(sortField)'>${sortField}</when> " +
                    "<otherwise>id</otherwise> " +
                    "</choose> " +
                    "<choose> " +
                    "<when test='@com.databuff.util.SqlUtils@isValidSortOrder(sortOrder)'>${sortOrder}</when> " +
                    "<otherwise>ASC</otherwise> " +
                    "</choose> " +
                    "</when> " +
                    "</script>")
    @Results({
            @Result(column = "filter_conditions", property = "filterConditions", typeHandler = FastjsonListTypeHandler.class),
            @Result(column = "resp_conditions", property = "respConditions", typeHandler = FastjsonListTypeHandler.class),
            @Result(column = "resp_actions", property = "respActions", typeHandler = FastjsonListTypeHandler.class)
    })
    List<RespPolicyRet> pageAllGid(RespPolicyParams queryParamDTO);

    @Select("<script>" +
            "SELECT policy_name " +
            "FROM dc_databuff_resp_policy " +
            "WHERE 1=1 AND api_key=#{apiKey} " +
            "<if test='!allEntityPermission'>" +
            "<if test='gids != null and gids.size() > 0'>AND (gid IN " +
            "<foreach item='gid' collection='gids' separator=',' open='(' close=')'>#{gid}</foreach>)</if>" +
            "<if test='gids == null or gids.size() == 0'>AND 1 = 0</if>" +
            "</if>" +
            "</script>")
    List<String> findRespPolicyNames(@Param("apiKey") String apiKey,
                                     @Param("allEntityPermission") boolean allEntityPermission,
                                     @Param("gids") Collection<String> gids);

    @Delete("<script>" +
            "DELETE FROM dc_databuff_resp_policy " +
            "WHERE api_key=#{apiKey} AND id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int deleteBatchIds(@Param("ids") List<Integer> ids, @Param("apiKey") String apiKey);

    /**
     * 根据gid批量删除响应策略
     *
     * @param gid 响应策略的gid
     * @return 删除的记录数
     */
    @Delete("<script>" +
            "DELETE FROM dc_databuff_resp_policy " +
            "WHERE gid = #{gid} " +
            "</script>")
    int deleteBatchByGid(@Param("gid") String gid);
}
