package com.databuff.common.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * @author:TianMing
 * @date: 2023/12/19
 * @time: 9:35
 */
public class ConditionalVerifyUtil {

    public static boolean evaluate(Map<String,Object> alarmTags , List<Object> conditions) {
        if (MapUtils.isEmpty(alarmTags) || CollectionUtils.isEmpty(conditions)) {
            return false;
        }
        Boolean result = null;
        for (Object obj : conditions) {
            if (!(obj instanceof Map)) {
                continue;
            }
            JSONObject expression = new JSONObject((Map<String, Object>) obj) ;
            if (expression.containsKey("left")) {
                Object left = expression.get("left");
                if (left==null){
                    result = false;
                    continue;
                }
                if (left instanceof List) {
                    boolean nestedResult = evaluate(alarmTags,(List<Object>) left);
                    result = applyOperator(result, nestedResult, (String) expression.get("connector"));
                } else if (left instanceof String) {
                    //未获取到数据中的字段
                    Object leftObj = alarmTags.get((String) left);
                    boolean leftResult = false ;
                    if (leftObj != null){
                        leftResult = evaluateExpression(leftObj, expression.get("right"), (String) expression.get("operator"));
                    }
                    result = applyOperator(result, leftResult, (String) expression.get("connector"));
                }
            }else{
                result = false;
            }
        }
        if (result == null){
            return false;
        }
        return result;
    }

    private static boolean evaluateExpression(Object leftObj, Object rightObj, String operator) {
        if ((rightObj == null || StringUtils.EMPTY.equals(rightObj))
                && (operator.equals("notEmpty") || operator.equals("empty"))) {
            boolean isNotEmpty = operator.equals("notEmpty");
            if (leftObj instanceof List) {
                return isNotEmpty ? !((List<?>) leftObj).isEmpty() : ((List<?>) leftObj).isEmpty();
            } else {
                return isNotEmpty ? StringUtils.isNotBlank(leftObj.toString()) : StringUtils.isBlank(leftObj.toString());
            }
        }
        String right = rightObj.toString();
        if (operator.equals("=")) {
            if (leftObj instanceof List){
                //如果是list且只有一个则取出来比较
                List<String> leftList = (List<String>)leftObj;
                return leftList.size()==1?leftList.get(0).equals(right):leftObj.toString().equals(right);
            }else{
                return leftObj.toString().equals(right);
            }
        } else if (operator.equals("!=")) {
            if (leftObj instanceof List){
                List<String> leftList = (List<String>)leftObj;
                return !(leftList.size()==1?leftList.get(0).equals(right):leftObj.toString().equals(right));
            }else{
                return !leftObj.toString().equals(right);
            }
        } else if (operator.equals("like")) {
            if (leftObj instanceof List){
                for (Object item : (List<?>) leftObj) {
                    if (item.toString().contains(right)) {
                        return true;
                    }
                }
                return false;
            }else{
                return leftObj.toString().contains(right);
            }
        } else if (operator.equals("notLike")) {
            if (leftObj instanceof List){
                for (Object item : (List<?>) leftObj) {
                    if (item.toString().contains(right)) {
                        return false;
                    }
                }
                return true;
            }else{
                return !leftObj.toString().contains(right);
            }
        } else if (operator.equals("inList")) {
            Set<String> rightList = new HashSet<>(Arrays.asList(right.split(",")));
            if (leftObj instanceof Set) {
                SetUtils.SetView<String> intersection = SetUtils.intersection(rightList, (Set<String>) leftObj);
                return !intersection.isEmpty();
            } else if (leftObj instanceof Collection) {
                return CollectionUtils.containsAny((Collection<String>) leftObj, rightList);
            } else {
                return rightList.contains(leftObj.toString());
            }
        } else if (operator.equals("notInList")) {
            Set<String> rightList = new HashSet<>(Arrays.asList(right.split(",")));
            if (leftObj instanceof Set) {
                SetUtils.SetView<String> intersection = SetUtils.intersection(rightList, (Set<String>) leftObj);
                return intersection.isEmpty();
            } else if (leftObj instanceof Collection) {
                return !CollectionUtils.containsAny((Collection<String>) leftObj, rightList);
            } else {
                return !rightList.contains(leftObj.toString());
            }
        }
        return false;
    }

    private static boolean applyOperator(Boolean leftResult, boolean rightResult, String operator) {
        if (leftResult == null){
            return rightResult;
        }
        if (operator == null){
            return leftResult && rightResult;
        }else if (operator.equals("AND")) {
            return leftResult && rightResult;
        } else if (operator.equals("OR")) {
            return leftResult || rightResult;
        }
        return false;
    }


    public static void main(String[] args) {
        String strArr = "[{\"left\":[{\"connector\":\"OR\",\"left\":\"host\",\"right\":\"host193\",\"operator\":\"=\"},{\"connector\":\"OR\",\"left\":\"host\",\"right\":\"host110\",\"operator\":\"=\"}],\"right\":\"\",\"operator\":\"=\"},{\"connector\":\"OR\",\"left\":\"service\",\"right\":\"webapp\",\"operator\":\"=\"}]";

        List<Object> listObjectSec = JSONArray.parseObject(strArr,List.class);
        Map<String,Object> rets = new HashMap<>();
        rets.put("host_id","1231312");
        rets.put("source","DataBuff");
        rets.put("host","centos-qingwu");
        boolean r = evaluate(rets,listObjectSec);

        System.out.println(r);


    }
}
