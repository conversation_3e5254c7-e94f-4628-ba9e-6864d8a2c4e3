package com.databuff.dao.mysql;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.databuff.audit.DcAudit;
import com.databuff.entity.dto.AuditSearchCriteria;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
public interface DcAuditMapper extends BaseMapper<DcAudit> {

    @Select("<script>" +
            "SELECT * FROM dc_audit " +
            "WHERE 1=1 " +
            "<if test='criteria.actor != null'>AND actor LIKE CONCAT('%', #{criteria.actor}, '%')</if> " +
            "<if test='criteria.action != null'>AND action LIKE CONCAT('%', #{criteria.action}, '%')</if> " +
            "<if test='criteria.entityName != null'>AND entityName LIKE CONCAT('%', #{criteria.entityName}, '%')</if> " +
            "<if test='criteria.entityType != null'>AND entityType = #{criteria.entityType}</if> " +
            "<if test='criteria.outcome != null'>AND outcome LIKE CONCAT('%', #{criteria.outcome}, '%')</if> " +
            "<if test='criteria.errorMessage != null'>AND errorMessage LIKE CONCAT('%', #{criteria.errorMessage}, '%')</if> " +
            "<if test='criteria.fromTime != null'>AND timestamp &gt;= #{criteria.fromTime}</if> " +
            "<if test='criteria.toTime != null'>AND timestamp &lt; #{criteria.toTime}</if> " +
            "ORDER BY timestamp DESC" +
            "</script>")
    List<DcAudit> search(@Param("criteria") AuditSearchCriteria criteria);

    @Select("<script>" +
            "SELECT DISTINCT actor, action, entityType, entityName, outcome FROM dc_audit " +
            "WHERE 1=1 " +
            "<if test='criteria.actor != null'>AND actor LIKE CONCAT('%', #{criteria.actor}, '%')</if> " +
            "<if test='criteria.action != null'>AND action LIKE CONCAT('%', #{criteria.action}, '%')</if> " +
            "<if test='criteria.entityName != null'>AND entityName LIKE CONCAT('%', #{criteria.entityName}, '%')</if> " +
            "<if test='criteria.entityType != null'>AND entityType = #{criteria.entityType}</if> " +
            "<if test='criteria.outcome != null'>AND outcome LIKE CONCAT('%', #{criteria.outcome}, '%')</if> " +
            "<if test='criteria.errorMessage != null'>AND errorMessage LIKE CONCAT('%', #{criteria.errorMessage}, '%')</if> " +
            "<if test='criteria.fromTime != null'>AND timestamp &gt;= #{criteria.fromTime}</if> " +
            "<if test='criteria.toTime != null'>AND timestamp &lt; #{criteria.toTime}</if> " +
            "</script>")
    List<Map<String, String>> getAuditTags(@Param("criteria") AuditSearchCriteria criteria);
}