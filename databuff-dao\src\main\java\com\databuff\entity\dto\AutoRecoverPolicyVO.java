package com.databuff.entity.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


@Data
@Setter
@Getter
@ToString
public class AutoRecoverPolicyVO {

    @ApiModelProperty(value = "周期数", example = "3")
    private Integer period;

    @ApiModelProperty(value = "是否触发事件恢复通知", example = "false")
    private Boolean senderNotify = false;
}
