package com.databuff.common.metric;

import com.databuff.common.utils.EventIdGenerator;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class EventIdGeneratorTest {

    @Test
    void shouldGenerateUniqueEventId() {
        EventIdGenerator generator = new EventIdGenerator();
        String eventId1 = generator.generateEventId(10, "daskhdjsadj", "123");
        String eventId2 = generator.generateEventId(10, "A", "123");
        Assertions.assertNotEquals(eventId1, eventId2);
    }

    @Test
    void shouldStartWithE() {
        EventIdGenerator generator = new EventIdGenerator();
        String eventId = generator.generateEventId(10, "A", "123");
        Assertions.assertTrue(eventId.startsWith("E"));
    }

    @Test
    void shouldContainSource() {
        EventIdGenerator generator = new EventIdGenerator();
        String eventId = generator.generateEventId(10, "A", "123");
        Assertions.assertTrue(eventId.contains("10"));
    }

    @Test
    void shouldContainType() {
        EventIdGenerator generator = new EventIdGenerator();
        String eventId = generator.generateEventId(10, "A", "123");
        Assertions.assertTrue(eventId.contains("A"));
    }

    @Test
    void shouldContainMonitorId() {
        EventIdGenerator generator = new EventIdGenerator();
        String eventId = generator.generateEventId(10, "A", "123");
        Assertions.assertTrue(eventId.contains("M123"));
    }
}