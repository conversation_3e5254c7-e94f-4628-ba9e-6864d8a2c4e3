# DataBuff SkyWalking 异常检测方法核心逻辑分析

## 目录

1. [概述](#概述)
2. [阈值检测核心逻辑](#阈值检测核心逻辑)
3. [动态基线检测核心逻辑](#动态基线检测核心逻辑)
4. [波动检测核心逻辑](#波动检测核心逻辑)
5. [状态检测核心逻辑](#状态检测核心逻辑)
6. [突变检测核心逻辑](#突变检测核心逻辑)
7. [共同特性分析](#共同特性分析)

## 概述

DataBuff SkyWalking告警系统实现了五种不同的异常检测方法，每种方法都有其特定的应用场景和算法逻辑。本文档深入分析每种检测方法的核心算法、参数配置和判断逻辑。

## 阈值检测核心逻辑

### 算法原理

阈值检测是最基础的异常检测方法，通过将指标值与预设的阈值进行比较来判断是否异常。

### 核心参数

- **critical**: 重要告警阈值
- **warning**: 次要告警阈值  
- **comparison**: 比较运算符（>、>=、<、<=、==）
- **continuous**: 是否要求连续触发
- **continuous_n**: 连续触发次数阈值
- **timeAggregator**: 时间维度聚合方式（avg、sum、max、min、last、least、always）

### 判断逻辑

1. **数据聚合**: 根据timeAggregator对时间窗口内的数据进行聚合
2. **阈值比较**: 使用comparison运算符将聚合值与阈值进行比较
3. **状态分级**: 
   - critical阈值触发 → 状态3（重要告警）
   - warning阈值触发 → 状态2（次要告警）
   - 都不触发 → 状态0（正常）

### 核心代码逻辑

```java
// 阈值判断核心方法
public EventEntity judgeThresholdStatus(ThresholdsDTO thresholds, String comparison, Map.Entry<Long, Double> timeValue) {
    final Double critical = thresholds.getCritical();
    final Double warning = thresholds.getWarning();
    final Double value = timeValue.getValue();
    
    // 根据比较运算符进行条件检查
    boolean conditionMet = false;
    switch (comparison) {
        case ">": conditionMet = checkCondition(value, critical, warning, (v, t) -> v > t); break;
        case ">=": conditionMet = checkCondition(value, critical, warning, (v, t) -> v >= t); break;
        case "<": conditionMet = checkCondition(value, critical, warning, (v, t) -> v < t); break;
        case "<=": conditionMet = checkCondition(value, critical, warning, (v, t) -> v <= t); break;
        default: conditionMet = checkCondition(value, critical, warning, (v, t) -> v.equals(t)); break;
    }
    
    // 状态分级
    int status = NORMAL_STATUS;
    if(conditionMet) {
        status = (critical != null && checkCriticalCondition(value, critical, comparison)) 
                ? CRITICAL_STATUS : WARNING_STATUS;
    }
    
    return EventEntity.builder()
            .way(EventEntity.DetectionType.threshold)
            .value(value)
            .status(status)
            .build();
}
```

### 特殊处理

- **连续触发检测**: 当continuous=true时，需要连续continuous_n次触发才产生告警
- **无数据处理**: 支持无数据场景的告警配置
- **时间聚合策略**: 支持多种聚合方式，如always模式根据比较符选择最值

## 动态基线检测核心逻辑

### 算法原理

动态基线检测基于历史数据计算动态基线，通过比较当前值与基线的偏离程度来判断异常。

### 核心参数

- **baselineScope**: 基线范围系数，用于计算上下边界
- **comparison**: 比较运算符
- **thresholds**: 偏离阈值配置
- **历史数据要求**: 至少需要2016个数据点（7天*20%*24小时*60分钟）

### 判断逻辑

1. **基线计算**: 
   - 从Redis缓存获取已计算的基线
   - 如果缓存不存在，异步调用CalculateBaseline计算
   - 基线包含：baseline（基线值）、upperBound（上边界）、lowerBound（下边界）

2. **偏离度计算**: 
   ```java
   double deviation = Math.abs(currentValue - baseline) / baseline;
   ```

3. **异常判断**:
   - critical阈值触发 → 状态3
   - warning阈值触发 → 状态2
   - 都不触发 → 状态0

### 核心代码逻辑

```java
// 动态基线检测核心方法
protected Map<Map, EventEntity> getAggValue(Map<Map, Map<Object, Double>> aggTimeSeries, 
                                           String comparison, ThresholdsDTO thresholds, 
                                           double baselineScope, Collection<QueryRequest> queries) {
    Map<Map, EventEntity> result = new HashMap<>();
    
    for (Map.Entry<Map, Map<Object, Double>> lineData : aggTimeSeries.entrySet()) {
        // 构建Redis缓存键
        final String keyStr = REDIS_PREFIX + "baseline" + SEPARATOR + group + SEPARATOR + comparison + SEPARATOR + baselineScope;
        final String baselineJson = jedisService.getJson(keyStr);
        
        if (baselineJson == null) {
            // 异步计算基线
            calculateBaseline.processAsync(keyStr, queries, lineDataKey, metricAggregator, comparison, baselineScope);
            continue;
        }
        
        // 解析基线结果
        final JSONObject baselineResult = JSON.parseObject(baselineJson);
        final double baseline = baselineResult.getDoubleValue("baseline");
        final double num = baselineResult.getDoubleValue("num");
        
        // 检查数据量是否充足
        if (num < 2016 || baseline <= 0) {
            continue;
        }
        
        // 计算偏离程度
        double deviation = Math.abs(currentValue - baseline) / baseline;
        
        // 判断异常状态
        EventEntity eventEntity = EventEntity.builder()
                .way(EventEntity.DetectionType.baseline)
                .value(currentValue)
                .originalValue(baseline)
                .build();
                
        if (critical != null && deviation > critical) {
            eventEntity.setStatus(3);
        } else if (warning != null && deviation > warning) {
            eventEntity.setStatus(2);
        } else {
            eventEntity.setStatus(0);
        }
        
        result.put(lineDataKey, eventEntity);
    }
    
    return result;
}
```

### 基线计算特性

- **分布式锁**: 使用DConfigLockOperator确保同一基线只被计算一次
- **缓存策略**: 数据充足时缓存1天，数据不足时缓存1小时
- **异步计算**: 支持异步和同步两种计算方式
- **数据质量检查**: 要求至少2016个有效数据点

## 波动检测核心逻辑

### 算法原理

波动检测通过比较当前周期与历史周期的数据变化来检测异常波动，支持多种波动计算方式。

### 核心参数

- **comparePeriod**: 比较周期偏移量
- **fluctuate**: 波动计算方式（valUp、valDown、yoyUp、yoyDown）
- **timeAggregator**: 时间聚合方式
- **thresholds**: 波动阈值配置

### 波动计算方式

1. **valUp**: 数据增加量 = currentVal - previousVal
2. **valDown**: 数据减少量 = previousVal - currentVal  
3. **yoyUp**: 同比增长率 = (currentVal - previousVal) / previousVal
4. **yoyDown**: 同比下降率 = (previousVal - currentVal) / previousVal

### 判断逻辑

1. **历史数据获取**: 通过设置timeOffset获取历史周期数据
2. **数据聚合**: 对当前和历史数据分别进行聚合
3. **波动计算**: 根据fluctuate参数计算波动值
4. **异常判断**: 将波动值与阈值比较

### 核心代码逻辑

```java
// 波动值计算方法
private double getFluctuationValue(double currentVal, double previousVal, String fluctuateType) {
    switch (fluctuateType) {
        case "valUp": 
            return currentVal - previousVal;
        case "valDown": 
            return previousVal - currentVal;
        case "yoyUp": 
            return (currentVal - previousVal) / (previousVal == 0 ? 1 : previousVal);
        case "yoyDown": 
            return (previousVal - currentVal) / (previousVal == 0 ? 1 : previousVal);
        default:
            return currentVal - previousVal;
    }
}

// 波动检测核心处理
protected Map<Map, EventEntity> getAggValue(final String timeAggregator,
                                           final Map<Map, Map<Object, Double>> aggTimeSeries,
                                           final Map<Map, Map<Object, Double>> beforeAggTimeSeries,
                                           final String fluctuate, ThresholdsDTO thresholds) {
    Map<Map, EventEntity> result = new HashMap<>();
    
    for (Map.Entry<Map, Map<Object, Double>> entry : aggTimeSeries.entrySet()) {
        final Map lineDataKey = entry.getKey();
        final Collection<Double> xValues = entry.getValue().values();
        
        // 获取对应的历史数据
        final Map<Object, Double> beforeData = beforeAggTimeSeries.get(lineDataKey);
        final Collection<Double> yValues = beforeData.values();
        
        // 根据时间聚合方式计算聚合值
        Double xMean, yMean, fluctuationValue;
        switch (timeAggregator) {
            case "avg":
                xMean = xValues.stream().filter(Objects::nonNull).mapToDouble(t -> t).average().getAsDouble();
                yMean = yValues.stream().filter(Objects::nonNull).mapToDouble(t -> t).average().getAsDouble();
                fluctuationValue = getFluctuationValue(xMean, yMean, fluctuate);
                break;
            // ... 其他聚合方式
        }
        
        // 根据阈值判断状态
        EventEntity eventEntity = EventEntity.builder().way(mutation).build();
        if (critical != null && fluctuationValue > critical) {
            eventEntity.setStatus(3);
        } else if (warning != null && fluctuationValue > warning) {
            eventEntity.setStatus(2);
        } else {
            eventEntity.setStatus(0);
        }
        
        eventEntity.setValue(fluctuationValue);
        result.put(lineDataKey, eventEntity);
    }
    
    return result;
}
```

### 特殊处理

- **零值处理**: 在计算同比时，分母为0时使用1代替避免除零错误
- **数据匹配**: 确保当前数据和历史数据的标签键完全匹配
- **时间聚合**: 支持多种聚合方式，包括avg、sum、max、min等

## 状态检测核心逻辑

### 算法原理

状态检测专门用于检测状态类指标的异常情况，通过统计异常状态的数量来判断是否触发告警。适用于服务状态、健康检查等场景。

### 核心参数

- **thresholds**: 异常状态数量阈值
- **fullWindow**: 是否要求周期内完整数据
- **period**: 检测周期（秒）

### 状态值定义

- **0**: 正常状态
- **1**: 警告状态
- **2**: 严重状态
- **3**: 致命状态

### 判断逻辑

1. **数据完整性检查**:
   ```java
   long expectedCount = period / 60L;  // 期望数据点数
   long actualCount = pointMap.values().stream()
           .filter(Objects::nonNull)
           .filter(v -> v >= 0)
           .count();
   return actualCount >= expectedCount;
   ```

2. **异常状态统计**:
   - 统计致命状态数量（value >= 3）
   - 统计严重状态数量（value >= 2）
   - 统计警告状态数量（value >= 1）

3. **阈值比较**:
   - criticalStateCount >= criticalThreshold → 状态3
   - warningStateCount >= warnThreshold → 状态2
   - 否则 → 状态0

### 核心代码逻辑

```java
// 状态阈值判断核心方法
public EventEntity judgeThresholdStatus(ThresholdsDTO thresholds, Collection<Double> realValues) {
    final Double criticalThreshold = thresholds.getCritical();
    final Double warnThreshold = thresholds.getWarning();

    // 统计异常状态数量
    long criticalStateCount = 0;
    long warningStateCount = 0;

    for (Double value : realValues) {
        if (value == null) continue;

        if (value >= 3) {          // 致命状态
            criticalStateCount++;
        } else if (value >= 2) {   // 严重状态
            criticalStateCount++;
        } else if (value >= 1) {   // 警告状态
            warningStateCount++;
        }
        // value == 0 为正常状态，不计入异常统计
    }

    // 根据阈值判断状态
    if (criticalThreshold != null && criticalStateCount >= criticalThreshold) {
        return EventEntity.builder()
                .way(EventEntity.DetectionType.stateThreshold)
                .value(criticalStateCount)
                .status(3)  // 重要告警
                .threshold(String.valueOf(criticalThreshold))
                .build();
    } else if (warnThreshold != null && warningStateCount >= warnThreshold) {
        return EventEntity.builder()
                .way(EventEntity.DetectionType.stateThreshold)
                .value(warningStateCount)
                .status(2)  // 次要告警
                .threshold(String.valueOf(warnThreshold))
                .build();
    } else {
        return EventEntity.builder()
                .way(EventEntity.DetectionType.stateThreshold)
                .value(0)
                .status(0)  // 正常状态
                .build();
    }
}
```

### 应用场景

- **服务健康检查**: 统计不健康服务实例数量
- **接口状态监控**: 统计异常状态的接口数量
- **资源状态检测**: 统计异常状态的资源数量

## 突变检测核心逻辑

### 算法原理

突变检测通过调用外部算法服务来检测时间序列数据中的突变点，适用于检测数据的突然变化。

### 核心参数

- **apiUrl**: 外部算法服务URL
- **timeout**: API调用超时时间
- **defTimeOffset**: 默认时间偏移量（默认60秒）

### 判断逻辑

1. **数据准备**:
   ```java
   Collection<JSONObject> datas = new ArrayList<>();
   for (Map.Entry<Map, Map<Object, Double>> entry : aggTimeSeries.entrySet()) {
       final Map<String, String> trigger = entry.getKey();
       datas.add(new JSONObject()
               .fluentPut("trigger", trigger)
               .fluentPut("data", entry.getValue()));
   }
   ```

2. **API调用**:
   ```java
   JSONObject requestBody = new JSONObject();
   requestBody.put("rule", detectQueryRequest);
   requestBody.put("datas", datas);

   Request request = new Request.Builder()
           .url(apiUrl)
           .post(RequestBody.create(requestStr, MediaType.parse("application/json; charset=utf-8")))
           .build();
   ```

3. **结果处理**:
   - 解析API返回的检测结果
   - 根据level字段设置告警级别
   - 创建EventEntity对象

### 核心代码逻辑

```java
// 调用外部算法服务进行突变检测
private Collection<JSONObject> getAggValueFromApi(DetectQueryRequest detectQueryRequest, Collection<JSONObject> datas) {
    JSONObject requestBody = new JSONObject();
    requestBody.put("rule", detectQueryRequest);
    requestBody.put("datas", datas);

    String requestStr = requestBody.toJSONString();
    Request request = new Request.Builder()
            .url(apiUrl)
            .post(RequestBody.create(requestStr, MediaType.parse("application/json; charset=utf-8")))
            .build();

    try (Response response = client.newCall(request).execute()) {
        if (!response.isSuccessful()) {
            log.error("突变检测API调用失败，状态码: {}", response.code());
            return Collections.emptyList();
        }

        String responseBody = response.body().string();
        JSONObject jsonResponse = JSON.parseObject(responseBody);

        if (jsonResponse == null || jsonResponse.getInteger("code") != 200) {
            log.error("突变检测API返回错误: {}", responseBody);
            return Collections.emptyList();
        }

        Object data = jsonResponse.get("data");
        if (data instanceof Collection) {
            return (Collection<JSONObject>) data;
        }

    } catch (IOException e) {
        log.error("突变检测API调用异常", e);
    }

    return Collections.emptyList();
}

// 创建事件实体
private EventEntity createEventEntity(long timestamp, double value, double originalValue,
                                    String level, double threshold, String signal) {
    EventEntity eventEntity = EventEntity.builder()
            .way(EventEntity.DetectionType.changePoint)
            .value(value)
            .originalValue(originalValue)
            .abnormalTime(timestamp)
            .threshold(String.valueOf(threshold))
            .comparison(signal)
            .build();

    switch (level) {
        case "critical":
            eventEntity.setStatus(3);
            break;
        case "warning":
            eventEntity.setStatus(2);
            break;
        case "nodata":
            eventEntity.setStatus(0);
            eventEntity.setNoData(true);
            break;
        default:
            return null;
    }
    return eventEntity;
}
```

### 特殊处理

- **时间偏移**: 通过defTimeOffset参数调整检测时间，避免数据延迟影响
- **HTTP客户端**: 使用OkHttpClient进行API调用，支持超时配置
- **错误处理**: 完善的异常处理机制，确保服务稳定性
- **结果验证**: 验证API返回结果的格式和内容

## 共同特性分析

### 统一的架构设计

所有异常检测方法都遵循统一的架构设计模式：

1. **接口实现**: 都实现了`AlarmCheckOperatorV2`接口
2. **基类继承**: 都继承了`BaseAlarmCheckV2`抽象基类
3. **Spring组件**: 都使用`@Component`注解注册为Spring Bean
4. **日志记录**: 都使用`@Slf4j`注解进行日志记录

### 核心方法结构

每个检测类都包含以下核心方法：

```java
// 主要检测逻辑
Map<Object, Object> afterCheckResult(DatabuffMonitor monitor,
                                    Map<Map, Map<Object, Double>> aggTimeSeries,
                                    DetectQueryRequest detectQueryRequest,
                                    MetricAggregator metricAggregator,
                                    Collection<QueryRequest> queries);

// 无数据处理逻辑
Map<Object, Object> afterCheckNoDataResult(DatabuffMonitor monitor,
                                          String mKey,
                                          Set<Map<String, String>> map,
                                          DetectQueryRequest detectQueryRequest);

// 标签增强逻辑
Map<Object, Object> needMoreTags(Map<Object, Object> eventsMap,
                               Collection<QueryRequest> queries,
                               Collection<String> gids);
```

### 数据处理流程

所有检测方法都遵循相似的数据处理流程：

1. **数据验证**: 检查输入数据是否为空
2. **数据过滤**: 根据配置过滤不满足条件的数据
3. **数据完整性检查**: 可选的数据完整性验证
4. **核心计算**: 执行具体的异常检测算法
5. **结果封装**: 将检测结果封装为EventEntity对象
6. **状态设置**: 设置检测方式和告警状态

### 配置参数体系

#### 通用参数

- **way**: 检测方式标识
- **thresholds**: 阈值配置（critical/warning）
- **period**: 检测周期
- **fullWindow**: 是否要求完整数据窗口
- **lessDataTimeframe**: 是否启用数据过滤

#### 时间聚合参数

- **timeAggregator**: 时间维度聚合方式
  - `avg/mean`: 平均值
  - `sum`: 求和
  - `max`: 最大值
  - `min`: 最小值
  - `last`: 最后一个值
  - `least`: 至少n个连续点
  - `always`: 始终满足条件

#### 比较运算参数

- **comparison**: 比较运算符
  - `>`: 大于
  - `>=`: 大于等于
  - `<`: 小于
  - `<=`: 小于等于
  - `==`: 等于

### 状态码体系

所有检测方法使用统一的状态码体系：

- **0**: 正常状态
- **2**: 次要告警（warning级别）
- **3**: 重要告警（critical级别）

### 缓存策略

#### Redis缓存使用

- **阈值检测**: 使用Redis记录连续触发次数
- **动态基线**: 缓存基线计算结果
- **波动检测**: 缓存历史数据对比结果
- **状态检测**: 缓存状态统计信息
- **突变检测**: 缓存API调用结果

#### 缓存键命名规范

```java
// 通用格式
private static final String REDIS_PREFIX = "monitor:metric:";
private static final String SEPARATOR = ":";

// 具体示例
String key = REDIS_PREFIX + "baseline" + SEPARATOR + group + SEPARATOR + comparison + SEPARATOR + baselineScope;
```

### 异常处理机制

#### 数据异常处理

1. **空数据检查**: 使用`CollectionUtils.isEmpty()`检查集合是否为空
2. **空值过滤**: 使用`Objects::nonNull`过滤空值
3. **数值验证**: 检查数值是否有效（如基线检测中的baseline > 0）

#### 配置异常处理

1. **阈值配置检查**: 确保critical和warning至少配置一个
2. **参数有效性验证**: 检查必要参数是否存在
3. **默认值设置**: 为可选参数提供合理默认值

#### 外部依赖异常处理

1. **Redis连接异常**: 使用try-catch包装Redis操作
2. **API调用异常**: 突变检测中的HTTP请求异常处理
3. **分布式锁异常**: 基线计算中的锁获取异常处理

### 性能优化策略

#### 数据处理优化

1. **流式处理**: 使用Java 8 Stream API进行数据处理
2. **早期过滤**: 在数据处理早期过滤无效数据
3. **懒加载**: 按需计算和加载数据

#### 缓存优化

1. **分层缓存**: 使用Redis作为分布式缓存
2. **缓存预热**: 异步计算基线等耗时操作
3. **缓存失效**: 合理设置缓存过期时间

#### 并发优化

1. **分布式锁**: 使用DConfigLockOperator避免重复计算
2. **异步处理**: 使用@Async注解进行异步处理
3. **线程安全**: 使用AtomicReference等线程安全类

### 扩展性设计

#### 接口抽象

通过`AlarmCheckOperatorV2`接口定义统一的检测方法签名，便于添加新的检测算法。

#### 基类复用

`BaseAlarmCheckV2`提供了通用的数据处理方法，新的检测算法可以直接复用。

#### 配置驱动

通过`DetectQueryRequest`对象传递配置参数，支持灵活的配置组合。

#### 插件化架构

每个检测方法都是独立的Spring组件，支持插件化的部署和管理。
