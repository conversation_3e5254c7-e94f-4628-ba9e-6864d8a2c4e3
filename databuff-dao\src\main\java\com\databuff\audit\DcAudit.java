package com.databuff.audit;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;

@Data
@TableName("dc_audit")
@ApiModel(description = "审计实体")
public class DcAudit {

    @TableId
    @TableField("identifier")
    @ApiModelProperty(value = "唯一标识符")
    private String identifier;

    @TableField("timestamp")
    @ApiModelProperty(value = "时间戳")
    private Timestamp timestamp;

    @TableField("actor")
    @ApiModelProperty(value = "操作人")
    private String actor;

    @TableField("origin")
    @ApiModelProperty(value = "来源")
    private String origin;

    @TableField("action")
    @ApiModelProperty(value = "操作")
    private String action;

    @TableField("entityType")
    @ApiModelProperty(value = "实体类型")
    private String entityType;

    @TableField("hasLink")
    @ApiModelProperty(value = "是否可以下钻，0-否，1-是")
    private Boolean hasLink;

    @TableField("entityId")
    @ApiModelProperty(value = "实体ID")
    private String entityId;

    @TableField("entityName")
    @ApiModelProperty(value = "实体名称")
    private String entityName;

    @TableField("url")
    @ApiModelProperty(value = "URL")
    private String url;

    @TableField("outcome")
    @ApiModelProperty(value = "结果")
    private String outcome;

    @TableField("beforeValue")
    @ApiModelProperty(value = "变更前值")
    private String beforeValue;

    @TableField("afterValue")
    @ApiModelProperty(value = "变更后值")
    private String afterValue;

    @TableField("errorMessage")
    @ApiModelProperty(value = "错误信息")
    private String errorMessage;
}