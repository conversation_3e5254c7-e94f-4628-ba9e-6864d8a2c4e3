package com.databuff.common.tsdb.wrapper;

import com.databuff.common.metric.AutoFillThreadLocal;
import com.databuff.common.metric.dto.Query;
import com.databuff.common.tsdb.model.DatabaseType;
import com.databuff.common.tsdb.model.TSDBResult;
import com.databuff.common.tsdb.model.TSDBResultSet;
import com.databuff.common.tsdb.model.TSDBSeries;
import com.databuff.common.tsdb.util.MoreDBRetConvertUtil;
import com.databuff.common.utils.HttpUtil;
import com.databuff.moredb.client.MoreDB;
import com.databuff.moredb.client.MoreDBConnect;
import com.databuff.moredb.client.MoreDBConnectFactory;
import com.databuff.moredb.client.config.MoreDBClientConfig;
import com.databuff.moredb.client.search.AsyncRequest;
import com.databuff.moredb.client.search.AsyncSearchRequest;
import com.databuff.moredb.client.search.AsyncSuggestRequest;
import com.databuff.moredb.common.model.query.SyncMoreDBHandler;
import com.databuff.moredb.model.query.QueryResult;
import com.databuff.moredb.proto.Common;
import com.databuff.moredb.proto.Meta;
import lombok.extern.slf4j.Slf4j;

import java.net.InetSocketAddress;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.databuff.moredb.proto.Statement.Stmt.StmtCase.QUERY;

@Slf4j
public class MoreDBWrapper implements DatabaseWrapper {
    private MoreDBConnect moreDBConnect;
    private final DatabaseType databaseType;
    private final Map<String, Object> config;

    public MoreDBWrapper(Map<String, Object> config, DatabaseType databaseType) {
        this.databaseType = databaseType;
        this.config = config;
        String serverUrl = (String) config.get("url");
        long queryTimeout = Long.parseLong((String) config.get("queryTimeout"));
        MoreDBClientConfig clientConfig = new MoreDBClientConfig();
        clientConfig.setQueryTimeout(queryTimeout);

        // 支持逗号分隔的集群配置
        // 当前版本的MoreDBConnectFactory已经支持逗号分隔的集群配置
        // 直接传入即可
        MoreDBConnect moreDBConnect = MoreDBConnectFactory.connect(serverUrl, clientConfig);
        this.moreDBConnect = moreDBConnect;
        log.info("MoreDBWrapper initialized with URL: {}", serverUrl);
    }

    private Map<String, MoreDB> databases = new ConcurrentHashMap<>();

    private final AtomicBoolean closed = new AtomicBoolean(false);

    @Override
    public DatabaseType getDatabaseType() {
        return databaseType;
    }

    @Override
    public Map<String, Object> getConfigs() {
        return config;
    }

    public void close() {
        // 关闭底层连接，确保Netty事件循环组优雅关闭
        if (closed.compareAndSet(false, true)) {
            try {
                log.info("Closing MoreDBWrapper connection");
                if (this.moreDBConnect != null) {
                    this.moreDBConnect.close();
                    this.moreDBConnect = null;
                }
                if (databases != null) {
                    databases.clear();
                }
                log.info("MoreDBWrapper connection closed successfully");
            } catch (Exception e) {
                log.error("Error closing MoreDBWrapper connection: {}", e.getMessage(), e);
            }
        }
    }


    private String regexNotExist = "the tag key .* not exist";
    private Pattern patternNotExist = Pattern.compile(regexNotExist);

    //    private String regexNotFound = "Measurement \\[.*\\] of Field name \\[.*\\]not found";
    private String regexNotFound = ".*not found";
    private Pattern patternNotFound = Pattern.compile(regexNotFound);
    private String regexMeasurementNotExist = "measurement name: \\S+ not exist";
    private Pattern patternMeasurementNotExist = Pattern.compile(regexMeasurementNotExist);

    /**
     * MoreDB 数据库执行查询。
     *
     * @param query    包含要执行的命令和要查询的数据库的Query对象。
     * @param timeUnit TimeUnit 对象，用于确定查询结果的时间单位。
     * @return ResultSet 查询执行的结果。
     */
    public TSDBResultSet query(Query query, TimeUnit timeUnit) {
        TSDBResult tsdbResult = new TSDBResult();
        com.databuff.moredb.model.query.QueryResult moreDBQueryResult = queryResultMoreDB(tsdbResult,query);
        final TSDBResultSet resultSet = MoreDBRetConvertUtil.formatResult(tsdbResult,moreDBQueryResult, TimeUnit.SECONDS.equals(timeUnit) ? "s" : "ms", new AtomicBoolean(false));

        String queryType = moreDBQueryResult.getQueryType();
        final Double fill = AutoFillThreadLocal.get();
        if (fill == null || !Objects.equals(QUERY.name(), queryType)) {
            return resultSet;
        }
        final List<TSDBResult> results = resultSet.getResults();
        if (results == null) {
            return resultSet;
        }
        for (TSDBResult result : results) {
            if (result == null || result.getSeries() == null) {
                continue;
            }
            for (TSDBSeries series : result.getSeries()) {
                if (series == null || series.getValues() == null) {
                    continue;
                }
                for (List<Object> value : series.getValues()) {
                    value.replaceAll(o -> o == null ? fill : o);
                }
            }
        }
        return resultSet;
    }

    /**
     * 根据给定的查询参数，从查询结果中提取并返回标签键的字符串集合。
     *
     * @param query 查询对象，用于指定查询条件。
     * @return 包含标签键的 {@code Collection<String>}，如果数据为空则返回空集合。
     * @throws IllegalArgumentException 如果查询结果类型不合法（非Map类型）或数据字段类型不合法（非Collection类型）。
     */
    public Collection<String> showTagKeys(Query query) {
        TSDBResult tsdbResult = new TSDBResult();
        com.databuff.moredb.model.query.QueryResult moreDBQueryResult = queryResultMoreDB(tsdbResult,query);
        Object result = moreDBQueryResult.getResult();

        // 检查结果是否为 Map 类型
        if (!(result instanceof Map)) {
            throw new IllegalArgumentException("Expected Map result but got: " + result.getClass().getName());
        }

        Map<?, ?> resultResult = (Map<?, ?>) result;
        Object data = resultResult.get("data");

        // 处理 data 为 null 的情况，返回空集合
        if (data == null) {
            return Collections.emptyList();
        }

        // 检查 data 是否为 Collection 类型
        if (data instanceof Collection<?>) {
            return (Collection<String>) data;
        }

        if (data instanceof Map<?, ?>) {
            return (Collection<String>) ((Map<?, ?>) data).keySet();
        }
        throw new IllegalArgumentException("Expected Collection for 'data' but got: " + data.getClass().getName());
    }


    /**
     * 执行对MoreDB数据库的查询并返回查询结果。
     *
     * @param query 包含要执行的命令和要查询的数据库的Query对象。
     * @return QueryResult 查询执行的结果。
     */
    public com.databuff.moredb.model.query.QueryResult queryResultMoreDB(TSDBResult tsdbResult,Query query) {
        // 获取查询命令和数据库名称
        final String command = query.getCommand();
        if (command == null) {
            throw new IllegalArgumentException("query command is null");
        }
        final String database = query.getDatabase();
        if (database == null) {
            throw new IllegalArgumentException("query database is null");
        }
        try {
            SyncMoreDBHandler syncMoreDBHandler = new SyncMoreDBHandler();
            final AsyncRequest asyncRequest;
            // 检查命令是否以"show"开头
            if (command.toLowerCase().startsWith("show")) {
                asyncRequest = new AsyncSuggestRequest(command, syncMoreDBHandler);
            } else {
                asyncRequest = new AsyncSearchRequest(command, syncMoreDBHandler);
            }
            asyncRequest.addDatabase(getMoreDB(database));
            asyncRequest.doSearch();
            return syncMoreDBHandler.getResultSet(60, TimeUnit.SECONDS);
        } catch (NegativeArraySizeException e) {
            log.error("NegativeArraySizeException occurred during query DATABASE 【{}】 SQL 【{}】", database, command, e);
            return new QueryResult();
        } catch (Throwable e) {
            // 获取错误消息
            final String message = e.getMessage();
            if (message == null) {
                log.error("query error DATABASE 【{}】 SQL 【{}】", database, command, e);
                return new QueryResult();
            }
            tsdbResult.setError(message);
            // 检查错误消息是否与不存在的标签键或字段名称的模式匹配
            final Matcher notExist = patternNotExist.matcher(message);
            final Matcher notFound = patternNotFound.matcher(message);
            final Matcher measurementNotExist = patternMeasurementNotExist.matcher(message);

            // 如果匹配，记录警告
            if (notExist.matches() || notFound.matches() || measurementNotExist.find()) {
                log.warn("query warn DATABASE 【{}】 SQL 【{}】", database, command);
            } else {
                // 否则，记录错误
                log.error("query error DATABASE 【{}】 SQL 【{}】", database, command, e);
            }
        }
        // 如果抛出异常，返回一个空的结果集
        return new QueryResult();
    }

    public boolean databaseExists(String database) {
        try {
            getMoreDB(database);
            return true;
        } catch (Exception e) {
            log.error("检测数据库存在性时发生异常", e);
            return false;
        }
    }

    public void createDatabase(String database, String userName, String password, int shard, int replication, int keepDay, int interval) {
        String url = generateHttpUrl();
        Map<String, String> headers = new HashMap<>();
        headers.put("userName", userName);
        headers.put("password", password);

        Map<String, Object> params = new HashMap<>();
        params.put("sql", generateCreateDatabaseSql(database, shard, replication, keepDay, interval));
        HttpUtil.postJson(url, headers, params, -1);
    }

    private String generateCreateDatabaseSql(String database, int shard, int replication, int keepDay, int interval) {
        keepDay++;
        long monthInterval = interval * 10;
        return "create database " + database + " with shard " + shard + ", replication " + replication + ", metaTtl " + keepDay + "d, futureTtl 1h, pastTtl 1h, (name day, ttl " + keepDay + "d, interval " + interval + "s), (name month, ttl " + keepDay + "d, interval " + monthInterval + "s)";
    }

    private String generateHttpUrl() {
        Map<Integer, Meta.Node> aliveNodes = moreDBConnect.getAliveNodes();
        if (aliveNodes == null || aliveNodes.size() == 0) {
            log.error("no alive nodes found to get http url");
            return null;
        }
        List<Meta.Node> list = new ArrayList<>(aliveNodes.values());
        Meta.Node node = list.get(0);
        InetSocketAddress address = moreDBConnect.getConfig().getAddresses().get(0);
        return "http://" + address.getHostName() + ":" + node.getHttp() + "/api/admin/database";
    }

    public void write(String database, List<Common.Point> points) {
        MoreDB moreDB = getMoreDB(database);
        moreDB.write(points);
    }

    public TSDBResultSet query(Query query) {
        return query(query, TimeUnit.MILLISECONDS);
    }

    private MoreDB getMoreDB(String database) {
        MoreDB moreDB = databases.get(database);
        if (moreDB == null) {
            moreDB = databases.computeIfAbsent(database, k -> moreDBConnect.getDB(k));
        }
        return moreDB;
    }

    @Override
    public boolean isConnected() {
        try {
            if (moreDBConnect == null) {
                return false;
            }

            // 首先尝试使用getAliveNodes方法检查
            Map<Integer, Meta.Node> aliveNodes = moreDBConnect.getAliveNodes();
            if (aliveNodes != null && !aliveNodes.isEmpty()) {
                return true;
            }

            // 如果getAliveNodes返回空，尝试通过其他方式验证连接
            // 尝试获取配置信息，这通常不需要活跃的连接
            if (moreDBConnect.getConfig() != null) {
                log.info("Connection appears to be initialized but no alive nodes reported");
                // 即使没有活跃节点，如果配置有效，我们也认为连接是有效的
                // 因为实际的读写操作可能仍然能够工作
                return true;
            }

            return false;
        } catch (Exception e) {
            log.warn("Error checking MoreDBWrapper connection: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public boolean reconnect() {
        try {
            log.info("Attempting to reconnect MoreDBWrapper");
            // 关闭现有连接
            if (moreDBConnect != null) {
                try {
                    moreDBConnect.close();
                } catch (Exception e) {
                    log.warn("Error closing existing connection during reconnect: {}", e.getMessage());
                }
            }

            // 重新创建连接
            String serverUrl = (String) config.get("url");
            long queryTimeout = Long.parseLong((String) config.get("queryTimeout"));
            MoreDBClientConfig clientConfig = new MoreDBClientConfig();
            clientConfig.setQueryTimeout(queryTimeout);

            moreDBConnect = MoreDBConnectFactory.connect(serverUrl, clientConfig);
            databases = new ConcurrentHashMap<>();

            // 验证连接是否成功
            boolean connected = isConnected();
            if (connected) {
                log.info("MoreDBWrapper reconnected successfully");
            } else {
                log.error("MoreDBWrapper reconnection failed");
            }
            return connected;
        } catch (Exception e) {
            log.error("Error reconnecting MoreDBWrapper: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public void resetState() {
        // 对于MoreDB连接，重置状态主要是确保连接有效
        try {
            if (!isConnected()) {
                reconnect();
            }
        } catch (Exception e) {
            log.warn("Error resetting MoreDBWrapper state: {}", e.getMessage());
        }
    }

    @Override
    public void clearTemporaryState() {
        // 对于MoreDB连接，清理临时状态可能包括清理查询缓存或其他临时资源
        // 当前实现中，我们不需要特别的清理操作
    }
}