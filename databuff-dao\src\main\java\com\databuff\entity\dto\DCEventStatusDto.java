package com.databuff.entity.dto;

import lombok.Builder;
import lombok.Data;

/**
 * 根据数据库表字段建立实体类
 */

@Data
@Builder
public class DCEventStatusDto {

    private String id;
    // 创建日期
    private String createDt;
    // 是否已读
    private Boolean read;

    private String readTime;

    public Boolean isRead() {
        return this.read;
    }
    public Boolean getRead() {
        return this.read;
    }
}
