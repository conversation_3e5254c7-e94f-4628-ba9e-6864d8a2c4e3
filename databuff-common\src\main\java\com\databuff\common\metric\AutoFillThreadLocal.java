package com.databuff.common.metric;

import java.util.concurrent.Callable;

/**
 * 管理线程局部变量的自动填充和清理工具类。使用ThreadLocal在当前线程中临时存储值，并确保在执行完回调后清理。
 */
public class AutoFillThreadLocal {
    private static final ThreadLocal<Double> THREAD_LOCAL = new ThreadLocal<>();

    /**
     * 在指定回调执行期间，设置并自动清理线程局部变量的值。
     *
     * @param fill     需要填充到线程局部变量中的值
     * @param callback 要执行的回调任务
     * @return 回调任务执行的结果
     * @throws Exception 由回调任务抛出的异常
     */
    public static <T> T setAutoFillValue(Double fill, Callable<T> callback) throws Exception {
        // 设置线程局部变量的值
        THREAD_LOCAL.set(fill);

        try {
            // 执行用户提供的回调逻辑
            return callback.call();
        } finally {
            // 确保无论是否发生异常，线程局部变量都会被清理
            THREAD_LOCAL.remove();
        }
    }

    /**
     * 获取当前线程绑定的线程局部变量的值。
     *
     * @return 当前线程的线程局部变量值，可能为null
     */
    public static Double get() {
        return THREAD_LOCAL.get();
    }
}
