package com.databuff.common.tsdb.model;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * 表示时间序列数据库支持的聚合函数类型。
 */
public enum AggFun {
    SUM, AVG, MEAN, MIN, MAX, COUNT, PERCENTILE, FIRST, LAST;

    private static final Map<String, AggFun> NAME_MAP;

    /**
     * 初始化名称到枚举实例的映射，将所有枚举值的小写名称作为键存入不可变映射。
     */
    static {
        Map<String, AggFun> map = new HashMap<>();
        for (AggFun e : values()) {
            map.put(e.name().toLowerCase(), e);
        }
        NAME_MAP = Collections.unmodifiableMap(new HashMap<>(map));
    }

    /**
     * 根据输入字符串获取对应的AggFun枚举实例。
     * 
     * @param value 需要转换的字符串。如果为null或无法匹配任何枚举值，则返回默认值MEAN
     * @return 对应的AggFun实例，转换失败时返回默认值MEAN
     */
    public static AggFun fromString(String value) {
        // 处理输入为null的情况
        if (value == null) {
            return AVG;
        }
        // 通过小写转换后的键从名称映射获取枚举值
        AggFun result = NAME_MAP.get(value.toLowerCase());
        // 当映射结果不存在时返回默认值
        if (result == null) {
            return AVG;
        }
        return result;
    }

}
