package com.databuff.dao.mysql.datahub;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.databuff.entity.datahubv2.DataHubPipeline;
import com.databuff.handler.TimestampToLongTypeHandler;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * DatahubPipeline数据访问接口
 * <p>
 * 该接口提供了对datahub_pipeline表的CRUD操作以及批量更新状态的功能
 * 继承自MyBatis-Plus的BaseMapper，自动获得基础的CRUD方法
 */
@Mapper
@Repository
public interface DataHubPipelineMapper extends BaseMapper<DataHubPipeline> {

    /**
     * 批量更新Pipeline状态
     * <p>
     * 根据提供的Pipeline ID列表，批量更新这些Pipeline的状态
     * 同时会更新updated_at字段为当前时间
     *
     * @param pipelineId Pipeline ID
     * @param status     要更新的状态值，如"DRAFT"、"ACTIVE"、"ARCHIVED"
     * @return 更新的记录数
     */
    @Update("UPDATE datahub_pipeline SET status = #{status}, updated_at = NOW() , update_user_id = #{updateUser} WHERE pipeline_id = #{pipelineId}")
    int updateStatus(@Param("pipelineId") Integer pipelineId, @Param("status") String status, @Param("updateUser") String updateUser);

    /**
     * 根据集群ID批量更新Pipeline状态
     * <p>
     * 根据提供的集群ID，批量更新该集群下所有Pipeline的状态
     * 同时会更新updated_at字段为当前时间
     *
     * @param clusterId 集群ID
     * @param status    要更新的状态值，如"DRAFT"、"ACTIVE"、"ARCHIVED"
     * @return 更新的记录数
     */
    @Update("UPDATE datahub_pipeline SET status = #{status}, updated_at = NOW() WHERE cluster_id = #{clusterId}")
    int batchUpdateStatusByClusterId(@Param("clusterId") Integer clusterId, @Param("status") String status);

    /**
     * 根据集群名称查询所有活跃的Pipeline
     */
    @Select("SELECT p.* FROM datahub_pipeline p " +
            "JOIN dc_databuff_datahub_cluster c ON p.cluster_id = c.id " +
            "WHERE c.cluster_name = #{clusterName} AND p.status != 'DELETED' AND c.is_delete = 0 AND c.enabled = 1")
    @Results({
            @Result(property = "lastUpdateTime", column = "last_update_time", typeHandler = TimestampToLongTypeHandler.class),
            @Result(property = "updatedAt", column = "updated_at", typeHandler = TimestampToLongTypeHandler.class),
            @Result(property = "createdAt", column = "created_at", typeHandler = TimestampToLongTypeHandler.class)
    })
    List<DataHubPipeline> selectByClusterName(@Param("clusterName") String clusterName);
} 