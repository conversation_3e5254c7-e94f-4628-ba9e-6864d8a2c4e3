package com.databuff.common.tsdb.pool;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@ConfigurationProperties(prefix = "tsdb")
@Component
@Slf4j
public class TSDBPoolConfig extends GenericObjectPoolConfig {

    public TSDBPoolConfig(){

    }
}
