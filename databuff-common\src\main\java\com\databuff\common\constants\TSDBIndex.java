package com.databuff.common.constants;

import com.google.common.collect.Sets;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static com.databuff.common.constants.Constant.COLUMN_POOL_NAME;
import static com.databuff.common.constants.Constant.COLUMN_THREAD_POOL_NAME;

/**
 * @author:TianMing
 * @date: 2022/7/1
 * @time: 15:40
 */
public class TSDBIndex {

    /**
     * MoreDB 应用性能指标库 数据库名称
     */
    public static final String TSDB_APM_METRIC_DATABASE_NAME = "apm_metric";
    /**
     * MoreDB npm指标库 数据库名称
     */
    public static final String TSDB_NPM_METRIC_DATABASE_NAME = "npm";
    public static final String TSDB_NPM_METRIC_TABLE_NAME = "npm";
    /**
     * MoreDB trace指标 数据库 之 服务表名
     */
    public static final String TSDB_METRIC_SERVICE_TABLE_NAME = "service";

    /**
     * MoreDB trace心跳产生的服务实例心跳指标
     */
    public static final String TSDB_METRIC_SERVICE_INSTANCE_TABLE_NAME = "service.instance";

    public static final String TSDB_METRIC_BUSINESS_SERVICE_TABLE_NAME = "business.service";

    /**
     * trace指标 es调用表
     */
    public static final String TSDB_METRIC_ES_TABLE_NAME = "service.es";
    /**
     * trace指标 数据库调用
     */
    public static final String TSDB_METRIC_DB_TABLE_NAME = "service.db";
    /**
     * trace指标 redis调用
     */
    public static final String TSDB_METRIC_REDIS_TABLE_NAME = "service.redis";
    /**
     * trace指标 http类型
     */
    public static final String TSDB_METRIC_HTTP_TABLE_NAME = "service.http";
    /**
     * trace指标 rpc类型
     */
    public static final String TSDB_METRIC_RPC_TABLE_NAME = "service.rpc";
    /**
     * trace指标 mq类型
     */
    public static final String TSDB_METRIC_MQ_TABLE_NAME = "service.mq";
    /**
     * trace指标 config类型 nacos
     */
    public static final String TSDB_METRIC_CONFIG_TABLE_NAME = "service.config";
    /**
     * trace指标 远程调用类型
     */
    public static final String TSDB_METRIC_REMOTE_TABLE_NAME = "service.remote";
    /**
     * 服务流指标
     */
    public static final String TSDB_METRIC_FLOW_TABLE_NAME = "service.flow";

    /**
     * trace指标 当前未支持的归属其它类型
     */
    public static final String TSDB_METRIC_OTHER_TABLE_NAME = "service.other";

    public static final String TSDB_METRIC_SERVICE_TRACE = "service.trace";

    public static final String TSDB_METRIC_POOL_HTTP_GET_TABLE_NAME = "service.http.connection.pool.get";
    public static final String TSDB_METRIC_POOL_DB_GET_TABLE_NAME = "service.db.connection.pool.get";
    public static final String TSDB_METRIC_POOL_OBJ_GET_TABLE_NAME = "service.object.pool.get";
    public static final String TSDB_METRIC_POOL_THREAD_GET_TABLE_NAME = "service.thread.pool.cost";

    /**
     * 业务系统服务
     */
    public static final String TSDB_METRIC_BUSINESS_SERVICE = "business.service";

    public static final String TSDB_METRIC_KUBERNETES_CPU_NAME = "kubernetes.cpu";
    public static final String TSDB_METRIC_PROCESS_CPU_NAME = "process.cpu";


    public static final Set<String> RESOURCE_METRIC_TABLES = Sets.newHashSet(TSDB_METRIC_RPC_TABLE_NAME, TSDB_METRIC_HTTP_TABLE_NAME, TSDB_METRIC_MQ_TABLE_NAME
            , TSDB_METRIC_REDIS_TABLE_NAME, TSDB_METRIC_DB_TABLE_NAME, TSDB_METRIC_ES_TABLE_NAME, TSDB_METRIC_CONFIG_TABLE_NAME, TSDB_METRIC_REMOTE_TABLE_NAME, TSDB_METRIC_OTHER_TABLE_NAME);

    public static final Set<String> POOL_GET_METRIC_TABLES = Sets.newHashSet(TSDB_METRIC_POOL_HTTP_GET_TABLE_NAME, TSDB_METRIC_POOL_DB_GET_TABLE_NAME, TSDB_METRIC_POOL_OBJ_GET_TABLE_NAME);

    public static final Map<String, String> RESOURCE_COST_METRIC_TABLES = new HashMap<>();
    public static final Map<String, String> POOL_GROUP_BY_MAPPING = new HashMap<>();

    static {
        RESOURCE_COST_METRIC_TABLES.put(TSDB_METRIC_RPC_TABLE_NAME, "RPC");
        RESOURCE_COST_METRIC_TABLES.put(TSDB_METRIC_HTTP_TABLE_NAME, "HTTP");
        RESOURCE_COST_METRIC_TABLES.put(TSDB_METRIC_MQ_TABLE_NAME, "MQ");
        RESOURCE_COST_METRIC_TABLES.put(TSDB_METRIC_REDIS_TABLE_NAME, "Redis");
        RESOURCE_COST_METRIC_TABLES.put(TSDB_METRIC_DB_TABLE_NAME, "DB");
        RESOURCE_COST_METRIC_TABLES.put(TSDB_METRIC_ES_TABLE_NAME, "DB");
        RESOURCE_COST_METRIC_TABLES.put(TSDB_METRIC_CONFIG_TABLE_NAME, "配置");
        RESOURCE_COST_METRIC_TABLES.put(TSDB_METRIC_REMOTE_TABLE_NAME, "远程调用");
        RESOURCE_COST_METRIC_TABLES.put(TSDB_METRIC_POOL_HTTP_GET_TABLE_NAME, "HTTP连接池");
        RESOURCE_COST_METRIC_TABLES.put(TSDB_METRIC_POOL_DB_GET_TABLE_NAME, "DB连接池");
        RESOURCE_COST_METRIC_TABLES.put(TSDB_METRIC_POOL_OBJ_GET_TABLE_NAME, "对象池");
        RESOURCE_COST_METRIC_TABLES.put(TSDB_METRIC_POOL_THREAD_GET_TABLE_NAME, "线程池");

        POOL_GROUP_BY_MAPPING.put(TSDB_METRIC_POOL_HTTP_GET_TABLE_NAME, COLUMN_POOL_NAME);
        POOL_GROUP_BY_MAPPING.put(TSDB_METRIC_POOL_DB_GET_TABLE_NAME, COLUMN_POOL_NAME);
        POOL_GROUP_BY_MAPPING.put(TSDB_METRIC_POOL_OBJ_GET_TABLE_NAME, COLUMN_POOL_NAME);
        POOL_GROUP_BY_MAPPING.put(TSDB_METRIC_POOL_THREAD_GET_TABLE_NAME, COLUMN_THREAD_POOL_NAME);
    }

    /**
     * trace指标 Exception
     */
    public static final String TSDB_METRIC_EXCEPTION_TABLE_NAME = "service.exception";
    /**
     * 系统自监控数据库
     */
    public static final String DATABASE_NAME_DATABUFF_SYSTEM = "databuff_system";

    public static final String TSDB_METRIC_NAME_BIZ_EVENT = "biz.event";
    public static final String TSDB_METRIC_NAME_BIZ_EVENT_KPI = "biz.event_kpi";


    /**
     * rum 页面指标
     */
    public static final String TSDB_METRIC_RUM_WEB_STATS = "rum.web.stats";
    public static final String TSDB_METRIC_RUM_WEB_STATS_PERCENTILE = "rum.web.stats.percentile";

    public static final String TSDB_METRIC_RUM_WEB_ACTION = "rum.web.action";
    public static final String TSDB_METRIC_RUM_WEB_ACTION_PERCENTILE = "rum.web.action.percentile";

    public static final String TSDB_METRIC_RUM_WEB_REQUEST = "rum.web.request";
    public static final String TSDB_METRIC_RUM_WEB_REQUEST_PERCENTILE = "rum.web.request.percentile";

    public static final String TSDB_METRIC_RUM_ERROR_LOG = "rum.error.log";

    public static final String TSDB_RUM_METRIC_DATABASE_NAME = "rum";


    public static final String TSDB_METRIC_RUM_IOS_APP_STATS = "rum.ios.app.stats";
    public static final String TSDB_METRIC_RUM_IOS_APP_STATS_PERCENTILE = "rum.ios.app.stats.percentile";

    /**
     * RUM iOS 应用请求指标表
     */
    public static final String TSDB_METRIC_RUM_IOS_REQUEST = "rum.ios.request";

    public static final String TSDB_METRIC_RUM_ANDROID_APP_STATS = "rum.android.app.stats";
    public static final String TSDB_METRIC_RUM_ANDROID_APP_STATS_PERCENTILE = "rum.android.app.stats.percentile";
    /**
     * RUM Android 应用请求指标表
     */
    public static final String TSDB_METRIC_RUM_ANDROID_REQUEST = "rum.android.request";

    public static final String TSDB_METRIC_NPM_METRIC_TABLE_NAME = "npm";


    public static final String METRIC_DATABUFF_ALARM = "databuff.alarm";
    public static final String METRIC_DATABUFF_ALARM_BUS = "databuff.bus";

    public static final String FIELD_USAGE_PCT = "usage.pct";

}
