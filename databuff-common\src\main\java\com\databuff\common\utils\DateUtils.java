package com.databuff.common.utils;

import java.time.temporal.ChronoUnit;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static java.util.Locale.US;

/**
 * 日期工具类, 继承org.apache.commons.lang.time.DateUtils类
 * <AUTHOR>
 *
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {

    private static final DateTimeFormatter HOUR_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHH");
    private static final DateTimeFormatter MINUTE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmm");
    private static final DateTimeFormatter TSDB_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter FULL_MINUTE_PATTERN =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:00");

    private static String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    /**
     * 得到当前日期字符串 格式（yyyy-MM-dd）
     */
    public static String getDate() {
        return getDate("yyyy-MM-dd");
    }

    /**
     * 得到当前日期字符串 格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
     */
    public static String getDate(String pattern) {
        return DateFormatUtils.format(new Date(), pattern);
    }

    /**
     * 得到日期字符串 默认格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
     */
    public static String formatDate(Date date, Object... pattern) {
        String formatDate = null;
        if (pattern != null && pattern.length > 0) {
            formatDate = DateFormatUtils.format(date, pattern[0].toString());
        } else {
            formatDate = DateFormatUtils.format(date, "yyyy-MM-dd");
        }
        return formatDate;
    }

    public static String formatDate(Date date, String pattern, Object obj) {
        String formatDate = null;
        formatDate = DateFormatUtils.format(date, pattern);
        return formatDate;
    }

    /**
     * 得到日期时间字符串，转换格式（yyyy-MM-dd HH:mm:ss）
     */
    public static String formatDateTime(Date date) {
        return formatDate(date, "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 得到当前时间字符串 格式（HH:mm:ss）
     */
    public static String getTime() {
        return formatDate(new Date(), "HH:mm:ss");
    }

    /**
     * 得到当前日期和时间字符串 格式（yyyy-MM-dd HH:mm:ss）
     */
    public static String getDateTime() {
        return formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 得到当前年份字符串 格式（yyyy）
     */
    public static String getYear(Date date) {
        if (date == null) {
            return formatDate(new Date(), "yyyy");
        }
        return formatDate(date, "yyyy");
    }

    /**
     * 得到当前月份字符串 格式（MM）
     */
    public static String getMonth(Date date) {
        if (date == null) {
            return formatDate(new Date(), "MM");
        }
        return formatDate(date, "MM");
    }

    /**
     * 得到当天字符串 格式（dd）
     */
    public static String getDay(Date date) {
        if (date == null) {
            return formatDate(new Date(), "dd");
        }
        return formatDate(date, "dd");
    }

    /**
     * 得到当前星期字符串 格式（E）星期几
     */
    public static String getWeek(Date date) {
        if (date == null) {
            return formatDate(new Date(), "E");
        }
        return formatDate(date, "E");
    }

    /**
     * 日期型字符串转化为日期 格式
     * { "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm",
     * "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm",
     * "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm" }
     */
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return parseDate(str.toString(), parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 计算工作时间年
     */
    public static int pastYears(Date workTime) {
        long date = pastDays(workTime);
        int work = (int) (date / 365);
        return work;
    }

    /**
     * 获取过去的天数
     * @param date
     * @return
     */
    public static long pastDays(Date date) {
        long t = System.currentTimeMillis() - date.getTime();
        return t / (24 * 60 * 60 * 1000);
    }

    /**
     * 获取过去的小时
     * @param date
     * @return
     */
    public static long pastHour(Date date) {
        long t = System.currentTimeMillis() - date.getTime();
        return t / (60 * 60 * 1000);
    }

    /**
     * 获取过去的分钟
     * @param date
     * @return
     */
    public static long pastMinutes(Date date) {
        long t = System.currentTimeMillis() - date.getTime();
        return t / (60 * 1000);
    }

    /**
     * 获取当前时间的时间戳，删除秒和毫秒
     * @return 获取当前时间的时间戳，删除秒和毫秒
     */
    public static long nowMinutes() {
        // 获取当前时间戳
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        // 将秒和毫秒设置为零
        timestamp.setSeconds(0);
        timestamp.setNanos(0);
        // 获取毫秒数
        return timestamp.getTime();
    }

    /**
     * 转换为时间（天,时:分:秒.毫秒）
     * @param timeMillis
     * @return
     */
    public static String formatDateTime(long timeMillis) {
        long day = timeMillis / (24 * 60 * 60 * 1000);
        long hour = (timeMillis / (60 * 60 * 1000) - day * 24);
        long min = ((timeMillis / (60 * 1000)) - day * 24 * 60 - hour * 60);
        long s = (timeMillis / 1000 - day * 24 * 60 * 60 - hour * 60 * 60 - min * 60);
        long sss = (timeMillis - day * 24 * 60 * 60 * 1000 - hour * 60 * 60 * 1000 - min * 60 * 1000 - s * 1000);
        return (day > 0 ? day + "," : "") + hour + ":" + min + ":" + s + "." + sss;
    }

    /**
     * 获取两个日期之间的天数
     *
     * @param before
     * @param after
     * @return
     */
    public static double getDistanceOfTwoDate(Date before, Date after) {
        long beforeTime = before.getTime();
        long afterTime = after.getTime();
        return (afterTime - beforeTime) / (double) (1000 * 60 * 60 * 24);
    }

    /**
     * 两个时间的比较
     * @param before
     * @param after
     * @return
     */
    public static boolean compareOfTwoDate(Date before, Date after) {
        long beforeTime = before.getTime();
        long afterTime = after.getTime();
        return afterTime > beforeTime;
    }

    /**
     * String转date
     *
     * 得到当前日期字符串 格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
     *
     */
    public static Date strToDate(String pattern, String source) {
        Date date = new Date();
        try {
            if (pattern == null || "".equals(pattern)) {
                DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                date = df.parse(source);
                return date;
            }
            DateFormat df = new SimpleDateFormat(pattern);
            date = df.parse(source);
        } catch (Exception e) {
        }
        return date;
    }

    /**
     * Long转 date
     */
    public static Date longToDate(long date) {
        Date d = new Date(date);
        return d;
    }

    /**
     * date转String
     */
    public static String dateToString(String pattern, Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(date);
    }

    /**
     * date 转long
     */
    public static long dateToLong(Date date) {
        return date.getTime();
    }

    /**
     * string 转long
     */
    public static long stringToLong(String pattern, String source) {
        long date = System.currentTimeMillis();
        try {
            if (pattern == null || "".equals(pattern)) {
                DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                return df.parse(source).getTime();
            }
            DateFormat df = new SimpleDateFormat(pattern);
            date = df.parse(source).getTime();
        } catch (Exception e) {
        }
        return date;
    }

    public static long tsdbTimeToMilliseconds(String source) {
        LocalDateTime localDateTime = LocalDateTime.parse(source, TSDB_FORMATTER);
        return localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    public static long tsdbTimeToMilliseconds(Date source) {
        return source.getTime();
    }

    /**
     * 获取每月的第一天
     * @param date
     * @return
     */
    public static Date getMonthFirstDay(Date date) {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));
        String first = df.format(calendar.getTime());
        return calendar.getTime();
    }

    /**
     * 获取每月的最后一天
     * @param date
     * @return
     */
    public static Date getMonthLastDay(Date date) {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        String last = df.format(calendar.getTime());
        return calendar.getTime();
    }

    /**
     * 判断是不是周六日
     * @param date
     * @return
     * @throws ParseException
     */
    public static boolean isWeek(Date date) throws ParseException {
        boolean flag = false;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        if (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY ||
                calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
            flag = true;
            return flag;
        }
        return flag;

    }

    //两个日期相减得到的毫秒数
    public static long dateDiff(Date beginDate, Date endDate) {
        long begin = beginDate.getTime();
        long end = endDate.getTime();
        return end - begin;
    }

    /**
     * 俩个日期之间包含多少个周末
     * @throws ParseException
     * type true 双休
     *                        false 单休
     */

    public static int getWeekdayCount(Date begin, Date end, boolean type) throws ParseException {
        int count = 0;
        Date flag = begin;
        Calendar calendar = Calendar.getInstance();
        while (flag.compareTo(end) < 1) {
            calendar.setTime(flag);
            //判断是否为周六日
            int week = calendar.get(Calendar.DAY_OF_WEEK) - 1;
            if (type) {
                //0为周日，6为周六
                if (week == 0 || week == 6) {
                    //跳出循环进入下一个日期
                    calendar.add(Calendar.DAY_OF_MONTH, +1);
                    flag = calendar.getTime();
                    count++;
                    continue;
                }
            } else {
                if (week == 0) {
                    calendar.add(Calendar.DAY_OF_MONTH, +1);
                    flag = calendar.getTime();
                    count++;
                    continue;
                }
            }
            calendar.add(Calendar.DAY_OF_MONTH, +1);
            flag = calendar.getTime();
        }
        return count;
    }

    public static long formatUsTime(String dateTime) throws ParseException {
        String pattern = "dd-MMM-yy HH.mm.ss.SSSSSS a";
        //初始化美国环境的时间
        SimpleDateFormat sdf = new SimpleDateFormat(pattern, US);
        Date parse = sdf.parse(dateTime);
        return parse.getTime() / 1000L;
    }

    /**
     * 日期的加减方法
     * 用于在当前的天或者小时或者分钟或者月份的基础上加上或者减去若干小时，分钟，日，月
     * @param calendarFormat Calendar.DATE 天 Calendar.HOUR 小时 Calendar.MINUTE 分钟 Calendar.MONTH 月
     * @param day            需要加上的天数或者需要减去的天数，
     *                       例如：加上10天：(Calendar.DATE,10）减去十天：(Calendar.DATE,-10)
     * @return 返回加上或者减去的那个日期
     */
    public static Date dayAddAndSub(Date currentDate, int calendarFormat, int day) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        calendar.add(calendarFormat, day);
        return calendar.getTime();
    }

    /**
     * 获取当前时间所在周的周几的日期时间
     * @param day
     * @param num 周一为1，依次2,3,4,5,6,7
     * @return
     */
    public static Date getWeekDate(Date day, int num) {
        Calendar cal = Calendar.getInstance();
        // 设置一个星期的第一天，按中国的习惯一个星期的第一天是星期一
        cal.setFirstDayOfWeek(Calendar.MONDAY);
        cal.setTime(day);
        // 获得当前日期是一个星期的第几天
        int dayWeek = cal.get(Calendar.DAY_OF_WEEK);
        if (dayWeek == 1) {
            dayWeek = 8;
        }
        // 根据日历的规则，给当前日期减去星期几与一个星期第一天的差值,则为星期一
        cal.add(Calendar.DATE, cal.getFirstDayOfWeek() - dayWeek);

        cal.add(Calendar.DATE, num - 1);
        return cal.getTime();
    }

    /**
     * 查找时间范围内的所有日期
     * @param from
     * @param to
     * @return
     * @throws ParseException
     */
    public static List<String> findDates(String from, String to, String format) throws ParseException {
        Date fromDate = DateUtils.strToDate(format, from);
        Date toDate = DateUtils.strToDate(format, to);
        return findDates(fromDate, toDate);
    }

    /**
     * 查找时间范围内的所有日期
     * @param startTime
     * @param endTime
     * @return
     * @throws ParseException
     */
    public static List<String> findDates(Date startTime, Date endTime) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

        List<String> allDate = new ArrayList<>();
        allDate.add(sdf.format(startTime));
        Calendar calBegin = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        calBegin.setTime(startTime);
        Calendar calEnd = Calendar.getInstance();

        String format = sdf.format(endTime);
        Date etime = sdf.parse(format);
        // 使用给定的 Date 设置此 Calendar 的时间
        calEnd.setTime(etime);
        // 测试此日期是否在指定日期之后
        while (etime.after(calBegin.getTime())) {
            // 根据日历的规则，为给定的日历字段添加或减去指定的时间量
            calBegin.add(Calendar.DAY_OF_MONTH, 1);
            allDate.add(sdf.format(calBegin.getTime()));
        }
        return allDate;
    }

    public static List<String> getFormattedDateRange(long startTimestamp, long endTimestamp) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Set<String> dateList = new HashSet<>();

        Calendar startCal = Calendar.getInstance();
        startCal.setTimeInMillis(startTimestamp);
        dateList.add(sdf.format(startCal.getTime()));

        Calendar endCal = Calendar.getInstance();
        endCal.setTimeInMillis(endTimestamp);
        dateList.add(sdf.format(endCal.getTime()));

        while (!startCal.after(endCal)) {
            Date currentDate = startCal.getTime();
            dateList.add(sdf.format(currentDate));
            startCal.add(Calendar.DATE, 1);
        }

        return new ArrayList<>(dateList);
    }

    public static List<String> getFormattedDateRange(String startTime, String endTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Set<String> dateList = new HashSet<>();

        try {
            if (startTime == null) {
                startTime = sdf.format(new Date());
            }
            if (endTime == null) {
                endTime = sdf.format(new Date());
            }

            Date startDate = sdf.parse(startTime);
            Date endDate = sdf.parse(endTime);

            Calendar startCal = Calendar.getInstance();
            startCal.setTime(startDate);
            dateList.add(sdf.format(startCal.getTime()));

            Calendar endCal = Calendar.getInstance();
            endCal.setTime(endDate);
            dateList.add(sdf.format(endCal.getTime()));

            while (!startCal.after(endCal)) {
                Date currentDate = startCal.getTime();
                dateList.add(sdf.format(currentDate));
                startCal.add(Calendar.DATE, 1);
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return new ArrayList<>(dateList);
    }

    /**
     * 时间范围内比较判断
     * @param pattern
     * @param current
     * @param after
     * @param before
     * @return
     */
    public static boolean dateScopeCompar(String pattern, Date current, int after, int before) {
        String currentTime = DateUtils.dateToString(pattern, current);
        Integer currentNumber = Integer.valueOf(currentTime);
        return currentNumber >= after && currentNumber <= before;
    }

    /**
     * 获取分钟的时间戳
     *
     * @return
     */
    public static long getTimeMinute() {
        LocalDate localDate = LocalDate.now();
        LocalTime localTime = LocalTime.now();
        return LocalDateTime.of(localDate.getYear(), localDate.getMonth(), localDate.getDayOfMonth(), localTime.getHour(), localTime.getMinute(), 0)
                .atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 切分日期 返回日期区间
     * @param num
     * @param fromTime
     * @param toTime
     * @return
     */
    public static Map<String, String> cutDataStr(int num, String fromTime, String toTime) {
        Map<String, String> dm = new LinkedHashMap<>(num);

        long f = stringToLong("yyyy-MM-dd HH:mm:ss", fromTime);
        long t = stringToLong("yyyy-MM-dd HH:mm:ss", toTime);
        long d = t - f;
        int ms = (int) Math.ceil(d * 1.0 / num);
        long n = f;
        for (int i = 0; i < num; i++) {
            String nf = formatDateTime(new Date(n));
            n = n + ms;
            if (n > t) {
                n = t;
            }
            String nt = formatDateTime(new Date(n));
            dm.put(nf, nt);
        }
        return dm;
    }

    /**
     * 切分日期 返回毫秒区间
     * @param num
     * @param fromTime
     * @param toTime
     * @return
     */
    public static Map<Long, Long> cutDataLong(int num, String fromTime, String toTime) {
        Map<Long, Long> dm = new LinkedHashMap<>(num);

        long f = stringToLong("yyyy-MM-dd HH:mm:ss", fromTime);
        long t = stringToLong("yyyy-MM-dd HH:mm:ss", toTime);
        long d = t - f;
        int ms = (int) Math.ceil(d * 1.0 / num);
        long n = f;
        for (int i = 0; i < num; i++) {
            long nf = n;
            n = n + ms;
            if (n > t) {
                n = t;
            }
            long nt = n;
            dm.put(nf, nt);
        }
        return dm;
    }

    /**
     * 1m、1h、1d、1w 转换为秒
     * @param unit
     * @param value
     * @return
     */
    public static long convertToSeconds(String unit, Integer value) {
        long seconds = 0;

        switch (unit) {
            case "s":
                seconds = value;
                break;
            case "m":
                seconds = value * 60;
                break;
            case "h":
                seconds = value * 60 * 60;
                break;
            case "d":
                seconds = value * 60 * 60 * 24;
                break;
            case "w":
                seconds = value * 60 * 60 * 24 * 7;
                break;
            default:
                throw new IllegalArgumentException("Invalid time unit: " + unit);
        }

        return seconds;
    }

    /**
     * Convert Date to hour format (YYYYMMDDHH) 暂时不考虑21xx年会超过int大小
     * @param date input date
     * @return hour as integer in YYYYMMDDHH format
     */
    public static Integer getHourFromDate(Date date) {
        if (date == null) {
            return null;
        }
        return Integer.parseInt(HOUR_FORMATTER.format(
                date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime()
        ));
    }

    public static Long getMinuteFromDate(Date date) {
        if (date == null) {
            return null;
        }
        return Long.parseLong(MINUTE_FORMATTER.format(
                date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime()
        ));
    }

    /**
     * Add or subtract hours from a date string
     * @param dateStr Date string in format "yyyy-MM-dd HH:mm:ss"
     * @param hours Number of hours to add (positive) or subtract (negative)
     * @return Modified date string in same format
     */
    public static String addHours(String dateStr, int hours) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = sdf.parse(dateStr);

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.add(Calendar.HOUR, hours);

            return sdf.format(calendar.getTime());
        } catch (ParseException e) {
            // Return original string if parsing fails
            return dateStr;
        }
    }

    public static long getTimestampMinutesAgo(int minutesAgo) {
        return LocalDateTime.now().minusMinutes(minutesAgo)
                .atZone(ZoneId.systemDefault())
                .toInstant()
                .toEpochMilli();
    }

    public static LocalDateTime timestampToLocalDateTime(long timestampMillis) {
        return Instant.ofEpochMilli(timestampMillis)
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
    }

    public static String getMinuteByLocalDateTime(LocalDateTime localDateTime, int plusMinutes) {
        return localDateTime.plusMinutes(plusMinutes).format(FULL_MINUTE_PATTERN);
    }

    public static String getMinuteByLong(long timestamp, int plusMinutes) {
        LocalDateTime dateTime = LocalDateTime.ofInstant(
                Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
        return dateTime.plusMinutes(plusMinutes).format(FULL_MINUTE_PATTERN);
    }

}