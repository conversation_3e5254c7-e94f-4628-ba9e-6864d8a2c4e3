package com.databuff.common.annotation;

import com.alibaba.fastjson.JSON;
import com.databuff.common.utils.OtelMetricUtil;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Aspect
@Component
public class LogExecutionTimeAspect {

    @Around("@annotation(logExecutionTime)")
    public Object logExecutionTime(ProceedingJoinPoint joinPoint, LogExecutionTime logExecutionTime) throws Throwable {
        long start = System.currentTimeMillis();
        boolean success = true;
        String exceptionClassName = null;
        try {
            Object result = joinPoint.proceed();
            return result;
        } catch (Throwable throwable) {
            exceptionClassName = throwable.getClass().getName();
            success = false;
            throw throwable;
        } finally {
            long executionTime = System.currentTimeMillis() - start;

            final Signature signature = joinPoint.getSignature();
            final String className = signature.getDeclaringTypeName();
            final String methodName = signature.getName();
            final String groupName = Thread.currentThread().getThreadGroup().getName();
            final String name = Thread.currentThread().getName();

            Map<String, String> tagMap = new HashMap<>();
            tagMap.put("group", groupName);
            tagMap.put("name", name);
            tagMap.put("class", className);
            tagMap.put("method", methodName);
            tagMap.put("success", Boolean.toString(success));


            // 如果发生异常且logOnException=true，则记录异常信息
            if (!success && !logExecutionTime.logOnException()) {
                if (exceptionClassName != null) {
                    tagMap.put("exceptionClassName", exceptionClassName);
                }

                MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
                String[] parameterNames = methodSignature.getParameterNames();
                Object[] parameterValues = joinPoint.getArgs();

                String[] tags = logExecutionTime.tag();
                for (String tag : tags) {
                    for (int i = 0; i < parameterNames.length; i++) {
                        final String parameterName = parameterNames[i];
                        if (!Objects.equals(parameterName, tag) || !(parameterValues[i] instanceof String)) {
                            continue;
                        }
                        String paramValue = (String) parameterValues[i];
                        if (paramValue.length() > 20) {
                            paramValue = paramValue.substring(0, 20);
                        }
                        if (JSON.isValid(paramValue)) {
                            continue;
                        }
                        tagMap.put(parameterName, paramValue);
                    }
                }
            }

            String metricName;
            if (logExecutionTime.value() == null || logExecutionTime.value().isEmpty()) {
                metricName = String.format("%s.%s", className, methodName);
            } else {
                metricName = String.format("%s.%s:%s", className, methodName, logExecutionTime.value());
            }
            OtelMetricUtil.logHistogram(metricName, tagMap, executionTime);
        }
    }
}