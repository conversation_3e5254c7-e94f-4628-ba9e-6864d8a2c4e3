package com.databuff.common.tsdb.builder;

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * OpenGemini字符串转义性能优化测试
 * 比较原始实现和高度优化的实现在处理大量长字符串时的性能差异
 */
public class OpenGeminiEscapeOptimizedTest {

    // 原始实现的备份
    private String originalDealSpecialChar(String value) {
        // 当原始字符串包含单引号时，转义所有单引号并包裹在单引号中
        if (value.contains("'")) {
            return "'" + value.replace("'", "\\'") + "'";
        } else {
            // 当原始字符串不包含单引号时，直接包裹在单引号中
            return "'" + value + "'";
        }
    }

    // 原始实现的备份
    private String originalEscapeSpecialChar(String input) {
        if (StringUtils.isBlank(input)) {
            return input;
        }
        String[] defaultCharacters = {"'"};
        Set<String> characters = new HashSet<>();
        characters.addAll(Arrays.asList(defaultCharacters));
        String output = input;
        for (String character : characters) {
            output = output.replace(character, "\\" + character);
        }
        return output;
    }

    // 高度优化的实现 - dealSpecialChar
    private String optimizedDealSpecialChar(String value) {
        // 快速路径：空值处理
        if (value == null || value.isEmpty()) {
            return "''";
        }

        // 快速路径：如果不包含单引号和反斜杠，直接包裹并返回
        // 使用indexOf而不是contains，避免创建正则表达式
        if (value.indexOf('\'') == -1 && value.indexOf('\\') == -1) {
            return "'" + value + "'";
        }

        // 预分配空间减少扩容操作
        int length = value.length();
        StringBuilder result = new StringBuilder(length + 10);
        result.append('\''); // 开始单引号

        // 直接遍历字符串，避免创建字符数组
        for (int i = 0; i < length; i++) {
            char c = value.charAt(i);

            // 检查是否是已经转义的单引号 (\') - 不需要再次转义
            if (c == '\\' && i + 1 < length) {
                char next = value.charAt(i + 1);
                if (next == '\'') {
                    // 已转义的单引号，直接复制
                    result.append('\\').append('\'');
                    i++; // 跳过已处理的字符
                } else {
                    // 普通反斜杠，直接复制
                    result.append(c);
                }
            } else if (c == '\'') {
                // 未转义的单引号，需要转义
                result.append('\\').append('\'');
            } else {
                // 其他字符直接添加
                result.append(c);
            }
        }

        result.append('\''); // 结束单引号
        return result.toString();
    }

    // 高度优化的实现 - escapeSpecialChar
    private String optimizedEscapeSpecialChar(String input) {
        // 快速路径：空值处理
        if (input == null || input.isEmpty()) {
            return input;
        }

        // 快速路径：如果不包含单引号和反斜杠，直接返回
        // 使用indexOf而不是contains，避免创建正则表达式
        if (input.indexOf('\'') == -1 && input.indexOf('\\') == -1) {
            return input;
        }

        // 预分配空间减少扩容操作
        int length = input.length();
        StringBuilder result = new StringBuilder(length + 10);

        // 直接遍历字符串，避免创建字符数组
        for (int i = 0; i < length; i++) {
            char c = input.charAt(i);

            // 处理已转义的单引号
            if (c == '\\' && i + 1 < length) {
                char next = input.charAt(i + 1);
                if (next == '\'') {
                    // 已转义的单引号，直接复制
                    result.append('\\').append('\'');
                    i++; // 跳过已处理的字符
                } else {
                    // 普通反斜杠，直接复制
                    result.append(c);
                }
            } else if (c == '\'') {
                // 未转义的单引号，需要转义
                result.append('\\').append('\'');
            } else {
                // 其他字符直接添加
                result.append(c);
            }
        }

        return result.toString();
    }


    /**
     * 生成测试数据
     * @param count 生成的字符串数量
     * @param avgLength 平均字符串长度
     * @param quoteRatio 包含单引号的字符串比例 (0-1)
     * @param escapedQuoteRatio 已转义单引号的比例 (0-1)
     * @return 测试数据列表
     */
    private List<String> generateTestData(int count, int avgLength, double quoteRatio, double escapedQuoteRatio) {
        List<String> testData = new ArrayList<>(count);
        Random random = new Random(System.currentTimeMillis());

        for (int i = 0; i < count; i++) {
            // 随机生成字符串长度，在平均长度的80%-120%之间浮动
            int length = (int) (avgLength * (0.8 + 0.4 * random.nextInt()));

            StringBuilder sb = new StringBuilder(length);

            // 生成基础字符串
            for (int j = 0; j < length; j++) {
                // 随机生成ASCII可打印字符 (32-126)，但排除单引号和反斜杠
                char c;
                do {
                    c = (char) (32 + random.nextInt(95));
                } while (c == '\'' || c == '\\');

                sb.append(c);
            }

            // 根据比例添加单引号
            if (random.nextDouble() < quoteRatio) {
                int quoteCount = 1 + random.nextInt(5); // 1-5个单引号
                for (int j = 0; j < quoteCount; j++) {
                    int position = random.nextInt(sb.length());

                    // 根据比例决定是添加普通单引号还是已转义单引号
                    if (random.nextDouble() < escapedQuoteRatio) {
                        sb.insert(position, "\\'");
                    } else {
                        sb.insert(position, "'");
                    }
                }
            }

            testData.add(sb.toString());
        }

        return testData;
    }

    /**
     * 生成SQL查询样本
     * @param count 生成的查询数量
     * @return SQL查询列表
     */
    private List<String> generateSqlQueries(int count) {
        List<String> queries = new ArrayList<>(count);
        Random random = new Random(System.currentTimeMillis());

        String[] templates = {
                "SELECT * FROM table WHERE column = 'value'",
                "SELECT id, name FROM users WHERE status = 'active' AND role = 'admin'",
                "SELECT get_json_string(`trigger`, \"$.serviceId\") as triggerId , count(1) as cnt FROM dc_alarm da WHERE (apiKey=?$.serviceId\") is not null AND da.gid IS NULL) AND (da.createDt IN (\\' 2025-05-12 13:58:00 \\')",
                "SELECT SUM(amount) FROM transactions WHERE date BETWEEN '2023-01-01' AND '2023-12-31' GROUP BY customer_id HAVING SUM(amount) > 1000",
                "INSERT INTO logs (message, level, timestamp) VALUES ('Error occurred: could not connect to \\'database\\'', 'ERROR', CURRENT_TIMESTAMP)",
                "UPDATE products SET description = 'This product\\'s features include: 1) Easy setup, 2) Low maintenance' WHERE category = 'electronics'",
                "SELECT * FROM \"measurement\" WHERE \"tag\"::tag = 'value with \\'quoted\\' text' AND time > now() - 7d"
        };

        for (int i = 0; i < count; i++) {
            String base = templates[random.nextInt(templates.length)];

            // 随机添加更多的单引号和已转义单引号
            StringBuilder sb = new StringBuilder(base);
            int modifications = random.nextInt(10); // 0-9次修改

            for (int j = 0; j < modifications; j++) {
                int position = random.nextInt(sb.length());

                if (random.nextBoolean()) {
                    // 添加普通单引号
                    sb.insert(position, "'");
                } else {
                    // 添加已转义单引号
                    sb.insert(position, "\\'");
                }
            }

            // 随机增加字符串长度
            int extraLength = random.nextInt(500); // 0-499额外字符
            for (int j = 0; j < extraLength; j++) {
                int position = random.nextInt(sb.length());
                char c = (char) (32 + random.nextInt(95)); // ASCII可打印字符
                sb.insert(position, c);
            }

            queries.add(sb.toString());
        }

        return queries;
    }

    @Test
    @DisplayName("测试原始实现和高度优化实现的性能差异 - 随机字符串")
    public void testPerformanceWithRandomStrings() {
        // 测试参数
        int[] testSizes = {1000, 10000, 100000}; // 测试数据量
        int[] avgLengths = {100, 500, 1000}; // 平均字符串长度

        System.out.println("=== 随机字符串性能测试 ===");
        System.out.println("数据量\t平均长度\t原始实现(ms)\t优化实现(ms)\t性能比例(优化/原始)");

        for (int size : testSizes) {
            for (int avgLength : avgLengths) {
                // 生成测试数据 - 50%的字符串包含单引号，30%的单引号是已转义的
                List<String> testData = generateTestData(size, avgLength, 0.5, 0.3);

                // 测试原始实现
                long startOriginal = System.nanoTime();
                for (String data : testData) {
                    String escaped = originalEscapeSpecialChar(data);
                    originalDealSpecialChar(escaped);
                }
                long endOriginal = System.nanoTime();
                long originalTime = TimeUnit.NANOSECONDS.toMillis(endOriginal - startOriginal);

                // 测试优化实现
                long startOptimized = System.nanoTime();
                for (String data : testData) {
                    String escaped = optimizedEscapeSpecialChar(data);
                    optimizedDealSpecialChar(escaped);
                }
                long endOptimized = System.nanoTime();
                long optimizedTime = TimeUnit.NANOSECONDS.toMillis(endOptimized - startOptimized);

                // 计算性能比例
                double ratio = (double) optimizedTime / originalTime;

                System.out.printf("%d\t%d\t%d\t%d\t%.2f%n", size, avgLength, originalTime, optimizedTime, ratio);
            }
        }

        Assertions.assertTrue(testSizes.length>1);

    }

    @Test
    @DisplayName("测试原始实现和高度优化实现的性能差异 - SQL查询")
    public void testPerformanceWithSqlQueries() {
        // 测试参数
        int[] testSizes = {1000, 10000, 50000}; // 测试数据量

        System.out.println("=== SQL查询性能测试 ===");
        System.out.println("数据量\t原始实现(ms)\t优化实现(ms)\t性能比例(优化/原始)");

        for (int size : testSizes) {
            // 生成SQL查询测试数据
            List<String> testData = generateSqlQueries(size);

            // 测试原始实现
            long startOriginal = System.nanoTime();
            for (String data : testData) {
                String escaped = originalEscapeSpecialChar(data);
                originalDealSpecialChar(escaped);
            }
            long endOriginal = System.nanoTime();
            long originalTime = TimeUnit.NANOSECONDS.toMillis(endOriginal - startOriginal);

            // 测试优化实现
            long startOptimized = System.nanoTime();
            for (String data : testData) {
                String escaped = optimizedEscapeSpecialChar(data);
                optimizedDealSpecialChar(escaped);
            }
            long endOptimized = System.nanoTime();
            long optimizedTime = TimeUnit.NANOSECONDS.toMillis(endOptimized - startOptimized);

            // 计算性能比例
            double ratio = (double) optimizedTime / originalTime;

            System.out.printf("%d\t%d\t%d\t%.2f%n", size, originalTime, optimizedTime, ratio);
        }
        Assertions.assertTrue(testSizes.length>1);

    }

    @Test
    @DisplayName("测试原始实现和高度优化实现的正确性差异")
    public void testCorrectness() {
        // 测试用例
        String[] testCases = {
                // 普通字符串
                "Normal string without quotes",
                // 包含单引号的字符串
                "String with 'single quotes'",
                // 包含已转义单引号的字符串
                "String with \\'escaped\\' quotes",
                // 混合单引号和已转义单引号
                "Mixed 'quotes' and \\'escaped\\' quotes",
                // SQL查询
                "SELECT * FROM table WHERE column = 'value'",
                // 复杂SQL查询
                "SELECT get_json_string(`trigger`, \"$.serviceId\") as triggerId , count(1) as cnt FROM dc_alarm da WHERE (apiKey=?$.serviceId\") is not null AND da.gid IS NULL) AND (da.createDt IN (\\' 2025-05-12 13:58:00 \\')"
        };

        System.out.println("=== 正确性测试 ===");
        System.out.println("测试用例\t原始实现\t优化实现\t结果相同");

        for (String testCase : testCases) {
            // 原始实现
            String originalEscaped = originalEscapeSpecialChar(testCase);
            String originalResult = originalDealSpecialChar(originalEscaped);

            // 优化实现
            String optimizedEscaped = optimizedEscapeSpecialChar(testCase);
            String optimizedResult = optimizedDealSpecialChar(optimizedEscaped);

            // 比较结果
            boolean isSame = originalResult.equals(optimizedResult);

            // 输出简短版本以便于比较
            String shortTestCase = testCase.length() > 30 ? testCase.substring(0, 27) + "..." : testCase;
            String shortOriginal = originalResult.length() > 30 ? originalResult.substring(0, 27) + "..." : originalResult;
            String shortOptimized = optimizedResult.length() > 30 ? optimizedResult.substring(0, 27) + "..." : optimizedResult;

            System.out.printf("\"%s\"\t\"%s\"\t\"%s\"\t%s%n",
                    shortTestCase, shortOriginal, shortOptimized, isSame ? "是" : "否");

            // 如果结果不同，输出详细信息
            if (!isSame) {
                System.out.println("原始输入: " + testCase);
                System.out.println("原始实现: " + originalResult);
                System.out.println("优化实现: " + optimizedResult);
                System.out.println();
            }
        }
        Assertions.assertTrue(testCases.length>1);

    }

    @Test
    @DisplayName("测试极端情况 - 非常长的字符串")
    public void testExtremelyLongString() {
        // 创建一个非常长的字符串，包含大量单引号和转义字符
        StringBuilder longStringBuilder = new StringBuilder(1000000); // 1百万字符
        Random random = new Random(System.currentTimeMillis());

        for (int i = 0; i < 1000000; i++) {
            int r = random.nextInt(100);
            if (r < 1) { // 1%的概率添加单引号
                longStringBuilder.append("'");
            } else if (r < 2) { // 1%的概率添加已转义单引号
                longStringBuilder.append("\\'");
            } else {
                longStringBuilder.append((char) (32 + random.nextInt(95))); // 随机ASCII可打印字符
            }
        }

        String longString = longStringBuilder.toString();

        System.out.println("=== 极端情况测试 - 非常长的字符串 ===");
        System.out.println("字符串长度: " + longString.length());

        // 测试原始实现
        long startOriginal = System.nanoTime();
        String originalEscaped = originalEscapeSpecialChar(longString);
        String originalResult = originalDealSpecialChar(originalEscaped);
        long endOriginal = System.nanoTime();
        long originalTime = TimeUnit.NANOSECONDS.toMillis(endOriginal - startOriginal);

        // 测试优化实现
        long startOptimized = System.nanoTime();
        String optimizedEscaped = optimizedEscapeSpecialChar(longString);
        String optimizedResult = optimizedDealSpecialChar(optimizedEscaped);
        long endOptimized = System.nanoTime();
        long optimizedTime = TimeUnit.NANOSECONDS.toMillis(endOptimized - startOptimized);

        // 计算性能比例
        double ratio = (double) optimizedTime / originalTime;

        System.out.println("原始实现处理时间: " + originalTime + "ms");
        System.out.println("优化实现处理时间: " + optimizedTime + "ms");
        System.out.println("性能比例(优化/原始): " + ratio);

        // 检查结果长度
        System.out.println("原始实现结果长度: " + originalResult.length());
        System.out.println("优化实现结果长度: " + optimizedResult.length());

        // 检查结果是否相同
        boolean isSame = originalResult.equals(optimizedResult);
        System.out.println("结果相同: " + (isSame ? "是" : "否"));

        if (!isSame) {
            // 找出第一个不同的位置
            int diffPos = -1;
            int minLength = Math.min(originalResult.length(), optimizedResult.length());
            for (int i = 0; i < minLength; i++) {
                if (originalResult.charAt(i) != optimizedResult.charAt(i)) {
                    diffPos = i;
                    break;
                }
            }

            if (diffPos != -1) {
                int contextStart = Math.max(0, diffPos - 20);
                int contextEnd = Math.min(minLength, diffPos + 20);
                System.out.println("第一个不同位置: " + diffPos);
                System.out.println("原始实现上下文: " + originalResult.substring(contextStart, contextEnd));
                System.out.println("优化实现上下文: " + optimizedResult.substring(contextStart, contextEnd));
            }
        }
        Assertions.assertTrue(isSame);

    }
}
