package com.databuff.common.tsdb.dto.detect;

import com.databuff.common.tsdb.model.LogicalOperator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.ObjectCodec;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.function.Consumer;

@Slf4j
public class MultiDetectQueryRequestDeserializer extends JsonDeserializer<MultiDetectQueryRequest> {

    /**
     * 反序列化JSON数据到MultiDetectQueryRequest对象
     *
     * @param p    JSON解析器，用于读取原始JSON数据流
     * @param ctxt 反序列化上下文，提供反序列化相关配置和工具
     * @return 解析后的MultiDetectQueryRequest请求对象
     * @throws IOException 当JSON解析过程中发生I/O错误时抛出
     */
    @Override
    public MultiDetectQueryRequest deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        JsonNode node = p.getCodec().readTree(p);
        MultiDetectQueryRequest request = new MultiDetectQueryRequest();

        // 处理数字字段映射
        setDetectQueryRequest(request::setA, node, "1", p);
        setDetectQueryRequest(request::setB, node, "2", p);
        setDetectQueryRequest(request::setC, node, "3", p);
        setDetectQueryRequest(request::setD, node, "4", p);
        setDetectQueryRequest(request::setE, node, "5", p);

        // 统一处理逻辑运算符字段
        handleLogicalOperator(node, "critical", request::setCritical);
        handleLogicalOperator(node, "warning", request::setWarning);
        handleLogicalOperator(node, "noData", request::setNoData);

        request.setStart(getLongField(node, "start"));
        request.setEnd(getLongField(node, "end"));
        request.setInterval(getIntField(node, "interval"));
        return request;
    }

    private Long getLongField(JsonNode node, String fieldName) throws IOException {
        JsonNode fieldNode = node.get(fieldName);
        if (fieldNode == null) {
            return null;
        }
        if (!fieldNode.isNumber()) {
            return null;
        }
        return fieldNode.asLong();
    }

    private Integer getIntField(JsonNode node, String fieldName) throws IOException {
        JsonNode fieldNode = node.get(fieldName);
        if (fieldNode == null) {
            return null;
        }
        if (!fieldNode.isIntegralNumber()) {
            return null;
        }
        return fieldNode.asInt();
    }


    /**
     * 处理JSON节点中的逻辑运算符字段，并将其转换为对应的枚举类型
     *
     * @param node      JSON数据节点，用于提取字段值
     * @param fieldName 需要处理的逻辑运算符字段名称
     * @param setter    消费者函数，用于接收解析成功的LogicalOperator枚举值
     *                  <p>
     *                  函数逻辑：
     *                  从JSON节点中获取指定字段
     *                  验证字段是否为有效文本类型（非空且可解析为字符串）
     *                  将文本值转换为大写进行匹配，支持"AND"/"OR"的任意大小写形式
     *                  通过setter回调将匹配的枚举值传递给调用方
     *                  默认忽略未定义值（可根据需求扩展错误处理）
     */
    private void handleLogicalOperator(JsonNode node, String fieldName, Consumer<LogicalOperator> setter) {
        JsonNode fieldNode = node.get(fieldName);
        if (fieldNode != null && !fieldNode.isNull() && fieldNode.isTextual()) {
            String value = fieldNode.asText().trim();
            if ("AND".equalsIgnoreCase(value)) {
                setter.accept(LogicalOperator.AND);
            } else if ("OR".equalsIgnoreCase(value)) {
                setter.accept(LogicalOperator.OR);
            }
        }
    }

    private void setDetectQueryRequest(
            Consumer<DetectQueryRequest> setter,
            JsonNode node,
            String key,
            JsonParser p) {
        if (node.has(key) && !node.get(key).isNull()) {
            try {
                final JsonNode nested = node.get(key);
                final ObjectCodec codec = p.getCodec();
                if (codec != null) {
                    DetectQueryRequest request = codec.treeToValue(nested, DetectQueryRequest.class);
                    if (request != null) {
                        setter.accept(request);
                    }
                }
            } catch (IOException e) {
                log.error("DetectQueryRequest deserialization failed for key {}: {}", key, e.getMessage());
            }
        }
    }
}
