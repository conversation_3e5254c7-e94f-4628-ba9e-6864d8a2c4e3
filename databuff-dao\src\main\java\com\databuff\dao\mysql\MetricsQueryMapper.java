package com.databuff.dao.mysql;

import com.alibaba.fastjson.JSONObject;
import com.databuff.entity.MetricsQuery;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface MetricsQueryMapper {
    @Select("SELECT * FROM dc_databuff_metrics_query WHERE id = #{id}")
    @Results({
            @Result(column = "filter", property = "filter", javaType = JSONObject.class)
    })
    MetricsQuery findById(@Param("id") Integer id);

    @Select({
            "<script>",
            "SELECT * FROM dc_databuff_metrics_query",
            "WHERE 1 = 1",
            "<if test='type1 != null'>AND type1 = #{type1}</if>",
            "<if test='type2 != null'>AND type2 = #{type2}</if>",
            "<if test='type3 != null'>AND type3 = #{type3}</if>",
            "<if test='isOpen != null'>AND is_open = #{isOpen}</if>",
            "ORDER BY update_time DESC",
            "</script>"
    })
    @Results({
            @Result(column = "filter", property = "filter", javaType = JSONObject.class)
    })
    List<MetricsQuery> findByTypesAndIsOpen(@Param("type1") String type1, @Param("type2") String type2, @Param("type3") String type3, @Param("isOpen") Boolean isOpen);

    @Insert("INSERT INTO dc_databuff_metrics_query(type1, type2, type3, database, measurement, field, desc, unit, formula, filter, create_time, update_time) VALUES(#{type1}, #{type2}, #{type3}, #{database}, #{measurement}, #{field}, #{desc}, #{unit}, #{formula}, #{filter}, #{createTime}, #{updateTime})")
    void insert(MetricsQuery dcDatabuffMetricsQuery);

    @Update("UPDATE dc_databuff_metrics_query SET type1 = #{type1}, type2 = #{type2}, type3 = #{type3}, database = #{database}, measurement = #{measurement}, field = #{field}, desc = #{desc}, unit = #{unit}, formula = #{formula}, filter = #{filter}, create_time = #{createTime}, update_time = #{updateTime} WHERE id = #{id}")
    void update(MetricsQuery dcDatabuffMetricsQuery);

    @Update({"<script>",
            "UPDATE dc_databuff_metrics_query",
            "SET isOpen = #{isOpen}",
            "WHERE id IN",
            "<foreach item='item' index='index' collection='idList' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"})
    void updateIsOpenInBulk(@Param("idList") List<Long> idList, @Param("isOpen") Boolean isOpen);

    @Update("UPDATE dc_databuff_metrics_query SET isOpen = #{isOpen} WHERE id = #{id}")
    void updateIsOpen(@Param("id") Long id, @Param("isOpen") Boolean isOpen);

    @Delete("DELETE FROM dc_databuff_metrics_query WHERE id = #{id}")
    void delete(@Param("id") Integer id);
}