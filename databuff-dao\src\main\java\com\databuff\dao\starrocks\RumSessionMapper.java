package com.databuff.dao.starrocks;

import com.databuff.entity.dto.TimeValue;
import com.databuff.entity.rum.web.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Mapper
@Repository
public interface RumSessionMapper {

    List<SessionDto> getSessionsByCriteria(@Param("request") SessionListRequest request,
                                           @Param("appIds") List<Integer> appIds,
                                           @Param("hourRange") Map<String, Integer> hourRange);

    SessionAnalysisDto getSessionAnalysis(@Param("request") SessionAnalysisRequest request,
                                          @Param("appIds") List<Integer> appIds,
                                          @Param("hourRange") Map<String, Integer> hourRange);

    List<TimeValue> getSessionTrend(@Param("request") SessionTrendRequest request,
                                    @Param("appIds") List<Integer> appIds,
                                    @Param("hourRange") Map<String, Integer> hourRange);

    List<AppSessionMetricsDto> getSessionMetrics(@Param("request") SessionListRequest request,
                                                 @Param("appIds") List<Integer> appIds,
                                                 @Param("hourRange") Map<String, Integer> hourRange);


    List<RumUser> getUserList(@Param("request") RumUserListRequest request,
                              @Param("appIds") List<Integer> appIds,
                              @Param("hourRange") Map<String, Integer> hourRange);

    RumUserProfile getUserProfile(@Param("userId") String userId,
                                  @Param("request") RumUserProfileRequest request,
                                  @Param("appIds") List<Integer> appIds,
                                  @Param("hourRange") Map<String, Integer> hourRange);

}
