package com.databuff.common.constants;

/**
 * 通用常量
 *
 * @Package com.databuff.config.common
 * @company: dacheng
 * @Author: zlh
 * @CreateDate: 2020/5/27
 */
public class CommonConstants {


    /**
     * 压测参数
     */
    public static final String TEST_TAG = "testTag";


    /**
     * Action 审计动作
     */
    public static final String ACTION_ADD = "新增";
    public static final String ACTION_EDIT = "编辑";
    public static final String ACTION_ENABLE = "启用";
    public static final String ACTION_DISABLE = "停用";
    public static final String ACTION_DELETE = "删除";

    /**
     * requestURI
     */
    public static final String REQUEST_URI_GRAPHQL = "/graphql";
    public static final String REQUEST_URI_QRCODE = "/user/qrCode";
    public static final String REQUEST_URI_LOGIN = "/user/login";
    public static final String REQUEST_URI_SAAS_CUSTOMER_LOGIN = "/saasCustomer/login";
    public static final String REQUEST_URI_SAAS_CUSTOMER_ACTION = "/saasCustomer/isActivate";
    public static final String REQUEST_URI_SAAS_CUSTOMER_OUT_TIME = "/saasCustomer/isOutTime";
    public static final String REQUEST_URI_IMGCAPT = "/user/imgcapt";
    public static final String REQUEST_URI_IS_ACTIVATE = "/user/isActivate";
    public static final String REQUEST_URI_VERSION = "/user/product/version";
    public static final String REQUEST_URI_LIS_UPLOAD = "/user/lisupload";
    public static final String REQUEST_URI_SAAS_LIC_UPLOAD = "/user/saasLicUpload";
    public static final String REQUEST_URI_CREATE_ADMIN_AUDIT = "/user/createAdminAduit";
    public static final String REQUEST_URI_DOWNLOAD_TEMPLATE = "/downLoadTemplate";
    public static final String REQUEST_URI_BATCH_ADD_METRICS = "/batchAddMetrics";
    public static final String REQUEST_URI_BATCH_NOTICE_METRICS = "/batchNoticeMetricsAddModel";
    public static final String REQUEST_URI_BATCH_NOTICE_RE_CREATE_METRICS = "/batchNoticeReCreateModel";
    public static final String REQUEST_URI_LICENSE_DATA = "/user/getLicenseSerialnum";
    public static final String REQUEST_URI_SAAS_LIC = "/user/isLic";

    public static final String REQUEST_URI_ALARM_FORWARD_TEST = "/api/alarm/forward/test";


    public static final String REQUEST_URI_DATAHUB_RUNNING_CONFIG = "/datahub/v1/manager/pipeline/run_config";

    public static final String REQUEST_URI_DATAHUB_REMOTE_RUNNER_CONFIG = "/datahub/pipeline/run_config";

    /**
     * 升级接口处理数据兼容内部api
     */
    public static final String REQUEST_URI_UPGRADE_API = "/upgrade/compatible/";
    /**
     * 事件开放api
     */
    public static final String REQUEST_URI_EVENT_API = "/monitor/listEvent";

    public static final String REQUEST_URI_IMC_CALLBACK = "/singleLogin/imc/callback";
    public static final String REQUEST_URI_IMC_AUTHLOGIN = "/singleLogin/imc/authlogin";


    public static final String REQUEST_URI_CONFIG_CENTER = "/configManage/getConfigs";
    public static final String REQUEST_URI_WEBAPP_CHECK = "/webapp/check";
    public static final String REQUEST_URI_DUMP_DOWNLOAD = "/api/dump/download/";
    public static final String REQUEST_URI_DTS_CHECK = "/dts/check";
    public static final String REQUEST_URI_AGENT_OPERATION_LIST = "/api/v1/update/list";
    public static final String REQUEST_URI_BIZ_MIGRATION = "/biz-m/migrate/biz-scenario";


    /**
     * xxl-job相关接口
     */
    public static final String REQUEST_URI_XXLJOB_API = "/xxl-job-admin";


    /**
     * userRealm
     */
    public static final String USER_LOCK_STATUS_LOCKED = "已锁定";
    public static final String USER_LINE_STATUS_OFF = "离线";
    public static final String USER_LINE_STATUS_ON = "在线";

    public static final String  DEMO_USER = "demo_Databuff";

    /**
     * 华为云IMC 用户同步接口安全令牌
     */
    public static final String  IMC_USER_SYNC = "Y05F59a9B0f127dC";
    /**
     * 华为云IMC 签名密钥
     */
    public static final String  IMC_SIGNATURE_KEY = "9A9f9dfL47bC6a2d";
    /**
     * 华为云IMC 消息体加密密钥
     */
    public static final String  IMC_SCERET_KEY = "Pd6J904ca943F4aL";

}
