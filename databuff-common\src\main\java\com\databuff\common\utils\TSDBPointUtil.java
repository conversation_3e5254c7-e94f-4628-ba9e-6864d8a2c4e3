package com.databuff.common.utils;

import com.databuff.common.tsdb.model.TSDBPoint;
import com.databuff.moredb.FieldUtil;
import com.databuff.moredb.aggregator.histogram.HistogramUtil;
import com.databuff.moredb.proto.Common;

import java.math.BigDecimal;
import java.net.InetAddress;
import java.util.HashMap;
import java.util.Map;

import static com.databuff.common.constants.Constant.Metric.FIELD_MAX_DURATION;
import static com.databuff.common.constants.Constant.Metric.FIELD_MIN_DURATION;
import static com.databuff.common.constants.MetricConstant.*;

public class TSDBPointUtil {

    public static final Map<String, Common.FieldType> DEFAULT_FIELD_TYPES = new HashMap<>();

    static {
        DEFAULT_FIELD_TYPES.put(COMPONENT_CALLS, Common.FieldType.SUM);
        DEFAULT_FIELD_TYPES.put(COMPONENT_SUM_DURATION, Common.FieldType.SUM);
        DEFAULT_FIELD_TYPES.put(FIELD_MAX_DURATION, Common.FieldType.MAX);
        DEFAULT_FIELD_TYPES.put(FIELD_MIN_DURATION, Common.FieldType.MIN);
        DEFAULT_FIELD_TYPES.put(HistogramUtil.HISTOGRAM_MAX, Common.FieldType.MAX);
        DEFAULT_FIELD_TYPES.put(SERVICE_HEALTH_STATUS, Common.FieldType.GAUGE);
    }

    public static TSDBPoint buildGaugePoint(String serviceName, String metricName, long currentTime, String fieldName, long value) {
        return buildGaugePoint(serviceName, metricName, currentTime, fieldName, value, null);
    }

    public static TSDBPoint buildGaugePoint(String serviceName, String metricName, long currentTime, String fieldName, long value, Map<String, String> tags) {
        TSDBPoint builder = buildBuilder(serviceName, metricName, currentTime, tags);
        Map<String, Object> pFields = builder.getFields();
        if (pFields == null) {
            pFields = new HashMap<>();
        }
        pFields.put(fieldName, value);
        Map<String, Common.FieldType> fieldTypes = builder.getFieldTypes();
        if (fieldTypes == null) {
            fieldTypes = new HashMap<>();
        }
        fieldTypes.put(fieldName, Common.FieldType.GAUGE);
        builder.setFieldTypes(fieldTypes);
        builder.setFields(pFields);
        return builder;
    }

    public static TSDBPoint buildSumPoint(String serviceName, String metricName, long currentTime, String fieldName, long value, Map<String, String> tags) {
        TSDBPoint builder = buildBuilder(serviceName, metricName, currentTime, tags);
        Map<String, Object> pFields = builder.getFields();
        if (pFields == null) {
            pFields = new HashMap<>();
        }
        pFields.put(fieldName, value);
        Map<String, Common.FieldType> fieldTypes = builder.getFieldTypes();
        if (fieldTypes == null) {
            fieldTypes = new HashMap<>();
        }
        fieldTypes.put(fieldName, Common.FieldType.SUM);
        builder.setFieldTypes(fieldTypes);
        builder.setFields(pFields);
        return builder;
    }

    public static TSDBPoint buildDoubleGaugePoint(String serviceName, String metricName, long currentTime, String fieldName, double value) {
        return buildDoubleGaugePoint(serviceName, metricName, currentTime, fieldName, value, null);
    }

    public static TSDBPoint buildDoubleGaugePoint(String serviceName, String metricName, long currentTime, String fieldName, double value, Map<String, String> tags) {
        TSDBPoint builder = buildBuilder(serviceName, metricName, currentTime, tags);
        Map<String, Object> pFields = builder.getFields();
        if (pFields == null) {
            pFields = new HashMap<>();
        }
        pFields.put(fieldName, value);
        Map<String, Common.FieldType> fieldTypes = builder.getFieldTypes();
        if (fieldTypes == null) {
            fieldTypes = new HashMap<>();
        }
        fieldTypes.put(fieldName, Common.FieldType.GAUGE);
        builder.setFieldTypes(fieldTypes);
        builder.setFields(pFields);
        return builder;
    }

    private static TSDBPoint buildBuilder(String serviceName, String metricName, long currentTime, Map<String, String> tags) {
        TSDBPoint builder = buildPoint(serviceName, metricName, currentTime);
        if (tags != null) {
            Map<String,String> pTags = builder.getTags();
            if (pTags == null) {
                pTags = new HashMap<>();
            }
            for (Map.Entry<String, String> entry : tags.entrySet()) {
                pTags.put(entry.getKey(), entry.getValue());
            }
            builder.setTags(pTags);
        }
        return builder;
    }

    public static TSDBPoint buildPoint(String serviceName, String metricName, long currentTimeMs) {
        try {
            TSDBPoint point = new TSDBPoint(serviceName + "." + metricName, currentTimeMs);
            Map<String,String> tags = new HashMap<>();
            InetAddress addr = InetAddress.getLocalHost();
            tags.put("ip", addr.getHostAddress());
            tags.put("hostName", addr.getHostName());
            point.setTags(tags);
            return point;
        } catch (Throwable e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    public static void addField(Common.Point.Builder builder, String fieldName, Object fieldValue, Common.FieldType fieldType) {
        if (fieldValue == null) {
            return;
        }
        Common.Field field = null;
        if (Common.FieldType.MAX.equals(fieldType)) {
            field = FieldUtil.createMaxField(fieldName, ((Number) fieldValue).longValue());
        } else if (Common.FieldType.MIN.equals(fieldType)) {
            field = FieldUtil.createMinField(fieldName, ((Number) fieldValue).longValue());
        } else if (Common.FieldType.GAUGE.equals(fieldType)) {
            field = FieldUtil.createGaugeField(fieldName, ((Number) fieldValue).doubleValue());
        } else if (Common.FieldType.SUM.equals(fieldType)) {
            field = FieldUtil.createSumField(fieldName, ((Number) fieldValue).longValue());
        } else {
            if (fieldValue instanceof Integer) {
                field = FieldUtil.createSumField(fieldName, ((Integer) fieldValue).longValue());
            } else if (fieldValue instanceof Long) {
                field = FieldUtil.createSumField(fieldName, (Long) fieldValue);
            } else if (fieldValue instanceof Double) {
                field = FieldUtil.createGaugeField(fieldName, (Double) fieldValue);
            } else if (fieldValue instanceof BigDecimal) {
                field = FieldUtil.createGaugeField(fieldName, ((BigDecimal) fieldValue).doubleValue());
            }
        }
        if (field != null) {
            builder.addFields(field);
        }
    }

    public static void addField(Common.Point.Builder builder, String fieldName, Object fieldValue) {
        addField(builder, fieldName, fieldValue, DEFAULT_FIELD_TYPES.get(fieldName));
    }

    public static void addField(TSDBPoint builder, String fieldName, Object fieldValue, Common.FieldType fieldType) {
        if (fieldValue == null) {
            return;
        }
        Map<String, Object> pFields = builder.getFields();
        if (pFields == null) {
            pFields = new HashMap<>();
        }
        Map<String, Common.FieldType> fieldTypes = builder.getFieldTypes();
        if (fieldTypes == null) {
            fieldTypes = new HashMap<>();
        }
        if (Common.FieldType.MAX.equals(fieldType)) {
            pFields.put(fieldName, ((Number) fieldValue).longValue());
            fieldTypes.put(fieldName, Common.FieldType.MAX);
        } else if (Common.FieldType.MIN.equals(fieldType)) {
            pFields.put(fieldName, ((Number) fieldValue).longValue());
            fieldTypes.put(fieldName, Common.FieldType.MIN);
        } else if (Common.FieldType.GAUGE.equals(fieldType)) {
            pFields.put(fieldName, ((Number) fieldValue).doubleValue());
            fieldTypes.put(fieldName, Common.FieldType.GAUGE);
        } else if (Common.FieldType.SUM.equals(fieldType)) {
            pFields.put(fieldName, ((Number) fieldValue).longValue());
            fieldTypes.put(fieldName, Common.FieldType.SUM);
        } else {
            if (fieldValue instanceof Integer) {
                pFields.put(fieldName, ((Integer) fieldValue).longValue());
                fieldTypes.put(fieldName, Common.FieldType.SUM);
            } else if (fieldValue instanceof Long) {
                pFields.put(fieldName, (Long) fieldValue);
                fieldTypes.put(fieldName, Common.FieldType.SUM);
            } else if (fieldValue instanceof Double) {
                pFields.put(fieldName, (Double) fieldValue);
                fieldTypes.put(fieldName, Common.FieldType.GAUGE);
            } else if (fieldValue instanceof BigDecimal) {
                pFields.put(fieldName, ((BigDecimal) fieldValue).doubleValue());
                fieldTypes.put(fieldName, Common.FieldType.GAUGE);
            }
        }
        builder.setFieldTypes(fieldTypes);
        builder.setFields(pFields);
    }

    public static void addField(TSDBPoint builder, String fieldName, Object fieldValue) {
        addField(builder, fieldName, fieldValue, DEFAULT_FIELD_TYPES.get(fieldName));
    }

}
