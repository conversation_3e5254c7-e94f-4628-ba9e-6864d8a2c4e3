package com.databuff.common.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class JSONUtils {

    public static JSONObject deepCopy(JSONObject original) {
        JSONObject copy = new JSONObject();
        if (original == null) {
            return copy;
        }
        for (String key : original.keySet()) {
            Object value = original.get(key);
            if (value instanceof JSONObject) {
                copy.put(key, deepCopy((JSONObject) value));
            } else if (value instanceof JSONArray) {
                copy.put(key, deepCopy((JSONArray) value));
            } else {
                copy.put(key, value);
            }
        }
        return copy;
    }

    private static JSONArray deepCopy(JSONArray original) {
        JSONArray copy = new JSONArray();
        if (original == null) {
            return copy;
        }
        for (Object value : original) {
            if (value instanceof JSONObject) {
                copy.add(deepCopy((JSONObject) value));
            } else if (value instanceof JSONArray) {
                copy.add(deepCopy((JSONArray) value));
            } else {
                copy.add(value);
            }
        }
        return copy;
    }


    public static Object findFirstValueForKey(JSONObject jsonObject, String key) {
        // 检查当前级别的键
        if (jsonObject.containsKey(key)) {
            return jsonObject.get(key);
        }

        // 如果当前级别没有找到，遍历所有键并递归查找
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            if (entry.getValue() instanceof JSONObject) {
                Object result = findFirstValueForKey((JSONObject) entry.getValue(), key);
                if (result != null) {
                    return result;
                }
            }
        }
        // 如果在整个JSON对象中都找不到该键，返回null
        return null;
    }

    /**
     * 查找所有键为targetKey的键值对
     * @param jsonObject JSON对象
     * @param targetKey 目标键
     * @return 列表
     */
    public static Set<Object> findEntries(JSONObject jsonObject, String targetKey) {
        if (jsonObject == null || targetKey == null) {
            throw new IllegalArgumentException("jsonObject and targetKey must not be null");
        }

        Set<Object> result = new HashSet<>();
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (key.equals(targetKey)) {
                result.add(value);
            }
            if (value instanceof JSONObject) {
                result.addAll(findEntries((JSONObject) value, targetKey));
            } else if (value instanceof JSONArray) {
                for (Object item : (JSONArray) value) {
                    if (item instanceof JSONObject) {
                        result.addAll(findEntries((JSONObject) item, targetKey));
                    }
                }
            }
        }
        return result;
    }

    /**
     * JSONObject 中的所有值是否都是空字符串。
     * @param jsonObject 要检查的 JSONObject。
     * @return true 如果 JSONObject 中的所有值都是空字符串，否则返回 false。
     */
    public static boolean areAllValuesEmpty(Map<String, String> jsonObject) {
        if (jsonObject == null) {
            return true;
        }
        for (Map.Entry<String, String> entry : jsonObject.entrySet()) {
            Object value = entry.getValue();
            if (value instanceof String && !((String) value).isEmpty()) {
                return false;
            }
        }
        return true;
    }

    /**
     * 将 JSONObject 转换为字符串。
     * @param jsonObject 要转换的 JSONObject。
     * @return
     */
    public static String jsonObjectToString(JSONObject jsonObject) {
        StringBuilder str = new StringBuilder();
        for (String key : jsonObject.keySet()) {
            str.append(key);
            str.append(":");
            str.append(jsonObject.getString(key));
            str.append(";");
        }
        final int length = str.length();
        if (length > 0) {
            str.setLength(length - 1);
        }
        return str.toString();
    }
}