package com.databuff.common.utils;

import com.google.common.hash.HashCode;
import com.google.common.hash.HashFunction;
import com.google.common.hash.Hashing;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
/**
 * `HashUtil` 提供了一系列哈希函数的实现，包括 `MurmurHash3_128`、`SipHash64`、`CityHash64`、`CityHash128` 和 `MD5`。
 * 这些哈希函数在性能、安全性及输出哈希值位数上各有不同，适用于不同的应用场景。
 * <p>
 * ## 哈希函数概览
 * 以下是每个哈希函数的简要说明及使用建议：
 * <p>
 * - **`MurmurHash3_128`**
 *   - **特点**：一种高效的非加密哈希函数，生成 128 位哈希值（以十六进制字符串形式返回）。专为哈希表设计，具有良好的分布性和低冲突率。
 *   - **性能**：处理 100 万次迭代耗时 374 毫秒（字符串输入）。
 *   - **使用场景**：适用于需要快速哈希计算且对哈希冲突有一定容忍度的场景，例如哈希表、布隆过滤器等。
 * <p>
 * - **`SipHash64`**
 *   - **特点**：一种密码学哈希函数，生成 64 位哈希值（以 long 类型返回）。设计用于防止哈希洪泛攻击，具有一定的安全性。
 *   - **性能**：处理 100 万次迭代耗时 356 毫秒（字符串输入）。
 *   - **使用场景**：适用于需要一定安全性的场景，例如防止哈希表攻击，或者在需要 64 位哈希值时使用。
 * <p>
 * - **`CityHash64`**
 *   - **特点**：由 Google 开发的高效非加密哈希函数，生成 64 位哈希值（以 long 类型返回）。专为快速哈希设计。
 *   - **性能**：
 *     - 字符串输入：处理 100 万次迭代耗时 281 毫秒。
 *     - 字节数组输入：处理 100 万次迭代耗时 125 毫秒。
 *   - **使用场景**：适用于需要极高性能的场景，特别是输入为字节数组时，性能更优。适合哈希表或需要快速计算的非安全场景。
 * <p>
 * - **`CityHash128`**
 *   - **特点**：`CityHash` 的 128 位版本，生成 128 位哈希值（以 long[] 数组形式返回）。提供更高的哈希值位数以减少冲突。
 *   - **性能**：
 *     - 字符串输入：处理 100 万次迭代耗时 334 毫秒。
 *     - 字节数组输入：处理 100 万次迭代耗时 100 毫秒。
 *   - **使用场景**：适用于需要更高哈希值位数以降低冲突概率的场景，尤其是输入为字节数组时，性能极佳。
 * <p>
 * - **`MD5`**
 *   - **特点**：一种广泛使用的加密哈希函数，生成 128 位哈希值（此处实现取前 64 位以 long 类型返回）。适用于数据完整性验证等加密场景。
 *   - **性能**：
 *     - 字符串输入：处理 100 万次迭代耗时 682 毫秒。
 *     - 字节数组输入：处理 100 万次迭代耗时 550 毫秒。
 *   - **使用场景**：适用于需要加密哈希的场景，例如数据完整性校验、数字签名等，但性能相对较低。
 * <p>
 * ## 性能测试结果
 * 以下是基于 100 万次迭代的性能数据（单位：毫秒）：
 * <p>
 * | 哈希函数          | 字符串输入 (ms) | 字节数组输入 (ms) |
 * |------------------|----------------|------------------|
 * | Murmur3_128      | 374            | 未测试           |
 * | SipHash64        | 356            | 未测试           |
 * | CityHash64       | 281            | 125             |
 * | CityHash128      | 334            | 100             |
 * | MD5              | 682            | 550             |
 * <p>
 * ## 使用建议
 * 在选择哈希函数时，请根据您的具体需求权衡以下因素：
 * <p>
 * - **性能优先**：
 *   - 如果追求极致性能且输入为字节数组，推荐使用 `CityHash128` (100 ms) 或 `CityHash64` (125 ms)。
 *   - 如果输入为字符串且性能要求较高，推荐使用 `CityHash64` (281 ms)。
 * <p>
 * - **安全性**：
 *   - 如果需要防止哈希表攻击或具备一定安全性，推荐使用 `SipHash64`。
 * <p>
 * - **加密需求**：
 *   - 如果需要加密哈希（如数据完整性验证），推荐使用 `MD5`，但需注意其性能较低。
 * <p>
 * - **哈希值位数**：
 *   - 需要 64 位哈希值时，可选择 `SipHash64` 或 `CityHash64`。
 *   - 需要 128 位哈希值时，可选择 `MurmurHash3_128`、`CityHash128` 或 `MD5`。
 * <p>
 * - **输入类型**：
 *   - 如果输入数据为字节数组，优先选择 `CityHash64` 或 `CityHash128`，因为它们在此场景下性能更优。
 *   - 如果输入数据为字符串，所有函数均适用，但需参考性能数据。
 * <p>
 * ## 注意事项
 * - 性能数据基于随机生成的 500 字符以内字符串或字节数组，实际性能可能因输入数据特性而异。
 * - `MD5` 在此实现中仅返回前 64 位哈希值，若需完整 128 位哈希，请自行调整实现。
 */
public class HashUtil {

    private static final long k0 = 0xc3a5c85c97cb3127L;
    private static final long k1 = 0xb492b66fbe98f273L;
    private static final long k2 = 0x9ae16a3b2f90404fL;
    private static final long k3 = 0xc949d7c7509e6557L;

    //todo 如果可以升级到java11 使用 https://github.com/dynatrace-oss/hash4j xxh3可以更快

//    public static long getXXH3Long(String input) {
//        if (input == null) {
//            throw new IllegalArgumentException("Input string cannot be null");
//        }
//        return com.dynatrace.hash4j.hashing.Hashing.xxh3_64().hashCharsToLong(input);
//    }
//
//    public static long getXXH3ByteLong(byte[] input) {
//        if (input == null) {
//            throw new IllegalArgumentException("Input string cannot be null");
//        }
//        return com.dynatrace.hash4j.hashing.Hashing.xxh3_64().hashBytesToLong(input);
//    }

    public static String murmur3_128Hash(String s) {
        // 创建 MurmurHash3 128 位哈希函数实例
        HashFunction murmur3_128 = Hashing.murmur3_128();

        // 对字符串进行哈希计算
        HashCode hashCode = murmur3_128.hashString(s, StandardCharsets.UTF_8);

        // 获取哈希值
//        byte[] hashBytes = hashCode.asBytes(); // 字节数组形式
        return hashCode.toString();  // 十六进制字符串形式
    }

    public static long sipHash64(String s) {
        // 创建 SipHash-2-4 哈希函数实例
        HashFunction sipHash24 = Hashing.sipHash24();

        // 对字符串进行哈希计算
        HashCode hashCode = sipHash24.hashString(s, StandardCharsets.UTF_8);

        // 将哈希值转换为 64 位的 long
        return hashCode.asLong();
    }

    public static long cityHash64(String s) {
        byte[] bytes = s.getBytes(StandardCharsets.UTF_8);
        return cityHash64(bytes, 0, bytes.length);
    }

    public static long cityHash64(byte[] s) {
        return cityHash64(s, 0, s.length);
    }

    /**
     * 组合了https://gist.github.com/andriimartynov/bae6b8b2e8a3ecaace61
     * https://github.com/ClickHouse/clickhouse-java/blob/main/clickhouse-data/src/main/java/com/clickhouse/data/ClickHouseCityHash.java
     * 跟https://github.com/ClickHouse/clickhouse-java/issues/610
     * 避免JAVA 符号范围问题
     * @param s
     * @param pos
     * @param len
     * @return long
     */
    public static long cityHash64(byte[] s, int pos, int len) {
        if (len <= 32) {
            if (len <= 16) {
                return hashLen0to16(s, pos, len);
            } else {
                return hashLen17to32(s, pos, len);
            }
        } else if (len <= 64) {
            return hashLen33to64(s, pos, len);
        }

        long x = fetch64(s, pos + len - 40);
        long y = fetch64(s, pos + len - 16) + fetch64(s, pos + len - 56);
        long z = hashLen16(fetch64(s, pos + len - 48) + len, fetch64(s, pos + len - 24));

        long[] v = weakHashLen32WithSeeds(s, pos + len - 64, len, z);
        long[] w = weakHashLen32WithSeeds(s, pos + len - 32, y + k1, x);
        x = x * k1 + fetch64(s, pos + 0);

        len = (len - 1) & (~63);
        do {
            x = rotate(x + y + v[0] + fetch64(s, pos + 8), 37) * k1;
            y = rotate(y + v[1] + fetch64(s, pos + 48), 42) * k1;
            x ^= w[1];
            y += v[0] + fetch64(s, pos + 40);
            z = rotate(z + w[0], 33) * k1;
            v = weakHashLen32WithSeeds(s, pos + 0, v[1] * k1, x + w[0]);
            w = weakHashLen32WithSeeds(s, pos + 32, z + w[1], y + fetch64(s, pos + 16));
            long swap = z;
            z = x;
            x = swap;
            pos += 64;
            len -= 64;
        } while (len != 0);

        return hashLen16(
                hashLen16(v[0], w[0]) + shiftMix(y) * k1 + z,
                hashLen16(v[1], w[1]) + x
        );
    }

    public static long cityHash64WithSeed(byte[] s, int pos, int len, long seed) {
        return cityHash64WithSeeds(s, pos, len, k2, seed);
    }

    public static long cityHash64WithSeeds(byte[] s, int pos, int len, long seed0, long seed1) {
        return hashLen16(cityHash64(s, pos, len) - seed0, seed1);
    }

    public static long[] cityHash128(String s) {
        byte[] bytes = s.getBytes(StandardCharsets.UTF_8);
        return cityHash128(bytes, 0, bytes.length);
    }

    public static long[] cityHash128(byte[] s) {
        return cityHash128(s, 0, s.length);
    }

    public static long[] cityHash128(byte[] s, int pos, int len) {
        if (len >= 16) {
            return cityHash128WithSeed(s, pos + 16, len - 16, fetch64(s, pos) ^ k3, fetch64(s, pos + 8));
        } else if (len >= 8) {
            return cityHash128WithSeed(new byte[0], 0, 0, fetch64(s, pos) ^ (len * k0), fetch64(s, pos + len - 8) ^ k1);
        } else {
            return cityHash128WithSeed(s, pos, len, k0, k1);
        }
    }

    private static long toLongLE(byte[] b, int i) {
        return 0xffffffffffffffffL & (((long) b[i + 7] << 56) + ((long) (b[i + 6] & 255) << 48)
                + ((long) (b[i + 5] & 255) << 40) + ((long) (b[i + 4] & 255) << 32) + ((long) (b[i + 3] & 255) << 24)
                + ((b[i + 2] & 255) << 16) + ((b[i + 1] & 255) << 8) + ((b[i + 0] & 255)));
    }

    private static long toIntLE(byte[] b, int i) {
        return 0xffffffffL & (((b[i + 3] & 255) << 24) + ((b[i + 2] & 255) << 16) + ((b[i + 1] & 255) << 8)
                + ((b[i + 0] & 255)));
    }

    private static long fetch64(byte[] s, int pos) {
        return toLongLE(s, pos);
    }

    private static long fetch32(byte[] s, int pos) {
        return toIntLE(s, pos);
    }

    private static int staticCastToInt(byte b) {
        return b & 0xFF;
    }

    private static long rotate(long val, int shift) {
        return shift == 0 ? val : (val >>> shift) | (val << (64 - shift));
    }

    private static long rotateByAtLeast1(long val, int shift) {
        return (val >>> shift) | (val << (64 - shift));
    }

    private static long shiftMix(long val) {
        return val ^ (val >>> 47);
    }

    private static final long kMul = 0x9ddfea08eb382d69L;

    private static long hash128to64(long u, long v) {
        long a = (u ^ v) * kMul;
        a ^= (a >>> 47);
        long b = (v ^ a) * kMul;
        b ^= (b >>> 47);
        b *= kMul;
        return b;
    }

    private static long hashLen16(long u, long v) {
        return hash128to64(u, v);
    }

    private static long hashLen0to16(byte[] s, int pos, int len) {
        if (len > 8) {
            long a = fetch64(s, pos + 0);
            long b = fetch64(s, pos + len - 8);
            return hashLen16(a, rotateByAtLeast1(b + len, len)) ^ b;
        }
        if (len >= 4) {
            long a = fetch32(s, pos + 0);
            return hashLen16((a << 3) + len, fetch32(s, pos + len - 4));
        }
        if (len > 0) {
            byte a = s[pos + 0];
            byte b = s[pos + (len >>> 1)];
            byte c = s[pos + len - 1];
            int y = staticCastToInt(a) + (staticCastToInt(b) << 8);
            int z = len + (staticCastToInt(c) << 2);
            return shiftMix(y * k2 ^ z * k3) * k2;
        }
        return k2;
    }

    private static long[] weakHashLen32WithSeeds(long w, long x, long y, long z, long a, long b) {

        a += w;
        b = rotate(b + a + z, 21);
        long c = a;
        a += x;
        a += y;
        b += rotate(a, 44);
        return new long[]{a + z, b + c};
    }

    private static long[] weakHashLen32WithSeeds(byte[] s, int pos, long a, long b) {
        return weakHashLen32WithSeeds(fetch64(s, pos + 0), fetch64(s, pos + 8), fetch64(s, pos + 16),
                fetch64(s, pos + 24), a, b);
    }

    private static long[] cityMurmur(byte[] s, int pos, int len, long seed0, long seed1) {

        long a = seed0;
        long b = seed1;
        long c = 0;
        long d = 0;

        int l = len - 16;
        if (l <= 0) {
            a = shiftMix(a * k1) * k1;
            c = b * k1 + hashLen0to16(s, pos, len);
            d = shiftMix(a + (len >= 8 ? fetch64(s, pos + 0) : c));
        } else {

            c = hashLen16(fetch64(s, pos + len - 8) + k1, a);
            d = hashLen16(b + len, c + fetch64(s, pos + len - 16));
            a += d;

            do {
                a ^= shiftMix(fetch64(s, pos + 0) * k1) * k1;
                a *= k1;
                b ^= a;
                c ^= shiftMix(fetch64(s, pos + 8) * k1) * k1;
                c *= k1;
                d ^= c;
                pos += 16;
                l -= 16;
            } while (l > 0);
        }

        a = hashLen16(a, c);
        b = hashLen16(d, b);

        return new long[]{a ^ b, hashLen16(b, a)};
    }

    private static long[] cityHash128WithSeed(byte[] s, int pos, int len, long seed0, long seed1) {
        if (len < 128) {
            return cityMurmur(s, pos, len, seed0, seed1);
        }

        long[] v = new long[2], w = new long[2];
        long x = seed0;
        long y = seed1;
        long z = k1 * len;
        v[0] = rotate(y ^ k1, 49) * k1 + fetch64(s, pos);
        v[1] = rotate(v[0], 42) * k1 + fetch64(s, pos + 8);
        w[0] = rotate(y + z, 35) * k1 + x;
        w[1] = rotate(x + fetch64(s, pos + 88), 53) * k1;

        // This is the same inner loop as CityHash64(), manually unrolled.
        do {
            x = rotate(x + y + v[0] + fetch64(s, pos + 16), 37) * k1;
            y = rotate(y + v[1] + fetch64(s, pos + 48), 42) * k1;

            x ^= w[1];
            y ^= v[0];

            z = rotate(z ^ w[0], 33);
            v = weakHashLen32WithSeeds(s, pos, v[1] * k1, x + w[0]);
            w = weakHashLen32WithSeeds(s, pos + 32, z + w[1], y);

            {
                long swap = z;
                z = x;
                x = swap;
            }
            pos += 64;
            x = rotate(x + y + v[0] + fetch64(s, pos + 16), 37) * k1;
            y = rotate(y + v[1] + fetch64(s, pos + 48), 42) * k1;
            x ^= w[1];
            y ^= v[0];
            z = rotate(z ^ w[0], 33);
            v = weakHashLen32WithSeeds(s, pos, v[1] * k1, x + w[0]);
            w = weakHashLen32WithSeeds(s, pos + 32, z + w[1], y);
            {
                long swap = z;
                z = x;
                x = swap;
            }
            pos += 64;
            len -= 128;
        } while (len >= 128);

        y += rotate(w[0], 37) * k0 + z;
        x += rotate(v[0] + z, 49) * k0;

        // If 0 < len < 128, hash up to 4 chunks of 32 bytes each from the end of s.
        for (int tail_done = 0; tail_done < len; ) {
            tail_done += 32;
            y = rotate(y - x, 42) * k0 + v[1];
            w[0] += fetch64(s, pos + len - tail_done + 16);
            x = rotate(x, 49) * k0 + w[0];
            w[0] += v[0];
            v = weakHashLen32WithSeeds(s, pos + len - tail_done, v[0], v[1]);
        }

        // At this point our 48 bytes of state should contain more than
        // enough information for a strong 128-bit hash. We use two
        // different 48-byte-to-8-byte hashes to get a 16-byte final result.

        x = hashLen16(x, v[0]);
        y = hashLen16(y, w[0]);

        return new long[]{hashLen16(x + v[1], w[1]) + y, hashLen16(x + w[1], y + v[1])};
    }


    private static long hashLen17to32(byte[] s, int pos, int len) {
        long a = fetch64(s, pos + 0) * k1;
        long b = fetch64(s, pos + 8);
        long c = fetch64(s, pos + len - 8) * k2;
        long d = fetch64(s, pos + len - 16) * k0;
        return hashLen16(
                rotate(a - b, 43) + rotate(c, 30) + d,
                a + rotate(b ^ k3, 20) - c + len
        );
    }

    private static long hashLen33to64(byte[] s, int pos, int len) {
        long z = fetch64(s, pos + 24);
        long a = fetch64(s, pos + 0) + (fetch64(s, pos + len - 16) + len) * k0;
        long b = rotate(a + z, 52);
        long c = rotate(a, 37);

        a += fetch64(s, pos + 8);
        c += rotate(a, 7);
        a += fetch64(s, pos + 16);

        long vf = a + z;
        long vs = b + rotate(a, 31) + c;

        a = fetch64(s, pos + 16) + fetch64(s, pos + len - 32);
        z = fetch64(s, pos + len - 8);
        b = rotate(a + z, 52);
        c = rotate(a, 37);
        a += fetch64(s, pos + len - 24);
        c += rotate(a, 7);
        a += fetch64(s, pos + len - 16);

        long wf = a + z;
        long ws = b + rotate(a, 31) + c;
        long r = shiftMix((vf + ws) * k2 + (wf + vs) * k0);

        return shiftMix(r * k0 + vs) * k2;
    }

    public static long getMD5Long(String input) {
        MessageDigest md;
        try {
            md = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
        byte[] digest = md.digest(input.getBytes());

        // Convert the first 8 bytes of the MD5 hash to a long
        long md5Long = 0;
        for (int i = 0; i < 8; i++) {
            md5Long = (md5Long << 8) | (digest[i] & 0xFF);
        }

        return md5Long;
    }

    public static long getMD5Long(byte[] input) {
        MessageDigest md;
        try {
            md = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
        byte[] digest = md.digest(input);

        // Convert the first 8 bytes of the MD5 hash to a long
        long md5Long = 0;
        for (int i = 0; i < 8; i++) {
            md5Long = (md5Long << 8) | (digest[i] & 0xFF);
        }

        return md5Long;
    }
}