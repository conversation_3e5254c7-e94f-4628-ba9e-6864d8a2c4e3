package com.databuff.common.utils;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 与时间相关的操作的实用程序类。
 */
public class TimeUtil {

    private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public static String getTime(long timestamp) {
        return sdf.format(new Date(timestamp));
    }

    /**
     * 表示一分钟（以毫秒为单位）的常量。
     */
    public static final int ONE_MINUTE_MS = 60000;

    /**
     * 表示一小时（以毫秒为单位）的常量。
     */
    public static final int ONE_HOUR_MS = 3600000;

    /**
     * 代表一天（以毫秒为单位）的常量。
     */
    public static final long ONE_DAY_MS = 24 * ONE_HOUR_MS;

    /**
     * 表示一天（以秒为单位）的常量。
     */
    public static final int ONE_DAY_S = 24 * 3600;

    /**
     * SimpleDateFormat的ThreadLocal变量，保证线程安全。
     * 以“yyyy-MM-dd HH:mm:ss”格式初始化。
     */
    public static final ThreadLocal<SimpleDateFormat> DATE_TIME_FORMAT = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
    public static final ThreadLocal<SimpleDateFormat> DATE_TIME_FORMAT_2 = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:00"));

    public static final long OUT_DAY_MS_LONG = 30 * 24 * 60 * 60 * 1000L;

    public static final long ONE_WEEK_MS = 7 * 24 * 60 * 60 * 1000L;

    public static final int OUT_DAY_SEC_INT = 30 * 24 * 60 * 60;

    // 1分钟的秒数
    public static final long ONE_MIN_SEC_LONG = 60L;
    public static final int ONE_MIN_SEC_INT = 60;

    // 1分钟的毫秒数
    public static final long ONE_MIN_MS_LONG = 60 * 1000L;
    public static final int ONE_MIN_MS_INT = 60 * 1000;
    // 1小时的毫秒数
    public static final long ONE_HOUR_MS_LONG = 60 * 60 * 1000L;
    // 1天的毫秒数
    public static final long ONE_DAY_MS_LONG = 24 * 60 * 60 * 1000L;

    /**
     * 将表示时间的长值转换为格式化字符串。
     *
     * @param time 以毫秒为单位的时间。
     * @return 表示格式化时间的字符串。
     */
    public static String formatLongToString(long time) {
        return DATE_TIME_FORMAT.get().format(new Date(time));
    }

    public static String formatLongToString2(long time) {
        return DATE_TIME_FORMAT_2.get().format(new Date(time));
    }

    public static long padTo19Digits(long timestamp) {
        int length = String.valueOf(timestamp).length();
        if (length < 19) {
            timestamp *= Math.pow(10, 19 - length);
        }
        return timestamp;
    }

    /**
     * 将时间戳向上取整到分钟，秒和毫秒都补0。
     *
     * @param timestamp 以毫秒为单位的时间戳。
     * @return 向上取整到分钟的时间戳。
     * <AUTHOR>
     */
    public static long roundUpToMinute(long timestamp) {
        return (timestamp / ONE_MIN_MS_LONG + 1) * ONE_MIN_MS_LONG;
    }

    /**
     * 将时间戳向下取整到分钟，秒和毫秒都补0。
     *
     * @param timestamp 以毫秒为单位的时间戳。
     * @return 向下取整到分钟的时间戳。
     * <AUTHOR>
     */
    public static long roundDownToMinute(long timestamp) {
        return timestamp / ONE_MIN_MS_LONG * ONE_MIN_MS_LONG;
    }

    public static long getTimeMinute(long time) {
        String str = TimeUtil.formatLongToString(time);
        return getTimeMinute(str);
    }

    public static String getTimeMinuteString(String time) {
        return time.substring(0, 16).replace("-", "").replace(" ", "").replace(":", "");
    }

    public static long getTimeMinute(String time) {
        String minutesString = getTimeMinuteString(time);
        long minutes = Long.parseLong(minutesString);
        return minutes;
    }

    /**
     * 将给定时间戳向下取整到最近的天的起始时间（即当天的00:00:00）。
     * 通过将时间戳除以一天的毫秒数后取整，再乘以一天的毫秒数，实现时间戳截断到当天的起始时刻。
     *
     * @param timestamp 需要处理的原始时间戳（单位：毫秒）
     * @return 截断后的当天起始时间戳（单位：毫秒）
     */
    public static long roundDownToDay(long timestamp) {
        return timestamp / ONE_DAY_MS_LONG * ONE_DAY_MS_LONG;
    }

    /**
     * 计算两个时间戳之间的天数差（带符号）。
     * 通过计算时间差的毫秒数除以一天的毫秒数得到精确天数差。
     *
     * @param timestamp1 第一个时间戳（单位：毫秒）
     * @param timestamp2 第二个时间戳（单位：毫秒）
     * @return 两时间戳的天数差（timestamp1 - timestamp2 的毫秒差 / 一天的毫秒数）
     */
    public static long computeDayDifference(long timestamp1, long timestamp2) {
        long timeDiff = roundDownToDay(timestamp1) - roundDownToDay(timestamp2);
        return timeDiff / ONE_DAY_MS_LONG;
    }

}
