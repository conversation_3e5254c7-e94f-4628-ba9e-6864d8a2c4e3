package com.databuff.common.utils.socket;
import java.io.*;
import java.net.ServerSocket;
import java.net.Socket;

public class SocketServer {
    private int port;
    private String encoding;

    public SocketServer(int port, String encoding) {
        this.port = port;
        this.encoding = encoding;
    }

    public void start() {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            System.out.println("Shutting down server gracefully...");
        }));

        try (ServerSocket serverSocket = new ServerSocket(port)) {
            System.out.println("Server is listening on port " + port);
            while (!Thread.currentThread().isInterrupted()) {
                try (Socket socket = serverSocket.accept()) {
                    System.out.println("New client connected");
                    handleClient(socket);
                } catch (IOException e) {
                    System.err.println("Client connection error: " + e.getMessage());
                }
            }
        } catch (IOException e) {
            System.err.println("Server error: " + e.getMessage());
        }
    }

    private void handleClient(Socket socket) throws IOException {
        try (InputStream input = socket.getInputStream();
             OutputStream output = socket.getOutputStream()) {

            BufferedReader reader = new BufferedReader(new InputStreamReader(input, encoding));
            BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(output, encoding));

            StringBuilder clientMessageBuilder = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                clientMessageBuilder.append(line).append("\n");
            }
            String clientMessage = clientMessageBuilder.toString();
            System.out.println("Received from client: ");
            System.out.println(clientMessage);

            String responseMessage =
                    "1111111<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
                            "<Service>" +
                            "<Service_Header>" +
                            "<msg_interval>25</msg_interval>" +
                            "<msg_expiry>38000</msg_expiry>" +
                            "<processes>" +
                            "<process>" +
                            "<end_timestamp></end_timestamp>" +
                            "<process_timestamp>2019-11-01 10:40:00.086</process_timestamp>" +
                            "<target_q>IBM.SERVICE.REQUEST.OUT.XBUS</target_q>" +
                            "<back_app_entry_id>COMMON_XBUS</back_app_entry_id>" +
                            "<sub_target_id>0000</sub_target_id>" +
                            "<target_id>0123</target_id>" +
                            "<sub_service_sn>1000026000009597486</sub_service_sn>" +
                            "<sub_reversal_service_id></sub_reversal_service_id>" +
                            "<async_reversal_service_id></async_reversal_service_id>" +
                            "<is_end>0</is_end>" +
                            "<skip_to_process>0</skip_to_process>" +
                            "<after_logic_class></after_logic_class>" +
                            "<timeout>3000</timeout>" +
                            "<reversal_seq>0</reversal_seq>" +
                            "<key_service>0</key_service>" +
                            "<status>COMPLETE</status>" +
                            "<service_type></service_type>" +
                            "<service_id>01230000000001</service_id>" +
                            "<code>S000A000</code>" +
                            "<desc>短信平台发送送错了~~~！！！</desc>" +
                            "<requester_code>S000A000</requester_code>" +
                            "<requester_desc>平台描述</requester_desc>" +  // 原乱码已替换
                            "</process>" +
                            "</processes>" +
                            "<msglog>1</msglog>" +
                            "<timeout>3000</timeout>" +
                            "<name>服务名称</name>" +  // 原乱码已替换
                            "<service_id>01230000000001</service_id>" +
                            "<service_sn>1000026000009597486</service_sn>" +
                            "<service_time>20191101104000</service_time>" +
                            "<branch_id>800777777</branch_id>" +
                            "<channel_id>96</channel_id>" +
                            "<requester_id>0026</requester_id>" +
                            "<service_response>" +
                            "<code>S000A000</code>" +
                            "<status>COMPLETE</status>" +
                            "<desc>执行成功</desc>" +
                            "</service_response>" +
                            "<global_trace_num>80000260000000000000000lmesbapp201911011E8F86</global_trace_num>" +
                            "<global_path_num>1</global_path_num>" +
                            "<start_timestamp>2019-11-01 10:40:00.083</start_timestamp>" +
                            "<trace_msg>SERVICE.SINGLE.ROUTE - Sent single request to provider</trace_msg>" +
                            "</Service_Header>" +
                            "<Service_Body>" +
                            "<ext_attributes>" +
                            "<REQ_GLB_APPS_SYS_ID>0000026</REQ_GLB_APPS_SYS_ID>" +
                            "<REQ_SUB_APPS_SYS_ID>0000</REQ_SUB_APPS_SYS_ID>" +
                            "<PRO_GLB_APPS_SYS_ID>0000123</PRO_GLB_APPS_SYS_ID>" +
                            "<PRO_SUB_APPS_SYS_ID>0000</PRO_SUB_APPS_SYS_ID>" +
                            "<INNER_DataProcPara>0,0,1</INNER_DataProcPara>" +
                            "</ext_attributes>" +
                            "<request>" +
                            "<yybs>017</yybs>" +
                            "<cpdm>**********</cpdm>" +
                            "<jydm>825000</jydm>" +
                            "<srvid>W01</srvid>" +
                            "<immedflag>1</immedflag>" +
                            "<objaddr1>13615311975</objaddr1>" +
                            "<objaddr2>18765634801</objaddr2>" +
                            "<objaddr3>17615406166</objaddr3>" +
                            "<objaddr4>15806655069</objaddr4>" +
                            "<objaddr5>17686411699</objaddr5>" +
                            "<msgcont>[E000A323] 消息内容省略</msgcont>" +  // 原乱码内容已替换
                            "</request>" +
                            "<response></response>" +
                            "</Service_Body>" +
                            "</Service>";

            writer.write(responseMessage);
            writer.newLine();
            writer.flush();
        }
    }

    public static void main(String[] args) {
        int port = 12345; // 服务器端口
        String encoding = "UTF-8"; // 编码格式

        SocketServer server = new SocketServer(port, encoding);
        server.start();
    }
}