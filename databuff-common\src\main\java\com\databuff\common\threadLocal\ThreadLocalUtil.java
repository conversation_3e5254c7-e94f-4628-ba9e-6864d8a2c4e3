package com.databuff.common.threadLocal;

import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.concurrent.Callable;

/**
 * ThreadLocal 提供了线程本地变量，每个线程都拥有自己独立的变量副本，线程之间互不干扰。
 * 适用于在多线程环境下，确保变量的线程安全性。
 */
@Slf4j
public class ThreadLocalUtil {
    private static final ThreadLocal<ThreadLocalEntity> THREAD_LOCAL = new ThreadLocal<>();

    public static void setThreadLocal(ThreadLocalEntity value) {
        THREAD_LOCAL.set(value);
    }

    /**
     * 设置域ID列表
     *
     * @param agis 管理域ids
     */
    public static void setGids(Collection<String> agis) {
        setGids(null, agis);
    }

    public static void setGids(String account, Collection<String> agis) {
        if (agis == null) {
            throw new IllegalArgumentException("域ID不能为空");
        }
        final ThreadLocalEntity threadLocal = getThreadLocal();
        if (threadLocal != null) {
            threadLocal.setAgis(agis);
            if (account != null) {
                threadLocal.setAccount(account);
            }
            threadLocal.setUpdateFlag(true);
        } else {
            setThreadLocal(new ThreadLocalEntity(account, agis));
        }
    }

    public static ThreadLocalEntity getThreadLocal() {
      return  THREAD_LOCAL.get();
    }
    public static void removeThreadLocal() {
        THREAD_LOCAL.remove();
    }

    public static Collection<String> getGid() {
        return THREAD_LOCAL.get()==null?null:THREAD_LOCAL.get().getAgis();
    }
    public static String getAccount() {
        return THREAD_LOCAL.get()==null?null:THREAD_LOCAL.get().getAccount();
    }

    public static boolean hasAnnotate() {
        final ThreadLocalEntity threadLocalEntity = THREAD_LOCAL.get();
        return threadLocalEntity == null ? false : threadLocalEntity.isHasAnnotate();
    }

    protected static void setAnnotate(boolean hasAnnotate) {
        final ThreadLocalEntity threadLocal = getThreadLocal();
        if (threadLocal != null) {
            threadLocal.setHasAnnotate(hasAnnotate);
            threadLocal.setUpdateFlag(true);
        } else {
            setThreadLocal(ThreadLocalEntity.builder()
                    .hasAnnotate(hasAnnotate)
                    .updateFlag(false)
                    .build());
        }
    }

    /**
     * 在线程本地上下文中使用指定的注释标志执行回调。
     *
     * @param hasAnnotate 要在线程本地上下文中设置的注释标志
     * @param <T>         回调返回结果的类型
     * @return 回调执行结果
     * @throws Exception 如果回调执行抛出异常
     * @paramcallback 要执行的回调
     */
    public static <T> T doWithThreadLocal(boolean hasAnnotate, Callable<T> callback) throws Exception {
        try {
            ThreadLocalUtil.setAnnotate(hasAnnotate);
            InheritableThreadLocalUtil.setAnnotate(hasAnnotate);
            TransmittableThreadLocalUtil.setAnnotate(hasAnnotate);
            return callback.call();
        } finally {
            if (!ThreadLocalUtil.getThreadLocal().isUpdateFlag()) {
                ThreadLocalUtil.removeThreadLocal();
            }
            if (!InheritableThreadLocalUtil.getThreadLocal().isUpdateFlag()) {
                InheritableThreadLocalUtil.removeThreadLocal();
            }
            if (!TransmittableThreadLocalUtil.getThreadLocal().isUpdateFlag()) {
                TransmittableThreadLocalUtil.removeThreadLocal();
            }
        }
    }

    public static <T> T doWithThreadLocal(Callable<T> callback) throws Exception {
        return doWithThreadLocal(true, callback);
    }

    public static void doWithThreadLocal(Runnable callback) throws Exception {
        doWithThreadLocal(true, () -> {
            callback.run();
            return null;
        });
    }

    public static <T> T doWithThreadLocal(Collection<String> agis, Callable<T> callback) throws Exception {
        try {
            setGids(agis);
            return callback.call();
        } finally {
            removeThreadLocal();
        }
    }

    /**
     * 在线程局部上下文中执行指定回调操作
     *
     * @param account  当前操作的账户标识
     * @param agis     关联的业务标识集合
     * @param callback 需要执行的业务逻辑回调
     * @return 回调执行结果
     */
    public static <T> T doWithThreadLocal(String account, Collection<String> agis, Callable<T> callback) {
        // 设置线程本地变量并执行回调逻辑
        try {
            setGids(account, agis);
            return callback.call();
        } catch (Exception e) {
            // 处理执行过程中的异常
            log.error("doWithThreadLocal error", e);
        } finally {
            // 确保清理线程本地变量
            removeThreadLocal();
        }
        return null;
    }

}
