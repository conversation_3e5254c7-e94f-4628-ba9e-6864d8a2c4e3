package com.databuff.common.utils.socket;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.io.Serializable;

@Data
public class NotifySockerConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;

    /**
     * api_key
     */
    private String apiKey;

    /**
     * 是否开启，默认1开启，0不开启
     */
    private Integer tenantEnable;
    /**
     * 是否开启，默认1开启，0不开启
     */
    private Integer enable;

    /**
     * dingtalk，wechat，sms，mail，socket
     */
    private String notifyType;

    /**
     * 配置
     * {
     *     "encoding": "UTF-8",
     *     "timeOut": 60000, //milliseconds
     *     "host": "127.0.0.1",
     *     "port": 12345
     * }
     */
    private JSONObject config;

}
