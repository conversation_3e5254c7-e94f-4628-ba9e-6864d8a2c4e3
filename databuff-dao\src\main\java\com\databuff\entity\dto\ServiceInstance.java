package com.databuff.entity.dto;

import com.databuff.entity.ServiceInstanceEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.*;

/**
 * @author:TianMing
 * @date: 2022/4/14
 * @time: 11:07
 */
@Data
public class ServiceInstance {
    @ApiModelProperty("服务名称")
    private String service;
    @ApiModelProperty("服务实例")
    private String serviceInstance;
    @ApiModelProperty("服务id")
    private String serviceId;
    @ApiModelProperty("来源于上级的调用次数")
    private Long srcCall = 1L;
    @ApiModelProperty("总调用次数（包含自己调用自己）")
    private Long call = 1L;
    @ApiModelProperty("主机名称")
    private String hostName;
    @ApiModelProperty("主机ip")
    private String hostIp;
    @ApiModelProperty("总错误次数（包含自己调用自己）")
    private Long error;
    @ApiModelProperty("错误百分比")
    private Double errPct;
    @ApiModelProperty("总响应时间（包含自己调用自己）")
    private Long duration;
    @ApiModelProperty("平均响应时间（包含自己调用自己）")
    private Long avgDuration;
    @ApiModelProperty("该节点最大响应时间")
    private Long maxDuration;
    @ApiModelProperty("该节点最小响应时间")
    private Long minDuration;
    private String k8sClusterId;
    private String k8sSvc;
    private String k8sNamespace;
    private String k8sPodName;
    private String spuid;
    private String pName;
    private String containerId;
    private String containerName;
    private String type;

}
