package com.databuff.dao.mysql;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.databuff.entity.DcDatabuffProblem;
import com.databuff.entity.dto.InfluenceSearch;
import org.apache.ibatis.annotations.*;

import java.util.*;

@Mapper
public interface DatabuffProblemMapper extends BaseMapper<DcDatabuffProblem> {

    @Select({
            "<script>",
            "SELECT EXISTS(SELECT 1 FROM dc_databuff_problem WHERE id = #{id} ",
            "<if test='!allEntityPermission'> ",
            "<if test='gids != null and gids.size() > 0'>AND (gid IN <foreach item='gid' collection='gids' separator=',' open='(' close=')'>#{gid}</foreach>)</if>",
            "<if test='gids == null or gids.size() == 0'>AND 1=0</if>",
            "</if>)",
            "</script>"
    })
    boolean exist(@Param("id") String id, @Param("allEntityPermission") boolean allEntityPermission, @Param("gids") Collection<String> gids);

    @Select({"<script>",
            "SELECT gid, id, problemShowId, problemService, problemServiceType, problemCauseType, problemDesc, problemStartTime, problemEndTime, analyseStartTime, analyseEndTime, influence, influenceServiceCount, influenceAlarmCount, status, isRoot, beginToActionTime,suggestStatus,suggest,feedbackStatus,feedbackMessage ",
            "FROM dc_databuff_problem",
            "WHERE id = #{id}",
            "<if test='!allEntityPermission'> ",
            "  <if test='gids != null and !gids.isEmpty()'>",
            "    AND gid IN <foreach item='gid' collection='gids' separator=',' open='(' close=')'>#{gid}</foreach>",
            "  </if>",
            "  <if test='gids == null or gids.isEmpty()'>",
            "    AND 1=0",
            "  </if>",
            "</if>",
            "<if test='domainManagerStatusOpen'>",
            "  AND gid IS NOT NULL",
            "</if>",
            "<if test='!domainManagerStatusOpen'>",
            "  AND gid IS NULL",
            "</if>",
            "</script>"})
    @Results({
            @Result(property = "influence", column = "influence", javaType = JSONArray.class)
    })
    DcDatabuffProblem findById(@Param("id") String id,
                               @Param("domainManagerStatusOpen") boolean domainManagerStatusOpen,
                               @Param("allEntityPermission") boolean allEntityPermission,
                               @Param("gids") Collection<String> gids);

    @Select({"<script>",
            "SELECT gid, id, problemShowId, problemService, problemServiceType, problemCauseType, problemDesc, problemStartTime, problemEndTime, analyseStartTime, analyseEndTime, influence, influenceServiceCount, influenceAlarmCount, status, isRoot, beginToActionTime,suggestStatus,suggest,feedbackStatus,feedbackMessage ",
            "FROM dc_databuff_problem",
            "WHERE ",
            "id IN <foreach item='item' collection='ids' open='(' separator=',' close=')'>#{item}</foreach>",
            "<if test='!allEntityPermission'> ",
            "  <if test='gids != null and !gids.isEmpty()'>",
            "    AND gid IN <foreach item='gid' collection='gids' separator=',' open='(' close=')'>#{gid}</foreach>",
            "  </if>",
            "  <if test='gids == null or gids.isEmpty()'>",
            "    AND 1=0",
            "  </if>",
            "</if>",
            "<if test='domainManagerStatusOpen'>",
            "  AND gid IS NOT NULL",
            "</if>",
            "<if test='!domainManagerStatusOpen'>",
            "  AND gid IS NULL",
            "</if>",
            "</script>"})
    @Results({
            @Result(property = "influence", column = "influence", javaType = JSONArray.class)
    })
    List<DcDatabuffProblem> findByIds(@Param("ids") Set<String> ids,
                                      @Param("domainManagerStatusOpen") boolean domainManagerStatusOpen,
                                      @Param("allEntityPermission") boolean allEntityPermission,
                                      @Param("gids") Collection<String> gids);

    @Select({
            "<script>",
            "SELECT EXISTS(SELECT 1 FROM dc_databuff_problem WHERE id = #{id} ",
            "<if test='!allEntityPermission'> ",
            "<if test='gids != null and gids.size() > 0'>AND (gid IN <foreach item='gid' collection='gids' separator=',' open='(' close=')'>#{gid}</foreach>)</if>",
            "<if test='gids == null or gids.size() == 0'>AND 1=0</if>",
            "</if> FOR UPDATE)",
            "</script>"
    })
    boolean lockById(@Param("id") String id, @Param("allEntityPermission") boolean allEntityPermission, @Param("gids") Collection<String> gids);

    @Update({
            "<script>",
            "UPDATE dc_databuff_problem",
            "<set>",
            "<if test='problemService != null'>problemService = #{problemService},</if>",
            "<if test='problemServiceType != null'>problemServiceType = #{problemServiceType},</if>",
            "<if test='problemCauseType != null'>problemCauseType = #{problemCauseType},</if>",
            "<if test='problemDesc != null'>problemDesc = #{problemDesc},</if>",
            "<if test='problemStartTime != null'>problemStartTime = #{problemStartTime},</if>",
            "<if test='problemEndTime != null'>problemEndTime = #{problemEndTime},</if>",
            "<if test='analyseStartTime != null'>analyseStartTime = #{analyseStartTime},</if>",
            "<if test='analyseEndTime != null'>analyseEndTime = #{analyseEndTime},</if>",
            "<if test='influence != null'>influence = #{influence},</if>",
            "<if test='influenceServiceCount != null'>influenceServiceCount = #{influenceServiceCount},</if>",
            "<if test='influenceAlarmCount != null'>influenceAlarmCount = #{influenceAlarmCount},</if>",
            "<if test='status != null'>status = #{status},</if>",
            "</set>",
            "WHERE id = #{id}",
            "</script>"
    })
    int update(DcDatabuffProblem dcDatabuffProblem);


    @Insert("INSERT INTO dc_databuff_problem (id, problemShowId, problemService, problemServiceType, problemStartTime, problemEndTime, analyseStartTime, analyseEndTime, influence, influenceServiceCount, influenceAlarmCount, status, isRoot, gid) " +
            "VALUES(#{id}, #{problemShowId}, #{problemService}, #{problemServiceType}, #{problemStartTime}, #{problemEndTime}, #{analyseStartTime}, #{analyseEndTime}, #{influence}, #{influenceServiceCount}, #{influenceAlarmCount}, #{status}, #{isRoot}, #{gid})")
    int insert(DcDatabuffProblem dcDatabuffProblem);

    @Select({
            "<script>",
            "SELECT d.problemCauseType as rootCauseType, COUNT(*) as cnt",
            "FROM dc_databuff_problem d",
            "<where>",
            "<if test='!allEntityPermission'> ",
            "<if test='gids != null and gids.size() > 0'>(d.gid IN <foreach item='gid' collection='gids' separator=',' open='(' close=')'>#{gid}</foreach>) AND </if>",
            "<if test='gids == null or gids.size() == 0'>(1=0) AND </if>",
            "</if>",
            "<if test='ew != null'>",
            "${ew.sqlSegment}",
            "</if>",
            "</where>",
            "GROUP BY d.problemCauseType",
            "ORDER BY cnt DESC",
            "LIMIT #{topN}",
            "</script>"
    })
    List<Map<String, Object>> chartProblemByProblemCauseType(@Param("ew") QueryWrapper<DcDatabuffProblem> queryWrapper, @Param("topN") Integer topN, @Param("allEntityPermission") boolean allEntityPermission, @Param("gids") Collection<String> gids);


    @Select({
            "<script>",
            "SELECT d.problemService as rootCauseNode, COUNT(*) as cnt",
            "FROM dc_databuff_problem d",
            "<where>",
            "<if test='!allEntityPermission'> ",
            "<if test='gids != null and gids.size() > 0'>(d.gid IN <foreach item='gid' collection='gids' separator=',' open='(' close=')'>#{gid}</foreach>) AND </if>",
            "<if test='gids == null or gids.size() == 0'>(1=0) AND </if>",
            "</if>",
            "<if test='ew != null'>",
            "${ew.sqlSegment}",
            "</if>",
            "</where>",
            "GROUP BY d.problemService",
            "ORDER BY cnt DESC",
            "LIMIT #{topN}",
            "</script>"
    })
    List<Map<String, Object>> chartProblemByProblemCauseNode(@Param("ew") QueryWrapper<DcDatabuffProblem> queryWrapper, @Param("topN") Integer topN, @Param("allEntityPermission") boolean allEntityPermission, @Param("gids") Collection<String> gids);


    @Select({
            "<script>",
            "SELECT d.gid,id,problemShowId,problemService,problemServiceType,problemCauseType,problemDesc,problemStartTime,problemEndTime,analyseStartTime,analyseEndTime,influenceServiceCount,influenceAlarmCount,status,isRoot,beginToActionTime,",
            "UNIX_TIMESTAMP(COALESCE(beginToActionTime, problemEndTime)) as beginToActionTimeFill,",
            "UNIX_TIMESTAMP(problemEndTime) - UNIX_TIMESTAMP(problemStartTime) as mttr,",
            "UNIX_TIMESTAMP(COALESCE(beginToActionTime, problemEndTime)) - UNIX_TIMESTAMP(problemStartTime) as mtta ",
            "FROM dc_databuff_problem d",
            "<where>",
            "<if test='!allEntityPermission'> ",
            "<if test='gids != null and gids.size() > 0'>(d.gid IN <foreach item='gid' collection='gids' separator=',' open='(' close=')'>#{gid}</foreach>) AND </if>",
            "<if test='gids == null or gids.size() == 0'>(1=0) AND </if>",
            "</if>",
            "<if test='ew != null'>",
            "${ew.sqlSegment}",
            "</if>",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(property = "influence", column = "influence", javaType = JSONArray.class)
    })
    List<DcDatabuffProblem> searchInfluence(@Param("ew") QueryWrapper<DcDatabuffProblem> queryWrapper, @Param("allEntityPermission") boolean allEntityPermission, @Param("gids") Collection<String> gids);


    @Select({
            "<script>",
            "SELECT ${ew.sqlSelect}",
            "FROM dc_databuff_problem d",
            "<where>",
            "<if test='!allEntityPermission'> ",
            "<if test='gids != null and gids.size() > 0'>(d.gid IN <foreach item='gid' collection='gids' separator=',' open='(' close=')'>#{gid}</foreach>) AND </if>",
            "<if test='gids == null or gids.size() == 0'>(1=0) AND </if>",
            "</if>",
            "<if test='ew != null'>",
            "${ew.sqlSegment}",
            "</if>",
            "</where>",
            "</script>"
    })
    List<Object> selectObjs(@Param("ew") QueryWrapper<DcDatabuffProblem> queryWrapper, @Param("allEntityPermission") boolean allEntityPermission, @Param("gids") Collection<String> gids);


    @Select({
            "<script>",
            "SELECT ${ew.sqlSelect}",
            "FROM dc_databuff_problem d USE INDEX (idx_problem_filter)",
            "<where>",
            "<if test='!allEntityPermission'> ",
            "<if test='gids != null and gids.size() > 0'>(d.gid IN <foreach item='gid' collection='gids' separator=',' open='(' close=')'>#{gid}</foreach>) AND </if>",
            "<if test='gids == null or gids.size() == 0'>(1=0) AND </if>",
            "</if>",
            "<if test='ew != null'>",
            "${ew.sqlSegment}",
            "</if>",
            "</where>",
            "</script>"
    })
    List<String> selectPmTypeAndCase(@Param("ew") QueryWrapper<DcDatabuffProblem> queryWrapper, @Param("allEntityPermission") boolean allEntityPermission, @Param("gids") Collection<String> gids);

    @Select({
            "<script>",
            "select count(*) from dc_databuff_problem WHERE problemStartTime is not null and influence is not null and isRoot = 1 and problemStartTime &lt; #{criteria.toTime} and problemEndTime >= #{criteria.fromTime}",
            "<if test='!allEntityPermission'> ",
            "<if test='gids != null and gids.size() > 0'>AND (gid IN <foreach item='gid' collection='gids' separator=',' open='(' close=')'>#{gid}</foreach>)</if>",
            "<if test='gids == null or gids.size() == 0'>AND 1=0</if>",
            "</if>",
            "</script>"
    })
    int countInfluence(@Param("criteria") InfluenceSearch criteria, @Param("allEntityPermission") boolean allEntityPermission, @Param("gids") Collection<String> gids);


    @Select({
            "<script>",
            "select max(mttr)/60 as max, min(mttr)/60 as min, avg(mttr)/60 as avg from (",
            "SELECT UNIX_TIMESTAMP(problemEndTime) - UNIX_TIMESTAMP(problemStartTime) + 60 as mttr ",
            "from dc_databuff_problem ",
            "where problemStartTime is not null and influence is not null and isRoot = 1 ",
            "and problemStartTime &lt; #{search.toTime} and problemEndTime &gt;= #{search.fromTime}",
            "<if test='!allEntityPermission'> ",
            "<if test='gids != null and gids.size() > 0'>AND (gid IN <foreach item='gid' collection='gids' separator=',' open='(' close=')'>#{gid}</foreach>)</if>",
            "<if test='gids == null or gids.size() == 0'>AND 1=0</if>",
            "</if>",
            ") as d",
            "</script>"
    })
    List<Map<String, Object>> queryMTTRSummary(@Param("search") InfluenceSearch search, @Param("allEntityPermission") boolean allEntityPermission, @Param("gids") Collection<String> gids);

    @Select({
            "<script>",
            "select max(mtta)/60 as max, min(mtta)/60 as min, avg(mtta)/60 as avg from (",
            "SELECT UNIX_TIMESTAMP(COALESCE(beginToActionTime, problemEndTime)) - UNIX_TIMESTAMP(problemStartTime) + 60 as mtta ",
            "from dc_databuff_problem ",
            "where problemStartTime is not null and influence is not null and isRoot = 1 ",
            "and problemStartTime &lt; #{search.toTime} and problemEndTime &gt;= #{search.fromTime}",
            "<if test='!allEntityPermission'> ",
            "<if test='gids != null and gids.size() > 0'>AND (gid IN <foreach item='gid' collection='gids' separator=',' open='(' close=')'>#{gid}</foreach>)</if>",
            "<if test='gids == null or gids.size() == 0'>AND 1=0</if>",
            "</if>",
            ") as d",
            "</script>"
    })
    List<Map<String, Object>> queryMTTASummary(@Param("search") InfluenceSearch search, @Param("allEntityPermission") boolean allEntityPermission, @Param("gids") Collection<String> gids);


    @Select({
            "<script>",
            "select avg(mttr) as avg, UNIX_TIMESTAMP(problemStartTime) DIV #{search.interval} * #{search.interval} as bucket from (",
            "SELECT UNIX_TIMESTAMP(problemEndTime) - UNIX_TIMESTAMP(problemStartTime) + 60 as mttr, problemStartTime ",
            "from dc_databuff_problem ",
            "where problemStartTime is not null and influence is not null and isRoot = 1 ",
            "and problemStartTime &lt; #{search.toTime} and problemEndTime &gt;= #{search.fromTime}",
            "<if test='!allEntityPermission'> ",
            "<if test='gids != null and gids.size() > 0'>AND (gid IN <foreach item='gid' collection='gids' separator=',' open='(' close=')'>#{gid}</foreach>)</if>",
            "<if test='gids == null or gids.size() == 0'>AND 1=0</if>",
            "</if>",
            ") as d ",
            "group by bucket ",
            "order by bucket",
            "</script>"
    })
    List<Map<String, Object>> queryMTTRList(@Param("search") InfluenceSearch search, @Param("allEntityPermission") boolean allEntityPermission, @Param("gids") Collection<String> gids);

    @Select({
            "<script>",
            "select avg(mtta) as avg, UNIX_TIMESTAMP(problemStartTime) DIV #{search.interval} * #{search.interval} as bucket from (",
            "SELECT UNIX_TIMESTAMP(COALESCE(beginToActionTime, problemEndTime)) - UNIX_TIMESTAMP(problemStartTime) + 60 as mtta, problemStartTime ",
            "from dc_databuff_problem ",
            "where problemStartTime is not null and influence is not null and isRoot = 1 ",
            "and problemStartTime &lt; #{search.toTime} and problemEndTime &gt;= #{search.fromTime}",
            "<if test='!allEntityPermission'> ",
            "<if test='gids != null and gids.size() > 0'>AND (gid IN <foreach item='gid' collection='gids' separator=',' open='(' close=')'>#{gid}</foreach>)</if>",
            "<if test='gids == null or gids.size() == 0'>AND 1=0</if>",
            "</if>",
            ") as d ",
            "group by bucket ",
            "order by bucket",
            "</script>"
    })
    List<Map<String, Object>> queryMTTAList(@Param("search") InfluenceSearch search, @Param("allEntityPermission") boolean allEntityPermission, @Param("gids") Collection<String> gids);

    @Update("update dc_databuff_problem set beginToActionTime = NOW() WHERE id = #{id} and (beginToActionTime IS NULL OR beginToActionTime < NOW())")
    void updateBeginToActionTime(@Param("id") String id);

    @Select({
            "<script>",
            "select UNIX_TIMESTAMP(problemStartTime) as problemStartTime, UNIX_TIMESTAMP(problemEndTime) as problemEndTime",
            "from dc_databuff_problem d",
            "where problemStartTime is not null and influence_exists = 1",
            "<if test='!allEntityPermission'> ",
            "<if test='gids != null and gids.size() > 0'>AND (gid IN <foreach item='gid' collection='gids' separator=',' open='(' close=')'>#{gid}</foreach>)</if>",
            "<if test='gids == null or gids.size() == 0'>AND 1=0</if>",
            "</if>",
            "<if test='ew != null'>",
            "AND ${ew.sqlSegment}",
            "</if>",
            "</script>"
    })
    List<Map<String, Object>> metric(@Param("ew") QueryWrapper<DcDatabuffProblem> queryWrapper, @Param("allEntityPermission") boolean allEntityPermission, @Param("gids") Collection<String> gids);

    @Update("update dc_databuff_problem set suggestStatus=1 WHERE id = #{problemId}")
    void beginSuggestForRoot(String problemId);

    @Update("update dc_databuff_problem set suggestStatus=3,suggest=#{suggest} WHERE id = #{problemId}")
    void suggestForRootFailed(@Param("problemId") String problemId, @Param("suggest") String suggest);

    @Update("update dc_databuff_problem set suggestStatus=2,suggest=#{suggest} WHERE id = #{problemId}")
    void suggestForRootSuccess(@Param("problemId") String problemId, @Param("suggest") String suggest);

    @Insert("insert into dc_databuff_problem_service(problemId,serviceId,analyseEndTime,caseTypes,problemEndTime,problemStartTime) values(#{problemId},#{serviceId},#{analyseEndTime},#{caseTypes},#{problemEndTime},#{problemStartTime})")
    void addProblemServiceRelation(@Param("problemId") String problemId, @Param("serviceId") String serviceId, @Param("analyseEndTime") Date analyseEndTime, @Param("caseTypes") String caseTypes, @Param("problemEndTime") Date problemEndTime, @Param("problemStartTime") Date problemStartTime);

    @Select("SELECT p.* \n" +
            "FROM dc_databuff_problem p\n" +
            "JOIN (\n" +
            "    SELECT DISTINCT problemId \n" +
            "    FROM dc_databuff_problem_service \n" +
            "    WHERE serviceId =  #{serviceId}\n" +
            "      AND problemEndTime >= #{fromTime} \n" +
            "      AND problemStartTime <= #{toTime} \n" +
            "    LIMIT 50\n" +
            ") AS tmp ON p.id = tmp.problemId")
    List<DcDatabuffProblem> queryProblemWithService(@Param("serviceId") String serviceId, @Param("fromTime") Date fromTime, @Param("toTime") Date toTime);

    @Select("select caseTypes,analyseEndTime,problemId from dc_databuff_problem_service where serviceId=#{serviceId} and problemEndTime >= #{fromTime} and problemStartTime <= #{toTime}")
    List<Map<String, Object>> queryProblemCaseTypesWithService(@Param("serviceId") String serviceId, @Param("fromTime") Date fromTime, @Param("toTime") Date toTime);

    @Update("update dc_databuff_problem set feedbackStatus=#{status},feedbackMessage=#{message} where id=#{problemId}")
    void feedback(@Param("problemId") String problemId, @Param("status") String status, @Param("message") String message);
}
