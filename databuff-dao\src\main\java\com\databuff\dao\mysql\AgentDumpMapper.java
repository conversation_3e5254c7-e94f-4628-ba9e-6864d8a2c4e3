package com.databuff.dao.mysql;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.databuff.dao.mysql.provider.AgentDumpSqlProvider;
import com.databuff.entity.dump.AgentDump;
import com.databuff.entity.dump.AgentDumpSearchCriteria;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @package com.databuff.dao.mysql
 * @className: AgentDumpMapper
 * @author: yuzhili
 * @createDate: 2025/01/15
 */
@Mapper
@Repository
public interface AgentDumpMapper extends BaseMapper<AgentDump> {

    /**
     * 插入AgentDump记录
     *
     * @param agentDump AgentDump对象
     */
    @Insert("INSERT INTO dc_agent_dump (service_id, service, service_instance, host, create_time, api_key, status, operation, progress, account, gid) " +
            "VALUES (#{serviceId}, #{service}, #{serviceInstance}, #{host}, #{createTime}, #{apiKey}, #{status}, #{operation}, #{progress}, #{account}, #{gid})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(AgentDump agentDump);

    @UpdateProvider(type = AgentDumpSqlProvider.class, method = "updateByIdSelective")
    int updateByIdSelective(@Param("agentDump") AgentDump agentDump);

    /**
     * 根据ID查询AgentDump记录
     *
     * @param id 主键ID
     * @return AgentDump记录
     */
    @Select("SELECT * FROM dc_agent_dump WHERE id = #{id}")
    AgentDump selectById(@Param("id") Long id);

    /**
     * 根据搜索条件查询AgentDump记录
     *
     * @param criteria 搜索条件
     * @return 符合条件的AgentDump记录列表
     */
    @SelectProvider(type = AgentDumpSqlProvider.class, method = "searchAgentDumps")
    List<AgentDump> searchAgentDumps(@Param("criteria") AgentDumpSearchCriteria criteria);

    /**
     * 插入或更新AgentDump记录
     *
     * @param agentDump AgentDump对象
     */
    @Update("INSERT INTO dc_agent_dump (id, file_name, file_size, path, service, service_instance, is_new, upload_time, create_time, update_time, api_key, status, account, gid) " +
            "VALUES (#{id}, #{fileName}, #{fileSize}, #{path}, #{service}, #{serviceInstance}, #{isNew}, #{uploadTime}, #{createTime}, #{updateTime}, #{apiKey}, #{status}, #{account}, #{gid}) " +
            "ON DUPLICATE KEY UPDATE " +
            "file_name = VALUES(file_name), " +
            "file_size = VALUES(file_size), " +
            "path = VALUES(path), " +
            "service = VALUES(service), " +
            "service_instance = VALUES(service_instance), " +
            "is_new = VALUES(is_new), " +
            "upload_time = VALUES(upload_time), " +
            "api_key = VALUES(api_key), " +
            "status = VALUES(status), " +
            "gid = VALUES(gid)")
    void upsert(AgentDump agentDump);

    @SelectProvider(type = AgentDumpSqlProvider.class, method = "getNextTask")
    AgentDump getNextTask(@Param("host") String host);
}