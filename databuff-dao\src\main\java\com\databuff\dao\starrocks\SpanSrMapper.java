package com.databuff.dao.starrocks;

import com.databuff.entity.TraceSearch;
import com.databuff.entity.dto.*;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Mapper
@Repository
public interface SpanSrMapper extends PartitionCleanupMapper {

    /**
     * 根据条件统计 Span 数量
     *
     * @param search 查询条件
     * @return Span 总数
     */
    Long spanCount(TraceSearch search);

    /**
     * 根据 Trace ID 统计 Span 数量 (按小时分区查询)
     *
     * @param hours   小时分区集合 (格式 yyyyMMddHH)
     * @param traceId Trace ID
     * @return Span 总数
     */
    Long spanCountByTraceId(@Param("hours") Set<Long> hours, @Param("traceId") String traceId);

    /**
     * 根据条件分页查询 Span 列表
     *
     * @param search 查询条件 (包含分页、排序信息)
     * @return SrSpan DTO 列表
     */
    List<SrSpan> spanSearch(TraceSearch search);

    /**
     * 从 dc_span 表查询 Span ID 和分钟列表 (通常用于查找 Trace 对应的分钟分区)
     *
     * @param search 查询条件 (主要使用 traceId, hours)
     * @return SrSpan DTO 列表 (只包含 span_id, minutes, startTime, is_parent)
     */
    List<SrSpan> spanIdListSearchFromDcSpan(TraceSearch search);

    /**
     * 从 dc_span 表根据 Trace ID 获取其涉及的所有分钟分区
     *
     * @param search 查询条件 (主要使用 traceId, hours)
     * @return 分钟分区 (yyyyMMddHHmm) 集合
     */
    Set<Long> getMinutesByTraceIdFromDcTrace(TraceSearch search);

    /**
     * 计算符合条件的 Span 的整体延迟百分位
     *
     * @param search 查询条件
     * @return 包含 P50 到 P100 延迟的对象
     */
    PercentageLatency percentageAllLatency(TraceSearch search);

    @MapKey("ts")
    Map<Long, Map<String, Object>> getTracePercentilesByTime(TraceSearch search);

    /**
     * 按时间桶聚合计算 Span 总数
     *
     * @param search 查询条件 (包含 timeBucket 表达式)
     * @return 包含时间戳和计数值的列表
     */
    List<TimeValue> countByTime(TraceSearch search);

    /**
     * 按时间桶和状态码聚合计算错误 Span 数量
     *
     * @param search 查询条件 (包含 timeBucket 表达式)
     * @return 包含时间戳、状态码(tag1)、计数值的列表
     */
    List<TimeValue> errorCountByTime(TraceSearch search);

    /**
     * 按时间桶和状态码聚合计算慢 Span 数量
     *
     * @param search 查询条件 (包含 timeBucket 表达式, 需要 slow=1 条件)
     * @return 包含时间戳、状态码(tag1)、计数值的列表
     */
    List<TimeValue> slowCountByTime(TraceSearch search);

    /**
     * 根据 Span ID 列表查询 Span 详细信息 (按分钟分区查询)
     *
     * @param minutes   分钟分区集合
     * @param isParent  是否根 Span
     * @param spanIds   要查询的 Span ID 列表
     * @param sortField 排序字段
     * @param sortOrder 排序顺序
     * @param fromTime  开始时间 (用于进一步过滤)
     * @param toTime    结束时间 (用于进一步过滤)
     * @return SrSpan DTO 列表
     */
    List<SrSpan> spanListSearchByIds(@Param("minutes") Set<Long> minutes, @Param("serviceIds") Set<String> serviceIds, @Param("isParent") Integer isParent, @Param("spanIds") List<String> spanIds, @Param("sortField") String sortField, @Param("sortOrder") String sortOrder, @Param("fromTime") String fromTime, @Param("toTime") String toTime);

    /**
     * 根据 Trace ID 查询所有 Span 详细信息 (按分钟分区查询)
     *
     * @param minutes 分钟分区集合
     * @param traceId Trace ID
     * @return SrSpan DTO 列表
     */
    List<SrSpan> spanListSearchByTraceId(@Param("minutes") Set<Long> minutes, @Param("traceId") String traceId);

    /**
     * 根据自定义标签聚合统计请求量 (按分钟) - 查询 dc_data_collect 表
     *
     * @param hashcode        标签 hashcode (如果需要分区)
     * @param tag             自定义标签 key=value
     * @param traceId         可选 Trace ID 过滤
     * @param resource        可选资源过滤
     * @param serviceId       可选服务 ID 过滤
     * @param serviceInstance 可选服务实例过滤
     * @param hours           小时分区集合
     * @param minMinute       最小分钟分区
     * @param maxMinute       最大分钟分区
     * @return 包含分钟和计数的列表
     */
    List<CountGraph> cntGraphStatsForTag(@Param("hashcode") Integer hashcode, @Param("tag") String tag, @Param("traceId") String traceId, @Param("resource") String resource, @Param("serviceId") String serviceId, @Param("serviceInstance") String serviceInstance, @Param("hours") Set<Long> hours, @Param("minMinute") Long minMinute, @Param("maxMinute") Long maxMinute, @Param("envTag1s") List<String> envTag1s, @Param("envTag2s") List<String> envTag2s);

    /**
     * 根据自定义标签聚合统计错误数量 (按分钟和状态码) - 查询 dc_data_collect 表
     *
     * @param hashcode        标签 hashcode
     * @param tag             自定义标签 key=value
     * @param traceId         可选 Trace ID 过滤
     * @param resource        可选资源过滤
     * @param serviceId       可选服务 ID 过滤
     * @param serviceInstance 可选服务实例过滤
     * @param hours           小时分区集合
     * @param minMinute       最小分钟分区
     * @param maxMinute       最大分钟分区
     * @return 包含分钟、状态码、计数的列表
     */
    List<CountGraph> errorCntGaphStatsForTag(@Param("hashcode") Integer hashcode, @Param("tag") String tag, @Param("traceId") String traceId, @Param("resource") String resource, @Param("serviceId") String serviceId, @Param("serviceInstance") String serviceInstance, @Param("hours") Set<Long> hours, @Param("minMinute") Long minMinute, @Param("maxMinute") Long maxMinute, @Param("envTag1s") List<String> envTag1s, @Param("envTag2s") List<String> envTag2s);

    /**
     * 根据自定义标签聚合统计慢请求数量 (按分钟和状态码) - 查询 dc_data_collect 表
     *
     * @param hashcode        标签 hashcode
     * @param tag             自定义标签 key=value
     * @param traceId         可选 Trace ID 过滤
     * @param resource        可选资源过滤
     * @param serviceId       可选服务 ID 过滤
     * @param serviceInstance 可选服务实例过滤
     * @param hours           小时分区集合
     * @param minMinute       最小分钟分区
     * @param maxMinute       最大分钟分区
     * @return 包含分钟、状态码、计数的列表
     */
    List<CountGraph> slowCntGaphStatsForTag(@Param("hashcode") Integer hashcode, @Param("tag") String tag, @Param("traceId") String traceId, @Param("resource") String resource, @Param("serviceId") String serviceId, @Param("serviceInstance") String serviceInstance, @Param("hours") Set<Long> hours, @Param("minMinute") Long minMinute, @Param("maxMinute") Long maxMinute, @Param("envTag1s") List<String> envTag1s, @Param("envTag2s") List<String> envTag2s);

    /**
     * 根据自定义标签分页查询 Span ID 和分钟 - 查询 dc_data_collect 表
     *
     * @param hashcode        标签 hashcode
     * @param tag             自定义标签 key=value
     * @param traceId         可选 Trace ID 过滤
     * @param resource        可选资源过滤
     * @param serviceId       可选服务 ID 过滤
     * @param serviceInstance 可选服务实例过滤
     * @param hours           小时分区集合
     * @param minutes         分钟分区集合
     * @param error           可选错误过滤 (1 或 0)
     * @param offset          分页偏移量
     * @param size            分页大小
     * @param sortField       排序字段
     * @param sortOrder       排序顺序
     * @return SrSpan DTO 列表 (只包含 span_id, minutes)
     */
    List<SrSpan> spanIdListSearchFromDcSpanForTag(@Param("hashcode") Integer hashcode, @Param("tag") String tag, @Param("traceId") String traceId, @Param("resource") String resource, @Param("serviceId") String serviceId, @Param("serviceInstance") String serviceInstance, @Param("hours") Set<Long> hours, @Param("minutes") Set<Long> minutes, @Param("error") Integer error, @Param("offset") Integer offset, @Param("size") Integer size, @Param("sortField") String sortField, @Param("sortOrder") String sortOrder);

    /**
     * 根据自定义标签统计 Span 数量 - 查询 dc_data_collect 表
     *
     * @param hashcode        标签 hashcode
     * @param tag             自定义标签 key=value
     * @param traceId         可选 Trace ID 过滤
     * @param resource        可选资源过滤 (模糊匹配)
     * @param serviceId       可选服务 ID 过滤
     * @param serviceInstance 可选服务实例过滤
     * @param hours           小时分区集合
     * @param minutes         分钟分区集合
     * @param error           可选错误过滤
     * @return Span 总数
     */
    Long countSpanIdListSearchFromDcSpanForTag(@Param("hashcode") Integer hashcode, @Param("tag") String tag, @Param("traceId") String traceId, @Param("resource") String resource, @Param("serviceId") String serviceId, @Param("serviceInstance") String serviceInstance, @Param("hours") Set<Long> hours, @Param("minutes") Set<Long> minutes, @Param("error") Integer error, @Param("envTag1s") List<String> envTag1s, @Param("envTag2s") List<String> envTag2s);

    /**
     * 根据自定义标签按分钟聚合计算延迟百分位 - 查询 dc_data_collect 表
     *
     * @param hashcode        标签 hashcode
     * @param tag             自定义标签 key=value
     * @param traceId         可选 Trace ID 过滤
     * @param resource        可选资源过滤
     * @param serviceId       可选服务 ID 过滤
     * @param serviceInstance 可选服务实例过滤
     * @param hours           小时分区集合
     * @param minMinute       最小分钟分区
     * @param maxMinute       最大分钟分区
     * @return 包含分钟和各百分位延迟的对象列表
     */
    List<percentileApprox> graphStatsForTag(@Param("hashcode") Integer hashcode, @Param("tag") String tag, @Param("traceId") String traceId, @Param("resource") String resource, @Param("serviceId") String serviceId, @Param("serviceInstance") String serviceInstance, @Param("hours") Set<Long> hours, @Param("minMinute") Long minMinute, @Param("maxMinute") Long maxMinute);

    /**
     * 根据条件和小时分区查找单个样本 Span。
     * 实现应使用 LIMIT 1 并根据提供的小时分区进行过滤。
     *
     * @param search 包含 apiKey、serviceId (可选)、resource (可选) 以及由服务层设置的 hours (Set<Long>)
     * @return 单个 SrSpan 对象；如果在指定的小时分区和条件下未找到，则返回 null。
     */
    SrSpan findSampleSpan(TraceSearch search);
}
