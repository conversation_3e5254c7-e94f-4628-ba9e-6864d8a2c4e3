package com.databuff.entity.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * @description 对应表dc_databuff_plugin_dashboard_dic 
 * <AUTHOR>
 * @date 2022-01-13 
 */
public class DatabuffPluginDashboardDic implements Serializable {
	private static final long serialVersionUID =  1561735862722281844L;

	/**
	 * 对应表字段 id
	 */
	private Long id;

	/**
	 * 插件字典id；对应表字段 plugin_dic_id
	 */
	private Long pluginDicId;

	/**
	 * 仪表盘名称可选；对应表字段 dashboard_name
	 */
	private String dashboardName;

	/**
	 * 仪表盘tag是否存在；对应表字段 plugin_collector
	 */
	private String pluginCollector;

	/**
	 * 插件标签 以逗号分隔；对应表字段 plugin_tags
	 */
	private String pluginTags;

	/**
	 * 预置仪表盘；对应表字段 plugin_dashboard
	 */
	private String pluginDashboard;

	/**
	 * 创建时间；对应表字段 create_time
	 */
	private Date createTime;

	/**
	 * 修改时间；对应表字段 update_time
	 */
	private Date updateTime;

	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getPluginId() {
		return this.pluginDicId;
	}

	public void setPluginId(Long pluginId) {
		this.pluginDicId = pluginId;
	}

	public String getDashboardName() {
		return this.dashboardName;
	}

	public void setDashboardName(String dashboardName) {
		this.dashboardName = dashboardName;
	}

	public String getPluginCollector() {
		return this.pluginCollector;
	}

	public void setPluginCollector(String pluginCollector) {
		this.pluginCollector = pluginCollector;
	}

	public String getPluginTags() {
		return this.pluginTags;
	}

	public void setPluginTags(String pluginTags) {
		this.pluginTags = pluginTags;
	}

	public String getPluginDashboard() {
		return this.pluginDashboard;
	}

	public void setPluginDashboard(String pluginDashboard) {
		this.pluginDashboard = pluginDashboard;
	}

	public Date getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return this.updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

}
