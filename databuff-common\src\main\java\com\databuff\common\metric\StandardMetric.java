package com.databuff.common.metric;

import com.databuff.moredb.proto.Common;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.HashMap;


/**
 * 标准化时序指标对象 (Standardized Time-Series Metric Object)。
 * <p>
 * **核心作用:**
 * 作为平台内指标生产者（例如 DTS 服务进行 Span 丰富化处理后）与下游消费者（如通用的 Flink 指标写入 Job）
 * 之间的数据传输契约 (Data Transfer Object - DTO)。它封装了一个逻辑上的时序数据点所需的所有信息，
 * 包括目标存储、维度标签、度量值、事件时间戳以及（关键的）指导后续如何聚合这些度量值的提示。
 * </p>
 * <p>
 * **数据流:**<p>
 * 1. 指标生产者 (DTS) 根据业务逻辑处理原始数据 (如 DCSpan)，为需要记录的指标创建 `StandardMetric` 实例列表。<p>
 * 2. DTS 将 `StandardMetric` 对象列表序列化 (通常为 JSON) 发送到指定的 Kafka Topic。<p>
 * 3. 通用 Flink 指标写入 Job 从 Kafka 消费这些 JSON 数据，反序列化为 `StandardMetric` 对象。<p>
 * 4. Flink Job 根据 `measurement` 和 `tags` 进行分组 (`keyBy`)。<p>
 * 5. Flink Job 应用时间窗口 (例如 1 分钟)。<p>
 * 6. Flink Job 在窗口触发时，根据 `flinkAggregationHints` 对窗口内的 `fields` 值执行聚合计算。<p>
 * 7. Flink Job 将聚合结果封装成 `TSDBPoint` 对象（包含最终的聚合值和对应的 `Common.FieldType`）。<p>
 * 8. Flink Job 调用 `TSDBOperateUtil` 将 `TSDBPoint` 写入目标 MoreDB。
 * </p>
 * <p>
 * **使用要点:**
 * <ul>
 * <li>生产者需要正确填充所有字段，特别是 `database`, `measurement`, `tags`, `fields`, `timestamp`。</li>
 * <li>`timestamp` 必须是指标事件发生的**原始时间戳 (毫秒)**。</li>
 * <li>`tags` 用于索引和分组，应避免高基数值。</li>
 * <li>`fields` 存储本次事件对应的瞬时值或增量值 (如计数器放入 `1L`)。</li>
 * <li>`flinkAggregationHints` **告知 Flink 如何聚合字段**。此 Map **可以不包含 `fields` 中的所有 key**。
 * 如果某个 Field Key 在此 Map 中不存在，Flink **默认会按 SUM 方式聚合**该字段。</li>
 * <li>`fieldTypes` 字段通常由 Flink 在聚合后生成，**生产者无需关心或填充此字段**。</li>
 * </ul>
 *
 * @see FlinkAggregationType Flink 聚合类型枚举
 * @see com.databuff.common.tsdb.model.TSDBPoint 最终写入 TSDB 的对象
 * @see com.databuff.moredb.proto.Common.FieldType TSDB 字段类型枚举
 */
@Data // Lombok 注解，自动生成 getter/setter/toString/equals/hashCode 等
@NoArgsConstructor // Lombok 注解，生成无参构造函数
@AllArgsConstructor // Lombok 注解，生成全参构造函数
@Slf4j
public class StandardMetric {

    /**
     * 目标 TSDB (如 MoreDB) 的数据库名称。
     * Flink Sink 会根据此字段将数据写入对应的 Database。
     * 必须由生产者指定。
     * 示例: "turing", "user_profile_metrics"
     */
    private String database;

    /**
     * 目标 TSDB Measurement (类似表名) 的名称。
     * 用于在 Database 内组织相关的时间序列数据。
     * 必须由生产者指定。
     * 示例: "biz_event", "biz_event_kpi", "api_calls"
     */
    private String measurement;

    /**
     * 维度标签集合 (Map<String, String>)。
     * 用于索引、过滤和分组 (GROUP BY) 数据。
     * Tag Key 和 Tag Value 对于一个唯一的时间序列至关重要。
     * **注意:** Tag Value 不应具有过高的基数（例如，用户 ID、Trace ID 不适合做 Tag）。
     * Tag Key 和 Value 通常不允许为 null 或空。
     * 示例: {"host": "server-1", "region": "us-east", "api": "/user/login", "status_code": "200"}
     */
    private Map<String, String> tags = new HashMap<>();

    /**
     * 指标字段集合 (Map<String, Object>)。
     * 存储本次事件对应的具体度量值或状态值。
     * Key 是字段名 (Field Key)。
     * Value 是字段值，可以是 Long, Double, String, Boolean 等 Java 对象。
     * **重要:** 对于需要数值聚合 (SUM, AVG, MIN, MAX) 的字段，Value 应该是 Number 类型或可解析为 Number 的 String。
     * 对于计数器类型的字段 (如调用次数 cnt, 错误次数 error)，通常放入 Long 类型的值 `1L` 作为增量。
     * 对于状态或类别字段 (用于 FIRST/LAST)，Value 可以是 String 或其他 Object。
     * 示例: {"cnt": 1L, "latency": 123.45, "status": "SUCCESS", "orderAmount": 99.9}
     */
    private Map<String, Object> fields = new HashMap<>();

    /**
     * (已废弃或 Flink 内部使用) TSDB 字段类型提示 (Map<String, Common.FieldType>)。
     * 这个字段**通常不由生产者 (DTS) 设置**。
     * 通用 Flink 任务会根据 `flinkAggregationHints` 和最终聚合结果的类型来推断并填充到 `TSDBPoint` 中。
     * 预留此字段可能用于某些特殊场景，但标准流程下应为空 Map。
     * 示例: {"cnt": "SUM", "latency": "GAUGE", "status": "GAUGE"} (使用 Common.FieldType 枚举)
     *
     * @deprecated 推荐由 Flink 根据 flinkAggregationHints 推断最终 TSDBPoint 的 fieldTypes。
     */
    @Deprecated
    private Map<String, Common.FieldType> fieldTypes = new HashMap<>();

    /**
     * 指标事件发生的**原始时间戳** (单位：毫秒，相对于 UTC Epoch)。
     * 这个时间戳对于 Flink 进行基于事件时间的窗口计算至关重要。
     * 必须由生产者准确设置。
     * 示例: System.currentTimeMillis(), span.getEnd()
     */
    private long timestamp;

    /**
     * DTS 接收到数据的时间戳 (毫秒)。
     * DTS_RECEIVE_TIME
     */
    private long dtsReceiveTime;

    /**
     * **核心字段:** Flink 聚合提示 (Map<String, FlinkAggregationType>)。
     * 这个 Map 的 Key 对应 `fields` 中的 Field Key。
     * Value 是 {@link FlinkAggregationType} 枚举，**明确告知**通用的 Flink 任务应该如何聚合这个字段的值。
     * 例如:
     * - "cnt": SUM (调用次数累加)
     * - "error": SUM (错误次数累加)
     * - "slowCnt": SUM (慢调用次数累加)
     * - "sumDuration": SUM (总耗时累加，后续可计算平均耗时)
     * - "kpiAttributeValue": SUM / LAST / FIRST (根据场景 KPI 配置决定)
     * - "cpuUsage": AVG (计算平均 CPU 使用率)
     * - "maxLatency": MAX (记录窗口内最大延迟)
     * - "minLatency": MIN (记录窗口内最小延迟)
     * - "status": LAST (记录窗口内最后的状态)
     * - "userId": FIRST (记录窗口内第一个用户 ID)
     * - "processedCount": COUNT (统计窗口内记录条数)
     * **如果 `fields` 中的某个字段在此 Map 中没有对应的 Hint，Flink 任务通常会默认按 SUM 处理（需要检查 Flink 实现）。**
     * 示例: {"cnt": FlinkAggregationType.SUM, "latency": FlinkAggregationType.AVG, "status": FlinkAggregationType.LAST}
     */
    private Map<String, FlinkAggregationType> flinkAggregationHints = new HashMap<>();

    /**
     * 辅助构造方法，快速创建对象并初始化内部 Map。
     *
     * @param database    目标数据库名
     * @param measurement 目标 Measurement 名
     * @param timestamp   原始事件时间戳 (ms)
     */
    public StandardMetric(String database, String measurement, long timestamp) {
        this.database = database;
        this.measurement = measurement;
        this.timestamp = timestamp;
        // 初始化内部 Map，避免 NullPointerException
        this.tags = new HashMap<>();
        this.fields = new HashMap<>();
        this.fieldTypes = new HashMap<>(); // 初始化为空
        this.flinkAggregationHints = new HashMap<>();
    }

    /**
     * 添加一个指标字段及其对应的 Flink 聚合提示。
     * 注意：此方法不设置最终的 TSDB FieldType，该类型由 Flink 推断。
     *
     * @param key          字段名 (Field Key)
     * @param value        字段值 (本次事件的值或增量)
     * @param flinkAggHint 该字段在 Flink 窗口中的聚合方式。如果为 null，则该字段可能按默认方式(如 SUM)聚合。
     */
    public void addField(String key, Object value, FlinkAggregationType flinkAggHint) {
        // 检查 Key 和 Value 是否为 null，Field Value 通常不应为 null
        if (key != null && !key.isEmpty() && value != null) {
            this.fields.put(key, value);
            // 如果提供了聚合提示，则添加；否则 Flink 端会使用默认值
            if (flinkAggHint != null) {
                this.flinkAggregationHints.put(key, flinkAggHint);
            } else {
                // 可以选择不设置，让 Flink 使用默认值(SUM)，或者在这里显式设置默认值
                // this.flinkAggregationHints.put(key, FlinkAggregationType.SUM);
            }
        } else {
            // 记录日志或抛出异常，指示无效的 Field Key 或 Value
            log.debug("Attempted to add field with null key or value. database:{}, measurement:{},Key: {}, Value: {}", database, measurement, key, value);
        }
    }

    /**
     * 添加一个维度标签。
     *
     * @param key   标签名 (Tag Key)
     * @param value 标签值 (Tag Value)
     */
    public void addTag(String key, String value) {
        // TSDB 的 Tag Key 不允许为 null 跟 ""  Value 不允许为 null
        if (key != null && !key.isEmpty() && value != null) {
            this.tags.put(key, value);
        } else {
            //强制转 ""  ,tsdb tag不可以写null
            this.tags.put(key, "");
            // 记录日志或根据严格性要求抛出异常
            log.debug("Attempted to add tag with null or empty key/value. database:{}, measurement:{}, Key: '{}', Value: '{}'", database, measurement, key, value);
        }
    }

    // Lombok 会自动生成 Getters 和 Setters

    // -------------------------------------------------------------------------
    //                            使用示例
    // -------------------------------------------------------------------------
    /*

    // --- 示例 1: 记录一次 API 调用成功 (biz_event) ---
    StandardMetric apiCallMetric = new StandardMetric("turing", "biz_event", System.currentTimeMillis());
    apiCallMetric.addTag("service", "user-service");
    apiCallMetric.addTag("resource", "/api/user/profile");
    apiCallMetric.addTag("http_method", "GET");
    apiCallMetric.addTag("status_code", "200");
    apiCallMetric.addTag("bizError", ""); // 无业务异常
    apiCallMetric.addTag("systemError", ""); // 无系统异常

    apiCallMetric.addField("cnt", 1L, FlinkAggregationType.SUM); // 调用次数 +1
    apiCallMetric.addField("error", 0L, FlinkAggregationType.SUM); // 错误次数 +0
    apiCallMetric.addField("slowCnt", 0L, FlinkAggregationType.SUM); // 慢调用次数 +0
    apiCallMetric.addField("sumDuration", 150000L, FlinkAggregationType.SUM); // 本次耗时 150ms (in µs)
    apiCallMetric.addField("latency_max", 150000L, FlinkAggregationType.MAX); // 可同时记录最大值
    apiCallMetric.addField("latency_min", 150000L, FlinkAggregationType.MIN); // 和最小值

    // kafkaProducer.send("dc_standard_metric", objectMapper.writeValueAsString(apiCallMetric));


    // --- 示例 2: 记录一次 API 调用失败 (biz_event)，且为慢请求 ---
    StandardMetric apiFailMetric = new StandardMetric("turing", "biz_event", System.currentTimeMillis());
    apiFailMetric.addTag("service", "order-service");
    apiFailMetric.addTag("resource", "/api/order/create");
    apiFailMetric.addTag("http_method", "POST");
    apiFailMetric.addTag("status_code", "500");
    apiFailMetric.addTag("bizError", ""); // 无业务异常
    apiFailMetric.addTag("systemError", "TIMEOUT_ERROR"); // 记录系统异常

    apiFailMetric.addField("cnt", 1L, FlinkAggregationType.SUM);
    apiFailMetric.addField("error", 1L, FlinkAggregationType.SUM); // 错误次数 +1
    apiFailMetric.addField("slowCnt", 0L, FlinkAggregationType.SUM); // 因为有错误，slowCnt 增量为 0
    apiFailMetric.addField("sumDuration", 5000000L, FlinkAggregationType.SUM); // 本次耗时 5s (in µs)

    // kafkaProducer.send("dc_standard_metric", objectMapper.writeValueAsString(apiFailMetric));


    // --- 示例 3: 记录一次支付事件的 KPI 数据 (biz_event_kpi) - 成功 ---
    StandardMetric kpiSuccessMetric = new StandardMetric("turing", "biz_event_kpi", System.currentTimeMillis());
    // Tags 定义了场景和事件上下文
    kpiSuccessMetric.addTag("bizScenarioId", "S_PAY_SUCCESS");
    kpiSuccessMetric.addTag("bizScenarioName", "支付成功场景");
    kpiSuccessMetric.addTag("bizEventId", "E_Payment");
    kpiSuccessMetric.addTag("bizEventName", "处理支付");
    kpiSuccessMetric.addTag("resource", "/callback/payment");
    kpiSuccessMetric.addTag("service", "payment-callback");

    // Fields 记录本次调用的指标
    kpiSuccessMetric.addField("cnt", 1L, FlinkAggregationType.SUM); // 转化计数 +1
    kpiSuccessMetric.addField("error", 0L, FlinkAggregationType.SUM); // 错误计数 +0
    // 假设此场景 KPI 是订单金额 (SUM)
    kpiSuccessMetric.addField("kpiAttributeValue", 199.99, FlinkAggregationType.SUM);

    // kafkaProducer.send("dc_standard_metric", objectMapper.writeValueAsString(kpiSuccessMetric));


    // --- 示例 4: 记录一次支付事件的 KPI 数据 (biz_event_kpi) - 失败 ---
    StandardMetric kpiFailMetric = new StandardMetric("turing", "biz_event_kpi", System.currentTimeMillis());
    // Tags 同上
    kpiFailMetric.addTag("bizScenarioId", "S_PAY_SUCCESS");
    kpiFailMetric.addTag("bizScenarioName", "支付成功场景");
    kpiFailMetric.addTag("bizEventId", "E_Payment");
    kpiFailMetric.addTag("bizEventName", "处理支付");
    kpiFailMetric.addTag("resource", "/callback/payment");
    kpiFailMetric.addTag("service", "payment-callback");

    // Fields
    kpiFailMetric.addField("cnt", 1L, FlinkAggregationType.SUM); // 转化计数 +1 (即使失败也算一次尝试)
    kpiFailMetric.addField("error", 1L, FlinkAggregationType.SUM); // 错误计数 +1
    // 因为有错误，KPI 值贡献为 0
    kpiFailMetric.addField("kpiAttributeValue", 0.0, FlinkAggregationType.SUM); // KPI hint 仍是 SUM

    // kafkaProducer.send("dc_standard_metric", objectMapper.writeValueAsString(kpiFailMetric));


     // --- 示例 5: 记录用户最后登录区域 (biz_event_kpi) ---
     StandardMetric lastLoginMetric = new StandardMetric("user_profile", "user_login_kpi", System.currentTimeMillis());
     lastLoginMetric.addTag("bizScenarioId", "S_USER_PROFILE");
     lastLoginMetric.addTag("bizEventId", "E_LoginSuccess");
     lastLoginMetric.addTag("service", "auth-service");
     lastLoginMetric.addTag("userId", "user-123"); // Tag 中不应放高基数 ID，仅作示例

     lastLoginMetric.addField("cnt", 1L, FlinkAggregationType.SUM); // 场景内登录次数
     lastLoginMetric.addField("error", 0L, FlinkAggregationType.SUM);
     // KPI 是记录最后登录区域 (LAST)
     lastLoginMetric.addField("kpiAttributeValue", "us-west-2", FlinkAggregationType.LAST);

     // kafkaProducer.send("dc_standard_metric", objectMapper.writeValueAsString(lastLoginMetric));

     */

}