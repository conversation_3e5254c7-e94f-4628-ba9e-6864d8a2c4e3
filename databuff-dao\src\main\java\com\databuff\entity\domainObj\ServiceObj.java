package com.databuff.entity.domainObj;

import lombok.Data;

import java.util.Objects;
import java.util.Set;

@Data
public class ServiceObj {

    /**
     * 服务id
     */
    private String serviceId;

    /**
     * 采集的服务名称
     */
    private String service;
    /**
     * 服务展示名称
     */
    private String name;
    /**
     * apikey
     */
    private String apikey;


    private String customTags;

    private Set<Integer> businessIds;

    // 重写 equals 和 hashCode
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ServiceObj serviceObj = (ServiceObj) o;
        return serviceId.equals(serviceObj.serviceId);
    }
    @Override
    public int hashCode() {
        return Objects.hash(serviceId);
    }

}
