package com.databuff.common.tsdb.adapter;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.tsdb.builder.QueryBuilder;
import com.databuff.common.tsdb.model.AggFun;
import com.databuff.common.tsdb.model.TSDBDatabaseInfo;
import com.databuff.common.tsdb.model.TSDBPoint;
import com.databuff.common.tsdb.model.TSDBResultSet;
import com.databuff.common.tsdb.wrapper.DatabaseWrapper;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 数据库适配器接口
 * 实现AutoCloseable接口以支持try-with-resources语法
 */
public interface DatabaseAdapter extends AutoCloseable {
    /**
     * 执行查询
     *
     * 该方法用于执行针对特定数据库的查询操作它接受一个查询字符串、一个数据库名称和一个查询构建器对象，
     * 并返回查询结果集此方法抛出异常，以处理查询执行过程中的错误
     *
     * @param dbClient        tsdb链接对象
     * @param query        查询字符串，包含具体的查询语句
     * @param databaseName 数据库名称，指定查询在哪个数据库上执行
     * @param builder      查询构建器对象，用于构建复杂的查询语句
     * @return TSDBResultSet 查询结果集，包含查询到的数据
     * @throws Exception   查询执行过程中可能抛出的异常
     */
    TSDBResultSet executeQuery(DatabaseWrapper dbClient, String query, String databaseName, QueryBuilder builder) throws Exception;

    /**
     * 执行查询，返回标签值集合
     * @param dbClient        tsdb链接对象
     * @param query 查询sql
     * @param databaseName 数据库名称
     * @return
     */
    Map<String, Set<String>> executeShowTagValues(DatabaseWrapper dbClient, String query, String databaseName);

    /**
     * 获取配置
     * @return
     */
    Map<String, Object> getConfigs();

    Collection<String> executeShowTagKeys(DatabaseWrapper dbClient, String sql, String databaseName);

    /**
     * 创建数据库
     *
     * @param dbClient
     * @param databaseInfo
     * @return
     */
    boolean createDatabase(DatabaseWrapper dbClient, TSDBDatabaseInfo databaseInfo);


    /**
     * 写入数据
     *
     * @param dbClient
     * @param databaseName
     * @param tsdbPoints
     * @return
     */
    boolean writePoints(DatabaseWrapper dbClient, String databaseName, List<TSDBPoint> tsdbPoints);

    /**
     * 构建完整的sql
     *
     * @param builder
     * @return
     */

    String buildFullSql(QueryBuilder builder);


    /**
     * show tag value sql
     *
     * @param dbClient
     * @param builder
     * @return
     */
    Map<String, String> buildShowTagValueSql(DatabaseWrapper dbClient, QueryBuilder builder);

    /**
     * 构建where条件
     *
     * @param builder
     * @return
     */

    String buildWhereSql(QueryBuilder builder);

    /**
     * 曲线topN查询
     */
    TSDBResultSet executeQuery(DatabaseWrapper dbClient, QueryBuilder builder, AggFun tsAgg, AggFun valAgg, AggFun topAgg, JSONObject otherParam) throws Exception;

    /**
     * apm 模块查询百分位独有接口，适配不同时序数据库实现
     * @param dbClient
     * @param builder
     * @return
     */
    TSDBResultSet apmPercentageLatency(DatabaseWrapper dbClient, QueryBuilder builder);
}
