package com.databuff.entity.extend;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import static com.databuff.common.utils.TimeUtil.ONE_HOUR_MS;

/**
 * @author:TianMing
 * @date: 2022/8/24
 * @time: 13:47
 */
@Data
@Deprecated
public class BusinessSearch {

    @ApiModelProperty(value = "开始时间", example = "2021-07-15 00:00:00")
    private String fromTime;
    @ApiModelProperty(value = "结束时间", example = "2021-07-15 23:59:59")
    private String toTime;
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "模糊查询名称")
    private String nameQuery;
    @ApiModelProperty(value = "列表模糊查询名称")
    private String listnameQuery;
    @ApiModelProperty("1顶级业务系统 2业务子系统 3服务")
    private Integer type;
    @ApiModelProperty("服务健康状态")
    private String health;
    @ApiModelProperty("服务状态, 0表示异常，非0表示正常，其余状态预留接口")
    private Integer statusType;
    @ApiModelProperty("apiKey")
    private String apiKey;
    @ApiModelProperty("父级id（顶层为0，子系统则关联父系统id）")
    private Integer pid;
    @ApiModelProperty("系统id")
    private Integer sysId;
    @ApiModelProperty("服务id或者系统id")
    private String id;
    @ApiModelProperty("系统id列表")
    private List<Integer> sysIds;
    @ApiModelProperty("账号")
    private String account;
    @ApiModelProperty("是否查询服务的告警数")
    private boolean queryServiceAlarmCount = false;

    @ApiModelProperty("是否跳过权限检查")
    private boolean skipPermissionCheck = false;

    public Long getFromTimeVul() {
        if (this.fromTime == null || "".equals(this.fromTime)) {
            return strToDate("yyyy-MM-dd HH:mm:ss", this.fromTime).getTime() - ONE_HOUR_MS;
        }
        return strToDate("yyyy-MM-dd HH:mm:ss", this.fromTime).getTime();
    }

    public Long getToTimeVul() {
        return strToDate("yyyy-MM-dd HH:mm:ss", this.toTime).getTime();
    }

    private static Date strToDate(String pattern, String source) {
        Date date = new Date();
        try {
            if (pattern == null || "".equals(pattern)) {
                DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                date = df.parse(source);
                return date;
            }
            DateFormat df = new SimpleDateFormat(pattern);
            date = df.parse(source);
        } catch (Exception e) {
        }
        return date;
    }
}
