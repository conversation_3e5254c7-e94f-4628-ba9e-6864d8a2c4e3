package com.databuff.common.tsdb.pool;

import com.databuff.common.constants.Constant;
import com.databuff.common.tsdb.wrapper.DatabaseWrapper;
import org.apache.commons.pool2.PooledObject;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.lang.reflect.Method;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({TSDBPoolFactory.class})
public class TSDBPoolFactoryTest {

    private TSDBPoolFactory factory;

    @Mock
    private DatabaseWrapper mockWrapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        
        // 创建一个测试用的TSDBPoolFactory实例
        factory = TSDBPoolFactory.builder()
                .tsdb(Constant.TS_DB.OPENGEMINI)
                .url("localhost:8086")
                .tsdbApi("api")
                .user("user")
                .pwd("password")
                .queryTimeout(5000)
                .duration("1h")
                .shard(1)
                .replication(1)
                .interval(1)
                .jmxEnabled(false)
                .build();
    }

    /**
     * 测试解析URL中的服务器列表
     * 使用反射调用私有方法parseServerList
     */
    @Test
    public void testParseServerList() throws Exception {
        // 使用反射获取私有方法
        Method parseServerListMethod = TSDBPoolFactory.class.getDeclaredMethod("parseServerList", String.class);
        parseServerListMethod.setAccessible(true);

        // 测试各种URL格式
        List<String> result1 = (List<String>) parseServerListMethod.invoke(factory, "ip1:2890,ip2:2890");
        assertEquals(2, result1.size());
        assertEquals("ip1:2890", result1.get(0));
        assertEquals("ip2:2890", result1.get(1));

        // 测试末尾有逗号的情况
        List<String> result2 = (List<String>) parseServerListMethod.invoke(factory, "ip1:2890,ip2:2890,");
        assertEquals(2, result2.size());
        assertEquals("ip1:2890", result2.get(0));
        assertEquals("ip2:2890", result2.get(1));

        // 测试开头有逗号的情况
        List<String> result3 = (List<String>) parseServerListMethod.invoke(factory, ",ip1:2890,ip2:2890,");
        assertEquals(2, result3.size());
        assertEquals("ip1:2890", result3.get(0));
        assertEquals("ip2:2890", result3.get(1));

        // 测试有空格的情况
        List<String> result4 = (List<String>) parseServerListMethod.invoke(factory, " ip1:2890 , ip2:2890 ");
        assertEquals(2, result4.size());
        assertEquals("ip1:2890", result4.get(0));
        assertEquals("ip2:2890", result4.get(1));

        // 测试有多个空格的情况
        List<String> result5 = (List<String>) parseServerListMethod.invoke(factory, "ip1:2890    ,ip2:2890");
        assertEquals(2, result5.size());
        assertEquals("ip1:2890", result5.get(0));
        assertEquals("ip2:2890", result5.get(1));

        // 测试中文逗号的情况
        List<String> result6 = (List<String>) parseServerListMethod.invoke(factory, "ip1:2890，ip2:2890");
        assertEquals(2, result6.size());
        assertEquals("ip1:2890", result6.get(0));
        assertEquals("ip2:2890", result6.get(1));

        // 测试中文句号的情况
        List<String> result7 = (List<String>) parseServerListMethod.invoke(factory, "ip1:2890。ip2:2890");
        assertEquals(2, result7.size());
        assertEquals("ip1:2890", result7.get(0));
        assertEquals("ip2:2890", result7.get(1));

        // 测试空字符串
        List<String> result8 = (List<String>) parseServerListMethod.invoke(factory, "");
        assertEquals(0, result8.size());

        // 测试null
        List<String> result9 = (List<String>) parseServerListMethod.invoke(factory, (String)null);
        assertEquals(0, result9.size());

        // 测试只有分隔符的情况
        List<String> result10 = (List<String>) parseServerListMethod.invoke(factory, ",,,");
        assertEquals(0, result10.size());

        // 测试混合各种情况
        List<String> result11 = (List<String>) parseServerListMethod.invoke(factory, " ,ip1:2890, ,ip2:2890，ip3:2890。 ip4:2890 , , ");
        assertEquals(4, result11.size());
        assertEquals("ip1:2890", result11.get(0));
        assertEquals("ip2:2890", result11.get(1));
        assertEquals("ip3:2890", result11.get(2));
        assertEquals("ip4:2890", result11.get(3));
    }

    /**
     * 测试makeObject方法处理单节点URL
     */
    @Test
    public void testMakeObjectWithSingleNode() throws Exception {
        // 创建一个测试用的TSDBPoolFactory实例，使用单节点URL
        TSDBPoolFactory testFactory = PowerMockito.spy(TSDBPoolFactory.builder()
                .tsdb(Constant.TS_DB.OPENGEMINI)
                .url("localhost:8086")
                .build());

        // Mock createWrapper方法返回我们的mockWrapper
        PowerMockito.doReturn(mockWrapper).when(testFactory, "createWrapper", any());
        
        // Mock isConnected方法返回true
        when(mockWrapper.isConnected()).thenReturn(true);

        // 调用makeObject方法
        PooledObject<DatabaseWrapper> result = testFactory.makeObject();
        
        // 验证结果
        assertNotNull(result);
        assertEquals(mockWrapper, result.getObject());
    }

    /**
     * 测试makeObject方法处理集群URL
     */
    @Test
    public void testMakeObjectWithClusterNodes() throws Exception {
        // 创建一个测试用的TSDBPoolFactory实例，使用集群URL
        TSDBPoolFactory testFactory = PowerMockito.spy(TSDBPoolFactory.builder()
                .tsdb(Constant.TS_DB.OPENGEMINI)
                .url("node1:8086,node2:8086")
                .build());

        // Mock connectToCluster方法返回我们的mockWrapper
        PowerMockito.doReturn(mockWrapper).when(testFactory, "connectToCluster", any(), any());

        // 调用makeObject方法
        PooledObject<DatabaseWrapper> result = testFactory.makeObject();
        
        // 验证结果
        assertNotNull(result);
        assertEquals(mockWrapper, result.getObject());
    }

    /**
     * 测试makeObject方法处理无效URL
     */
    @Test(expected = IllegalArgumentException.class)
    public void testMakeObjectWithInvalidUrl() {
        // 创建一个测试用的TSDBPoolFactory实例，使用无效URL
        TSDBPoolFactory testFactory = TSDBPoolFactory.builder()
                .tsdb(Constant.TS_DB.OPENGEMINI)
                .url(",,,,")
                .build();

        // 调用makeObject方法，应该抛出IllegalArgumentException
        testFactory.makeObject();
    }

    /**
     * 测试makeObject方法处理连接失败的情况
     */
    @Test(expected = IllegalStateException.class)
    public void testMakeObjectWithConnectionFailure() throws Exception {
        // 创建一个测试用的TSDBPoolFactory实例
        TSDBPoolFactory testFactory = PowerMockito.spy(TSDBPoolFactory.builder()
                .tsdb(Constant.TS_DB.OPENGEMINI)
                .url("localhost:8086")
                .build());

        // Mock createWrapper方法返回我们的mockWrapper
        PowerMockito.doReturn(mockWrapper).when(testFactory, "createWrapper", any());
        
        // Mock isConnected方法返回false，模拟连接失败
        when(mockWrapper.isConnected()).thenReturn(false);

        // 调用makeObject方法，应该抛出IllegalStateException
        testFactory.makeObject();
    }
}
