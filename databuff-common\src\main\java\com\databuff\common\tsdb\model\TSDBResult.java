package com.databuff.common.tsdb.model;

import java.util.List;

public class TSDBResult {
    private String queryType = "unknown";
    private List<TSDBSeries> series;
    private String error;
    public String getQueryType() {
        return this.queryType;
    }
    public void setQueryType(String queryType) {
        this.queryType = queryType;
    }
    public TSDBResult() {
    }

    public List<TSDBSeries> getSeries() {
        return this.series;
    }

    public void setSeries(List<TSDBSeries> series) {
        this.series = series;
    }

    public boolean hasError() {
        return this.error != null;
    }

    public String getError() {
        return this.error;
    }

    public void setError(String error) {
        this.error = error;
    }
}