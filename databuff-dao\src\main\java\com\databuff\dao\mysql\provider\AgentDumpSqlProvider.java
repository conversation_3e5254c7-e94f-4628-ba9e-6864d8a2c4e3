package com.databuff.dao.mysql.provider;

import com.databuff.entity.dump.AgentDump;
import com.databuff.entity.dump.AgentDumpSearchCriteria;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.jdbc.SQL;

import java.util.Collection;
import java.util.stream.Collectors;

public class AgentDumpSqlProvider {

    public String updateByIdSelective(@Param("agentDump") AgentDump agentDump) {
        return new SQL() {{
            UPDATE("dc_agent_dump");
            if (agentDump.getFileName() != null) {
                SET("file_name = #{agentDump.fileName}");
            }
            if (agentDump.getFileSize() != null) {
                SET("file_size = #{agentDump.fileSize}");
            }
            if (agentDump.getPath() != null) {
                SET("path = #{agentDump.path}");
            }
            if (agentDump.getStatus() != null) {
                SET("status = #{agentDump.status}");
            }
            if (agentDump.getProgress() != null) {
                SET("progress = #{agentDump.progress}");
            }
            if (agentDump.getPackName() != null) {
                SET("pack_name = #{agentDump.packName}");
            }
            if (agentDump.getVersion() != null) {
                SET("version = #{agentDump.version}");
            }
            if (agentDump.getOldVersion() != null) {
                SET("old_version = #{agentDump.oldVersion}");
            }
            if (agentDump.getUploadTime() != null) {
                SET("upload_time = #{agentDump.uploadTime}");
            }
            if (agentDump.getStartTime() != null) {
                SET("start_time = #{agentDump.startTime}");
            }
            if (agentDump.getEndTime() != null) {
                SET("end_time = #{agentDump.endTime}");
            }
            if (agentDump.getMsg() != null) {
                SET("msg = #{agentDump.msg}");
            }
            WHERE("id = #{agentDump.id}");
        }}.toString();
    }

    public String searchAgentDumps(@Param("criteria") AgentDumpSearchCriteria criteria) {
        final SQL sql = new SQL() {{
            SELECT("*");
            FROM("dc_agent_dump");
            if (criteria.getFileName() != null) {
                WHERE("file_name LIKE CONCAT('%', #{criteria.fileName}, '%')");
            }
            if (criteria.getPath() != null) {
                WHERE("path LIKE CONCAT('%', #{criteria.path}, '%')");
            }
            if (criteria.getServiceId() != null) {
                WHERE("service_id = #{criteria.serviceId}");
            }
            if (criteria.getService() != null) {
                WHERE("service = #{criteria.service}");
            }
            if (criteria.getServiceInstance() != null) {
                WHERE("service_instance = #{criteria.serviceInstance}");
            }
            if (criteria.getAccount() != null) {
                WHERE("account = #{criteria.account}");
            }
            if (criteria.getStatus() != null) {
                WHERE("status = #{criteria.status}");
            }
            if (criteria.getStartTime() != null) {
                WHERE("create_time >= #{criteria.startTime}");
            }
            if (criteria.getEndTime() != null) {
                WHERE("create_time <= #{criteria.endTime}");
            }
            // 新增 gid 条件
            Collection<String> gids = criteria.getGids();
            if (gids != null && !gids.isEmpty()) {
                String gidCondition = gids.stream()
                        .filter(gid -> gid != null)
                        .map(gid -> "'" + gid + "'")
                        .collect(Collectors.joining(",", "gid IN (", ")"));
                WHERE(gidCondition);
            }
        }};
        return sql.toString();
    }

    public String getNextTask(@Param("host") String host) {
        return new SQL() {{
            SELECT("*");
            FROM("dc_agent_dump");
            WHERE("host = #{host}");
            WHERE("status = 0");
            ORDER_BY("create_time DESC");
            LIMIT(1);
        }}.toString();
    }
}