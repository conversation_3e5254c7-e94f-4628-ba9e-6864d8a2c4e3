package com.databuff.dao.mysql;

import com.databuff.entity.RoleGroupRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface RoleGroupRelationMapper {

    // 插入角色和管理域的绑定
    int insertRoleGroupRelation(@Param("relations")  List<RoleGroupRelation> relations);

    // 根据角色ID查询对应的管理域绑定
    List<RoleGroupRelation> getRoleGroupRelationsByRoleId(@Param("roleId") Integer roleId,@Param("apiKey") String apiKey);

    // 删除角色和管理域的绑定关系
    int deleteRoleGroupRelation(@Param("gid") Integer gid,@Param("roleId") Integer roleId,@Param("apiKey") String apiKey);

    List<RoleGroupRelation> getAllRoleGroupRelations(@Param("apiKey") String apiKey);

    int updateRoleGroupRelationAuth(@Param("relations") List<RoleGroupRelation> relations);
}