package com.databuff.common.tsdb.builder;


import com.databuff.common.tsdb.model.Aggregation;
import com.databuff.common.tsdb.model.OrderBy;
import com.databuff.common.tsdb.model.Where;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

@Data
public class QueryBuilder implements Serializable {

    /**
     * 查询类型
     */
    private QueryType queryType = QueryType.STANDARD;

    /**
     * 查询类型枚举，用于标识不同的查询策略
     */
    public enum QueryType {
        /**
         * 标准查询，没有排序或只有基本排序
         */
        STANDARD,
        /**
         * 只有time字段排序
         */
        TOPN_MAX_TIME,
        /**
         * 只有非time字段排序
         */
        NON_TIME_ONLY_ORDER,
        /**
         * 同时有time和非time字段排序
         */
        MIXED_ORDER
    }

    private String databaseName;
    private String measurement;
    private List<Aggregation> aggregations;
    private List<Where> wheres;
    private List<Where> timeWhere;
    private List<String> groupBy;
    //间隔时间
    private Integer interval;
    //间隔单位
    private String intervalUnit = "s";
    private List<OrderBy> orderBy;
    private Long limit;

    private long start;
    private long end;

    /**
     * 获取调整后的OpenGemini查询限制值。
     *
     * @return 调整后的限制值，基于时间范围和间隔计算得出。若原limit不为null，则返回转换后的整数值。
     */
    public Long getOpenGeminiLimit() {
        if (limit == null) {
            return null;
        }
        if (interval == null || interval <= 0) {
            return limit;
        }
        if (end < start) {
            throw new IllegalArgumentException("结束时间不能早于开始时间");
        }
        long timeDifference = end - start;
        long seconds = timeDifference / 1000;
        long intervals = seconds / interval;
        return Math.multiplyExact(intervals, limit);
    }


    private Long offset;

    /**
     * 获取调整后的OpenGemini查询偏移量。
     *
     * @return 调整后的偏移量值，基于时间范围和间隔计算得出。若原offset不为null，则返回转换后的整数值。
     */
    public Long getOpengeminiOffset() {
        final long timeDifference = end - start;
        if (timeDifference <= 0) {
            return offset;
        }
        if (interval == null || interval == 0) {
            return offset;
        }
        if (offset == null) {
            return offset;
        }
        // 将offset转换为基于时间跨度和间隔的调整值，单位转换为毫秒
        return timeDifference / 1000 / interval * offset;
    }

    // 设置数据库类型
    public QueryBuilder() {
        this.aggregations = new ArrayList<>();
        this.wheres = new ArrayList<>();
        this.timeWhere = new ArrayList<>();
        this.groupBy = new ArrayList<>();
        this.orderBy = new ArrayList<>();
        this.wheres.add(Where.and(timeWhere));
    }

    public QueryBuilder setDatabaseName(String databaseName) {
        this.databaseName = databaseName;
        return this;
    }

    public QueryBuilder setDatabaseName(String apiKey, String databaseName) {
        this.databaseName = apiKey + "_" + databaseName;
        return this;
    }

    public QueryBuilder setMeasurement(String measurement) {
        this.measurement = measurement;
        return this;
    }

    public QueryBuilder addAgg(Aggregation aggregation) {
        this.aggregations.add(aggregation);
        return this;
    }

    public QueryBuilder addAllAgg(List<Aggregation> aggregations) {
        this.aggregations.addAll(aggregations);
        return this;
    }

    public QueryBuilder addAllWhere(List<Where> wheres) {
        if (wheres != null) {
            this.wheres.addAll(wheres);
        }
        return this;
    }

    public QueryBuilder addWhere(Where where) {
        if (where != null) {
            this.wheres.add(where);
        }
        return this;
    }

    public QueryBuilder addAllGroupBy(Collection<String> groupBy) {
        if (groupBy != null) {
            groupBy.stream()
                    .filter(Objects::nonNull)
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .forEach(this.groupBy::add);
        }
        return this;
    }

    public QueryBuilder addGroupBy(String groupBy) {
        if (groupBy != null && !groupBy.trim().isEmpty()) {
            this.groupBy.add(groupBy.trim());
        }
        return this;
    }

    // 默认开始时间大于等于 start
    public QueryBuilder start(long start) {
        this.start = start;
        timeWhere.add(Where.gte("time", start));
        return this;
    }

    // 默认结束时间小于 end
    public QueryBuilder end(long end) {
        this.end = end;
        timeWhere.add(Where.lt("time", end));
        return this;
    }

    // 设置 interval 参数
    public QueryBuilder setInterval(Integer interval) {
        this.interval = interval;
        return this;
    }

    // 设置 intervalUnit 参数
    public QueryBuilder setIntervalUnit(String intervalUnit) {
        if (intervalUnit != null && !intervalUnit.trim().isEmpty()) {
            this.intervalUnit = intervalUnit.trim();
        }
        return this;
    }

    // 设置 order by 参数
    public QueryBuilder addOrderBy(OrderBy orderBy) {
        if (orderBy != null && StringUtils.isNotBlank(orderBy.getField())) {
            this.orderBy.add(orderBy);
        }
        return this;
    }

    // 设置 limit 参数
    public QueryBuilder setLimit(Integer limit) {
        this.limit = limit.longValue();
        return this;
    }

    public QueryBuilder setLimit(Long limit) {
        this.limit = limit;
        return this;
    }

    // 设置 offset 参数
    public QueryBuilder setOffset(Integer offset) {
        this.offset = offset.longValue();
        return this;
    }

    public QueryBuilder setOffset(Long offset) {
        this.offset = offset;
        return this;
    }


    // ✅ **校验参数合法性**
    public void validate() {

        if (databaseName == null || databaseName.trim().isEmpty()) {
            throw new IllegalArgumentException("数据库名称 (databaseName) 不能为空！");
        }
        if (measurement == null || measurement.trim().isEmpty()) {
            throw new IllegalArgumentException("测量值 (measurement) 不能为空！");
        }
        if (interval != null && interval < 0) {
            throw new IllegalArgumentException("时间间隔 (interval) 不能为负数！");
        }
        if (limit != null && limit < 0) {
            throw new IllegalArgumentException("LIMIT 不能为负数！");
        }
        if (offset != null && offset < 0) {
            throw new IllegalArgumentException("OFFSET 不能为负数！");
        }
        if (aggregations == null || aggregations.isEmpty()) {
            throw new IllegalArgumentException("查询字段 (aggregations) 不能为空！");
        }
        if (!wheres.isEmpty() && wheres.stream().anyMatch(where -> where.getSubConditions() == null && (where.getField() == null || where.getOperator() == null))) {
            throw new IllegalArgumentException("WHERE 条件字段或操作符不能为空！");
        }
    }

    // 新增拷贝构造函数
    public QueryBuilder(QueryBuilder other) {
        this.queryType = other.queryType;
        this.databaseName = other.databaseName;
        this.measurement = other.measurement;

        // 深度拷贝集合
        this.aggregations = copyAggregations(other.aggregations);
        this.wheres = copyWheres(other.wheres);
        this.timeWhere = copyWheres(other.timeWhere);
        this.groupBy = new ArrayList<>(other.groupBy); // String无需深度拷贝
        this.orderBy = copyOrderBys(other.orderBy);

        this.interval = other.interval;
        this.intervalUnit = other.intervalUnit;
        this.limit = other.limit;
        this.offset = other.offset;
        this.start = other.start;
        this.end = other.end;
    }

    // 提供实例拷贝方法
    public QueryBuilder copy() {
        return new QueryBuilder(this);
    }

    // 辅助方法：深度拷贝集合
    private List<Aggregation> copyAggregations(List<Aggregation> original) {
        List<Aggregation> copy = new ArrayList<>();
        for (Aggregation agg : original) {
            copy.add(agg != null ? agg.copy() : null);
        }
        return copy;
    }

    private List<Where> copyWheres(List<Where> original) {
        List<Where> copy = new ArrayList<>();
        for (Where where : original) {
            copy.add(where != null ? where.copy() : null); // 假设Where也有copy()方法
        }
        return copy;
    }

    private List<OrderBy> copyOrderBys(List<OrderBy> original) {
        List<OrderBy> copy = new ArrayList<>();
        for (OrderBy order : original) {
            copy.add(order != null ? order.copy() : null); // 假设OrderBy也有copy()方法
        }
        return copy;
    }

}
