package com.databuff.common.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.FileReader;

public class ProcUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProcUtil.class);

    private static long oldCurrentPidCpuTime = 0;
    private static long oldTotalCpuTime = 0;
    private static double cpuRatio = 1;

    static {
        calCpuRatio();
    }

    private static void calCpuRatio() {
        try {
            int totalCpuNumber = readTotalCpuNumber();
            double requestCpuNumber = readRequestCpuNumber();
            if (requestCpuNumber <= 0) {
                cpuRatio = 1;
                return;
            }
            if (requestCpuNumber >= totalCpuNumber) {
                cpuRatio = 1;
            } else {
                cpuRatio = totalCpuNumber * 1.0 / requestCpuNumber;
            }
            String msg = "totalCpu=" + totalCpuNumber + ",requestCpu=" + requestCpuNumber;
            System.out.println(msg);
            LOGGER.info(msg);
        } catch (Throwable e) {
        }
    }

    public static int readTotalCpuNumber() {
        int cpuNumber = 0;
        try (
                FileReader fileReader = new FileReader("/proc/stat");
                BufferedReader reader = new BufferedReader(fileReader)
                ){

            String line = reader.readLine();
            while (line != null && !line.isEmpty()) {
                if (line.startsWith("cpu")) {
                    cpuNumber++;
                }
                line = reader.readLine();
            }
        } catch (Throwable e) {
        }
        return cpuNumber - 1;
    }

    public static double readRequestCpuNumber() {
        double cpuNumber = 0;
        try (
                FileReader fileReader = new FileReader("/sys/fs/cgroup/cpu/cpu.cfs_quota_us");
                BufferedReader reader = new BufferedReader(fileReader);
                ){
            String line = reader.readLine();
            if (line != null) {
                cpuNumber = Integer.parseInt(line.trim()) * 1.0 / 100000;
            }
        } catch (Throwable e) {
        }
        return cpuNumber;
    }

    public static double calCurrentCpuUsage() {
        long currentPidCpuTime = readCurrentPidCpuTime();
        long currentTotalCpuTime = readTotalCpuTime();

        double cpuUsage = -1;
        if (oldCurrentPidCpuTime > 0) {
            cpuUsage = (currentPidCpuTime - oldCurrentPidCpuTime) * cpuRatio / (currentTotalCpuTime - oldTotalCpuTime);
        }
        oldCurrentPidCpuTime = currentPidCpuTime;
        oldTotalCpuTime = currentTotalCpuTime;
        return cpuUsage;
    }

    private static long readCurrentPidCpuTime() {
        long cpuTime = 0;
        try (
                FileReader fileReader = new FileReader("/proc/" + PidUtil.getPid() + "/stat");
                BufferedReader reader = new BufferedReader(fileReader);
                ){
            String line = reader.readLine();
            if (line != null) {
                String[] fields = line.split("\\s+");
                // CPU 相关的字段在数组中的索引
                int utimeIndex = 13; // 用户态 CPU 时间
                int stimeIndex = 14; // 核心态 CPU 时间
                int cutimeIndex = 15; // 子进程用户态 CPU 时间
                int cstimeIndex = 16; // 子进程核心态 CPU 时间

                long utime = Long.parseLong(fields[utimeIndex]);
                long stime = Long.parseLong(fields[stimeIndex]);
                long cutime = Long.parseLong(fields[cutimeIndex]);
                long cstime = Long.parseLong(fields[cstimeIndex]);
                cpuTime = utime + stime + cutime + cstime;
            }
        } catch (Throwable e) {
        }
        return cpuTime;
    }

    private static long readTotalCpuTime() {
        long cpuTime = 0;
        try (
                FileReader fileReader = new FileReader("/proc/stat");
                BufferedReader reader = new BufferedReader(fileReader);
                ){
            String line = reader.readLine();
            if (line != null) {
                String[] fields = line.split("\\s+");
                for (int i = 1, len = fields.length; i < len; i++) {
                    cpuTime += Long.parseLong(fields[i]);
                }
            }
        } catch (Throwable e) {
        }
        return cpuTime;
    }
}
