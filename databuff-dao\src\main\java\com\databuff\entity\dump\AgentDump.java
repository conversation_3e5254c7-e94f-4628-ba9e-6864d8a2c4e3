package com.databuff.entity.dump;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.regex.Pattern;

/**
 * agent dump日志管理表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("dc_agent_dump")
public class AgentDump {
    @TableId
    private Long id;

    /**
     * 域ID
     */
    @TableField("gid")
    private String gid;

    /**
     * API密钥
     */
    @TableField("api_key")
    private String apiKey;

    /**
     * 文件名称
     */
    @TableField("file_name")
    private String fileName;

    // 新增自定义setter方法处理特殊字符
    public void setFileName(String fileName) {
        // 过滤Linux系统路径非法字符（冒号、问号、星号等）
        this.fileName = fileName != null ?
                fileName.replaceAll("[:\"*?<>|]", "")  // 正则表达式匹配常见非法字符
                : null;
    }

    /**
     * 文件大小
     */
    @TableField("file_size")
    private Long fileSize;

    /**
     * 文件路径
     */
    @TableField("path")
    private String path;

    // 新增自定义setter方法处理特殊字符
    public void setPath(String path) {
        // 过滤Linux系统路径非法字符（冒号、问号、星号等）
        this.path = path != null ?
                path.replaceAll("[:\"*?<>|]", "")  // 正则表达式匹配常见非法字符
                : null;
    }

    /**
     * 服务ID
     */
    @TableField("service_id")
    private String serviceId;

    /**
     * 服务名称
     */
    @TableField("service")
    private String service;

    /**
     * 服务实例名称
     */
    @TableField("service_instance")
    private String serviceInstance;

    private static final Pattern INVALID_CHARS_PATTERN = Pattern.compile("[:\"*?<>|]");

    /**
     * 格式化服务和服务实例的名称，移除其中包含的无效字符。
     * 该函数会检查 `service` 和 `serviceInstance` 是否为非空，
     * 如果非空，则使用预定义的 `INVALID_CHARS_PATTERN` 正则表达式
     * 匹配并移除其中的无效字符。
     */
    public void sanitizeServiceNames() {
        // 如果 service 不为空，移除其中的无效字符
        if (service != null) {
            service = INVALID_CHARS_PATTERN.matcher(service).replaceAll("");
        }
        // 如果 serviceInstance 不为空，移除其中的无效字符
        if (serviceInstance != null) {
            serviceInstance = INVALID_CHARS_PATTERN.matcher(serviceInstance).replaceAll("");
        }
    }


    /**
     * 主机名称
     */
    @TableField("host")
    private String host;

    /**
     * IP地址
     */
    @TableField("ip")
    private String ip;

    /**
     * 状态枚举
     * 1 - 安装包下载成功
     * 2 - 通知解压包成功
     * 3 - 更新/重启/停止/启动 进行中
     * 4 - 更新/重启/停止/启动 失败
     * 5 - 更新/重启/停止/启动 成功
     * 6 - 下载失败
     * 7 - 安装包下载中
     */
    @TableField("status")
    private Integer status;

    /**
     * 更新进度
     * 1 - 安装包下载成功
     * 2 - 通知解压包成功
     * 3 - 更新/重启/停止/启动 进行中
     * 4 - 更新/重启/停止/启动 失败
     * 5 - 更新/重启/停止/启动 成功
     * 6 - 下载失败
     * 7 - 安装包下载中
     */
    @TableField("progress")
    private String progress;

    /**
     * 操作类型
     */
    @TableField("operation")
    private Integer operation;

    /**
     * 更新包名称
     */
    @TableField("pack_name")
    private String packName;

    /**
     * 账户
     */
    @TableField("account")
    private String account;

    /**
     * 更新版本
     */
    @TableField("version")
    private String version;

    /**
     * 老版本
     */
    @TableField("old_version")
    private String oldVersion;

    /**
     * 上传时间
     */
    @TableField("upload_time")
    private Date uploadTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 开始时间
     */
    @TableField("start_time")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private Date endTime;

    /**
     * 失败原因等信息
     */
    @TableField("msg")
    private String msg;
}