package com.databuff.entity.label;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class EnvTagValue {
    @TableId(type = IdType.AUTO)
    private String id;
    private String tagId;     // 所属标签ID
    private String tagValue;  // 标签值
    private Date createTime;
    private Date updateTime;
    private List<String> agents; // 关联的探针ID列表
}
