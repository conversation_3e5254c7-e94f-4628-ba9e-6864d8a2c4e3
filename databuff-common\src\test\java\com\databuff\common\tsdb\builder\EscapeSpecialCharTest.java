package com.databuff.common.tsdb.builder;

import org.junit.Test;

/**
 * 一个简单的测试运行器，用于手动测试escapeSpecialChar方法
 */
public class EscapeSpecialCharTest {


    @Test
    public void testChar() {
        // 创建MoreDBQueryBuilder实例
        MoreDBQueryBuilder queryBuilder = new MoreDBQueryBuilder(new QueryBuilder());
        
        // 测试用例
        testCase(queryBuilder, null, null);
        testCase(queryBuilder, "", "");
        testCase(queryBuilder, "abc123", "abc123");
        testCase(queryBuilder, "*", "\\*");
        testCase(queryBuilder, "[0-9]+", "\\[0\\-9\\]\\+");
        testCase(queryBuilder, "(a|b)?.{1,3}", "\\(a\\|b\\)\\?\\.\\{1\\,3\\}");
        testCase(queryBuilder, "SELECT * FROM table WHERE id = 123", "SELECT \\* FROM table WHERE id \\= 123");
        testCase(queryBuilder, "<EMAIL>", "user\\@example\\.com");
        testCase(queryBuilder, "{\"name\":\"John\"}", "\\{\\\"name\\\"\\:\\\"John\\\"\\}");
        testCase(queryBuilder, "\\already\\escaped", "\\\\already\\\\escaped");
        testCase(queryBuilder, "你好，世界！(Hello World!)", "你好，世界！\\(Hello World\\!\\)");
        testCase(queryBuilder, "*+?()[]{}^$|\\", "\\*\\+\\?\\(\\)\\[\\]\\{\\}\\^\\$\\|\\\\");
    }
    
    private static void testCase(MoreDBQueryBuilder queryBuilder, String input, String expected) {
        String result = queryBuilder.escapeSpecialChar(input);
        boolean passed = (input == null && result == null) || (result != null && result.equals(expected));
        
        System.out.println("测试: " + (passed ? "通过 ✓" : "失败 ✗"));
        System.out.println("  输入: " + (input == null ? "null" : "\"" + input + "\""));
        System.out.println("  期望: " + (expected == null ? "null" : "\"" + expected + "\""));
        System.out.println("  实际: " + (result == null ? "null" : "\"" + result + "\""));
        System.out.println();
    }
}
