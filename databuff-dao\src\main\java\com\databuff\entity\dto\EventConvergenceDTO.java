package com.databuff.entity.dto;

import com.alibaba.fastjson.JSONObject;
import com.databuff.metric.RuleTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@ApiModel(description = "事件收敛数据传输对象")
public class EventConvergenceDTO {

    @ApiModelProperty(value = "触发时间", example = "1728953580000")
    private long triggerTime;

    @ApiModelProperty(value = "消息内容", example = "【服务名|服务名称:service-d::k8s】的 指标service.http.cnt当前291.00count\u003e\u003d阈值检测2.00count,")
    private String message;

    @ApiModelProperty(value = "数据来源", example = "DataBuff")
    private String source;

    @ApiModelProperty(value = "事件类型", example = "threshold")
    private String type;

    @ApiModelProperty(value = "事件ID", example = "E10S2024101500001045")
    private String eventId;

    @ApiModelProperty(value = "域ID", example = "31")
    private String gid;

    @ApiModelProperty(value = "编辑者ID", example = "63")
    private String editorId;

    @ApiModelProperty(value = "创建者ID", example = "63")
    private String creatorId;

    @ApiModelProperty(value = "API密钥", example = "NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4")
    private String apiKey;

    @ApiModelProperty(value = "事件级别", example = "3")
    private int level;

    @ApiModelProperty(value = "分类", example = "singleMetric")
    private RuleTypeEnum classification;

    @ApiModelProperty(value = "监控ID", example = "7124")
    private String monitorId;

    @ApiModelProperty(value = "规则名称", example = "最近2分钟服务请求检测（1分钟不出事件）")
    private String ruleName;

    @ApiModelProperty(value = "标签", example = "{ \"k8sNamespace\": \"test\", \"service\": \"service-d::k8s\" }")
    private JSONObject tags;

    @ApiModelProperty(value = "事件触发对象", example = "{ \"k8sNamespace\": \"test\", \"service\": \"service-d::k8s\" }")
    private JSONObject trigger;

    @ApiModelProperty(value = "收敛策略id列表")
    private Set<Integer> policyIds = Collections.synchronizedSet(ConcurrentHashMap.newKeySet());
}