package com.databuff.common.utils;

import org.apache.commons.lang3.StringUtils;
import java.util.HashMap;
import java.util.Map;

public class HostParseUtil {
    public static Map<String, Integer> hosts(String hostStr, int defaultPort){
        Map<String, Integer> hosts = new HashMap<String, Integer>();
        if (StringUtils.isBlank(hostStr)) {
            throw new RuntimeException("host string is blank!");
        }

        String str = hostStr;
        String[] tokens = str.split(",");
        for (String token : tokens) {
            String host;
            int port = defaultPort;
            if (token.contains(":")) {
                String[] parts = token.split(":");
                host = StringUtils.trim(parts[0]);
                port = Integer.parseInt(StringUtils.trim(parts[1]));
                if (StringUtils.isNotEmpty(host)) {
                    hosts.put(host, port);
                } else {
                    throw new RuntimeException(token + " is not valid Host:PORT configuration!");
                }
            } else {
                host = StringUtils.trim(token);
                if (StringUtils.isNotEmpty(host)) {
                    hosts.put(host, port);
                } else {
                    throw new RuntimeException(token + " is not valid Host configuration!");
                }
            }
        }
        if (hosts.size() < 1) {
            throw new RuntimeException(hostStr + " contains no valid hosts configuration!");
        }
        return hosts;
    }
}
