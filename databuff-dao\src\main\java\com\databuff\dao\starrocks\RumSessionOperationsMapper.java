package com.databuff.dao.starrocks;

import com.databuff.entity.rum.web.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface RumSessionOperationsMapper {
    SessionDetailDto getSessionDetail(@Param("sessionId") Long sessionId,
                                      @Param("request") SessionDetailRequest request,
                                      @Param("hourRange") Map<String, Integer> hourRange);

    List<InteractionDto> getInteractionData(@Param("request") InteractionDataRequest request);

    List<ExceptionDto> getExceptionData(@Param("request") InteractionDataRequest request);

}
