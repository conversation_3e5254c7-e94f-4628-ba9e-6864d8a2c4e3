package com.databuff.common.utils.wechat;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.databuff.common.constants.Constant;
import com.databuff.common.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.List;
import java.util.regex.Matcher;

import static com.databuff.common.constants.Constant.CONTENT_TYPE_JSON;

/**
 * @author:TianMing
 * @date: 2021/12/15
 * @time: 17:14
 */
@Slf4j
public class WeChatUtils {

    private static final String ACCESS_TOKEN_URL = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=%s&corpsecret=%s";

    private static final String USER_INFO_URL = "https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token=%s&userid=%s";

    private static final String USER_ID_URL = "https://qyapi.weixin.qq.com/cgi-bin/auth/getuserinfo?access_token=%s&code=%s";

    private static final String USER_ID_BY_MOBILE_URL = "https://qyapi.weixin.qq.com/cgi-bin/user/getuserid?access_token=%s";

    private static final String APP_INFO_URL = "https://qyapi.weixin.qq.com/cgi-bin/agent/get?access_token=%s&agentid=%s";

    private static final String APP_SEND_MSG_URL = "https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=%s";

    private static final String MSGTYPE = "msgtype";
    private static final String MARKDOWN = "markdown";
    private static final String ERRCODE = "errcode";
    private static final String ERRMSG= "errmsg";
    private static final String CONTENT= "content";


    /**
     * 获取ACCESS_TOKEN_URL返回结果中键值对中accessToken键的值
     *
     * @param corpid 应用组织编号   corpsecret 应用秘钥
     */
    public static String getToken(String corpid, String corpsecret){
        String url = String.format(ACCESS_TOKEN_URL,corpid,corpsecret);
        String accessToken ;
        try {
            String resp = toAuth(url);

            JSONObject res = JSON.parseObject(resp);

            if (res.getInteger(ERRCODE)!=0){
                log.error("WeChat getToken errcode:{},errmsg:{}",res.getInteger(ERRCODE),res.getString(ERRMSG));
                throw new CustomException(res.getString(ERRMSG));
            }
            accessToken = res.getString("access_token");
        } catch (IOException e) {
            log.error("WeChat getToken error:",e);
            throw new CustomException(e.getMessage());
        }
        return accessToken;
    }

    public static JSONObject getUserIdByMobile(String accessToken, String mobile) {
        String url = String.format(USER_ID_BY_MOBILE_URL,accessToken);
        JSONObject res ;
        JSONObject postData = new JSONObject();
        postData.put("mobile",mobile);
        try {
            String resp = post(Constant.Notify.CHARSET, Constant.CONTENT_TYPE_JSON, url, postData.toJSONString());

            res = JSON.parseObject(resp);

            if (res.getInteger(ERRCODE)!=0){
                log.error("WeChat getUserIdByMobile errcode:{},errmsg:{}",res.getInteger(ERRCODE),res.getString(ERRMSG));
                throw new CustomException(res.getString(ERRMSG));
            }

        } catch (IOException e) {
            log.error("WeChat getUserIdByMobile error:",e);
            throw new CustomException(e.getMessage());
        }

        return res;
    }
    /**
     * 获取USER_INFO_URL返回结果中键值对中accessToken键的值
     *
     * @param accessToken
     * @param userid 成员UserID。对应管理端的帐号，企业内必须唯一。不区分大小写，长度为1~64个字节
     */
    public static JSONObject getUserInfo(String accessToken, String userid) {
        String url = String.format(USER_INFO_URL,accessToken,userid);
        JSONObject res ;
        try {
            String resp = toAuth(url);

            res = JSON.parseObject(resp);

            if (res.getInteger(ERRCODE)!=0){
                log.error("WeChat getToken errcode:{},errmsg:{}",res.getInteger(ERRCODE),res.getString(ERRMSG));
                throw new CustomException(res.getString(ERRMSG));
            }

        } catch (IOException e) {
            log.error("WeChat getToken error:",e);
            throw new CustomException(e.getMessage());
        }

        return res;
    }

    /**
     * 获取USER_INFO_URL返回结果中键值对中accessToken键的值
     *
     * @param accessToken 调用接口凭证
     * @param code 通过成员授权获取到的code，最大为512字节。每次成员授权带上的code将不一样，code只能使用一次，5分钟未被使用自动过期。
     */
    public static String getUserId(String accessToken, String code) {
        String url = String.format(USER_ID_URL,accessToken,code);
        try {
            String resp = toAuth(url);
            JSONObject res = JSON.parseObject(resp);
            /**
             * {
             *    "errcode": 0,
             *    "errmsg": "ok",
             *    "userid":"USERID"
             * }
             */
            if (res.getInteger(ERRCODE)!=0){
                log.error("WeChat getUserId errcode:{},errmsg:{}",res.getInteger(ERRCODE),res.getString(ERRMSG));
                throw new CustomException(res.getString(ERRMSG));
            }
            return res.getString("userid");
        } catch (IOException e) {
            log.error("WeChat getUserId error:",e);
            throw new CustomException(e.getMessage());
        }
    }


    /**
     * 获取USER_INFO_URL返回结果中键值对中access_token键的值
     *
     * @param accessToken
     * @param agentid 应用id
     * @return
     * {
     *    "errcode": 0,
     *    "errmsg": "ok",
     *    "agentid": 1000005,
     *    "name": "HR助手",
     *    "square_logo_url":  "https://p.qlogo.cn/bizmail/FicwmI50icF8GH9ib7rUAYR5kicLTgP265naVFQKnleqSlRhiaBx7QA9u7Q/0",
     *    "description": "HR服务与员工自助平台",
     *    "allow_userinfos": {
     *        "user": [
     *              {"userid": "zhangshan"},
     *              {"userid": "lisi"}
     *        ]
     *     },
     *    "allow_partys": {
     *        "partyid": [1]
     *     },
     *    "allow_tags": {
     *        "tagid": [1,2,3]
     *     },
     *    "close": 0,
     *    "redirect_domain": "open.work.weixin.qq.com",
     *    "report_location_flag": 0,
     *    "isreportenter": 0,
     *    "home_url": "https://open.work.weixin.qq.com"
     * }
     */
    public static JSONObject getAppInfo(String accessToken, Long agentid) {
        String url = String.format(APP_INFO_URL,accessToken,agentid);
        JSONObject res ;
        try {
            String resp = toAuth(url);

            res = JSON.parseObject(resp);

            if (res.getInteger(ERRCODE)!=0){
                log.error("WeChat getAppInfo errcode:{},errmsg:{}",res.getInteger(ERRCODE),res.getString(ERRMSG));
                throw new CustomException(res.getString(ERRMSG));
            }

        } catch (IOException e) {
            log.error("WeChat getAppInfo error:",e);
            throw new CustomException(e.getMessage());
        }

        return res;
    }

    /**
     * 应用发送消息通知
     * @param config
     * @param accessToken
     * @param text 通知内容
     * @param textType text和markdown
     * @param useridList
     * @return
     */
    public static JSONObject sendNoticeMessage(NotifyWeChatConfig config , String accessToken, String text, String textType, List<String> useridList) {
        JSONObject postData = new JSONObject();
        postData.put("agentid",config.getWechatAgentId());
        if(useridList!=null && !useridList.isEmpty()){
            //成员ID列表（消息接收者，多个接收者用‘|’分隔，最多支持1000个）。特殊情况：指定为@all，则向关注该企业应用的全部成员发送
            StringBuilder sb = new StringBuilder();
            for (String u : useridList){
                sb.append(u);
                sb.append("|");
            }
            postData.put("touser", sb.substring(0, sb.length() - 1));
        }
        if (MARKDOWN.equals(textType)){
            postData.put(MSGTYPE,MARKDOWN);
            JSONObject markdown = new JSONObject();
            markdown.put(CONTENT,Matcher.quoteReplacement(text));
            postData.put(MARKDOWN,markdown);
        }else{
            postData.put(MSGTYPE,"text");
            JSONObject textContent = new JSONObject();
            textContent.put(CONTENT,text);
            postData.put("text",textContent);
        }
        String url = String.format(APP_SEND_MSG_URL,accessToken);
        JSONObject res ;
        try {
            String resp = post(Constant.Notify.CHARSET, CONTENT_TYPE_JSON, url, postData.toJSONString());

            res = JSON.parseObject(resp);

            if (res.getInteger(ERRCODE)!=0){
                log.error("WeChat sendNoticeMessage errcode:{},errmsg:{}",res.getInteger(ERRCODE),res.getString(ERRMSG));
                throw new CustomException(res.getString(ERRMSG));
            }

        } catch (IOException e) {
            log.error("WeChat sendNoticeMessage error:",e);
            throw new CustomException(e.getMessage());
        }

        return res;
    }

    /**
     * 机器人发送消息
     * @param webhookUrl
     * @param text 通知内容
     * @param textType  text和markdown
     * @param phoneList @用户手机列表
     * @param useridList @用户id列表
     * @return
     */
    public static JSONObject sendRobotMessage(String webhookUrl ,String text, String textType, List<String> phoneList, List<String> useridList) {
        JSONObject postData = new JSONObject();
        if (MARKDOWN.equals(textType)){
            postData.put(MSGTYPE,MARKDOWN);
            JSONObject markdown = new JSONObject();
            markdown.put(CONTENT, Matcher.quoteReplacement(text));
            postData.put(MARKDOWN,markdown);
        }else{
            postData.put(MSGTYPE,"text");
            JSONObject textContent = new JSONObject();
            textContent.put(CONTENT,text);
            postData.put("text",textContent);
        }
        if(phoneList!=null && !phoneList.isEmpty()) {
            postData.put("mentioned_mobile_list",phoneList);
        }
        if(useridList!=null && !useridList.isEmpty()) {
            postData.put("mentioned_list",useridList);
        }
        JSONObject res ;
        try {
            String resp = post(Constant.Notify.CHARSET, CONTENT_TYPE_JSON, webhookUrl, postData.toJSONString());

            res = JSON.parseObject(resp);

            if (res.getInteger(ERRCODE)!=0){
                log.error("WeChat sendRobotMessage errcode:{},errmsg:{}",res.getInteger(ERRCODE),res.getString(ERRMSG));
                throw new CustomException(res.getString(ERRMSG));
            }

        } catch (IOException e) {
            log.error("WeChat sendRobotMessage error:",e);
            throw new CustomException(e.getMessage());
        }

        return res;
    }

        /**
         * 微信授权请求，GET类型，获取授权响应，用于其他方法截取token
         *
         * @param getUrl
         * @return String 授权响应内容
         * @throws IOException
         */
    public static String toAuth(String getUrl) throws IOException {

        HttpGet httpGet = new HttpGet(getUrl);
        String resp;
        try (
                CloseableHttpClient httpClient = HttpClients.createDefault();
                CloseableHttpResponse response = httpClient.execute(httpGet);
                ){
            HttpEntity entity = response.getEntity();
            resp = EntityUtils.toString(entity, "utf-8");
            EntityUtils.consume(entity);
        }
        return resp;
    }

    /**
     * @param charset 消息编码    ，contentType 消息体内容类型，
     * @param url     微信消息发送请求地址，data为post数据
     * @return String
     * @Title 创建微信发送请求post实体
     */
    public static String post(String charset, String contentType, String url,
                       String data) throws IOException {
        HttpPost httpPost = new HttpPost(url);
        httpPost.setHeader(Constant.CONTENT_TYPE, contentType);
        httpPost.setEntity(new StringEntity(data, charset));
        String resp;
        try (
                CloseableHttpClient httpclient = HttpClients.createDefault();
                CloseableHttpResponse response = httpclient.execute(httpPost);
        ){
            HttpEntity entity = response.getEntity();
            resp = EntityUtils.toString(entity, charset);
            EntityUtils.consume(entity);
        } catch (IOException e) {
            log.error("WeChat post error:", e);
            throw e;
        }
        return resp;
    }
}
