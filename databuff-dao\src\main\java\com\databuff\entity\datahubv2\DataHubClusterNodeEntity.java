package com.databuff.entity.datahubv2;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.databuff.handler.TimestampToLongTypeHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2023/8/14 14:05
 * 集群节点实体
 */
@TableName(value = "dc_databuff_datahub_cluster_nodes", autoResultMap = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataHubClusterNodeEntity {

    @ApiModelProperty("节点id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("集群id")
    @TableField(value = "cluster_id")
    private Integer clusterId;

    @ApiModelProperty("节点名称")
    @TableField(value = "node_name")
    private String nodeName;

    @ApiModelProperty("节点状态")
    @TableField(value = "status")
    private String status;

    @ApiModelProperty("其他信息")
    @TableField(value = "other_info")
    private String otherInfo;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", typeHandler = TimestampToLongTypeHandler.class)
    private Long createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", typeHandler = TimestampToLongTypeHandler.class)
    private Long updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getClusterId() {
        return clusterId;
    }

    public void setClusterId(Integer clusterId) {
        this.clusterId = clusterId;
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getOtherInfo() {
        return otherInfo;
    }

    public void setOtherInfo(String otherInfo) {
        this.otherInfo = otherInfo;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /* Getter 和 Setter 方法 */

}
