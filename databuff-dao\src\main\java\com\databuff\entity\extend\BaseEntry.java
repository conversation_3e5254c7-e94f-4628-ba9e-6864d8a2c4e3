package com.databuff.entity.extend;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 基础Entry类
 * @Package com.databuff.webapp.admin.model
 * @company: dacheng
 * @Author: zlh
 * @CreateDate: 2020/5/26
 */
@Getter
@Setter
@ApiModel(value = "基础实体类，查询实体类的父类")
public class BaseEntry {
    /**查询条件*/
    @ApiModelProperty(value = "查询条件")
    private String query;
    @ApiModelProperty(value = "Mysql分页查询页码",example = "1")
    private Integer pageNum;
    @ApiModelProperty(value = "Mysql分页查询数",example = "1")
    private Integer pageSize;

    /**页码*/
    @ApiModelProperty(value = "ES分页查询偏移量",example = "1")
    private int offset;
    /**每页显示数量*/
    @ApiModelProperty(value = "ES分页查询数",example = "1")
    private int size;
    @ApiModelProperty(value = "查询开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date startTime;
    @ApiModelProperty(value = "查询结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date endTime;
    @ApiModelProperty(value = "es直方图时间间隔")
    private String interval;

    private Date createTime;
    private Date updateTime;
}
