package com.databuff.dao.starrocks;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.databuff.entity.dto.DCAlarmDto;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Mapper
@Repository
public interface DcAlarmMapper extends BaseMapper<DCAlarmDto> {

    int updateAlarmById(DCAlarmDto dcAlarmDto);

    List<DCAlarmDto> selectBatchIds(@Param("ids") List<String> ids,
                                    @Param("sortField") String sortField,
                                    @Param("sortOrder") String sortOrder);

    List<DCAlarmDto> selectAlarmIdsByTime(@Param("startTime") long startTime,
                                      @Param("endTime") long endTime,
                                      @Param("apiKey") String apiKey,
                                      @Param("createDt") String createDt);
}
