package com.databuff.common.utils;

import org.apache.commons.codec.digest.DigestUtils;

public class SpuidUtil {

    public static String getPodSpuid(String apiKey, String clusterId, String podName) {
        return getSpuid(apiKey, clusterId, "41", podName);
    }

    public static String getProcessSpuid(String hostname, String pname) {
        return DigestUtils.md5Hex(hostname + "_" + pname);
    }

    public static String getSpuid(String apiKey, String clusterId, String type, String podName) {
        return DigestUtils.md5Hex(apiKey + "|" + clusterId + "|" + type + "|" + podName);
    }
}
