package com.databuff.common.threadLocal;

/**
 * NowTimeThreadLocal类用于管理当前线程的时间。
 * 它使用ThreadLocal来存储和检索当前线程的时间值。
 */
public class NowTimeThreadLocal {
    private static final ThreadLocal<Long> nowTime = new ThreadLocal<>();

    /**
     * 设置当前线程的时间。
     * 注意：使用setNowTime方法设置的时间值只对当前线程有效。在其他线程中无法访问。
     * 如果需要在多个线程中共享时间值，请使用其他方式。
     * 在使用结束后，应该调用remove方法来清除当前线程的时间值。
     *
     * @param time 要设置的时间值（毫秒）。
     */
    public static void setNowTime(Long time) {
        nowTime.set(time);
    }

    /**
     * 获取当前线程的时间。
     *
     * @return 当前线程的时间值（毫秒），如果未设置则返回null。
     */
    public static Long getNowTime() {
        final Long now = nowTime.get();
        if (now == null) {
            return System.currentTimeMillis();
        }
        return now;
    }

    /**
     * 移除当前线程的时间值。
     */
    public static void remove() {
        nowTime.remove();
    }
}