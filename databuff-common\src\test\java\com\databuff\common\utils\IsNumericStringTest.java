package com.databuff.common.utils;

import org.junit.Assert;
import org.junit.Test;

import java.lang.reflect.Method;

/**
 * Comprehensive test cases for the isNumericString method in CalculatorUtil
 */
public class IsNumericStringTest {

    /**
     * Uses reflection to access the private isNumericString method
     */
    private boolean callIsNumericString(String input) throws Exception {
        Method method = CalculatorUtil.class.getDeclaredMethod("isNumericString", String.class);
        method.setAccessible(true);
        return (boolean) method.invoke(null, input);
    }

    @Test
    public void testValidNumericStrings() throws Exception {
        // Positive integers
        Assert.assertTrue("Simple integer should be valid", callIsNumericString("000.123"));
        Assert.assertTrue("Zero should be valid", callIsNumericString("0"));
        Assert.assertTrue("Single digit should be valid", callIsNumericString("5"));

        // Negative integers
        Assert.assertTrue("Negative integer should be valid", callIsNumericString("-123"));
        Assert.assertTrue("Negative single digit should be valid", callIsNumericString("-5"));

        // Decimal numbers
        Assert.assertTrue("Simple decimal should be valid", callIsNumericString("123.45"));
        Assert.assertTrue("Decimal with leading zero should be valid", callIsNumericString("0.45"));
        Assert.assertTrue("Negative decimal should be valid", callIsNumericString("-123.45"));
        Assert.assertTrue("Decimal with trailing zeros should be valid", callIsNumericString("123.450"));
        Assert.assertTrue("Integer with decimal point and zero should be valid", callIsNumericString("123.0"));
    }

    @Test
    public void testInvalidNumericStrings() throws Exception {
        // Null and empty
        Assert.assertFalse("Null should be invalid", callIsNumericString(null));
        Assert.assertFalse("Empty string should be invalid", callIsNumericString(""));

        // Non-numeric characters
        Assert.assertFalse("Alphabetic characters should be invalid", callIsNumericString("abc"));
        Assert.assertFalse("Alphanumeric should be invalid", callIsNumericString("123abc"));
        Assert.assertFalse("Special characters should be invalid", callIsNumericString("123$"));

        // Invalid number formats
        Assert.assertFalse("Multiple decimal points should be invalid", callIsNumericString("123.45.67"));
        Assert.assertFalse("Only decimal point should be invalid", callIsNumericString("."));
        Assert.assertFalse("Starting with decimal point should be invalid", callIsNumericString(".45"));
        Assert.assertFalse("Ending with decimal point should be invalid", callIsNumericString("123."));
        Assert.assertFalse("Only negative sign should be invalid", callIsNumericString("-"));
        Assert.assertFalse("Negative sign without digits should be invalid", callIsNumericString("-.45"));

        // Spaces
        Assert.assertFalse("Leading space should be invalid", callIsNumericString(" 123"));
        Assert.assertFalse("Trailing space should be invalid", callIsNumericString("123 "));
        Assert.assertFalse("Space in the middle should be invalid", callIsNumericString("123 45"));

        // Scientific notation
        Assert.assertFalse("Scientific notation should be invalid", callIsNumericString("1.23e4"));
        Assert.assertFalse("Scientific notation with capital E should be invalid", callIsNumericString("1.23E4"));

        // Other number formats
        Assert.assertFalse("Hexadecimal should be invalid", callIsNumericString("0xFF"));
        Assert.assertFalse("Octal should be invalid", callIsNumericString("0123"));
        Assert.assertFalse("Binary should be invalid", callIsNumericString("0b1010"));

        // Thousands separators
        Assert.assertFalse("Comma as thousands separator should be invalid", callIsNumericString("1,234"));
        Assert.assertFalse("Underscore as thousands separator should be invalid", callIsNumericString("1_234"));

        // Plus sign
        Assert.assertFalse("Leading plus sign should be invalid", callIsNumericString("+123"));

        // Double negative
        Assert.assertFalse("Double negative sign should be invalid", callIsNumericString("--123"));
    }

    @Test
    public void testEdgeCases() throws Exception {
        // Very large numbers
        Assert.assertTrue("Very large integer should be valid",
                callIsNumericString("12345678901234567890"));
        Assert.assertTrue("Very large decimal should be valid",
                callIsNumericString("12345678901234567890.12345678901234567890"));

        // Very small decimals
        Assert.assertTrue("Very small decimal should be valid",
                callIsNumericString("0.00000000000000000001"));

        // Leading zeros
        Assert.assertTrue("Integer with leading zeros should be valid",
                callIsNumericString("00123"));
        Assert.assertTrue("Negative integer with leading zeros should be valid",
                callIsNumericString("-00123"));

        // Interesting cases related to NumberUtils.createNumber
        Assert.assertTrue("'08' should be valid for isNumericString", callIsNumericString("08"));
        Assert.assertTrue("'09' should be valid for isNumericString", callIsNumericString("09"));

        // These would fail with NumberUtils.createNumber but should pass with isNumericString
        Assert.assertTrue("Leading zeros with 8 should be valid", callIsNumericString("0800"));
        Assert.assertTrue("Leading zeros with 9 should be valid", callIsNumericString("0900"));

        // Additional edge cases
        Assert.assertTrue("Single zero should be valid", callIsNumericString("0"));
        Assert.assertTrue("Negative number with leading zero should be valid", callIsNumericString("-08"));
        Assert.assertTrue("Decimal with leading zero should be valid", callIsNumericString("0.8"));
        Assert.assertTrue("Number with leading zero and trailing decimal zero should be valid",
                callIsNumericString("08.0"));

        // Invalid formats that might be confused with valid numbers
        Assert.assertFalse("Hexadecimal format should be invalid", callIsNumericString("0x8"));
        Assert.assertFalse("Number with trailing letter should be invalid", callIsNumericString("0a"));
        Assert.assertFalse("Number with trailing letter should be invalid", callIsNumericString("08a"));

        // More edge cases
        Assert.assertTrue("Zero with decimal should be valid", callIsNumericString("0.0"));
        Assert.assertTrue("Negative zero should be valid", callIsNumericString("-0"));
        Assert.assertTrue("Negative zero with decimal should be valid", callIsNumericString("-0.0"));
        Assert.assertTrue("Number with multiple leading zeros should be valid", callIsNumericString("000123"));
        Assert.assertTrue("Decimal with multiple leading and trailing zeros should be valid",
                callIsNumericString("000.000"));
    }

    @Test
    public void testComparisonWithNumberUtils() throws Exception {
        // Cases where isNumericString and NumberUtils.isCreatable behave differently

        // These pass with isNumericString but would fail with NumberUtils.createNumber
        Assert.assertTrue("'08' should be valid for isNumericString", callIsNumericString("08"));
        Assert.assertTrue("'09' should be valid for isNumericString", callIsNumericString("09"));

        // These fail with isNumericString but would pass with NumberUtils.isCreatable
        Assert.assertFalse("Scientific notation should be invalid for isNumericString",
                callIsNumericString("1.23e4"));
        Assert.assertFalse("Hexadecimal should be invalid for isNumericString",
                callIsNumericString("0xFF"));
        Assert.assertFalse("Starting with decimal point should be invalid for isNumericString",
                callIsNumericString(".45"));
    }
}
