package com.databuff.dao.mysql;

import com.databuff.entity.rum.mysql.RumResourceConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface RumResourceConfigMapper {
    
    // Insert new resource config
    void insert(RumResourceConfig config);
    
    // Get config by appId
    RumResourceConfig getByAppId(@Param("appId") Integer appId);
    
    // Update config
    int updateByAppId(RumResourceConfig config);

    List<RumResourceConfig> findAll();


}
