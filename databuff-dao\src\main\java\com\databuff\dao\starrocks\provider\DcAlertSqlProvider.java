package com.databuff.dao.starrocks.provider;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.databuff.metric.util.StarRocksMetricConstants;

import java.util.*;

public class DcAlertSqlProvider {

    /**
     * 生成告警趋势 SQL（按聚合表 dc_alarm）
     * <p>
     * 1. 仅用 createDt 做时间过滤，保证命中分区，避免在大表上跑函数。
     * 2. timestamp 只给 time_slice() 用，不再参加 WHERE。
     * 3. 所有分支（无分组 / 有分组；有 interval / 无 interval）统一走
     * “createDt BETWEEN … AND …” 的分区谓词。
     */
    public String getMetricTrendFromAggregate(Map<String, Object> params) {
        Integer               interval       = (Integer) params.get("interval");
        @SuppressWarnings("unchecked")
        Collection<String>    groupByFields  = (Collection<String>) params.get("groupByFields");
        Wrapper<?>            queryWrapper   = (Wrapper<?>) params.get(Constants.WRAPPER);

        // ───── 动态 groupBy / select 字段 ─────────────────────────────
        List<String> fieldsList = groupByFields != null
                ? new ArrayList<>(groupByFields)
                : Collections.emptyList();

        // SELECT … AS xxx
        List<String> selectGroupByFields = new ArrayList<>();
        // GROUP BY … 里用的表达式
        List<String> groupByExpr         = new ArrayList<>();
        for (String f : fieldsList) {
            String cast = "CAST(tags->'$." + f + "' AS VARCHAR)";
            selectGroupByFields.add(cast + " AS " + f);
            groupByExpr.add(cast);
        }

        // ───── 统一 WHERE 片段（wrapper + 分区过滤 + IS NOT NULL） ─────
        // 1) MyBatis Wrapper 生成的条件
        String baseWhere = (queryWrapper == null || queryWrapper.getSqlSegment() == null)
                ? "1 = 1"
                : queryWrapper.getSqlSegment().replaceFirst("(?i)^\\s*WHERE\\s+", "");

        // 2) 命中分区：createDt 取整日期即可
        String dateFilter =
                "createDt >= DATE(#{fromTime}) " +
                        "AND createDt <= DATE(#{toTime})";

        // 3) 过滤掉分组字段为 NULL 的行
        StringBuilder notNullFilter = new StringBuilder();
        for (String f : fieldsList) {
            notNullFilter.append(" AND CAST(tags->'$." )
                    .append(f)
                    .append("' AS VARCHAR) IS NOT NULL");
        }

        String whereClause = "WHERE " + baseWhere + " AND " + dateFilter + notNullFilter;

        // ───── 下面保持你原本的四大分支结构 ──────────────────────────
        StringBuilder sql = new StringBuilder();

        if (!fieldsList.isEmpty()) {
            // ============================= 有分组 ==============================
            if (interval != null && interval > 0) {
                // —— 有分组 + 有 interval（趋势）——
                sql.append("SELECT UNIX_TIMESTAMP(time_slice(FROM_UNIXTIME(CAST(timestamp/1000 AS BIGINT)), ")
                        .append("INTERVAL ").append(interval).append(" SECOND)) * 1000 AS timeBucket");
                // 追加 group by 字段
                for (String sel : selectGroupByFields) {
                    sql.append(", ").append(sel);
                }
                sql.append(", COUNT(DISTINCT id) AS cnt ")
                        .append("FROM dc_alarm ")
                        .append(whereClause)
                        .append(" GROUP BY timeBucket");
                for (String g : groupByExpr) {
                    sql.append(", ").append(g);
                }
                sql.append(" ORDER BY timeBucket ASC, cnt DESC");

            } else {
                // —— 有分组 + 无 interval（一次聚合）——
                sql.append("SELECT ");
                sql.append(String.join(", ", selectGroupByFields));
                sql.append(", COUNT(DISTINCT id) AS cnt ")
                        .append("FROM dc_alarm ")
                        .append(whereClause)
                        .append(" GROUP BY ")
                        .append(String.join(", ", groupByExpr))
                        .append(" ORDER BY cnt DESC");
            }

        } else {
            // =========================== 无分组 ==============================
            if (interval != null && interval > 0) {
                // —— 无分组 + 有 interval（趋势）——
                sql.append("SELECT UNIX_TIMESTAMP(time_slice(FROM_UNIXTIME(CAST(timestamp/1000 AS BIGINT)), ")
                        .append("INTERVAL ").append(interval).append(" SECOND)) * 1000 AS timeBucket, ")
                        .append("COUNT(DISTINCT id) AS cnt ")
                        .append("FROM dc_alarm ")
                        .append(whereClause)
                        .append(" GROUP BY timeBucket ")
                        .append("ORDER BY timeBucket ASC");
            } else {
                // —— 无分组 + 无 interval（一次聚合）——
                sql.append("SELECT COUNT(DISTINCT id) AS cnt ")
                        .append("FROM dc_alarm ")
                        .append(whereClause);
            }
        }

        return sql.toString();
    }


    /**
     * 【新增方法】
     * 构建基于UNION的SQL，用于查询多个维度的JSON数组字符串。
     */
    public String getTagValuesAsStringArrays(Map<String, Object> params) {
        Wrapper<?> queryWrapper = (Wrapper<?>) params.get(Constants.WRAPPER);
        List<String> dimensionsToQuery = new ArrayList<>(StarRocksMetricConstants.getAllowedGroupByFields());

        StringBuilder finalSql = new StringBuilder();

        for (int i = 0; i < dimensionsToQuery.size(); i++) {
            String tagField = dimensionsToQuery.get(i);

            StringBuilder subQuery = new StringBuilder();
            subQuery.append("SELECT '").append(tagField).append("' AS tag_type, ");
            // 使用 -> 操作符和 CAST
            subQuery.append("CAST(tags->'$.\"").append(tagField).append("\"' AS VARCHAR) AS tag_value ");
            subQuery.append("FROM dc_alarm ");
            subQuery.append("WHERE ");
            // 附加MyBatis Wrapper生成的过滤条件
            subQuery.append(queryWrapper.getSqlSegment().replaceFirst("(?i)WHERE", ""));
            // 附加时间过滤条件
            subQuery.append(" AND timestamp >= #{fromTimeMillis} AND timestamp < #{toTimeMillis}");
            // 附加JSON key存在性检查和非空数组检查
            subQuery.append(" AND json_length(tags, '$.\"").append(tagField).append("\"') > 0 ");
            subQuery.append("GROUP BY tag_value");

            finalSql.append(subQuery);

            if (i < dimensionsToQuery.size() - 1) {
                finalSql.append(" UNION ");
            }
        }

        finalSql.append(" ORDER BY tag_type, tag_value");
        finalSql.append(" LIMIT 6000"); // 保持安全限制

        return finalSql.toString();
    }

}