
package com.databuff.dao.mysql;

import com.databuff.entity.rum.mysql.RumJsError;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface RumJsErrorMapper {
    List<String> getFixedAndIgnoredErrorMessages();


    List<RumJsError> getJsErrorList(@Param("appId") int appId,
                                    @Param("handler") String handler,
                                    @Param("status") String status,
                                    @Param("errorMessage") String errorMessage,
                                    @Param("offset") int offset,
                                    @Param("pageSize") int pageSize,
                                    @Param("sortField") String sortField,
                                    @Param("sortOrder") Sort.Direction sortOrder);

    List<RumJsError> getAllErrorStatuses(@Param("appId") int appId);

    List<RumJsError> getJsErrorListByAppIdAndErrorMessage(@Param("appId") int appId, @Param("errorMessage") String errorMessage);

    List<RumJsError> getJsErrorListByAppIdAndHandler(@Param("appId") int appId, @Param("handler") String handler, @Param("errorMessage") String errorMessage, @Param("offset") int offset, @Param("pageSize") int pageSize);

    long countJsErrorsByAppIdAndHandler(@Param("appId") int appId, @Param("handler") String handler, @Param("errorMessage") String errorMessage);

    List<RumJsError> getJsErrorListByAppIdAndNotUnfixedStatus(@Param("appId") int appId, @Param("errorMessage") String errorMessage);

    List<RumJsError> getJsErrorListByAppIdAndStatus(@Param("appId") int appId, @Param("status") String status, @Param("errorMessage") String errorMessage, @Param("offset") int offset, @Param("pageSize") int pageSize);

    long countJsErrorsByAppIdAndStatus(@Param("appId") int appId, @Param("status") String status, @Param("errorMessage") String errorMessage);

    List<RumJsError> getJsErrorListByAppIdHandlerAndStatus(@Param("appId") int appId, @Param("handler") String handler, @Param("status") String status, @Param("errorMessage") String errorMessage, @Param("offset") int offset, @Param("pageSize") int pageSize);

    long countJsErrorsByAppIdHandlerAndStatus(@Param("appId") int appId, @Param("handler") String handler, @Param("status") String status, @Param("errorMessage") String errorMessage);



    void updateHandler(@Param("appId") int appId, @Param("errorMessage") String errorMessage, @Param("handler") String handler);

    void updateStatus(@Param("appId") int appId, @Param("errorMessage") String errorMessage, @Param("status") String status);


    String getHandlerByErrorMessage(@Param("appId") int appId, @Param("errorMessage") String errorMessage);


    List<String> getAllHandlers(@Param("appId") int appId);


}
