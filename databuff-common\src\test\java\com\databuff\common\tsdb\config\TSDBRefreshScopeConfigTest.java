package com.databuff.common.tsdb.config;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class TSDBRefreshScopeConfigTest {

    @InjectMocks
    private TSDBRefreshScopeConfig tsdbConfig;

    @Test
    public void testDefaultSqlPrintEnabled() {
        // 默认情况下，SQL打印应该是启用的
        assertTrue("SQL打印默认应该启用", tsdbConfig.isSqlPrintEnabled());
    }
}
