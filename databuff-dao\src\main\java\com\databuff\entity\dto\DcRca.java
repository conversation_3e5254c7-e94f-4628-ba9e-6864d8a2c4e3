package com.databuff.entity.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @author:TianMing
 * @date: 2024/1/6
 * @time: 19:32
 */
@Data
public class DcRca implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * API密钥
     */
    private String apiKey;

    /**
     * 问题id
     */
    private String id;

    /**
     * 事件ID
     */
    private String eventId;

    /**
     * 监控规则ID
     */
    private Integer monitorId;

    /**
     * 错误信息
     */
    private String errorInfo;

    /**
     * 开始触发时间
     */
    private Long startTriggerTime;

    /**
     * 故障树
     */
    private String faultTree;

    /**
     * 问题组id
     */
    private Integer groupId;

    /**
     * 描述
     */
    private String description;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     *
     */
    private Long duration;

    /**
     * 故障根因
     */
    private String rca;

    /**
     * 现象id
     */
    private String checkName;
    /**
     * 现象ids
     */
    private String checkNames;
    /**
     * 服务ID
     */
    private String serviceId;
    /**
     * 服务实例
     */
    private String serviceInstance;
    /**
     * 第一次触发时间
     */
    private Long firstTragerTime;

    /**
     * 受影响基础设施
     */
    private String influencedInfrastructure;

    /**
     * 受影响服务
     */
    private String influencedService;

    /**
     *
     */
    private String message;

    /**
     * 生成时间
     */
    private Date timestamp;

    /**
     * 分组
     */
    private String group;

    public DcRca() {}
}