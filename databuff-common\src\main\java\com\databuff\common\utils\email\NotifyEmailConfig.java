package com.databuff.common.utils.email;

import lombok.Data;

import java.io.Serializable;

@Data
public class NotifyEmailConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;

    /**
     * api_key
     */
    private String apiKey;

    /**
     * 是否开启，默认1开启，0不开启
     */
    private Integer tenantEnable;
    /**
     * 是否开启，默认1开启，0不开启
     */
    private Integer enable;

    /**
     * dingtalk，wechat，sms，mail
     */
    private String notifyType;

    /**
     * 邮件传输协议服务器地址
     */
    private String mailHost;

    /**
     * 邮件传输协议服务器端口
     */
    private Integer mailPort;

    /**
     * 是否开启ssl 0未开1开
     */
    private int mailSsl;

    /**
     * 邮件发送人账号
     */
    private String mailSender;

    /**
     * 邮件发送人昵称
     */
    private String mailNick;
    /**
     * 邮件发送人账号密码
     */
    private String mailSenderPwd;

    /**
     * 邮件授权码
     */
    private String mailSecretCode;

    private Long totalNum;

    private Long theMonthNum;
}
