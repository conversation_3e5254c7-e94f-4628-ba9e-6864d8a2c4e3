package com.databuff.common.utils.sms;

import com.alibaba.fastjson.JSONObject;

/**
 * @author:TianMing
 * @date: 2021/12/21
 * @time: 14:40
 */
public class SmsSendTest {


    public static void main(String[] args) {
        //AccessKey ID LTAI5tGw7S9uaWvAhjBfoD5a
        //AccessKey Secret ******************************

        String accessKeyId = "LTAI5tGw7S9uaWvAhjBfoD5a";
        String accessKeySecret = "******************************";
        String signName = "乘云Databuff";
        String templateCode = "SMS_230655794";
        JSONObject content = new JSONObject();
        content.put("time","22");
        content.put("host","node105");
        try {
            com.aliyun.dysmsapi20170525.models.SendSmsResponse sendSmsResponse = SmsUtil.sendSms(signName,templateCode,content.toJSONString(),"15824145588",accessKeyId,accessKeySecret);
            System.out.println(sendSmsResponse.getBody().getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
