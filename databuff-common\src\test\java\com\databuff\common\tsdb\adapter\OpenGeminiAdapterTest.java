package com.databuff.common.tsdb.adapter;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.metric.dto.Query;
import com.databuff.common.tsdb.builder.QueryBuilder;
import com.databuff.common.tsdb.model.*;
import com.databuff.common.tsdb.pool.TSDBConnectPool;
import com.databuff.common.tsdb.wrapper.OpenGeminiWrapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class OpenGeminiAdapterTest {

    @Mock
    private TSDBConnectPool mockPool;

    @Mock
    private OpenGeminiWrapper mockOpenGeminiWrapper;

    private OpenGeminiAdapter adapter;

    @BeforeEach
    void setUp() throws Exception {
        lenient().when(mockPool.getTSDBClient()).thenReturn(mockOpenGeminiWrapper);
        adapter = new OpenGeminiAdapter(mockPool);
    }

    /**
     * 测试标准查询的buildFullSql方法
     */
    @Test
    void testBuildFullSqlStandard() {
        // 创建标准查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("testdb");
        builder.setMeasurement("cpu");
        builder.addAgg(new Aggregation(AggFun.MEAN, "usage"));
        builder.setInterval(60);
        builder.setIntervalUnit("s");
        builder.setQueryType(QueryBuilder.QueryType.STANDARD);

        // 执行测试
        String sql = adapter.buildFullSql(builder);

        // 验证结果
        assertNotNull(sql);
        assertTrue(sql.contains("SELECT"));
        assertTrue(sql.contains("mean(\"usage\")"));
        assertTrue(sql.contains("FROM \"cpu\""));
        assertTrue(sql.contains("GROUP BY time(60s)"));

        System.out.println("标准查询SQL: " + sql);
    }

    /**
     * 测试非time字段排序查询的buildFullSql方法
     */
    @Test
    void testBuildFullSqlNonTimeOrder() {
        // 创建非time字段排序查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("testdb");
        builder.setMeasurement("cpu");
        builder.addAgg(new Aggregation(AggFun.MEAN, "usage"));
        builder.setInterval(60);
        builder.setIntervalUnit("s");
        builder.setOrderBy(Arrays.asList(new OrderBy("usage", false))); // 按usage降序排序
        builder.setQueryType(QueryBuilder.QueryType.NON_TIME_ONLY_ORDER);

        // 执行测试
        String sql = adapter.buildFullSql(builder);

        // 验证结果
        assertNotNull(sql);
        assertTrue(sql.contains("SELECT"));
        assertTrue(sql.contains("mean(\"usage\")"));
        assertTrue(sql.contains("FROM \"cpu\""));
        assertTrue(sql.contains("GROUP BY time(60s)"));

        System.out.println("非time字段排序查询SQL: " + sql);
    }

    /**
     * 测试混合排序查询的buildFullSql方法
     */
    @Test
    void testBuildFullSqlMixedOrder() {
        // 创建混合排序查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("testdb");
        builder.setMeasurement("cpu");
        builder.addAgg(new Aggregation(AggFun.MEAN, "usage"));
        builder.setInterval(60);
        builder.setIntervalUnit("s");
        builder.setOrderBy(Arrays.asList(
                new OrderBy("usage", false), // 按usage降序排序
                new OrderBy("time", true)    // 按time升序排序
        ));
        builder.setQueryType(QueryBuilder.QueryType.MIXED_ORDER);

        // 执行测试
        String sql = adapter.buildFullSql(builder);

        // 验证结果
        assertNotNull(sql);
        assertTrue(sql.contains("SELECT"));
        assertTrue(sql.contains("mean(\"usage\")"));
        assertTrue(sql.contains("FROM \"cpu\""));

        System.out.println("混合排序查询SQL: " + sql);
    }

    /**
     * 测试用于生成topn(max(xx))时序图的查询方法
     */
    @Test
    void testBuildFullSqlTimeOrder() {
        // 创建用于生成topn时序图的查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("testdb");
        builder.setMeasurement("cpu");
        builder.addAgg(new Aggregation(AggFun.MEAN, "usage"));
        builder.setInterval(60);
        builder.setIntervalUnit("s");
        builder.setOrderBy(Arrays.asList(new OrderBy("time", false))); // 按time降序排序
        builder.setQueryType(QueryBuilder.QueryType.TOPN_MAX_TIME);
        builder.addGroupBy("host"); // 添加分组字段，用于生成多条时间序列

        // 模拟第一步查询的结果
        TSDBResultSet mockResultSet = createMockTimeOrderQueryResult();

        // 设置模拟行为
        when(mockOpenGeminiWrapper.query(any(), any(), any())).thenReturn(mockResultSet);

        // 执行测试
        String sql = adapter.buildFullSql(builder);

        // 验证结果
        assertNotNull(sql);
        assertTrue(sql.contains("SELECT"));
        assertTrue(sql.contains("mean(\"usage\")"));
        assertTrue(sql.contains("FROM \"cpu\""));
        assertTrue(sql.contains("GROUP BY \"host\",time(60s)"));

        System.out.println("topn时序图查询SQL: " + sql);

        // 验证查询方法被调用
        verify(mockOpenGeminiWrapper).query(any(), any(), any());
    }

    /**
     * 测试传入null构建器的情况
     */
    @Test
    void testBuildFullSqlWithNullBuilder() {
        // 执行测试并验证异常
        Exception exception = assertThrows(IllegalArgumentException.class, () -> {
            adapter.buildFullSql(null);
        });

        // 验证异常消息
        assertTrue(exception.getMessage().contains("QueryBuilder不能为空"));
    }

    /**
     * 创建模拟的time字段排序查询结果
     */
    private TSDBResultSet createMockTimeOrderQueryResult() {
        TSDBResultSet resultSet = new TSDBResultSet();
        List<TSDBResult> results = new ArrayList<>();
        TSDBResult result = new TSDBResult();

        List<TSDBSeries> seriesList = new ArrayList<>();
        TSDBSeries series = new TSDBSeries();

        // 设置列名
        List<String> columns = Arrays.asList("time", "mean", "host");
        series.setColumns(columns);

        // 设置值
        List<List<Object>> values = new ArrayList<>();
        values.add(Arrays.asList(1609459200000L, 0.85, "server01"));
        values.add(Arrays.asList(1609459260000L, 0.75, "server02"));
        series.setValues(values);

        seriesList.add(series);
        result.setSeries(seriesList);
        results.add(result);
        resultSet.setResults(results);

        return resultSet;
    }

    @Test
    void testCreateDatabase() throws Exception {
        // 准备测试数据
        TSDBDatabaseInfo databaseInfo = new TSDBDatabaseInfo();
        databaseInfo.setDatabaseName("testdb");
        databaseInfo.setUserName("user");
        databaseInfo.setPassword("password");
        databaseInfo.setShard(2);
        databaseInfo.setReplication(1);
        databaseInfo.setKeepDay(30);
        databaseInfo.setInterval(10);

        // 设置模拟行为
        when(mockOpenGeminiWrapper.databaseExists("testdb")).thenReturn(false);
        doNothing().when(mockOpenGeminiWrapper).createDatabase(
                eq("testdb"), eq("user"), eq("password"), eq(2), eq(1), eq(30), eq(10)
        );

        // 执行测试
        boolean result = adapter.createDatabase(mockPool.getTSDBClient(),databaseInfo);

        // 验证结果
        assertTrue(result);
        verify(mockOpenGeminiWrapper).databaseExists("testdb");
        verify(mockOpenGeminiWrapper).createDatabase(
                eq("testdb"), eq("user"), eq("password"), eq(2), eq(1), eq(30), eq(10)
        );
    }

    @Test
    void testWritePoints() throws Exception {
        // 准备测试数据
        String databaseName = "testdb";
        List<TSDBPoint> points = new ArrayList<>();

        Map<String, String> tags = new HashMap<>();
        tags.put("host", "server01");

        Map<String, Object> fields = new HashMap<>();
        fields.put("usage", 0.85);

        TSDBPoint point = new TSDBPoint("cpu", System.currentTimeMillis(), tags, fields);
        points.add(point);

        // 执行测试
        boolean result = adapter.writePoints(mockPool.getTSDBClient(),databaseName, points);

        // 验证结果
        assertTrue(result);
        verify(mockOpenGeminiWrapper).write(eq(databaseName), eq(points));
    }

    @Test
    void testExecuteQuery() throws Exception {
        // 准备测试数据
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("testdb");
        builder.setMeasurement("cpu");
        builder.addAgg(new Aggregation(AggFun.MEAN, "usage"));
        builder.setInterval(60);
        builder.setIntervalUnit("s");

        // 执行测试
        adapter.executeQuery(mockPool.getTSDBClient(), builder, AggFun.MEAN, null, null, new JSONObject());

        // 验证结果
        verify(mockOpenGeminiWrapper).query(any(), any(), eq(builder));
    }

    @Test
    void testExecuteNestedQuery() throws Exception {
        // 准备测试数据 - 模拟错误SQL中的场景
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("kubernetes.memory");
        builder.addAgg(new Aggregation(AggFun.LAST, "usage"));
        builder.setInterval(60);
        builder.setIntervalUnit("s");

        // 添加WHERE条件
        builder.addWhere(new Where("time", WhereOp.GTE, 1745767740000L)); // 这会被转换为纳秒
        builder.addWhere(new Where("time", WhereOp.LT, 1745767800000L));
        builder.addWhere(new Where("clusterId", WhereOp.IN, "ce495c38-4a3d-48fb-8c5a-ec7f7d7232e8"));

        // 添加GROUP BY
        builder.addGroupBy("clusterId");

        // 捕获SQL查询
        org.mockito.ArgumentCaptor<Query> queryCaptor = org.mockito.ArgumentCaptor.forClass(Query.class);

        // 执行嵌套查询测试 - 使用三层嵌套聚合
        adapter.executeQuery(mockPool.getTSDBClient(), builder, AggFun.LAST, AggFun.SUM, AggFun.LAST, new JSONObject());

        // 验证结果
        verify(mockOpenGeminiWrapper).query(queryCaptor.capture(), any(), builder);

        // 获取生成的SQL
        String generatedSql = queryCaptor.getValue().getCommand();
        System.out.println("Generated SQL: " + generatedSql);

        // 验证SQL结构
        // 1. 验证WHERE条件在最内层查询中
        assertTrue(generatedSql.contains("FROM \"kubernetes.memory\" WHERE"), "WHERE条件应该紧跟在最内层FROM后面");

        // 2. 验证没有多余的FROM子句
        int fromCount = countOccurrences(generatedSql, "FROM");
        assertEquals(3, fromCount, "应该只有3个FROM子句");

        // 3. 验证GROUP BY在正确的位置
        assertTrue(generatedSql.contains("GROUP BY \"clusterId\",time(60s)"), "外层查询应该包含GROUP BY clusterId和时间间隔");
        assertTrue(generatedSql.contains("GROUP BY time(60s)"), "内层查询应该包含GROUP BY时间间隔");

        // 4. 验证聚合函数嵌套正确
        assertTrue(generatedSql.contains("last(middle_value)"), "外层应该使用last聚合函数");
        assertTrue(generatedSql.contains("sum(inner_value) AS middle_value"), "中间层应该使用sum聚合函数");
        assertTrue(generatedSql.contains("last(\"usage\") AS inner_value"), "内层应该使用last聚合函数");
    }

    @Test
    void testExecuteMultipleNestedQueries() throws Exception {
        // 准备测试数据
        QueryBuilder builder = new QueryBuilder();
        builder.addAgg(Aggregation.of(AggFun.LAST, "usage"));
        builder.addAgg(new Aggregation(AggFun.LAST, "usage"));
        builder.setDatabaseName("NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4_infrastructure");
        builder.setMeasurement("kubernetes.memory");
        builder.setInterval(60);
        builder.setIntervalUnit("s");

        // 添加WHERE条件
        builder.addWhere(new Where("time", WhereOp.GTE, 1745767740000L)); // 这会被转换为纳秒
        builder.addWhere(new Where("time", WhereOp.LT, 1745767800000L));
        builder.addWhere(new Where("clusterId", WhereOp.IN, "ce495c38-4a3d-48fb-8c5a-ec7f7d7232e8"));

        // 添加GROUP BY
        builder.addGroupBy("clusterId");

        // 捕获SQL查询
        org.mockito.ArgumentCaptor<Query> queryCaptor = org.mockito.ArgumentCaptor.forClass(Query.class);

        // 测试不同的聚合函数组合

        // 1. 三层嵌套查询 - tsAgg + valAgg + topAgg
        adapter.executeQuery(mockPool.getTSDBClient(), builder, AggFun.LAST, AggFun.SUM, AggFun.LAST, new JSONObject());
        verify(mockOpenGeminiWrapper).query(queryCaptor.capture(), any(TimeUnit.class), eq(builder));
        String sql1 = queryCaptor.getValue().getCommand();
        System.out.println("\nThree-level nested query: " + sql1);
        assertTrue(sql1.contains("last(middle_value)"), "外层应该使用last聚合函数");
        assertTrue(sql1.contains("sum(inner_value) AS middle_value"), "中间层应该使用sum聚合函数");
        assertTrue(sql1.contains("last(\"usage\") AS inner_value"), "内层应该使用last聚合函数");
        assertTrue(sql1.contains("FROM \"kubernetes.memory\" WHERE"), "WHERE条件应该紧跟在最内层FROM后面");

        // 验证外层查询中也有时间范围条件
        assertTrue(sql1.contains(") AS \"usage\" WHERE time >= 1745767740000000000 AND time < 1745767800000000000"), "外层查询应该包含时间范围条件");

        // 2. 两层嵌套查询 - valAgg + topAgg
        reset(mockOpenGeminiWrapper);
        when(mockPool.getTSDBClient()).thenReturn(mockOpenGeminiWrapper);
        adapter.executeQuery(mockPool.getTSDBClient(), builder, null, AggFun.SUM, AggFun.LAST, new JSONObject());
        verify(mockOpenGeminiWrapper).query(queryCaptor.capture(), any(TimeUnit.class), eq(builder));
        String sql2 = queryCaptor.getValue().getCommand();
        System.out.println("\nTwo-level nested query (valAgg + topAgg): " + sql2);
        assertTrue(sql2.contains("FROM \"kubernetes.memory\" WHERE"), "WHERE条件应该紧跟在FROM后面");
        assertTrue(sql2.contains(") AS \"usage\" WHERE time >= 1745767740000000000 AND time < 1745767800000000000"), "外层查询应该包含时间范围条件");

        // 3. 两层嵌套查询 - tsAgg + topAgg
        reset(mockOpenGeminiWrapper);
        when(mockPool.getTSDBClient()).thenReturn(mockOpenGeminiWrapper);
        adapter.executeQuery(mockPool.getTSDBClient(), builder, AggFun.LAST, null, AggFun.LAST, new JSONObject());
        verify(mockOpenGeminiWrapper).query(queryCaptor.capture(), any(TimeUnit.class), eq(builder));
        String sql3 = queryCaptor.getValue().getCommand();
        System.out.println("\nTwo-level nested query (tsAgg + topAgg): " + sql3);
        assertTrue(sql3.contains("FROM \"kubernetes.memory\" WHERE"), "WHERE条件应该紧跟在FROM后面");
        assertTrue(sql3.contains(") AS \"usage\" WHERE time >= 1745767740000000000 AND time < 1745767800000000000"), "外层查询应该包含时间范围条件");

        // 4. 两层嵌套查询 - tsAgg + valAgg
        reset(mockOpenGeminiWrapper);
        when(mockPool.getTSDBClient()).thenReturn(mockOpenGeminiWrapper);
        adapter.executeQuery(mockPool.getTSDBClient(), builder, AggFun.LAST, AggFun.SUM, null, new JSONObject());
        verify(mockOpenGeminiWrapper).query(queryCaptor.capture(), any(TimeUnit.class), eq(builder));
        String sql4 = queryCaptor.getValue().getCommand();
        System.out.println("\nTwo-level nested query (tsAgg + valAgg): " + sql4);
        assertTrue(sql4.contains("FROM \"kubernetes.memory\" WHERE"), "WHERE条件应该紧跟在FROM后面");
        assertTrue(sql4.contains(") AS \"usage\" WHERE time >= 1745767740000000000 AND time < 1745767800000000000"), "外层查询应该包含时间范围条件");

        // 5. 单层查询 - 只有 topAgg
        reset(mockOpenGeminiWrapper);
        when(mockPool.getTSDBClient()).thenReturn(mockOpenGeminiWrapper);
        adapter.executeQuery(mockPool.getTSDBClient(), builder, null, null, AggFun.LAST, new JSONObject());
        verify(mockOpenGeminiWrapper).query(queryCaptor.capture(), any(TimeUnit.class), eq(builder));
        String sql5 = queryCaptor.getValue().getCommand();
        System.out.println("\nSingle-level query (topAgg only): " + sql5);
        assertTrue(sql5.contains("FROM \"kubernetes.memory\" WHERE"), "WHERE条件应该紧跟在FROM后面");
        // 单层查询不需要在外层添加时间范围条件

        // 6. 单层查询 - 只有 tsAgg
        reset(mockOpenGeminiWrapper);
        when(mockPool.getTSDBClient()).thenReturn(mockOpenGeminiWrapper);
        adapter.executeQuery(mockPool.getTSDBClient(), builder, AggFun.LAST, null, null, new JSONObject());
        verify(mockOpenGeminiWrapper).query(queryCaptor.capture(), any(TimeUnit.class), eq(builder));
        String sql6 = queryCaptor.getValue().getCommand();
        System.out.println("\nSingle-level query (tsAgg only): " + sql6);
        assertTrue(sql6.contains("FROM \"kubernetes.memory\" WHERE"), "WHERE条件应该紧跟在FROM后面");
        // 单层查询不需要在外层添加时间范围条件
    }

    @Test
    void testClose() throws Exception {
        // Act
        adapter.close();

        // Assert
        verify(mockPool).close();
    }

    @Test
    void testAutoCloseable() {
        // Test that the adapter implements AutoCloseable and can be used with try-with-resources
        assertDoesNotThrow(() -> {
            try (OpenGeminiAdapter testAdapter = new OpenGeminiAdapter(mockPool)) {
                // Do something with the adapter
                assertNotNull(testAdapter);
            }
        });

        verify(mockPool).close();
    }

    /**
     * 计算字符串中子字符串出现的次数
     *
     * @param str 要搜索的字符串
     * @param substr 要计算出现次数的子字符串
     * @return 子字符串出现的次数
     */
    private int countOccurrences(String str, String substr) {
        if (str == null || substr == null || str.isEmpty() || substr.isEmpty()) {
            return 0;
        }

        int count = 0;
        int idx = 0;
        while ((idx = str.indexOf(substr, idx)) != -1) {
            count++;
            idx += substr.length();
        }
        return count;
    }

    @Test
    void testExecuteShowTagValues() throws Exception {
        // 准备测试数据
        String query = "SHOW TAG VALUES FROM \"cpu\" WITH KEY = \"host\"";
        String databaseName = "testdb";

        // 创建模拟的查询结果
        TSDBResultSet mockResultSet = new TSDBResultSet();
        List<TSDBResult> results = new ArrayList<>();
        TSDBResult result = new TSDBResult();
        result.setQueryType("SHOW TAG VALUES");

        List<TSDBSeries> seriesList = new ArrayList<>();
        TSDBSeries series = new TSDBSeries();
        series.setName("cpu");
        series.setColumns(Arrays.asList("key", "value"));

        List<List<Object>> values = new ArrayList<>();
        values.add(Arrays.asList("host", "server01"));
        values.add(Arrays.asList("host", "server02"));
        values.add(Arrays.asList("host", "server03"));
        series.setValues(values);

        seriesList.add(series);
        result.setSeries(seriesList);
        results.add(result);
        mockResultSet.setResults(results);

        // 设置模拟行为
        when(mockOpenGeminiWrapper.query(any(Query.class), eq(TimeUnit.MILLISECONDS), any()))
            .thenReturn(mockResultSet);

        // 执行测试
        Map<String, Set<String>> tagValues = adapter.executeShowTagValues(mockPool.getTSDBClient(), query, databaseName);

        // 验证结果
        assertNotNull(tagValues);
        assertEquals(1, tagValues.size());
        assertTrue(tagValues.containsKey("host"));
        assertEquals(3, tagValues.get("host").size());
        assertTrue(tagValues.get("host").contains("server01"));
        assertTrue(tagValues.get("host").contains("server02"));
        assertTrue(tagValues.get("host").contains("server03"));

        // 验证方法调用
        verify(mockOpenGeminiWrapper).query(any(Query.class), eq(TimeUnit.MILLISECONDS), any());
    }

    @Test
    void testExecuteShowTagValuesWithError() throws Exception {
        // 准备测试数据
        String query = "SHOW TAG VALUES FROM \"cpu\" WITH KEY = \"host\"";
        String databaseName = "testdb";

        // 创建带有错误的模拟查询结果
        TSDBResultSet mockResultSet = new TSDBResultSet();
        mockResultSet.setError("Database not found");

        // 设置模拟行为
        when(mockOpenGeminiWrapper.query(any(Query.class), eq(TimeUnit.MILLISECONDS), any()))
            .thenReturn(mockResultSet);

        // 执行测试
        Map<String, Set<String>> tagValues = adapter.executeShowTagValues(mockPool.getTSDBClient(), query, databaseName);

        // 验证结果 - 应该返回空映射
        assertNotNull(tagValues);
        assertTrue(tagValues.isEmpty());

        // 验证方法调用
        verify(mockOpenGeminiWrapper).query(any(Query.class), eq(TimeUnit.MILLISECONDS), any());
    }

    @Test
    void testExecuteShowTagValuesWithException() throws Exception {
        // 准备测试数据
        String query = "SHOW TAG VALUES FROM \"cpu\" WITH KEY = \"host\"";
        String databaseName = "testdb";

        // 设置模拟行为 - 抛出异常
        when(mockOpenGeminiWrapper.query(any(Query.class), eq(TimeUnit.MILLISECONDS), any()))
            .thenThrow(new RuntimeException("Connection failed"));

        // 执行测试并验证异常
        Exception exception = assertThrows(RuntimeException.class, () -> {
            adapter.executeShowTagValues(mockPool.getTSDBClient(), query, databaseName);
        });

        // 验证异常消息
        assertTrue(exception.getMessage().contains("查询标签值失败"));

        // 验证方法调用
        verify(mockOpenGeminiWrapper).query(any(Query.class), eq(TimeUnit.MILLISECONDS), any());
    }
}
