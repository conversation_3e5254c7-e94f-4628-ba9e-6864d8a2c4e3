package com.databuff.common.metric;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.Feature;
import com.databuff.common.tsdb.TSDBOperateUtil;
import com.databuff.common.tsdb.model.TSDBPoint;
import com.databuff.common.utils.ProcUtil;
import com.databuff.common.utils.TSDBPointUtil;
import com.databuff.moredb.proto.Common;
import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.common.Attributes;
import io.opentelemetry.sdk.common.CompletableResultCode;
import io.opentelemetry.sdk.metrics.InstrumentType;
import io.opentelemetry.sdk.metrics.data.*;
import io.opentelemetry.sdk.metrics.export.MetricExporter;
import io.opentelemetry.semconv.resource.attributes.ResourceAttributes;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.management.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

public class TSDBMetricExporter implements MetricExporter {

    private static final Logger LOGGER = LoggerFactory.getLogger(TSDBMetricExporter.class);
    public static final String DATABASE_NAME_DATABUFF_SYSTEM = "databuff_system";

    private final AtomicBoolean isShutdown = new AtomicBoolean();
    private TSDBOperateUtil tsdbOperateUtil;
    private Map<String, Long> gcCountInfos = new ConcurrentHashMap<>();
    private Map<String, Long> gcTimeInfos = new ConcurrentHashMap<>();


    public TSDBMetricExporter(TSDBOperateUtil tsdbOperateUtil) {
        this.tsdbOperateUtil = tsdbOperateUtil;
        JSON.DEFAULT_PARSER_FEATURE &= ~Feature.UseBigDecimal.getMask();
    }

    @Override
    public CompletableResultCode export(Collection<MetricData> metrics) {
        if (metrics == null || metrics.size() == 0) {
            return CompletableResultCode.ofFailure();
        }
        String serviceName = null;
        long currentTime = 0;
        List<TSDBPoint> pointList = new ArrayList<>();
        for (MetricData metricData : metrics) {
            Data<?> data = metricData.getData();
            serviceName = metricData.getResource().getAttribute(ResourceAttributes.SERVICE_NAME);
            for (PointData pointData : data.getPoints()) {
                try {
                    currentTime = pointData.getEpochNanos() / 1000000;
                    TSDBPoint tsdbPoint = TSDBPointUtil.buildPoint(serviceName, metricData.getName(), currentTime);
                    Map<String, String> tags = tsdbPoint.getTags();
                    if (tags == null) {
                        tags = new HashMap<>();
                    }
                    Map<String, Object> fields = tsdbPoint.getFields();
                    if (fields == null) {
                        fields = new HashMap<>();
                    }
                    Map<String, Common.FieldType> fieldTypes = tsdbPoint.getFieldTypes();
                    if (fieldTypes == null) {
                        fieldTypes = new HashMap<>();
                    }
                    Attributes attributes = pointData.getAttributes();
                    if (attributes != null) {
                        Map<AttributeKey<?>, Object> map = attributes.asMap();
                        for (Map.Entry<AttributeKey<?>, Object> entry : map.entrySet()) {
                            if (entry == null || entry.getKey() == null || entry.getValue() == null) {
                                continue;
                            }
                            if (tags!=null){
                                tags.put(entry.getKey().getKey(), entry.getValue().toString());
                            }else {
                                tags = new HashMap<>();
                                tags.put(entry.getKey().getKey(), entry.getValue().toString());
                            }
                        }
                    }

                    if (pointData instanceof LongPointData) {
                        long value = ((LongPointData) pointData).getValue();
                        fields.put("value", value);
                        fieldTypes.put("value", Common.FieldType.SUM);
                    } else if (pointData instanceof DoublePointData) {
                        double value = ((DoublePointData) pointData).getValue();
                        fields.put("value", value);
                        fieldTypes.put("value", Common.FieldType.GAUGE);
                    } else if (pointData instanceof HistogramPointData) {
                        long count = ((HistogramPointData) pointData).getCount();
                        long sum = ((Double) ((HistogramPointData) pointData).getSum()).longValue();
                        long min = ((Double) ((HistogramPointData) pointData).getMin()).longValue();
                        long max = ((Double) ((HistogramPointData) pointData).getMax()).longValue();
                        fields.put("count", count);
                        fields.put("sum", sum);
                        fields.put("min", min);
                        fields.put("max", max);
                        fieldTypes.put("count", Common.FieldType.SUM);
                        fieldTypes.put("sum", Common.FieldType.SUM);
                        fieldTypes.put("min", Common.FieldType.MIN);
                        fieldTypes.put("max", Common.FieldType.MAX);
                    } else {
                        continue;
                    }
                    pointList.add(tsdbPoint);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new RuntimeException(e);
                }
            }
        }
        tsdbOperateUtil.writePoints(DATABASE_NAME_DATABUFF_SYSTEM, pointList);
        sendMemoryMetric(serviceName, currentTime);
        sendGcMetric(serviceName, currentTime);
        sendThreadMetric(serviceName, currentTime);
        sendCpuMetric(serviceName, currentTime);
        sendThreadPoolMetric(serviceName, currentTime);
        return CompletableResultCode.ofSuccess();
    }

    private void sendThreadMetric(String serviceName, long currentTime) {
        ThreadMXBean threadMXBean = ManagementFactory.getThreadMXBean();
        int threadCount = threadMXBean.getThreadCount();
        List<TSDBPoint> pointList = new ArrayList<>();
        pointList.add(TSDBPointUtil.buildGaugePoint(serviceName, "system.thread", currentTime, "count", threadCount));
        tsdbOperateUtil.writePoints(DATABASE_NAME_DATABUFF_SYSTEM, pointList);
    }

    private void sendThreadPoolMetric(String serviceName, long currentTime) {
        List<TSDBPoint> pointList = TSDBThreadPoolMetric.getInstance().collectorMetric(serviceName, currentTime);
        tsdbOperateUtil.writePoints(DATABASE_NAME_DATABUFF_SYSTEM, pointList);
    }

    private void sendGcMetric(String serviceName, long currentTime) {
        List<TSDBPoint> pointList = new ArrayList<>();
        List<GarbageCollectorMXBean> beans = ManagementFactory.getGarbageCollectorMXBeans();
        for (GarbageCollectorMXBean mxBean : beans) {
            String name = mxBean.getName().replaceAll(" ", "");
            long currentGcCount = mxBean.getCollectionCount();
            long currentGcTime = mxBean.getCollectionTime();
            long oldGcCount = gcCountInfos.getOrDefault(name, 0L);
            long oldGcTime = gcTimeInfos.getOrDefault(name, 0L);
            long deltaGcCount = currentGcCount - oldGcCount;
            long deltaGcTime = currentGcTime - oldGcTime;
            gcCountInfos.put(name, currentGcCount);
            gcTimeInfos.put(name, currentGcTime);
            Map<String, String> tags = new HashMap<>();
            tags.put("name", name);
            pointList.add(TSDBPointUtil.buildSumPoint(serviceName, "system.gc.count", currentTime, "value", deltaGcCount, tags));
            pointList.add(TSDBPointUtil.buildSumPoint(serviceName, "system.gc.time", currentTime, "value", deltaGcTime, tags));
        }
        tsdbOperateUtil.writePoints(DATABASE_NAME_DATABUFF_SYSTEM, pointList);
    }

    private void sendMemoryMetric(String serviceName, long currentTime) {
        List<TSDBPoint> pointList = new ArrayList<>();
        MemoryMXBean bean = ManagementFactory.getMemoryMXBean();
        MemoryUsage heapMemoryUsage = bean.getHeapMemoryUsage();
        pointList.add(TSDBPointUtil.buildGaugePoint(serviceName, "system.memory.heap", currentTime, "max", heapMemoryUsage.getMax()));
        pointList.add(TSDBPointUtil.buildGaugePoint(serviceName, "system.memory.heap", currentTime, "used", heapMemoryUsage.getUsed()));
        tsdbOperateUtil.writePoints(DATABASE_NAME_DATABUFF_SYSTEM, pointList);
    }

    private void sendCpuMetric(String serviceName, long currentTime) {
        double cpuUsage = ProcUtil.calCurrentCpuUsage();
        if (cpuUsage > 0 && cpuUsage < 1000) {
            List<TSDBPoint> pointList = new ArrayList<>();
            pointList.add(TSDBPointUtil.buildDoubleGaugePoint(serviceName, "system.cpu", currentTime, "usage", cpuUsage));
            tsdbOperateUtil.writePoints(DATABASE_NAME_DATABUFF_SYSTEM, pointList);
        }
    }

    @Override
    public CompletableResultCode flush() {
        return CompletableResultCode.ofSuccess();
    }

    @SneakyThrows
    @Override
    public CompletableResultCode shutdown() {
        if (isShutdown.compareAndSet(false, true)) {
            LOGGER.info("Calling shutdown()");
            tsdbOperateUtil.close();
        } else {
            LOGGER.info("MoreDBMetricExporter already shut down");
        }
        return CompletableResultCode.ofSuccess();
    }

    @Override
    public AggregationTemporality getAggregationTemporality(InstrumentType instrumentType) {
        return AggregationTemporality.DELTA;
    }
}
