package com.databuff.entity.profiling.v3;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description = "线程堆栈信息")
public class ThreadStack {

    @ApiModelProperty(value = "摘要ID", example = "摘要ID")
    private String excerptId;

    @ApiModelProperty(value = "资源名称", example = "GET /methodB0")
    private String resource;

    @ApiModelProperty(value = "资源类型", example = "http")
    private String rsType;

    @ApiModelProperty(value = "采样时间", example = "1726817489025237000")
    private long sampleTime;

    @ApiModelProperty(value = "本次采集样例数", example = "5")
    private int samples;

    @ApiModelProperty(value = "线程ID", example = "158723")
    private int tid;

    @ApiModelProperty(value = "traceId", example = "trace_001")
    @JSONField(name = "traceId")
    private String traceId;

    @ApiModelProperty(value = "instanceAlloc", example = "分配内存大小(字节)")
    @JSONField(name = "instanceAlloc")
    private Integer instanceAlloc;

    @ApiModelProperty(value = "frameTypeIds", example = "11111111111111111111111111111111111111111111111111111111111111111111111111")
    @JSONField(name = "frameTypeIds")
    private String frameTypeIds;

    @ApiModelProperty(value = "线程名称", example = "158723")
    private String tName;

    @ApiModelProperty(value = "业务方法对应堆栈索引", example = "10")
    private Integer rsFlagIndex;

    @ApiModelProperty(value = "堆栈跟踪信息", example = "[\"com.zaxxer.hikari.pool.ProxyConnection.close\", \"org.springframework.jdbc.datasource.DataSourceUtils.doCloseConnection\", \"java.util.concurrent.ThreadPoolExecutor$Worker.run\", \"org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run\"]")
    private List<String> stackTraces;

    @ApiModelProperty(value = "eventType", example = "cpu/alloc/wall")
    @JSONField(name = "eventType")
    private String eventType;

    @ApiModelProperty(value = "操作", example = "http-server/mysql.query/spring.handler/redis.query")
    @JSONField(name = "onOperation")
    private String onOperation;

    /**
     * 栈顶的是热点方法
     * @return
     */
    public String getHotspotMethod() {
        if (stackTraces == null || stackTraces.isEmpty()) {
            return null;
        }
        return stackTraces.get(0);
    }

    /**
     * 获取堆栈层级 = 栈的深度
     * @return
     */
    public Integer getLayer() {
        if (stackTraces == null || stackTraces.isEmpty()) {
            return 1;
        }
        return stackTraces.size();
    }
}