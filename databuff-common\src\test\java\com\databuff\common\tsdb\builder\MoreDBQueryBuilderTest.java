package com.databuff.common.tsdb.builder;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MoreDBQueryBuilder的测试类，主要测试特殊字符转义功能
 */
public class MoreDBQueryBuilderTest {

    private MoreDBQueryBuilder queryBuilder;

    @BeforeEach
    void setUp() {
        // 创建一个MoreDBQueryBuilder实例用于测试
        queryBuilder = new MoreDBQueryBuilder(new QueryBuilder());
    }

    /**
     * 测试空输入和null输入的情况
     */
    @ParameterizedTest
    @NullAndEmptySource
    void testEscapeSpecialCharWithNullOrEmpty(String input) {
        assertEquals(input, queryBuilder.escapeSpecialChar(input), "空字符串或null应该原样返回");
    }

    /**
     * 测试不包含特殊字符的普通字符串
     */
    @Test
    void testEscapeSpecialCharWithNormalString() {
        String input = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        assertEquals(input, queryBuilder.escapeSpecialChar(input), "不包含特殊字符的字符串应该原样返回");
    }

    /**
     * 测试包含单个特殊字符的情况
     */
    @ParameterizedTest
    @MethodSource("singleSpecialCharProvider")
    void testEscapeSpecialCharWithSingleSpecialChar(String input, String expected) {
        assertEquals(expected, queryBuilder.escapeSpecialChar(input), "单个特殊字符应该被正确转义");
    }

    /**
     * 提供单个特殊字符的测试数据
     */
    static Stream<Arguments> singleSpecialCharProvider() {
        return Stream.of(
                Arguments.of("\\", "\\\\"),       // 反斜杠
                Arguments.of("\"", "\\\""),       // 双引号
                Arguments.of("!", "\\!"),         // 感叹号
                Arguments.of("#", "\\#"),         // 井号
                Arguments.of("$", "\\$"),         // 美元符号
                Arguments.of("%", "\\%"),         // 百分号
                Arguments.of("&", "\\&"),         // 和号
                Arguments.of("'", "\\'"),         // 单引号
                Arguments.of("(", "\\("),         // 左括号
                Arguments.of(")", "\\)"),         // 右括号
                Arguments.of("*", "\\*"),         // 星号
                Arguments.of("+", "\\+"),         // 加号
                Arguments.of(",", "\\,"),         // 逗号
                Arguments.of("-", "\\-"),         // 减号/连字符
                Arguments.of(".", "\\."),         // 点号
                Arguments.of("/", "\\/"),         // 斜杠
                Arguments.of(":", "\\:"),         // 冒号
                Arguments.of(";", "\\;"),         // 分号
                Arguments.of("<", "\\<"),         // 小于号
                Arguments.of("=", "\\="),         // 等号
                Arguments.of(">", "\\>"),         // 大于号
                Arguments.of("?", "\\?"),         // 问号
                Arguments.of("@", "\\@"),         // 艾特符号
                Arguments.of("[", "\\["),         // 左方括号
                Arguments.of("]", "\\]"),         // 右方括号
                Arguments.of("^", "\\^"),         // 脱字符
                Arguments.of("_", "\\_"),         // 下划线
                Arguments.of("`", "\\`"),         // 反引号
                Arguments.of("{", "\\{"),         // 左花括号
                Arguments.of("|", "\\|"),         // 竖线
                Arguments.of("}", "\\}"),         // 右花括号
                Arguments.of("~", "\\~")          // 波浪号
        );
    }

    /**
     * 测试包含多个特殊字符的复杂字符串
     */
    @ParameterizedTest
    @MethodSource("complexStringProvider")
    void testEscapeSpecialCharWithComplexString(String input, String expected) {
        assertEquals(expected, queryBuilder.escapeSpecialChar(input), "包含多个特殊字符的字符串应该被正确转义");
    }

    /**
     * 提供包含多个特殊字符的复杂字符串测试数据
     */
    static Stream<Arguments> complexStringProvider() {
        return Stream.of(
                // SQL查询示例
                Arguments.of(
                        "SELECT * FROM table WHERE id = 123",
                        "SELECT \\* FROM table WHERE id \\= 123"
                ),
                // 正则表达式示例
                Arguments.of(
                        "value[0-9]+",
                        "value\\[0\\-9\\]\\+"
                ),
                Arguments.of(
                        "(a|b)?.{1,3}",
                        "\\(a\\|b\\)\\?\\.\\{1\\,3\\}"
                ),
                // 包含多种特殊字符的混合字符串
                Arguments.of(
                        "<EMAIL> (John Doe) [ID: 123+456]",
                        "user\\@example\\.com \\(John Doe\\) \\[ID\\: 123\\+456\\]"
                ),
                // 包含URL的字符串
                Arguments.of(
                        "https://example.com/path?query=value&param=123",
                        "https\\:\\/\\/example\\.com\\/path\\?query\\=value\\&param\\=123"
                ),
                // 包含JSON格式的字符串
                Arguments.of(
                        "{\"name\":\"John\",\"age\":30,\"city\":\"New York\"}",
                        "\\{\\\"name\\\"\\:\\\"John\\\"\\,\\\"age\\\"\\:30\\,\\\"city\\\"\\:\\\"New York\\\"\\}"
                ),
                // 包含数学表达式的字符串
                Arguments.of(
                        "f(x) = x^2 + 2*x + 1",
                        "f\\(x\\) \\= x\\^2 \\+ 2\\*x \\+ 1"
                ),
                // 包含Shell命令的字符串
                Arguments.of(
                        "grep -E \"pattern\" file.txt | sort > output.txt",
                        "grep \\-E \\\"pattern\\\" file\\.txt \\| sort \\> output\\.txt"
                )
        );
    }

    /**
     * 测试已经转义过的字符串
     */
    @Test
    void testEscapeSpecialCharWithAlreadyEscapedString() {
        // 注意：这个测试可能会失败，因为当前实现会对已转义的字符再次转义
        // 如果需要避免重复转义，需要修改escapeSpecialChar方法的实现
        String input = "SELECT \\* FROM table";
        String expected = "SELECT \\* FROM table";
        assertEquals(expected, queryBuilder.escapeSpecialChar(input), "已转义的字符串会被再次转义");
    }

    /**
     * 测试边界情况：只包含特殊字符的字符串
     */
    @Test
    void testEscapeSpecialCharWithOnlySpecialChars() {
        String input = "*+?()[]{}^$|\\";
        String expected = "\\*\\+\\?\\(\\)\\[\\]\\{\\}\\^\\$\\|\\\\";
        assertEquals(expected, queryBuilder.escapeSpecialChar(input), "只包含特殊字符的字符串应该全部被转义");
    }

    /**
     * 测试Unicode字符和特殊字符混合的情况
     */
    @Test
    void testEscapeSpecialCharWithUnicodeAndSpecialChars() {
        String input = "你好，世界！(Hello World!)";
        String expected = "你好，世界！\\(Hello World\\!\\)";
        assertEquals(expected, queryBuilder.escapeSpecialChar(input), "Unicode字符不应被转义，但特殊字符应被转义");
    }
}
