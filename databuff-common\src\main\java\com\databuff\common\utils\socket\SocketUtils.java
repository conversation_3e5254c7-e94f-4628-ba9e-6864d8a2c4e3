package com.databuff.common.utils.socket;

import com.databuff.common.utils.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.ServerSocket;
import java.net.Socket;
import java.net.UnknownHostException;
import java.util.Date;

public class SocketUtils {
    private static final String ENCODING = "GBK";
    public static final Logger log = LoggerFactory.getLogger(SocketUtils.class);

    /**
     * 监听指定端口并处理接收到的内容
     * @param port 监听的端口
     * @param content 要发送的内容
     * @param encoding 编码格式
     * @param function 回调函数
     */
    public static void listen(int port, String content,String encoding ,SocketFunction function) throws IOException {
        try (ServerSocket serverSocket = new ServerSocket(port);
             Socket socket = serverSocket.accept()) {
            log.info("Listening on port: " + port);
            new WorkClass(socket, content,encoding, function).work();
        }
    }

    static class WorkClass {
        private Socket socket;
        private String content;
        private String encoding;
        private SocketFunction function;

        public WorkClass(Socket socket, String content, String encoding, SocketFunction function) {
            this.socket = socket;
            this.content = content;
            this.encoding = encoding;
            this.function = function;
        }

        public void work() throws IOException {
            try (InputStream in = socket.getInputStream();
                 OutputStream out = socket.getOutputStream()) {
                String msg = input(in,encoding);
                output(out, content,encoding);
                function.callback(msg);
            }
        }
    }
    /**
     * 发送消息到指定主机和端口
     * @param host 目标主机
     * @param port 目标端口
     * @param content 要发送的内���
     * @param encoding 编码格式
     * @param timeOut 超时时间
     * @param function 回调函数
     */
    public static void send(String host, int port, String content,String encoding,int timeOut, SocketFunction function) throws IOException {
        try (Socket socket = new Socket(host, port)) {
            socket.setSoTimeout(timeOut);
            try (OutputStream out = socket.getOutputStream();
                 InputStream in = socket.getInputStream()) {
                out.write(content.getBytes(encoding));
                out.flush();
                socket.shutdownOutput();
                String msg = input(in,encoding);
                function.callback(msg);
            }
        }
    }
    private static String input(InputStream in,String encoding) throws IOException {
        StringBuilder sb = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(in, encoding))) {
            char[] buffer = new char[1024];
            int len;
            while ((len = reader.read(buffer)) != -1) {
                sb.append(buffer, 0, len);
            }
        }
        return sb.toString();
    }

    private static void output(OutputStream out, String content,String encoding) throws IOException {
        out.write(content.getBytes(encoding));
    }

    public interface SocketFunction {
        void callback(String msg) throws UnsupportedEncodingException;
    }

    public static void main(String[] args) throws IOException {




        Date alarmTime = new Date();
        String serviceTime = String.format("%tY%<tm%<td%<tH%<tM%<tS", alarmTime);
        System.out.println(serviceTime);
        String data = "This is test data:1234";
        // Example usage
        try {
            SocketUtils.send("localhost", 12345, data, "UTF-8",  0, new SocketFunction() {
                @Override
                public void callback(String msg) throws UnsupportedEncodingException {
                    System.out.println("callback msg:"+msg);
                }
            });
        } catch (UnknownHostException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}