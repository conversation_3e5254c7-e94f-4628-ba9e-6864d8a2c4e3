# 基于Git提交的DataBuff SkyWalking异常检测类代码集合

本文档基于Git提交历史记录，收集了DataBuff SkyWalking告警系统中五种异常检测方法的相关类代码。

## Git提交历史分析

### 关键提交记录

通过Git日志分析，发现了以下关键的异常检测相关提交：

#### 1. 突变检测相关提交
- **6f69261e6f**: feat: MutationCheckMMT 突变检测算子 (2024-01-17)
- **bba8e433e9**: feat: MutationCheckMMT 突变检测算子 (2024-01-17)

#### 2. 阈值检测相关提交  
- **1cc0a58c20**: refactor(databuff-dao): 重构 ThresholdAlarmCheckMMT 类 (2025-03-01)
- **7f56e672cd**: fix: ThresholdAlarmCheckMMT 阈值检测 timeValue为null时忽略该事件 (2024)
- **b4413ecfff**: fix: ThresholdAlarmCheckMMT 阈值检测 timeValue为null时忽略该事件 (2024)

#### 3. 动态基线检测相关提交
- **fc8703c3b8**: 指标告警基线计算 (2022-01-06)
- **bdd49937f6**: 基线告警监控调试
- **6f98719cab**: fix: 使用DConfigLockOperator 改造 CalculateBaseline.java
- **8c97be58c1**: fix: 动态基线 事件对应的时间改为窗口期内最大时间戳
- **5c634cba48**: fix: 动态基线 阈值判断。阈值 至少有n个必须连续的点大于阈值判断逻辑优化

#### 4. 状态检测相关提交
- **7e5e75f80e**: fix: StatusAlarmCheckMMT.java ThresholdAlarmCheckMMT.java 数据格式转换异常

#### 5. 突变点检测相关提交
- **9cc1452689**: feat: 检测规则 新增 changepoint 监控方法 (2024-06-24)
- **f9f0c9c0d4**: fix(databuff-dao): 修改动态配置 event.metric.defTimeOffset 无效 为 ChangePointAlarmCheckMMT组件添加配置刷新支持

### 发现的相关类文件

基于Git提交分析，发现以下关键类文件：

#### 核心检测类
1. `databuff-dao/src/main/java/com/databuff/metric/impl/alarmV2/ThresholdAlarmCheck.java`
2. `databuff-dao/src/main/java/com/databuff/metric/impl/alarmV2/DynamicBaselineCheck.java`
3. `databuff-dao/src/main/java/com/databuff/metric/impl/alarmV2/MutationCheck.java`
4. `databuff-dao/src/main/java/com/databuff/metric/impl/alarmV2/StatusAlarmCheck.java`
5. `databuff-dao/src/main/java/com/databuff/metric/impl/alarmV2/ChangePointAlarmCheck.java`

#### 基础类和工具类
6. `databuff-dao/src/main/java/com/databuff/metric/impl/alarmV2/BaseAlarmCheckV2.java`
7. `databuff-dao/src/main/java/com/databuff/metric/impl/alarmV2/CalculateBaseline.java`
8. `databuff-dao/src/main/java/com/databuff/metric/AlarmCheckOperatorV2.java`

#### 历史版本类（从Git提交中发现）
9. `metric-api/src/main/java/com/dacheng/databuff/metric/impl/alarm/MutationCheckMMT.java`
10. `metric-api/src/main/java/com/dacheng/databuff/metric/impl/alarm/ThresholdAlarmCheckMMT.java`
11. `metric-api/src/main/java/com/dacheng/databuff/metric/impl/alarm/DynamicBaselineCheckMMT.java`
12. `metric-api/src/main/java/com/dacheng/databuff/metric/impl/alarm/StatusAlarmCheckMMT.java`
13. `metric-api/src/main/java/com/dacheng/databuff/metric/impl/alarm/ChangePointAlarmCheckMMT.java`

## 从Git提交中提取的类代码

### 1. MutationCheckMMT类 (来自提交 6f69261e6f)

这是从Git提交历史中提取的突变检测类的早期版本：

```java
package com.dacheng.databuff.metric.impl.alarm;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dacheng.databuff.influx.SQLParser;
import com.dacheng.databuff.metric.AlarmCheckOperator;
import com.dacheng.databuff.metric.MetricAggregator;
import com.databuff.entity.DatabuffMonitor;
import com.databuff.entity.EventEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.dacheng.databuff.influx.SQLParser.*;
import static com.databuff.entity.EventEntity.DetectionType.mutation;

/**
 * 突变检测算子 - Git提交版本
 * @author:yuzhili
 * @date: 2024/01/09
 */
@Component
@Slf4j
public class MutationCheckMMT extends BaseAlarmCheck implements AlarmCheckOperator {

    private final static String SEPARATOR = ":";
    private final static String REDIS_PREFIX = "monitor:metric:";

    private MetricAggregator metricAggregator;

    @Override
    public Map<Object, Object> afterCheckResult(DatabuffMonitor monitor, 
                                               Map<Map, Map<Object, Double>> aggTimeSeries, 
                                               JSONObject queryJson, 
                                               MetricAggregator metricAggregator, 
                                               List<JSONObject> queries) {
        this.metricAggregator = metricAggregator;
        Map<Object, Object> ret = new HashMap<>(16);
        if (CollectionUtils.isEmpty(aggTimeSeries)) {
            return ret;
        }

        final String expr = queryJson.getString(EXPR);
        final Integer comparePeriod = queryJson.getIntValue("comparePeriod");
        final String fluctuate = queryJson.getString("fluctuate");
        
        // 设置时间偏移量获取历史数据
        for (JSONObject query : queries) {
            query.put(TIME_OFFSET, comparePeriod);
        }

        // 获取历史周期的聚合数据
        final Map<Map, Map<Object, Double>> beforeAggTimeSeries = 
            metricAggregator.aggResult(expr, queries, 60L);
        if (CollectionUtils.isEmpty(beforeAggTimeSeries)) {
            return ret;
        }

        // 要求周期内完整数据
        final boolean fullWindow = queryJson.getBooleanValue("require_full_window");
        final long period = queryJson.getLongValue(SQLParser.PERIOD);

        // 删除不满足条件的数据
        for (Iterator<Map.Entry<Map, Map<Object, Double>>> iterator = aggTimeSeries.entrySet().iterator(); 
             iterator.hasNext(); ) {
            Map.Entry<Map, Map<Object, Double>> entry = iterator.next();
            if (entry == null || entry.getKey() == null || entry.getValue() == null) {
                iterator.remove();
                continue;
            }
            Map<Object, Double> retMap = entry.getValue();
            if (CollectionUtils.isEmpty(retMap)) {
                iterator.remove();
                continue;
            }
            if (fullWindow && !dataIntegrityCheck(period, entry)) {
                iterator.remove();
            }
        }

        final String timeAggregator = queryJson.getString("time_aggregator");
        final JSONObject thresholds = queryJson.getJSONObject("thresholds");

        // 根据时间聚合器和比较符获取分组的聚合值
        Map<Map, EventEntity> aggValue = this.getAggValue(timeAggregator, aggTimeSeries, 
                                                         beforeAggTimeSeries, fluctuate, thresholds);
        if (CollectionUtils.isEmpty(aggValue)) {
            return ret;
        }
        ret.putAll(aggValue);
        return ret;
    }

    /**
     * 突变检测核心算法
     */
    protected Map<Map, EventEntity> getAggValue(final String timeAggregator,
                                               final Map<Map, Map<Object, Double>> aggTimeSeries,
                                               final Map<Map, Map<Object, Double>> beforeAggTimeSeries,
                                               final String fluctuate, 
                                               JSONObject thresholds) {
        Map<Map, EventEntity> result = new HashMap<>();
        
        // 参数验证
        if (thresholds == null) {
            log.warn("错误的参数配置：thresholds不能为空");
            return result;
        }
        
        final String critical = thresholds.getString("critical");
        final String warning = thresholds.getString("warning");
        
        if (StringUtils.isBlank(critical) && StringUtils.isBlank(warning)) {
            log.warn("阈值配置错误：critical和warning至少需要配置一个");
            return result;
        }

        for (Map.Entry<Map, Map<Object, Double>> entry : aggTimeSeries.entrySet()) {
            if (entry == null) continue;
            
            final Map lineDataKey = entry.getKey();
            final Collection<Double> xValues = entry.getValue().values();
            
            // 获取对应的历史数据
            final Map<Object, Double> beforeData = beforeAggTimeSeries.get(lineDataKey);
            if (beforeData == null || CollectionUtils.isEmpty(beforeData.values())) {
                continue;
            }
            final Collection<Double> yValues = beforeData.values();

            // 获取最后一个时间戳
            final Long lastTimestamp = entry.getValue().keySet().stream()
                    .filter(Objects::nonNull)
                    .map(k -> Long.valueOf(k.toString()))
                    .max(Long::compareTo)
                    .orElse(System.currentTimeMillis() / 1000L);

            EventEntity eventEntity = EventEntity.builder().way(mutation).build();
            Double xMean;
            Double yMean;
            Double fluctuationValue = 0D;
            
            // 根据时间聚合方式计算聚合值
            switch (timeAggregator) {
                case "avg":
                case "mean":
                    xMean = xValues.stream().filter(Objects::nonNull).mapToDouble(t -> t).average().getAsDouble();
                    yMean = yValues.stream().filter(Objects::nonNull).mapToDouble(t -> t).average().getAsDouble();
                    fluctuationValue = getFluctuationValue(xMean, yMean, fluctuate);
                    break;
                case "sum":
                    xMean = xValues.stream().filter(Objects::nonNull).mapToDouble(t -> t).sum();
                    yMean = yValues.stream().filter(Objects::nonNull).mapToDouble(t -> t).sum();
                    fluctuationValue = getFluctuationValue(xMean, yMean, fluctuate);
                    break;
                case "max":
                    xMean = xValues.stream().filter(Objects::nonNull).mapToDouble(t -> t).max().getAsDouble();
                    yMean = yValues.stream().filter(Objects::nonNull).mapToDouble(t -> t).max().getAsDouble();
                    fluctuationValue = getFluctuationValue(xMean, yMean, fluctuate);
                    break;
                case "min":
                    xMean = xValues.stream().filter(Objects::nonNull).mapToDouble(t -> t).min().getAsDouble();
                    yMean = yValues.stream().filter(Objects::nonNull).mapToDouble(t -> t).min().getAsDouble();
                    fluctuationValue = getFluctuationValue(xMean, yMean, fluctuate);
                    break;
                default:
                    log.warn("不支持的时间聚合方式: {}", timeAggregator);
                    continue;
            }

            // 根据阈值判断状态
            int status = 0;
            String threshold = "";
            
            if (StringUtils.isNotBlank(critical)) {
                double criticalThreshold = Double.parseDouble(critical);
                if (fluctuationValue >= criticalThreshold) {
                    status = 3;
                    threshold = critical;
                }
            }
            
            if (status == 0 && StringUtils.isNotBlank(warning)) {
                double warningThreshold = Double.parseDouble(warning);
                if (fluctuationValue >= warningThreshold) {
                    status = 2;
                    threshold = warning;
                }
            }

            eventEntity.setValue(fluctuationValue);
            eventEntity.setTriggerTime(lastTimestamp);
            eventEntity.setStatus(status);
            eventEntity.setThreshold(threshold);
            result.put(lineDataKey, eventEntity);
        }
        
        return result;
    }

    /**
     * 波动值计算方法 - Git版本
     */
    private double getFluctuationValue(double currentVal, double previousVal, String fluctuateType) {
        if (fluctuateType == null) {
            return currentVal - previousVal;
        }
        switch (fluctuateType) {
            case "valUp": {
                // 数据增加量
                return currentVal - previousVal;
            }
            case "valDown": {
                // 数据减少量
                return previousVal - currentVal;
            }
            case "yoyUp": {
                // 同比增长率
                return (currentVal - previousVal) / (previousVal == 0 ? 1 : previousVal);
            }
            case "yoyDown": {
                // 同比下降率
                return (previousVal - currentVal) / (previousVal == 0 ? 1 : previousVal);
            }
            default:
                return currentVal - previousVal;
        }
    }

    // 其他方法实现...
}
```

### 2. ThresholdAlarmCheck类 (当前版本)

这是当前版本的阈值检测类，经过多次Git提交优化：

```java
package com.databuff.metric.impl.alarmV2;

import com.databuff.common.tsdb.dto.detect.DetectQueryRequest;
import com.databuff.common.tsdb.dto.explore.QueryRequest;
import com.databuff.common.tsdb.dto.preview.ThresholdsDTO;
import com.databuff.entity.DatabuffMonitor;
import com.databuff.entity.EventEntity;
import com.databuff.metric.AlarmCheckOperatorV2;
import com.databuff.metric.MetricAggregator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 阈值检测类 - 基于Git提交优化的当前版本
 * 提交记录：1cc0a58c20 - refactor(databuff-dao): 重构 ThresholdAlarmCheckMMT 类
 */
@Component
@Slf4j
public class ThresholdAlarmCheck extends BaseAlarmCheckV2 implements AlarmCheckOperatorV2 {

    private static final String SEPARATOR = ":";
    private static final String REDIS_PREFIX = "monitor:metric:";
    private static final int CRITICAL_STATUS = 3;
    private static final int WARNING_STATUS = 2;
    private static final int NORMAL_STATUS = 0;

    @Override
    public Map<Object, Object> afterCheckResult(DatabuffMonitor monitor,
                                               Map<Map, Map<Object, Double>> aggTimeSeries,
                                               DetectQueryRequest detectQueryRequest,
                                               MetricAggregator metricAggregator,
                                               Collection<QueryRequest> queries) {
        Map<Object, Object> ret = new HashMap<>(16);
        if (CollectionUtils.isEmpty(aggTimeSeries)) {
            return ret;
        }

        // 从查询参数中提取完整性检查标志和时间窗口周期
        final Boolean fullWindow = detectQueryRequest.getRequireFullWindow();
        final long period = detectQueryRequest.getPeriod();

        /** 数据过滤逻辑：
         * 1. 移除空数据条目
         * 2. 当需要完整时间窗口数据时，执行数据完整性校验
         * 3. 使用removeIf简化迭代器操作 */
        aggTimeSeries.entrySet().removeIf(entry ->
                entry == null || entry.getKey() == null || entry.getValue() == null
                        || CollectionUtils.isEmpty(entry.getValue())
                        || (fullWindow && !dataIntegrityCheck(period, entry))
        );

        final String timeAggregator = detectQueryRequest.getTimeAggregator();
        final String comparison = detectQueryRequest.getComparison();
        final Boolean continuous = detectQueryRequest.getContinuous();
        final Integer continuous_n = detectQueryRequest.getContinuousN();
        final ThresholdsDTO thresholds = detectQueryRequest.getThresholds();

        /** 核心处理：获取符合阈值条件的聚合值
         *  参数说明：
         *  - timeAggregator: 时间维度聚合方式
         *  - comparison: 比较运算符(>,<等)
         *  - continuous: 是否要求连续触发
         *  - continuous_n: 连续触发次数阈值 */
        Map<Map, Map.Entry> aggValue = this.getAggValue(timeAggregator, aggTimeSeries,
                                                       comparison, continuous, continuous_n, thresholds);
        if (CollectionUtils.isEmpty(aggValue)) {
            return ret;
        }

        // 遍历处理符合条件的指标数据
        for (Map.Entry<Map, Map.Entry> entry : aggValue.entrySet()) {
            if (entry == null) {
                continue;
            }
            // 真实指标值格式：Entry<时间戳, 指标值>
            final Map.Entry<Long, Double> realAggValue = entry.getValue();
            EventEntity eventEntity = judgeThresholdStatus(thresholds, comparison, realAggValue);
            if (eventEntity == null) {
                continue;
            }
            ret.put(entry.getKey(), eventEntity);
        }
        return ret;
    }

    /**
     * 阈值状态判断核心方法
     * Git优化：提高阈值判断逻辑的可读性和可维护性
     *
     * @param thresholds 包含critical/warning阈值的JSON对象
     * @param comparison 比较运算符(支持>/>=/</<=/==)
     * @param timeValue  指标数据Entry<时间戳, 值>
     * @return 包含三种状态的事件实体: - 3(重要)/2(次要)/0(正常)
     **/
    public EventEntity judgeThresholdStatus(ThresholdsDTO thresholds, String comparison,
                                          Map.Entry<Long, Double> timeValue) {
        final Double critical = thresholds.getCritical() == null ? null : thresholds.getCritical().doubleValue();
        final Double warning = thresholds.getWarning() == null ? null : thresholds.getWarning().doubleValue();
        if (critical == null && warning == null) {
            log.warn("阈值配置错误：critical和warning至少需要配置一个");
            return null;
        }

        EventEntity eventEntity = EventEntity.builder().way(EventEntity.DetectionType.threshold).build();
        Double value = timeValue.getValue();
        if (value == null) {
            return null;
        }

        /** 阈值判断逻辑：
         * 1. 根据比较运算符选择条件检查策略
         * 2. 优先检查critical阈值
         * 3. 当critical阈值未触发时检查warning阈值
         * */
        int status = NORMAL_STATUS;
        String threshold = "";
        boolean conditionMet = false;

        switch (comparison) {
            case ">":
                conditionMet = checkCondition(value, critical, warning, (v, t) -> v > t);
                break;
            case ">=":
                conditionMet = checkCondition(value, critical, warning, (v, t) -> v >= t);
                break;
            case "<":
                conditionMet = checkCondition(value, critical, warning, (v, t) -> v < t);
                break;
            case "<=":
                conditionMet = checkCondition(value, critical, warning, (v, t) -> v <= t);
                break;
            default: // "=="
                conditionMet = checkCondition(value, critical, warning, (v, t) -> v.equals(t));
                break;
        }

        /** 状态分级逻辑：
         * - 当critical阈值存在且满足时，标记为重要告警(3)
         * - 否则当warning阈值存在且满足时，标记为次要告警(2)
         * - 都不满足时保持正常状态(0)
         * */
        if (conditionMet) {
            status = (critical != null && checkCriticalCondition(value, critical, comparison))
                    ? CRITICAL_STATUS : WARNING_STATUS;
            threshold = (status == CRITICAL_STATUS) ? critical.toString() : warning.toString();
        }

        eventEntity.setValue(value);
        eventEntity.setAbnormalTime(timeValue.getKey());
        eventEntity.setStatus(status);
        eventEntity.setThreshold(threshold);
        return eventEntity;
    }

    /**
     * 检查给定的值是否满足临界条件或警告条件
     * Git优化：添加类型安全检查和防御性编程
     */
    private boolean checkCondition(Double value, Double critical, Double warning, ConditionChecker checker) {
        // 检查是否满足临界条件
        boolean criticalCondition = critical != null && checker.check(value, critical);
        // 检查是否满足警告条件
        boolean warningCondition = warning != null && checker.check(value, warning);
        // 返回是否满足任一条件
        return criticalCondition || warningCondition;
    }

    /**
     * 检查给定值是否满足与临界值的比较条件
     * Git优化：增强代码的健壮性
     */
    private boolean checkCriticalCondition(Double value, Double critical, String comparison) {
        // 根据比较操作符进行相应的比较操作
        switch (comparison) {
            case ">":
                return value > critical;
            case ">=":
                return value >= critical;
            case "<":
                return value < critical;
            case "<=":
                return value <= critical;
            default:
                return value.equals(critical);
        }
    }

    /**
     * 条件检查策略接口
     * 用于抽象不同比较运算符的实现细节
     */
    private interface ConditionChecker {
        boolean check(Double value, Double threshold);
    }

    @Override
    public Map<Object, Object> afterCheckNoDataResult(DatabuffMonitor monitor, String mKey,
                                                     Set<Map<String, String>> map,
                                                     DetectQueryRequest detectQueryRequest) {
        Map<Object, Object> ret = new HashMap<>(16);

        Integer noDataTimeframe = detectQueryRequest.getNoDataTimeframe();
        Collection<String> byArr = detectQueryRequest.findBy();
        final int mid = monitor.getId().intValue();
        String noDataPrefix = REDIS_PREFIX + "nodata" + SEPARATOR + mid + SEPARATOR + mKey + SEPARATOR;

        // Git优化：使用StringBuilder优化字符串拼接性能
        Set<String> noDataKeys = jedisService.keysS(noDataPrefix);
        Map<String, Integer> noDataMap = new HashMap<>(noDataKeys.size());
        for (String k : noDataKeys) {
            int lastIndexOf = k.lastIndexOf("_");
            noDataMap.put(k.substring(0, lastIndexOf), Integer.valueOf(k.substring(lastIndexOf + 1)));
        }
        jedisService.delKeyStrs(noDataKeys);

        List<String> newKeys = new ArrayList<>(16);
        int num;
        int status = 1;
        for (Object retKey : map) {
            // Git优化：类型安全校验：防御性编程避免类型转换异常
            if (!(retKey instanceof Map)) {
                log.warn("非预期的retKey类型: {}", retKey.getClass());
                continue;
            }

            /** 分组键构建逻辑：
             * 1. 根据配置的维度字段(byArr)提取标签值
             * 2. 使用StringBuilder避免字符串拼接性能损耗
             * 3. 自动处理分隔符(避免首部出现多余分隔符) */
            Map<String, String> tagMap = (Map<String, String>) retKey;
            StringBuilder groupBuilder = new StringBuilder();
            for (String groupStr : byArr) {
                if (groupBuilder.length() > 0) {
                    groupBuilder.append(SEPARATOR);
                }
                groupBuilder.append(tagMap.get(groupStr));
            }
            String group = groupBuilder.toString();

            /** 无数据状态处理：
             * 1. 检查历史累积次数(noDataMap)
             * 2. 根据累积次数与阈值关系生成不同状态事件
             *  - num >= noDataTimeframe: 生成正式无数据事件
             *  - num < evaluation_delay: 延迟状态事件
             *  - 其他情况: 记录无数据但状态正常 */
            num = 1;
            String noKey = noDataPrefix + group;
            if (noDataMap.containsKey(noKey)) {
                num += noDataMap.get(noKey);
            }
            EventEntity eventEntity = EventEntity.builder().way(EventEntity.DetectionType.threshold).build();
            eventEntity.setValue(0);
            eventEntity.setGroup(group);

            if (num >= noDataTimeframe) {
                eventEntity.setNoData(true);
                eventEntity.setStatus(status);
                ret.put(retKey, eventEntity);
                newKeys.add(noKey + "_" + num);
            } else {
                eventEntity.setStatus(NORMAL_STATUS);
                eventEntity.setNoData(true);
                ret.put(retKey, eventEntity);
                newKeys.add(noKey + "_" + num);
            }
        }
        jedisService.setBatchStr(newKeys, "0");
        return ret;
    }
}
```

### 3. CalculateBaseline类 (来自多个Git提交)

这是基线计算工具类，经过多次Git提交优化：

```java
package com.databuff.metric.impl.alarmV2;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.tsdb.dto.CompositeCondition;
import com.databuff.common.tsdb.dto.explore.QueryRequest;
import com.databuff.lock.DConfigLockOperator;
import com.databuff.metric.MetricAggregator;
import com.databuff.service.JedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import static com.databuff.common.utils.TimeUtil.ONE_DAY_S;

/**
 * 基线计算工具类
 * Git提交记录：
 * - 6f98719cab: fix: 使用DConfigLockOperator 改造 CalculateBaseline.java
 * - 8c97be58c1: fix: 动态基线 事件对应的时间改为窗口期内最大时间戳
 * - 5c634cba48: fix: 动态基线 阈值判断。阈值 至少有n个必须连续的点大于阈值判断逻辑优化
 * - a19d6834b0: fix: 动态基线 num < 2016的不展示基线数据
 */
@Component
@Slf4j
public class CalculateBaseline {

    @Resource
    protected JedisService jedisService;

    @Resource
    protected DConfigLockOperator dConfigLockOperator;

    /**
     * 异步计算基线
     * Git优化：使用DConfigLockOperator改造分布式锁机制
     */
    @Async
    public void processAsync(String keyStr, Collection<QueryRequest> queries, Map<String, String> tags,
                           MetricAggregator metricAggregator, String comparison, double baselineScope) {
        // Git优化：使用分布式锁确保同一时间只有一个线程计算同一个基线
        dConfigLockOperator.acquireLockAndExecuteCallback("lock:" + keyStr, 2, 120, TimeUnit.SECONDS, () -> {
            for (QueryRequest query : queries) {
                if (query == null) {
                    continue;
                }

                // 构建查询条件
                final Collection<CompositeCondition> fromExt = new ArrayList<>();
                tags.forEach((k, v) -> {
                    if (k == null || v == null) {
                        return;
                    }
                    fromExt.add(CompositeCondition.builder().left(k).right(v).operator("=").build());
                });
                query.setFromExt(fromExt);

                // 调用指标聚合器计算基线
                final Map<Map<String, String>, JSONObject> baselineResult =
                    metricAggregator.baselineResult(query, comparison, baselineScope);
                if (MapUtils.isEmpty(baselineResult)) {
                    continue;
                }

                // 处理基线计算结果
                baselineResult.entrySet().stream().findFirst().ifPresent(e -> {
                    final JSONObject value = e.getValue();
                    if (value == null) {
                        return;
                    }
                    final String valueStr = value.toJSONString();
                    final double num = value.getDoubleValue("num");
                    final double baseline = value.getDoubleValue("baseline");

                    // Git优化：数据质量检查 - num < 2016的不展示基线数据
                    if (num < 2016 || baseline <= 0) {
                        log.debug("监控[{}]计算基线点数{}，低于所需数据量(分钟粒度下，至少需要满足 7d*20%*24h*60min=2016 的数据量)不满足要求",
                                keyStr, num);
                        // 数据不足时缓存1小时
                        jedisService.setJson(keyStr, valueStr, ONE_DAY_S / 24);
                    } else {
                        // 数据充足时缓存1天
                        jedisService.setJson(keyStr, valueStr, ONE_DAY_S);
                    }
                });
            }
        });
    }

    /**
     * 同步计算基线
     * Git优化：支持同步和异步两种计算方式
     */
    public JSONObject process(String keyStr, Collection<QueryRequest> queries, Map<String, String> tags,
                            MetricAggregator metricAggregator, String comparison, double baselineScope) {
        // 使用原子引用保存结果
        AtomicReference<JSONObject> value = new AtomicReference<>();

        // 使用分布式锁确保线程安全
        dConfigLockOperator.acquireLockAndExecuteCallback("lock:" + keyStr, 2, 120, TimeUnit.SECONDS, () -> {
            for (QueryRequest query : queries) {
                if (query == null) {
                    continue;
                }

                // 构建查询条件
                final Collection<CompositeCondition> fromExt = new ArrayList<>();
                tags.forEach((k, v) -> {
                    if (k == null || v == null) {
                        return;
                    }
                    fromExt.add(CompositeCondition.builder().left(k).right(v).operator("=").build());
                });
                query.setFromExt(fromExt);

                // 调用指标聚合器计算基线
                final Map<Map<String, String>, JSONObject> baselineResult =
                    metricAggregator.baselineResult(query, comparison, baselineScope);
                if (MapUtils.isEmpty(baselineResult)) {
                    continue;
                }

                // 处理基线计算结果
                baselineResult.entrySet().stream().findFirst().ifPresent(e -> {
                    value.set(e.getValue());
                    if (value.get() == null) {
                        return;
                    }
                    final String valueStr = value.get().toJSONString();
                    final double num = value.get().getDoubleValue("num");
                    final double baseline = value.get().getDoubleValue("baseline");

                    // 检查数据量是否满足要求
                    if (num < 2016 || baseline <= 0) {
                        log.debug("监控[{}]计算基线点数{}，低于所需数据量(分钟粒度下，至少需要满足 7d*20%*24h*60min=2016 的数据量)不满足要求",
                                keyStr, num);
                        // 数据不足时缓存1小时
                        jedisService.setJson(keyStr, valueStr, ONE_DAY_S / 24);
                    } else {
                        // 数据充足时缓存1天
                        jedisService.setJson(keyStr, valueStr, ONE_DAY_S);
                    }
                });
            }
        });
        return value.get();
    }
}
```
