# DataBuff SkyWalking 报告系统代码分析 - 一般搜索结果

## 1. 核心实体类

### 1.1 ReportEntity (报告实体)
- **路径**: `webapp/src/main/java/com/databuff/webapp/report/model/ReportEntity.java`
- **功能**: 报告记录实体，存储生成的报告信息
- **主要字段**:
  - `id`: 报告ID
  - `name`: 报告名称
  - `type`: 报告类型（1-日报，2-周报，3-自定义）
  - `template`: 对应模版数据（JSON字符串）
  - `createTime`: 报告生成时间
  - `apiKey`: 租户API Key
  - `status`: 报告生成状态（1-成功，2-生成失败，3-报告不存在）

### 1.2 ReportTemplateEntity (报告模板实体)
- **路径**: `webapp/src/main/java/com/databuff/webapp/report/model/ReportTemplateEntity.java`
- **功能**: 报告模板配置实体
- **主要字段**:
  - `id`: 模板ID
  - `name`: 模板名称
  - `type`: 报告类型
  - `content`: 报告内容配置（JSON数组，包含各种组件配置）
  - `status`: 模板状态（0-停用，1-启用）
  - `creator`: 创建者
  - `dingable`: 是否支持钉钉通知
  - `emailable`: 是否支持邮件通知
  - `receivers`: 接收者列表
  - `cycleTime`: 周期时间配置
  - `customTime`: 自定义时间配置
  - `apiKey`: 租户API Key

## 2. 核心服务层

### 2.1 ReportService 接口
- **路径**: `webapp/src/main/java/com/databuff/webapp/report/service/ReportService.java`
- **主要方法**:
  - `getReportList()`: 获取报告列表
  - `getReportTemplateList()`: 获取报告模板列表
  - `addTemplate()`: 添加报告模板
  - `editTemplate()`: 编辑报告模板
  - `deleteTemplate()`: 删除报告模板
  - `toggleStatus()`: 启用/停用报告模板
  - `download()`: 下载报告
  - `preview()`: 预览报告
  - `generateRecord()`: 生成报告记录
  - `taskDownload()`: 任务下载（生成Word文档）
  - `wordGeneratorDownload()`: 使用WordGenerator生成下载
  - `sendMail()`: 发送邮件

### 2.2 ReportImpl 实现类
- **路径**: `webapp/src/main/java/com/databuff/webapp/report/service/impl/ReportImpl.java`
- **核心功能**:
  - 报告模板的CRUD操作
  - 报告文件的生成和下载
  - 邮件发送功能
  - 文件存储路径: `/var/dacheng/staticDir/report`

## 3. 数据访问层

### 3.1 ReportMapper 接口
- **路径**: `webapp/src/main/java/com/databuff/webapp/report/mapper/ReportMapper.java`
- **主要方法**:
  - `getReportList()`: 查询报告列表
  - `getReportById()`: 根据ID获取报告
  - `addReport()`: 添加报告记录
  - `getReportTemplateList()`: 查询模板列表
  - `getTemplateById()`: 根据ID获取模板
  - `addTemplate()`: 添加模板
  - `editTemplate()`: 编辑模板
  - `deleteTemplate()`: 删除模板
  - `toggleStatus()`: 切换模板状态
  - `updateReportStatus()`: 更新报告状态

### 3.2 MyBatis映射文件
- **路径**: `webapp/src/main/resources/mapper/ReportMapper.xml`
- **数据库表**:
  - `dc_report`: 报告记录表
  - `dc_report_template`: 报告模板表

## 4. 控制器层

### 4.1 ReportController
- **路径**: `webapp/src/main/java/com/databuff/webapp/report/controller/ReportController.java`
- **API端点**:
  - `POST /report/list`: 获取报告列表
  - `POST /report/addTemplate`: 添加报告模板
  - `GET /report/download`: 下载报告
  - `GET /report/preview`: 预览报告PDF

## 5. 报告生成工具类

### 5.1 ReportWordUtil
- **路径**: `webapp/src/main/java/com/databuff/webapp/report/utils/ReportWordUtil.java`
- **核心功能**:
  - 支持两种Word生成方式：原生POI和WordGenerator
  - 处理多种报告组件：文本、饼图、折线图、柱状图、表格
  - 数据查询和转换
  - 时间范围计算

### 5.2 WordGenerator
- **路径**: `webapp/src/main/java/com/databuff/webapp/report/utils/WordGenerator.java`
- **功能**: Word文档生成工具类，支持：
  - 添加标题和段落
  - 生成图表（折线图、柱状图、饼图）
  - 创建表格
  - 目录生成

### 5.3 WrapDataConverter
- **路径**: `webapp/src/main/java/com/databuff/webapp/report/utils/WrapDataConverter.java`
- **功能**: 数据转换工具类
  - 将WrapData转换为图表数据
  - 支持TopN过滤
  - 时间序列数据处理
  - 表格数据生成

## 6. 定时任务

### 6.1 ReportTimer
- **路径**: `webapp/src/main/java/com/databuff/webapp/task/report/ReportTimer.java`
- **功能**: 报告定时生成器
- **执行频率**: 每5分钟执行一次 (`@Scheduled(cron = "0 */5 * * * ?")`)
- **核心逻辑**:
  1. 获取所有启用的报告模板
  2. 根据周期配置计算是否需要生成报告
  3. 生成报告文件
  4. 发送邮件通知
  5. 清理过期报告文件

## 7. 数据库设计

### 7.1 dc_report 表
```sql
CREATE TABLE `dc_report` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(255) NOT NULL COMMENT '报告名称',
  `type` int NOT NULL COMMENT '报告类型',
  `template` longtext COMMENT '报告模版数据',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `api_key` varchar(64) NOT NULL COMMENT '租户 apiKey',
  `status` int DEFAULT NULL COMMENT '报告生成状态： 1-成功，2-生成失败，3-报告不存在',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='报告表';
```

### 7.2 dc_report_template 表
- 存储报告模板配置
- 包含模板内容、周期配置、通知设置等字段

## 8. 报告组件类型

报告支持以下组件类型：
- **text**: 文本段落
- **pie**: 饼图
- **line**: 折线图  
- **bar**: 柱状图
- **table**: 表格

每个组件配置格式：
```json
{
  "type": "text/pie/line/bar/table",
  "title": "组件标题",
  "text": "文本内容",
  "query": "查询配置JSON字符串",
  "limit": "数据限制数量",
  "interval": "时间间隔(60/180/600/1800秒)"
}
```

## 9. 邮件通知功能

- 支持报告生成后自动发送邮件
- 通过NotifyConfig配置邮件服务器
- 使用MailUtil工具类发送邮件
- 支持附件形式发送报告文件
