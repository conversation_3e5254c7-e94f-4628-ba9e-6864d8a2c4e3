package com.databuff.dao.mysql;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.databuff.entity.ConvergencePolicy;
import com.databuff.handler.FastjsonListTypeHandler;
import org.apache.ibatis.annotations.*;

import java.util.Collection;
import java.util.List;

@Mapper
public interface ConvergencePolicyMapper extends BaseMapper<ConvergencePolicy> {
    @Select("SELECT * FROM dc_databuff_convergence_policy")
    @Results({
            @Result(column = "filter_conditions", property = "filterConditions", typeHandler = FastjsonListTypeHandler.class),
            @Result(column = "convergence_conditions", property = "convergenceConditions", typeHandler = FastjsonListTypeHandler.class),
            @Result(column = "def", property = "def")
    })
    List<ConvergencePolicy> findAll();

    @Select("<script>" +
            "SELECT policy_name FROM dc_databuff_convergence_policy WHERE api_key = #{apiKey} " +
            "<if test='!allEntityPermission'>" +
            "<if test='gids != null and gids.size() > 0'>AND (gid IN " +
            "<foreach item='gid' collection='gids' separator=',' open='(' close=')'>#{gid}</foreach>)</if>" +
            "<if test='gids == null or gids.size() == 0'>AND 1 = 0</if>" +
            "</if>" +
            "</script>")
    List<String> findConvergencePolicyNames(
            @Param("apiKey") String apiKey,
            @Param("allEntityPermission") boolean allEntityPermission,
            @Param("gids") Collection<String> gids);

    @Select("<script>" +
            "SELECT * FROM dc_databuff_convergence_policy WHERE id = #{id} " +
            "<if test='!allEntityPermission'>" +
            "<if test='gids != null and gids.size() > 0'>AND (gid IN " +
            "<foreach item='gid' collection='gids' separator=',' open='(' close=')'>#{gid}</foreach>)</if>" +
            "<if test='gids == null or gids.size() == 0'>AND 1 = 0</if>" +
            "</if>" +
            "</script>")
    @Results({
            @Result(column = "filter_conditions", property = "filterConditions", typeHandler = FastjsonListTypeHandler.class),
            @Result(column = "convergence_conditions", property = "convergenceConditions", typeHandler = FastjsonListTypeHandler.class),
            @Result(column = "def", property = "def")
    })
    ConvergencePolicy findById(
            @Param("id") Integer id,
            @Param("allEntityPermission") boolean allEntityPermission,
            @Param("gids") Collection<String> gids);

    @Select("<script>" +
            "SELECT * FROM dc_databuff_convergence_policy WHERE id IN " +
            "<foreach item='id' index='index' collection='ids' open='(' separator=',' close=')'>#{id}</foreach> " +
            "<if test='!allEntityPermission'>" +
            "<if test='gids != null and gids.size() > 0'>AND (gid IN " +
            "<foreach item='gid' collection='gids' separator=',' open='(' close=')'>#{gid}</foreach>)</if>" +
            "<if test='gids == null or gids.size() == 0'>AND 1 = 0</if>" +
            "</if>" +
            "</script>")
    @Results({
            @Result(column = "filter_conditions", property = "filterConditions", typeHandler = FastjsonListTypeHandler.class),
            @Result(column = "convergence_conditions", property = "convergenceConditions", typeHandler = FastjsonListTypeHandler.class),
            @Result(column = "def", property = "def")
    })
    List<ConvergencePolicy> findByIds(
            @Param("ids") List<Integer> ids,
            @Param("allEntityPermission") boolean allEntityPermission,
            @Param("gids") Collection<String> gids);

    @Insert("INSERT INTO dc_databuff_convergence_policy(policy_name, is_ai, enabled, end_judgment_window, fixed_duration_window, " +
            "filter_conditions, convergence_conditions, alarm_description_template, pattern, variables, creator_id, editor_id, creator, " +
            "editor, api_key, sameRootCause, gid, def) VALUES" +
            "(#{policyName}, #{isAI}, #{enabled}, #{endJudgmentWindow}, #{fixedDurationWindow}, #{filterConditions}, " +
            "#{convergenceConditions}, #{alarmDescriptionTemplate}, #{pattern}, " +
            "#{variables, typeHandler=com.databuff.handler.FastjsonListTypeHandler}, #{creatorId}, #{editorId}, #{creator}, #{editor}, " +
            "#{apiKey}, #{sameRootCause}, #{gid}, #{def})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(ConvergencePolicy policy);

    @Update("UPDATE dc_databuff_convergence_policy SET policy_name = #{policyName}, is_ai = #{isAI}, enabled = " +
            "#{enabled}, " +
            "end_judgment_window = #{endJudgmentWindow}, " +
            "fixed_duration_window = #{fixedDurationWindow}, " +
            "filter_conditions = #{filterConditions}, " +
            "convergence_conditions = #{convergenceConditions}, " +
            "alarm_description_template = #{alarmDescriptionTemplate}, " +
            "pattern = #{pattern}, " +
            "variables = #{variables, typeHandler=com.databuff.handler.FastjsonListTypeHandler}, " +
            "editor_id = #{editorId}, " +
            "editor = #{editor}, " +
            "api_key = #{apiKey}, " +
            "def = #{def} " +
            "WHERE id = #{id}")
    void update(ConvergencePolicy policy);

    @Delete("DELETE FROM dc_databuff_convergence_policy WHERE id = #{id}")
    void delete(@Param("id") Integer id);

    @Select("<script>" +
            "SELECT * FROM dc_databuff_convergence_policy " +
            "<where>" +
            "    (def = false OR def IS NULL)" +
            "    <if test='keyword != null'>AND policy_name LIKE CONCAT('%', #{keyword}, '%')</if>" +
            "    <if test='enabled != null'>AND enabled = #{enabled}</if>" +
            "    <choose>" +
            "        <when test='allEntityPermission'>" +
            "            <choose>" +
            "                <when test='domainManagerStatus'>AND gid IS NOT NULL</when>" +
            "                <otherwise>AND gid IS NULL</otherwise>" +
            "            </choose>" +
            "        </when>" +
            "        <otherwise>" +
            "            <choose>" +
            "                <when test='gids != null and gids.size() > 0'>" +
            "                    AND gid IN " +
            "                    <foreach item='gid' collection='gids' open='(' close=')' separator=','>#{gid}</foreach>" +
            "                </when>" +
            "                <otherwise>AND 1 = 0</otherwise>" +
            "            </choose>" +
            "        </otherwise>" +
            "    </choose>" +
            "</where>" +
            "</script>")
    @Results({
            @Result(column = "filter_conditions", property = "filterConditions", typeHandler = FastjsonListTypeHandler.class),
            @Result(column = "convergence_conditions", property = "convergenceConditions", typeHandler = FastjsonListTypeHandler.class),
            @Result(column = "def", property = "def")
    })
    List<ConvergencePolicy> search(
            @Param("keyword") String keyword,
            @Param("enabled") Boolean enabled,
            @Param("domainManagerStatus") boolean domainManagerStatus,
            @Param("allEntityPermission") boolean allEntityPermission,
            @Param("gids") Collection<String> gids);

    @Select("<script>" +
            "SELECT * FROM dc_databuff_convergence_policy " +
            "WHERE def = true AND gid IS NULL " +
            "ORDER BY updated_time DESC limit 1" +
            "</script>")
    @Results({
            @Result(column = "filter_conditions", property = "filterConditions", typeHandler = FastjsonListTypeHandler.class),
            @Result(column = "convergence_conditions", property = "convergenceConditions", typeHandler = FastjsonListTypeHandler.class),
            @Result(column = "def", property = "def")
    })
    ConvergencePolicy defConvergencePolicy();

    @Select("<script>" +
            "SELECT * FROM dc_databuff_convergence_policy " +
            "<if test='ids != null and ids.size() > 0'>WHERE id IN " +
            "<foreach item='id' index='index' collection='ids' open='(' separator=',' close=')'>#{id}</foreach></if> " +
            "<if test='!allEntityPermission'>" +
            "<if test='gids != null and gids.size() > 0'>AND (gid IN " +
            "<foreach item='gid' collection='gids' separator=',' open='(' close=')'>#{gid}</foreach>)</if>" +
            "<if test='gids == null or gids.size() == 0'>AND 1 = 0</if>" +
            "</if> " +
            "ORDER BY " +
            "<if test='orderBy != null'>#{orderBy}</if>" +
            "<if test='orderBy == null'>updated_time DESC</if>" +
            "</script>")
    @Results({
            @Result(column = "filter_conditions", property = "filterConditions", typeHandler = FastjsonListTypeHandler.class),
            @Result(column = "convergence_conditions", property = "convergenceConditions", typeHandler = FastjsonListTypeHandler.class),
            @Result(column = "def", property = "def")
    })
    List<ConvergencePolicy> findByIdsAndOrderBy(
            @Param("ids") List<Long> ids,
            @Param("orderBy") String orderBy,
            @Param("allEntityPermission") boolean allEntityPermission,
            @Param("gids") Collection<String> gids);

    @Delete("DELETE FROM dc_databuff_convergence_policy WHERE gid = #{gid}")
    void deleteByGid(@Param("gid") String gid);

    @Select("SELECT id,fixed_duration_window,end_judgment_window FROM dc_databuff_convergence_policy")
    List<ConvergencePolicy> list();
}