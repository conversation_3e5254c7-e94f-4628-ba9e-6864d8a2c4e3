package com.databuff.entity.profiling.v3;

import com.databuff.common.utils.EventIdGenerator;
import com.databuff.entity.dto.IDGenerator;

import java.util.*;
import java.util.stream.Collectors;

import static com.databuff.common.constants.Constant.ENV_TAG1;
import static com.databuff.common.constants.Constant.ENV_TAG2;
import static com.databuff.common.utils.TimeUtil.formatLongToString;
import static com.databuff.entity.profiling.v3.ProfilingStackBase.generateBaseExcerptId;

public class SampleDataV3Converter {

    public static Set<ProfilingStack> toProfilingStackList(SampleDataV3 sampleData, Map<String, String> envTags) {
        final String service = sampleData.getService();
        Set<ProfilingStack> profilingStacks = new HashSet<>();
        for (ThreadStack threadStack : sampleData.getSamples()) {
            if (threadStack == null) {
                continue;
            }
            final List<String> stackTraces = threadStack.getStackTraces();
            final String frameTypeIds = threadStack.getFrameTypeIds();
            final String createDt = EventIdGenerator.extractStrDateFrom(threadStack.getSampleTime() / 1000000);

            ProfilingStack profilingStack = new ProfilingStack();
            profilingStack.setStackTrace(stackTraces);
            profilingStack.setFrameTypeIds(frameTypeIds);
            profilingStack.setCreateDt(createDt);
            profilingStack.setApiKey(sampleData.getApiKey());
            profilingStack.setObserverTool(sampleData.getObserverTool());
            profilingStack.setService(service);
            profilingStack.setServiceId(sampleData.getServiceId());
            profilingStack.setServiceInstance(sampleData.getServiceInstance());
            profilingStack.setHost(sampleData.getHost());
            profilingStack.setResource(threadStack.getResource());
            profilingStack.setResourceType(threadStack.getRsType());
            profilingStack.setEventType(threadStack.getEventType());
            profilingStack.setOnOperation(threadStack.getOnOperation());
            profilingStack.setRsFlagIndex(threadStack.getRsFlagIndex());
            profilingStack.setEnvTag1(envTags.get(ENV_TAG1));
            profilingStack.setEnvTag2(envTags.get(ENV_TAG2));
            profilingStacks.add(profilingStack);
            // 设置摘录ID
            threadStack.setExcerptId(profilingStack.getExcerptId());
        }
        return profilingStacks;
    }

    public static Set<ProfilingStackBase> toProfilingStackBaseList(SampleDataV3 sampleData) {
        if (sampleData == null || sampleData.getSampleBases() == null) {
            return null;
        }
        Set<ProfilingStackBase> profilingStacks = new HashSet<>();
        for (ThreadStackBase threadStack : sampleData.getSampleBases()) {
            if (threadStack == null) {
                continue;
            }
            final List<String> stackTraces = threadStack.getStackTraces();
            final String frameTypeIds = threadStack.getFrameTypeIds();
            final String createDt = EventIdGenerator.extractStrDateFrom(System.currentTimeMillis());
            final String serviceId = sampleData.getServiceId();
            final String serviceInstance = sampleData.getServiceInstance();
            final String resource = threadStack.getResource();
            final String rsType = threadStack.getRsType();
            final String apiKey = sampleData.getApiKey();

            ProfilingStackBase profilingStack = ProfilingStackBase.builder()
                    .createDt(createDt)
                    .stackTrace(stackTraces)
                    .apiKey(apiKey)
                    .frameTypeIds(frameTypeIds)
                    .excerptId(generateBaseExcerptId(createDt, serviceId, serviceInstance, resource, rsType))
                    .build();
            profilingStacks.add(profilingStack);
        }
        return profilingStacks;
    }

    public static Set<ProfilingHotspot> toProfilingHotspotList(SampleDataV3 sampleData) {
        return sampleData.getSamples().stream().map(threadStack -> {
            final String hotspotMethod = threadStack.getHotspotMethod();
            if (hotspotMethod == null) {
                return null;
            }

            ProfilingHotspot profilingHotspot = new ProfilingHotspot();
            final long sampleTime = threadStack.getSampleTime();
            profilingHotspot.setTime(formatLongToString(sampleTime / 1_000_000));
            profilingHotspot.setNanoTimestamp(sampleTime);
            profilingHotspot.setExcerptId(threadStack.getExcerptId());
            profilingHotspot.setInstanceAlloc(threadStack.getInstanceAlloc());
            final String traceId = threadStack.getTraceId();
            if (traceId != null) {
                profilingHotspot.setTraceId(Long.parseLong(traceId));
            }
            return profilingHotspot;
        }).filter(Objects::nonNull).collect(Collectors.toSet());
    }

    public static String generateExcerptId(ThreadStack threadStack, String serviceId, String serviceInstance) {
        if (threadStack == null) {
            return null;
        }
        final String createDt = EventIdGenerator.extractStrDateFrom(threadStack.getSampleTime() / 1000000);
        final String seed = createDt + serviceId + serviceInstance + threadStack.getResource() + threadStack.getRsType() + threadStack.getHotspotMethod() + threadStack.getLayer();
        return IDGenerator.getNextID(seed);
    }

    public static String generateExcerptId(ProfilingStack profilingStack, String serviceId, String serviceInstance) {
        if (profilingStack == null) {
            return null;
        }
        final String createDt = profilingStack.getCreateDt();
        final String seed = createDt + serviceId + serviceInstance + profilingStack.getResource() + profilingStack.getResourceType();
        return IDGenerator.getNextID(seed);
    }

    public static String[] parseMethod(String method) {
        String[] parts = method.split("\\.");
        String methodName, className, namespace;
        switch (parts.length) {
            case 1:
                methodName = parts[0];
                className = null;
                namespace = null;
                break;
            case 2:
                methodName = parts[1];
                className = parts[0];
                namespace = null;
                break;
            default:
                methodName = parts[parts.length - 1];
                className = parts[parts.length - 2];
                namespace = String.join(".", Arrays.copyOfRange(parts, 0, parts.length - 2));
                break;
        }

        return new String[]{namespace, className, methodName};
    }
}