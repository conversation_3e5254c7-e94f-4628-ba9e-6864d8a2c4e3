package com.databuff.common.threadLocal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collection;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ThreadLocalEntity {
    /**
     * 账号
     */
    private String account ;
    /**
     * 管理域ids
     */
    private Collection<String> agis ;

    private Boolean isSuperAdmin;

    /**
     * 判断是新建的对象还是更新后的对象
     */
    private boolean updateFlag;

    /**
     * 是否有注解
     */
    private boolean hasAnnotate;

    public ThreadLocalEntity(String account, Collection<String> agis) {
        this.account = account;
        this.agis = agis;
    }

}
