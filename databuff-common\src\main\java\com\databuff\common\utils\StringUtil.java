package com.databuff.common.utils;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * String工具
 * <AUTHOR>
 * @date 2019/3/14 14:48
 */
public class StringUtil extends StringUtils {

    private static final Random random = new Random();  // Compliant

    /**
     * 判断字符串是否为null或者空字符串
     *
     * @param str
     * @return
     */
    public static boolean isSingleUppercaseLetter(String str) {
        return str != null && str.matches("^[A-Z]$");
    }

    /**
     * 定义下划线
     */
    private static final char UNDERLINE = '_';

    /**
     * String为空判断(不允许空格)
     * @param str
     * @return boolean
     * <AUTHOR>
     * @date 2019/3/4 14:49
     */
    public static boolean isBlank(String str) {
        return str == null || "".equals(str.trim());
    }

    /**
     * String不为空判断(不允许空格)
     * @param str
     * @return boolean
     * <AUTHOR>
     * @date 2019/3/4 14:51
     */
    public static boolean isNotBlank(String str) {
        return !isBlank(str);
    }

    /**
     * Byte数组为空判断
     * @param bytes
     * @return boolean
     * <AUTHOR>
     * @date 2019/3/4 15:39
     */
    public static boolean isNull(byte[] bytes) {
        // 根据byte数组长度为0判断
        return bytes == null || bytes.length == 0;
    }

    /**
     * Byte数组不为空判断
     * @param bytes
     * @return boolean
     * <AUTHOR>
     * @date 2019/3/4 15:41
     */
    public static boolean isNotNull(byte[] bytes) {
        return !isNull(bytes);
    }

    /**
     * 驼峰转下划线工具
     * @param param
     * @return java.lang.String
     * <AUTHOR>
     * @date 2019/3/4 14:52
     */
    public static String camelToUnderline(String param) {
        if (isNotBlank(param)) {
            int len = param.length();
            StringBuilder sb = new StringBuilder(len);
            for (int i = 0; i < len; i++) {
                char c = param.charAt(i);
                if (Character.isUpperCase(c)) {
                    sb.append(UNDERLINE);
                    sb.append(Character.toLowerCase(c));
                } else {
                    sb.append(c);
                }
            }
            return sb.toString();
        } else {
            return "";
        }
    }

    /**
     * 下划线转驼峰工具
     * @param param
     * @return java.lang.String
     * <AUTHOR>
     * @date 2019/3/4 14:52
     */
    public static String underlineToCamel(String param) {
        if (isNotBlank(param)) {
            int len = param.length();
            StringBuilder sb = new StringBuilder(len);
            for (int i = 0; i < len; i++) {
                char c = param.charAt(i);
                if (c == 95) {
                    ++i;
                    if (i < len) {
                        sb.append(Character.toUpperCase(param.charAt(i)));
                    }
                } else {
                    sb.append(c);
                }
            }
            return sb.toString();
        } else {
            return "";
        }
    }

    /**
     * 驼峰转下划线工具
     * @param input
     * @return
     */
    public static String camelToUnderscore(String input) {
        if (input == null) {
            return null;
        }
        return input.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
    }

    /**
     * 在字符串两周添加''
     * @param param
     * @return java.lang.String
     * <AUTHOR>
     * @date 2019/3/4 14:53
     */
    public static String addSingleQuotes(String param) {
        return "\'" + param + "\'";
    }

    private static final char[] chars = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678".toCharArray();
    /**
     * 生成随机String
     * @param bit
     * @return
     */
    public static String randomString(int bit) {
        StringBuilder sb = new StringBuilder(bit);
        for (int i = 0; i < bit; i++) {
            sb.append(chars[random.nextInt(chars.length)]);
        }
        return sb.toString();
    }


    /**
     * 定义一个函数，输入一个字符串，返回一个包含所有大写字母的数组
     *
     * @param input
     * @return
     */
    public static List<String> parseUpperCase(String input) {
        if (StringUtils.isBlank(input)) {
            return Lists.newArrayList("A");
        }
        //创建一个空的字符数组，长度为26，用来存放大写字母
        char[] output = new char[26];
        //创建一个变量，用来记录数组中已经存放了多少个字母
        int count = 0;
        //遍历输入字符串中的每个字符
        for (int i = 0; i < input.length(); i++) {
            //获取当前字符
            char c = input.charAt(i);
            //判断当前字符是否是大写字母
            if (c >= 'A' && c <= 'Z') {
                //如果是大写字母，就把它存放到数组中
                output[count] = c;
                //增加计数器的值
                count++;
            }
        }
        //创建一个新的数组，长度为计数器的值，用来存放最终结果
        char[] result = new char[count];
        //把output数组中的前count个元素复制到result数组中
        System.arraycopy(output, 0, result, 0, count);
        //返回result数组
        ArrayList<String> list = new ArrayList<>();
        //遍历数组中的每个元素
        for (char c : result) {
            //把每个元素转换成一个字符串，并添加到列表中
            list.add(String.valueOf(c));
        }
        return list;
    }

    public static String convertNumberToString(Number number) {
        return number == null ? null : String.valueOf(number);
    }

    public static  String truncateString(String input, int maxLength) {
        if (input == null) {
            return null;
        }
        return input.length() > maxLength ? input.substring(0, maxLength) : input;
    }


    /**
     * 将给定的字符串截断，确保其 UTF-8 编码表示不超过指定的字节长度。
     * 此方法保留字符完整性，确保不会在多字节字符的中间截断。
     *
     * @param input    要截断的输入字符串。如果为 null，方法将返回 null。
     * @param maxBytes UTF-8 编码输出字符串允许的最大字节数。必须是非负数。
     * @return 一个 UTF-8 编码形式不超过指定最大字节长度的字符串。
     *         如果输入字符串的 UTF-8 编码形式已经在指定限制内，则返回原始字符串。
     *         如果输入为 null，则返回 null。
     *
     * @throws IllegalArgumentException 如果 maxBytes 为负数。
     *
     * @implNote 此方法计算每个字符的 UTF-8 字节长度时不实际执行 UTF-8 编码，
     *           这使得它对大型字符串更加高效。
     *           它正确处理所有 Unicode 字符，包括那些在 Java 的 UTF-16 内部表示中
     *           由代理对表示的基本多文种平面（BMP）之外的字符。
     *
     * @implSpec 返回的字符串保证是输入字符串的前缀，没有字符被改变，只是可能从末尾删除。
     *           返回字符串的 UTF-8 编码形式保证不超过 maxBytes 长度，但可能会更短。
     *
     * @example
     * <pre>
     * String input = "你好，世界!";
     * String result = StringUtil.truncateToByteLength(input, 9);
     * // result 将会是 "你好，"
     * // 因为 "你好，" 占 7 个字节，而 "世" 占 3 个字节，共 10 个字节超过了限制，
     * // 所以最终结果是只保留 "你好，"， 7 个字节。
     * </pre>
     */
    public static String truncateToByteLength(String input, int maxBytes) {
        if (input == null) {
            return null;
        }

        int byteCount = 0;
        int charIndex = 0;

        while (charIndex < input.length()) {
            char c = input.charAt(charIndex);
            int charByteCount = getUtf8ByteLength(c);

            if (byteCount + charByteCount > maxBytes) {
                break;
            }

            byteCount += charByteCount;
            charIndex++;
        }

        return input.substring(0, charIndex);
    }

    private static int getUtf8ByteLength(char c) {
        if (c <= 0x7F) {
            return 1;
        } else if (c <= 0x7FF) {
            return 2;
        } else if (Character.isHighSurrogate(c)) {
            return 4; // UTF-8 uses 4 bytes for characters outside the BMP
        } else {
            return 3;
        }
    }


    /**
     * 检测字符串是否为null，如果是null返回空字符串，否则返回原字符串
     * @param str 需要检测的字符串
     * @return 处理后的字符串
     */
    public static String nullToEmpty(String str) {
        return str == null ? "" : str;
    }


}
