```# 报告模板编辑器二期 PRD（增量部分）

> **版本**：v2.0.0
> **撰写日期**：2025‑07‑25
> **适用产品线**：Ultra Lens（可编辑）、Holo Lens（只读）
> **说明**：本文档仅列出 **在原有报告系统基础上新增或改动** 的功能，未变更内容不再赘述。字数≈ 2900 字。

---

## 1. 背景

现有弹窗式模板编辑器交互层级深、异常检测功能单一，且缺乏 AI 洞察。为提高运维/研发生成报告的效率与洞察深度，本期迭代聚焦以下三点：

1. **编辑体验升级**——“弹窗”改“整页”，所见即所得。
2. **组件体系扩充**——新增标题/正文/异常评估三类组件。
3. **智能洞察增强**——引入阈值 & 动态基线检测、AI 异常洞察与优化建议。

---

## 2. 范围与目标

| 模块     | 目标                  | 是否新增   |
| ------ | ------------------- | ------ |
| 模板编辑器  | 弹窗 → 整页，支持拖拽与自动编号   | **改动** |
| 组件库    | 标题、正文、异常评估          | **新增** |
| 图表高级配置 | 异常检测（阈值/动态基线）、AI 见解 | **新增** |
| 报告渲染脚本 | 全量序列、动态基线阈值、异常评估聚合  | **改动** |

---

## 3. 功能需求

### 3.1 整页式模板编辑器

| #   | 描述                               | 交互要点                            | Ultra‑Lens | Holo‑Lens |
| --- | -------------------------------- | ------------------------------- | ---------- | --------- |
| E‑1 | 编辑入口由弹窗改为整页`/template/edit/<id>` | ① 编辑区≥90 % viewport；② 离开未保存二次确认 | ✓          | 只读        |
| E‑2 | 保存即生效，版本号沿用旧逻辑                   | 保存后返回模板列表，状态显示“已更新”             | ✓          | ×         |

### 3.2 新组件

| 组件       | 关键信息                                  | 属性面板                  |
| -------- | ------------------------------------- | --------------------- |
| **标题**   | 大/中/小三级，按层级自动编号（大：一、二…；中：1.1；小：1.1.1） | `level`=1/2/3，最大 50 字 |
| **正文**   | 富文本输入，字数上限沿用旧值                        | 实时字数计数；超限保存禁用         |
| **异常评估** | 汇总所有启用异常检测的指标异常段                      | 无自定义属性                |

### 3.3 图表高级配置

| 功能           | 交互说明                                                                | 规则                          |
| ------------ | ------------------------------------------------------------------- | --------------------------- |
| **异常检测**     | 勾选后出现“检测方法”下拉：<br>· **阈值**——出现数值输入框<br>· **动态基线**——直接选择，无输入框        | 二选一必填；保存前校验                 |
| **阈值校验**     | 若检测方法=阈值且输入为空：<br>· 前端保存按钮置灰并提示<br>· 后端再次校验，空值返回 `ERR_NO_THRESHOLD` | 无自动兜底                       |
| **动态基线**     | 后端按历史数据实时计算阈值，写入渲染脚本                                                | 无需用户输入                      |
| **AI 见解与建议** | 复选框；仅 Ultra Lens 可勾选<br>Holo Lens 灰态并提示“启用外接大模型后可用”                 | 勾选后渲染两段文本：AI 异常洞察 / AI 优化建议 |

*限制：每个图表仅允许 1 个指标；如尝试添加第二个指标或公式，前端红框提示并禁用保存。*

### 3.4 报告渲染

| 流程        | 说明                                            |
| --------- | --------------------------------------------- |
| 触发方式      | 后端定时任务每 5 分钟扫描「待生成」报告，进入渲染队列                  |
| 数据输入      | 全量序列（无降采样）；指标元数据；检测方法与阈值                      |
| 异常检测      | · 阈值：直接对比<br>· 动态基线：后端实时阈值<br>检测结果最多返回 3 段    |
| AI Prompt | 将完整序列、阈值、异常段直接写入 Prompt，要求模型“仅输出 100 % 可验证原因” |
| 输出        | Word(*.docx*)文件；插入标题目录书签；异常评估区块置于末尾           |

---

## 4. 业务流程

```mermaid
flowchart TD
A[模板编辑器整页] -->|拖拽组件| B{图表配置}
B --> C1[异常检测 √]
B --> C2[AI 见解 √]
C1 -->|阈值/基线| D[保存模板]
D --> E[5 min 轮询生成]
E --> F[渲染脚本]
F --> G1[异常评估聚合]
F --> G2[AI Prompt 生成]
G1 & G2 --> H[Word 报告输出]
```

---

## 5. 非功能需求

| 类别  | 指标                                 |
| --- | ---------------------------------- |
| 性能  | 单份报告渲染 ≤ 30 s / 50 MB 数据；同期并发 30 份 |
| 安全  | 全量序列数据仅在后端内网与 LLM 服务交互，HTTPS 传输    |
| 国际化 | 暂不考虑                               |
| 可运维 | 报告渲染失败记录 stderr，支持 Kibana 检索       |

---

## 6. 出界范围

* 模板“复制/克隆”
* 图片/PDF 报告输出
* 多指标图表与公式
* 自动扩容、调度失败重试逻辑
* 动态基线算法可视化

---

a.	将模板编辑窗口改造为模板编辑页面；
b.	增加更多可用报告组件：
a)	标题：
i.	即标题类型的文本输入；
ii.	生成报告时，报告目录依据标题自动生成；
iii.	可配置项：
1.	标题类别：可选项有大标题、中标题、小标题；
      a)	样式参考如下（请UI协助给出更合适的样式）：
      i.	大标题：
1.	字号：一号；
2.	字体：黑体；
3.	对齐方式：靠左对齐；
4.	标题序号：按照大标题从上到下越来越大的顺序，自动生成并填充，用户不可修改；采用中文数字，如：一、二、三；若标题序号无法生成，则采用不要标题序号的方案；
      ii.	中标题：
1.	字号：二号；
2.	字体：黑体；
3.	对齐方式：靠左对齐；
4.	标题序号：根据当前标题所处在大标题下的位次和顺序，自动生成并填充，用户不可修改；采用阿拉伯数字，如：1.2、2.3、3.1；
      iii.	小标题：
1.	字号：三号；
2.	字体：黑体；
3.	对齐方式：靠左对齐；
4.	标题序号：根据当前标题所处在大标题下的位次和顺序，自动生成并填充，用户不可修改；采用阿拉伯数字，如：1.1.1、2.3.1、3.1.3；
      b)	字数限制：暂定50个字；
      b)	正文：
      i.	即正文类型的文本输入；
      ii.	由用户自定义输入文本内容；
      iii.	字数限制：保持原设计；
      c)	异常评估：
      i.	评估本报告中所有设置了异常检测的指标，将评估出存在异常的指标放在此区域内；按模板中指标从上到下的顺序显示每个异常，并总结出异常描述，异常描述模板【xx系统的xx服务的xx指标在xx时间点开始出现异常，持续xx时长】；
      ii.	若配置了“异常评估”，但没有指标设置“异常检测”，或未检测到异常，则异常评估的显示内容为“本次报告周期内无异常”；
      d)	折线图、饼状图、柱状图的配置，基本保持原设计不变；仅改变布局和样式；
      e)	在组件配置中增加高级配置项“异常检测”和“添加AI见解与建议”；
      i.	异常检测：可配置针对当前指标的异常检测功能
1.	检测方法：支持阈值检测和动态基线两种检测方法；
      a)	阈值检测：根据用户填写的阈值进行检测，超出阈值的时段即为异常时段；
      b)	动态基线：自动根据指标的历史数据，自动计算指标基线，并实时与实际值对比，判断指标是否存在异常；
2.	异常检测配置后检测到的异常会通过“异常评估”组件输出到报告中；
      ii.	添加AI见解与建议：
1.	当开启外接大模型功能时，在图表配置栏的高级配置中“添加AI见解与建议”选项变为正常可选状态；否则是灰色显示，禁止勾选状态；禁止勾选状态时，鼠标悬浮显示提示文案“启用外接大模型功能后可用”；
2.	勾选上此设置项后，在报告输出时，会在对应的指标图表下方，展示【AI分析见解】与【AI优化建议】；
      iii.	为更好保障异常检测功能的实现和可用，暂不支持在同一图表中添加多个指标，或进行指标计算；


```