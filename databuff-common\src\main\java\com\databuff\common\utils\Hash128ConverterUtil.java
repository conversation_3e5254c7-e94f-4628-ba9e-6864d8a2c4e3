package com.databuff.common.utils;

import com.google.common.io.BaseEncoding;

import java.math.BigInteger;
import java.nio.ByteBuffer;

public class Hash128ConverterUtil {
     public static String twoLongsToString(long msb, long lsb) {
        ByteBuffer buffer = ByteBuffer.allocate(16);
        buffer.putLong(msb);
        buffer.putLong(lsb);
        return BaseEncoding.base16().encode(buffer.array());
    }

    public static long[] stringToTwoLongs(String hex) {
        byte[] bytes = BaseEncoding.base16().decode(hex);
        ByteBuffer buffer = ByteBuffer.wrap(bytes);
        return new long[] { buffer.getLong(), buffer.getLong() };
    }

    /**
     * 将两个long（128位哈希）转换为StarRocks LARGEINT的字符串表示
     *
     * @param hashBits 包含两个long的数组，[0]为高位，[1]为低位
     * @return 适用于StarRocks LARGEINT类型的十进制字符串
     */
    public static String longArrayToLargeIntString(long[] hashBits) {
        if (hashBits.length != 2) {
            throw new IllegalArgumentException("需要包含2个long的数组");
        }

        // 方法1: 使用ByteBuffer和BigInteger（处理有符号整数最安全的方式）
        byte[] bytes = new byte[16];
        ByteBuffer buffer = ByteBuffer.wrap(bytes);
        buffer.putLong(hashBits[0]);  // 高64位
        buffer.putLong(hashBits[1]);  // 低64位

        // 转换为BigInteger并返回十进制字符串
        return new BigInteger(bytes).toString();
    }

    /**
     * 从StarRocks LARGEINT的字符串表示转回两个long（128位哈希）
     *
     * @param largeIntStr StarRocks LARGEINT的字符串表示
     * @return 包含两个long的数组，[0]为高位，[1]为低位
     */
    public static long[] largeIntStringToLongArray(String largeIntStr) {
        // 将字符串解析为BigInteger
        BigInteger bigInt = new BigInteger(largeIntStr);

        // 转换为字节数组
        byte[] bytes = bigInt.toByteArray();

        // BigInteger.toByteArray可能返回17字节（如果最高位为符号位）
        // 或少于16字节，所以需要处理这种情况
        byte[] paddedBytes = new byte[16];

        if (bytes.length >= 16) {
            // 复制最后16个字节（如果超过16个字节）
            System.arraycopy(bytes, bytes.length - 16, paddedBytes, 0, 16);
        } else {
            // 填充前导零字节
            System.arraycopy(bytes, 0, paddedBytes, 16 - bytes.length, bytes.length);
        }

        // 从字节数组提取两个long
        ByteBuffer buffer = ByteBuffer.wrap(paddedBytes);
        return new long[] { buffer.getLong(), buffer.getLong() };
    }

    // 测试代码
    public static void main(String[] args) {
        long long1 = 123456789L;
        long long2 = 987654321L;

        // 测试 twoLongsToString
        String str = twoLongsToString(long1, long2);
        System.out.println("String: " + str +" Stringlength: " + str.length());

        // 测试 stringToTwoLongs
        long[] longs = stringToTwoLongs(str);
        System.out.println("Long1: " + longs[0] + ", Long2: " + longs[1]);

        // 测试值
        long high = 0x1234567890ABCDEFL;
        long low = 0xFEDCBA0987654321L;
        long[] original = {high, low};

        // 转换为LARGEINT字符串
        String largeIntStr = longArrayToLargeIntString(original);
        System.out.println("LARGEINT字符串: " + largeIntStr);

        // 转回long数组
        long[] converted = largeIntStringToLongArray(largeIntStr);
        System.out.println("转换回的high: " + String.format("0x%016X", converted[0]));
        System.out.println("转换回的low: " + String.format("0x%016X", converted[1]));

        // 验证转换的正确性
        System.out.println("转换是否正确: " + (original[0] == converted[0] && original[1] == converted[1]));

    }
}