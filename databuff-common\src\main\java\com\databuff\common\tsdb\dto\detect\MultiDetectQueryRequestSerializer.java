package com.databuff.common.tsdb.dto.detect;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.io.IOException;

public class MultiDetectQueryRequestSerializer extends JsonSerializer<MultiDetectQueryRequest> {

    // 复用单个ObjectMapper实例
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void serialize(
            MultiDetectQueryRequest value,
            JsonGenerator gen,
            SerializerProvider serializers)
            throws IOException {
        ObjectNode root = objectMapper.createObjectNode();

        // 序列化 "1" -> A
        serializeField(root, "1", value.getA(), serializers);
        // 序列化 "2" -> B
        serializeField(root, "2", value.getB(), serializers);
        serializeField(root, "3", value.getC(), serializers);
        serializeField(root, "4", value.getD(), serializers);
        serializeField(root, "5", value.getE(), serializers);

        // 序列化其他字段（如 critical、warning）
        root.put("critical", value.getCritical().name());
        root.put("warning", value.getWarning().name());
        root.put("noData", value.getNoData().name());

        // 新增三个数值字段的序列化
        root.put("start", value.getStart());
        root.put("end", value.getEnd());
        root.put("interval", value.getInterval());

        gen.writeTree(root);
    }

    private void serializeField(
            ObjectNode root,
            String key,
            DetectQueryRequest request,
            SerializerProvider serializers) {
        if (request != null) {
            // 移除循环引用的错误逻辑
            ObjectNode nestedNode = objectMapper.valueToTree(request);
            root.set(key, nestedNode); // 直接设置到目标键下
        }
    }
}
