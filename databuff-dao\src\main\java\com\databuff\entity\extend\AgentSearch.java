package com.databuff.entity.extend;

import com.baomidou.mybatisplus.annotation.TableName;
import com.databuff.entity.AgentEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * @author:TianMing
 * @date: 2021/7/14
 * @time: 14:26
 */
@Data
public class AgentSearch extends AgentEntity {

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private String status;
    /**
     * 状态列表 Anomaly OffLine OnLine
     */
    @ApiModelProperty("状态列表")
    private List<String> statuses;

    @ApiModelProperty("综合查询")
    private String query;


    @ApiModelProperty(value = "Mysql分页查询页码",example = "1")
    private Integer pageNum;
    @ApiModelProperty(value = "Mysql分页查询数",example = "1")
    private Integer pageSize;


    @ApiModelProperty(value = "开始时间",example = "2021-07-15 00:00:00")
    private String fromTime;
    @ApiModelProperty(value = "结束时间",example = "2021-07-15 23:59:59")
    private String toTime;

    @ApiModelProperty(value = "top几",example = "1")
    private int topNum = 5;

    @ApiModelProperty(value = "排列顺序",example = "正序asc 倒序 desc")
    private String order = "desc";

    @ApiModelProperty(value = "离线时间，s",example = "300")
    private Long offlineTime = 300L;

    @ApiModelProperty("管理域权限主机")
    private List<String> permissionHosts ;
}
