package com.databuff.dao.mysql.datahub;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.databuff.entity.datahubv2.DataHubClusterEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

@Mapper
@Repository
public interface DataHubClusterMapper extends BaseMapper<DataHubClusterEntity> {
    @Select("select id, cluster_name, remark, enabled, create_user_id, update_user_id, port, " +
            " unix_timestamp(create_time)*1000 as create_time, unix_timestamp(update_time)*1000 as update_time " +
            " from dc_databuff_datahub_cluster " +
            " where is_delete=0 AND id = #{clusterId}")
    DataHubClusterEntity selectByClusterId(@Param("clusterId") Integer clusterId);


    @Select("select id, cluster_name, remark, enabled, create_user_id, update_user_id, port, " +
            " unix_timestamp(create_time)*1000 as create_time, unix_timestamp(update_time)*1000 as update_time " +
            " from dc_databuff_datahub_cluster " +
            " where is_delete=0 AND id = " +
            " (select cluster_id from dc_databuff_datahub_pipeline where is_delete=0 AND id = #{pipelineId}) ")
    DataHubClusterEntity selectClusterByPipelineId(@Param("pipelineId") Integer pipelineId);

    @Update("update dc_databuff_datahub_cluster set port=port+1 where is_delete=0 AND id = #{clusterId}")
    Integer increaseCLusterPort(@Param("clusterId") Integer clusterId);
}
