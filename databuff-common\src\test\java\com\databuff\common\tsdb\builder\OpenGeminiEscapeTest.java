package com.databuff.common.tsdb.builder;

import com.databuff.common.tsdb.model.AggFun;
import com.databuff.common.tsdb.model.Aggregation;
import com.databuff.common.tsdb.model.Where;
import com.databuff.common.tsdb.model.WhereOp;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * OpenGemini字符串转义测试
 * 包含功能测试和性能测试
 */
public class OpenGeminiEscapeTest {

    // ==================== 功能测试 ====================

    @Test
    @DisplayName("测试已转义单引号的处理")
    public void testEscapedSingleQuotes() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("service.http");
        builder.setMeasurement("service.http");
        builder.addAgg(new Aggregation(AggFun.SUM, "cnt"));

        // 添加包含已转义单引号的条件
        String valueWithEscapedQuotes = "SELECT get_json_string(`trigger`, \"$.serviceId\") as triggerId , count(1) as cnt FROM dc_alarm da WHERE (apiKey=?$.serviceId\") is not null AND da.gid IS NULL) AND (da.createDt IN (\\' 2025-05-12 13:58:00 \\')";
        builder.addWhere(new Where("resource", WhereOp.EQ, valueWithEscapedQuotes));

        // 构建SQL
        OpenGeminiQueryBuilder openGeminiBuilder = new OpenGeminiQueryBuilder(builder);
        String sql = openGeminiBuilder.buildQuery();

        // 验证SQL中的转义是否正确
        System.out.println("生成的SQL: " + sql);

        // 确保不会出现双重转义 \\\'
        assertTrue(!sql.contains("\\\\\'"), "SQL不应包含双重转义的单引号 (\\\\\\')");

        // 确保已转义的单引号保持原样
        assertTrue(sql.contains("\\'"), "SQL应包含正确转义的单引号 (\\')");
    }

    @Test
    @DisplayName("测试未转义单引号的处理")
    public void testUnescapedSingleQuotes() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("service.http");
        builder.setMeasurement("service.http");
        builder.addAgg(new Aggregation(AggFun.SUM, "cnt"));

        // 添加包含未转义单引号的条件
        String valueWithUnescapedQuotes = "SELECT * FROM table WHERE column = 'value'";
        builder.addWhere(new Where("resource", WhereOp.EQ, valueWithUnescapedQuotes));

        // 构建SQL
        OpenGeminiQueryBuilder openGeminiBuilder = new OpenGeminiQueryBuilder(builder);
        String sql = openGeminiBuilder.buildQuery();

        // 验证SQL中的转义是否正确
        System.out.println("生成的SQL: " + sql);

        // 确保单引号被正确转义
        assertTrue(sql.contains("\\'"), "SQL应包含正确转义的单引号 (\\')");
    }

    @Test
    @DisplayName("测试混合转义和未转义单引号的处理")
    public void testMixedQuotes() {
        // 创建查询构建器
        QueryBuilder builder = new QueryBuilder();
        builder.setDatabaseName("service.http");
        builder.setMeasurement("service.http");
        builder.addAgg(new Aggregation(AggFun.SUM, "cnt"));

        // 添加包含混合转义和未转义单引号的条件
        String valueWithMixedQuotes = "SELECT * FROM table WHERE column = 'value\\' AND another_column = 'test'";
        builder.addWhere(new Where("resource", WhereOp.EQ, valueWithMixedQuotes));

        // 构建SQL
        OpenGeminiQueryBuilder openGeminiBuilder = new OpenGeminiQueryBuilder(builder);
        String sql = openGeminiBuilder.buildQuery();

        // 验证SQL中的转义是否正确
        System.out.println("生成的SQL: " + sql);

        // 确保不会出现双重转义 \\\'
        assertTrue(!sql.contains("\\\\\'"), "SQL不应包含双重转义的单引号 (\\\\\\')");

        // 确保已转义的单引号保持原样，未转义的单引号被正确转义
        assertTrue(sql.contains("\\'"), "SQL应包含正确转义的单引号 (\\')");
    }

    // ==================== 性能测试 ====================

    // 原始实现的备份
    private String originalDealSpecialChar(String value) {
        // 当原始字符串包含单引号时，转义所有单引号并包裹在单引号中
        if (value.contains("'")) {
            return "'" + value.replace("'", "\\'") + "'";
        } else {
            // 当原始字符串不包含单引号时，直接包裹在单引号中
            return "'" + value + "'";
        }
    }

    // 原始实现的备份
    private String originalEscapeSpecialChar(String input) {
        if (StringUtils.isBlank(input)) {
            return input;
        }
        String[] defaultCharacters = {"'"};
        Set<String> characters = new HashSet<>();
        characters.addAll(Arrays.asList(defaultCharacters));
        String output = input;
        for (String character : characters) {
            output = output.replace(character, "\\" + character);
        }
        return output;
    }

    // 新实现
    private String newDealSpecialChar(String value) {
        if (StringUtils.isBlank(value)) {
            return "''";
        }

        // 当原始字符串包含单引号时，需要处理转义
        if (value.contains("'")) {
            StringBuilder result = new StringBuilder("'");
            char[] chars = value.toCharArray();
            for (int i = 0; i < chars.length; i++) {
                // 检查是否是已经转义的单引号 (\') - 不需要再次转义
                if (chars[i] == '\\' && i + 1 < chars.length && chars[i + 1] == '\'') {
                    result.append("\\'"); // 保持原有的转义
                    i++; // 跳过下一个字符，因为已经处理了
                } else if (chars[i] == '\'') {
                    // 未转义的单引号，需要转义
                    result.append("\\'");
                } else {
                    // 其他字符直接添加
                    result.append(chars[i]);
                }
            }
            result.append("'");
            return result.toString();
        } else {
            // 当原始字符串不包含单引号时，直接包裹在单引号中
            return "'" + value + "'";
        }
    }

    // 新实现
    private String newEscapeSpecialChar(String input) {
        if (StringUtils.isBlank(input)) {
            return input;
        }

        String[] defaultCharacters = {"'"};
        Set<String> characters = new HashSet<>();
        characters.addAll(Arrays.asList(defaultCharacters));

        StringBuilder result = new StringBuilder();
        char[] chars = input.toCharArray();

        for (int i = 0; i < chars.length; i++) {
            boolean isEscaped = false;

            // 检查当前字符是否是转义字符
            if (chars[i] == '\\' && i + 1 < chars.length) {
                char nextChar = chars[i + 1];
                // 检查下一个字符是否是需要转义的特殊字符
                for (String character : characters) {
                    if (character.charAt(0) == nextChar) {
                        // 已经转义的特殊字符，保持原样
                        result.append("\\").append(nextChar);
                        i++; // 跳过下一个字符
                        isEscaped = true;
                        break;
                    }
                }
            }

            if (!isEscaped) {
                // 检查当前字符是否是需要转义的特殊字符
                boolean isSpecialChar = false;
                for (String character : characters) {
                    if (character.charAt(0) == chars[i]) {
                        // 未转义的特殊字符，需要转义
                        result.append("\\").append(chars[i]);
                        isSpecialChar = true;
                        break;
                    }
                }

                if (!isSpecialChar) {
                    // 普通字符，直接添加
                    result.append(chars[i]);
                }
            }
        }

        return result.toString();
    }

    /**
     * 生成测试数据
     * @param count 生成的字符串数量
     * @param avgLength 平均字符串长度
     * @param quoteRatio 包含单引号的字符串比例 (0-1)
     * @param escapedQuoteRatio 已转义单引号的比例 (0-1)
     * @return 测试数据列表
     */
    private List<String> generateTestData(int count, int avgLength, double quoteRatio, double escapedQuoteRatio) {
        List<String> testData = new ArrayList<>(count);
        Random random = new Random(System.currentTimeMillis());

        for (int i = 0; i < count; i++) {
            // 随机生成字符串长度，在平均长度的80%-120%之间浮动
            int length = (int) (avgLength * (0.8 + 0.4 * random.nextInt()));

            StringBuilder sb = new StringBuilder(length);

            // 生成基础字符串
            for (int j = 0; j < length; j++) {
                // 随机生成ASCII可打印字符 (32-126)，但排除单引号和反斜杠
                char c;
                do {
                    c = (char) (32 + random.nextInt(95));
                } while (c == '\'' || c == '\\');

                sb.append(c);
            }

            // 根据比例添加单引号
            if (random.nextDouble() < quoteRatio) {
                int quoteCount = 1 + random.nextInt(5); // 1-5个单引号
                for (int j = 0; j < quoteCount; j++) {
                    int position = random.nextInt(sb.length());

                    // 根据比例决定是添加普通单引号还是已转义单引号
                    if (random.nextDouble() < escapedQuoteRatio) {
                        sb.insert(position, "\\'");
                    } else {
                        sb.insert(position, "'");
                    }
                }
            }

            testData.add(sb.toString());
        }

        return testData;
    }

    /**
     * 生成SQL查询样本
     * @param count 生成的查询数量
     * @return SQL查询列表
     */
    private List<String> generateSqlQueries(int count) {
        List<String> queries = new ArrayList<>(count);
        Random random = new Random(System.currentTimeMillis());

        String[] templates = {
                "SELECT * FROM table WHERE column = 'value'",
                "SELECT id, name FROM users WHERE status = 'active' AND role = 'admin'",
                "SELECT get_json_string(`trigger`, \"$.serviceId\") as triggerId , count(1) as cnt FROM dc_alarm da WHERE (apiKey=?$.serviceId\") is not null AND da.gid IS NULL) AND (da.createDt IN (\\' 2025-05-12 13:58:00 \\')",
                "SELECT SUM(amount) FROM transactions WHERE date BETWEEN '2023-01-01' AND '2023-12-31' GROUP BY customer_id HAVING SUM(amount) > 1000",
                "INSERT INTO logs (message, level, timestamp) VALUES ('Error occurred: could not connect to \\'database\\'', 'ERROR', CURRENT_TIMESTAMP)",
                "UPDATE products SET description = 'This product\\'s features include: 1) Easy setup, 2) Low maintenance' WHERE category = 'electronics'",
                "SELECT * FROM \"measurement\" WHERE \"tag\"::tag = 'value with \\'quoted\\' text' AND time > now() - 7d"
        };

        for (int i = 0; i < count; i++) {
            String base = templates[random.nextInt(templates.length)];

            // 随机添加更多的单引号和已转义单引号
            StringBuilder sb = new StringBuilder(base);
            int modifications = random.nextInt(10); // 0-9次修改

            for (int j = 0; j < modifications; j++) {
                int position = random.nextInt(sb.length());

                if (random.nextBoolean()) {
                    // 添加普通单引号
                    sb.insert(position, "'");
                } else {
                    // 添加已转义单引号
                    sb.insert(position, "\\'");
                }
            }

            // 随机增加字符串长度
            int extraLength = random.nextInt(500); // 0-499额外字符
            for (int j = 0; j < extraLength; j++) {
                int position = random.nextInt(sb.length());
                char c = (char) (32 + random.nextInt(95)); // ASCII可打印字符
                sb.insert(position, c);
            }

            queries.add(sb.toString());
        }

        return queries;
    }

    @Test
    @DisplayName("测试原始实现和新实现的性能差异 - 随机字符串")
    public void testPerformanceWithRandomStrings() {
        // 测试参数
        int[] testSizes = {1000, 10000, 100000}; // 测试数据量
        int[] avgLengths = {100, 500, 1000}; // 平均字符串长度

        System.out.println("=== 随机字符串性能测试 ===");
        System.out.println("数据量\t平均长度\t原始实现(ms)\t新实现(ms)\t性能比例(新/原)");

        for (int size : testSizes) {
            for (int avgLength : avgLengths) {
                // 生成测试数据 - 50%的字符串包含单引号，30%的单引号是已转义的
                List<String> testData = generateTestData(size, avgLength, 0.5, 0.3);

                // 测试原始实现
                long startOriginal = System.nanoTime();
                for (String data : testData) {
                    String escaped = originalEscapeSpecialChar(data);
                    originalDealSpecialChar(escaped);
                }
                long endOriginal = System.nanoTime();
                long originalTime = TimeUnit.NANOSECONDS.toMillis(endOriginal - startOriginal);

                // 测试新实现
                long startNew = System.nanoTime();
                for (String data : testData) {
                    String escaped = newEscapeSpecialChar(data);
                    newDealSpecialChar(escaped);
                }
                long endNew = System.nanoTime();
                long newTime = TimeUnit.NANOSECONDS.toMillis(endNew - startNew);

                // 计算性能比例
                double ratio = (double) newTime / originalTime;

                System.out.printf("%d\t%d\t%d\t%d\t%.2f%n", size, avgLength, originalTime, newTime, ratio);
            }
        }
        assertTrue(testSizes.length>1);

    }

    @Test
    @DisplayName("测试原始实现和新实现的性能差异 - SQL查询")
    public void testPerformanceWithSqlQueries() {
        // 测试参数
        int[] testSizes = {1000, 10000, 50000}; // 测试数据量

        System.out.println("=== SQL查询性能测试 ===");
        System.out.println("数据量\t原始实现(ms)\t新实现(ms)\t性能比例(新/原)");

        for (int size : testSizes) {
            // 生成SQL查询测试数据
            List<String> testData = generateSqlQueries(size);

            // 测试原始实现
            long startOriginal = System.nanoTime();
            for (String data : testData) {
                String escaped = originalEscapeSpecialChar(data);
                originalDealSpecialChar(escaped);
            }
            long endOriginal = System.nanoTime();
            long originalTime = TimeUnit.NANOSECONDS.toMillis(endOriginal - startOriginal);

            // 测试新实现
            long startNew = System.nanoTime();
            for (String data : testData) {
                String escaped = newEscapeSpecialChar(data);
                newDealSpecialChar(escaped);
            }
            long endNew = System.nanoTime();
            long newTime = TimeUnit.NANOSECONDS.toMillis(endNew - startNew);

            // 计算性能比例
            double ratio = (double) newTime / originalTime;

            System.out.printf("%d\t%d\t%d\t%.2f%n", size, originalTime, newTime, ratio);
        }
        assertTrue(testSizes.length>1);

    }

    @Test
    @DisplayName("测试原始实现和新实现的正确性差异")
    public void testCorrectness() {
        // 测试用例
        String[] testCases = {
                // 普通字符串
                "Normal string without quotes",
                // 包含单引号的字符串
                "String with 'single quotes'",
                // 包含已转义单引号的字符串
                "String with \\'escaped\\' quotes",
                // 混合单引号和已转义单引号
                "Mixed 'quotes' and \\'escaped\\' quotes",
                // SQL查询
                "SELECT * FROM table WHERE column = 'value'",
                // 复杂SQL查询
                "SELECT get_json_string(`trigger`, \"$.serviceId\") as triggerId , count(1) as cnt FROM dc_alarm da WHERE (apiKey=?$.serviceId\") is not null AND da.gid IS NULL) AND (da.createDt IN (\\' 2025-05-12 13:58:00 \\')"
        };

        System.out.println("=== 正确性测试 ===");
        System.out.println("测试用例\t原始实现\t新实现\t结果相同");

        for (String testCase : testCases) {
            // 原始实现
            String originalEscaped = originalEscapeSpecialChar(testCase);
            String originalResult = originalDealSpecialChar(originalEscaped);

            // 新实现
            String newEscaped = newEscapeSpecialChar(testCase);
            String newResult = newDealSpecialChar(newEscaped);

            // 比较结果
            boolean isSame = originalResult.equals(newResult);

            // 输出简短版本以便于比较
            String shortTestCase = testCase.length() > 30 ? testCase.substring(0, 27) + "..." : testCase;
            String shortOriginal = originalResult.length() > 30 ? originalResult.substring(0, 27) + "..." : originalResult;
            String shortNew = newResult.length() > 30 ? newResult.substring(0, 27) + "..." : newResult;

            System.out.printf("\"%s\"\t\"%s\"\t\"%s\"\t%s%n",
                    shortTestCase, shortOriginal, shortNew, isSame ? "是" : "否");

            // 如果结果不同，输出详细信息
            if (!isSame) {
                System.out.println("原始输入: " + testCase);
                System.out.println("原始实现: " + originalResult);
                System.out.println("新实现: " + newResult);
                System.out.println();
            }
        }
        assertTrue(testCases.length>1);
    }

    @Test
    @DisplayName("测试大量重复转义的情况")
    public void testMultipleEscaping() {
        // 创建包含多层转义的测试用例
        String[] testCases = {
                // 单引号
                "'",
                // 已转义单引号
                "\\'",
                // 双重转义单引号
                "\\\\'",
                // 三重转义单引号
                "\\\\\\'"
        };

        System.out.println("=== 多层转义测试 ===");
        System.out.println("原始输入\t原始实现\t新实现");

        for (String testCase : testCases) {
            // 原始实现
            String originalEscaped = originalEscapeSpecialChar(testCase);
            String originalResult = originalDealSpecialChar(originalEscaped);

            // 新实现
            String newEscaped = newEscapeSpecialChar(testCase);
            String newResult = newDealSpecialChar(newEscaped);

            System.out.println(testCase + "\t" + originalResult + "\t" + newResult);
        }
        assertTrue(testCases.length>1);

    }

    @Test
    @DisplayName("测试极端情况 - 非常长的字符串")
    public void testExtremelyLongString() {
        // 创建一个非常长的字符串，包含大量单引号和转义字符
        StringBuilder longStringBuilder = new StringBuilder(1000000); // 1百万字符
        Random random = new Random(System.currentTimeMillis());

        for (int i = 0; i < 1000000; i++) {
            int r = random.nextInt(100);
            if (r < 1) { // 1%的概率添加单引号
                longStringBuilder.append("'");
            } else if (r < 2) { // 1%的概率添加已转义单引号
                longStringBuilder.append("\\'");
            } else {
                longStringBuilder.append((char) (32 + random.nextInt(95))); // 随机ASCII可打印字符
            }
        }

        String longString = longStringBuilder.toString();
        assertTrue(longString.length()>1);

        System.out.println("=== 极端情况测试 - 非常长的字符串 ===");
        System.out.println("字符串长度: " + longString.length());

        // 测试原始实现
        long startOriginal = System.nanoTime();
        String originalEscaped = originalEscapeSpecialChar(longString);
        String originalResult = originalDealSpecialChar(originalEscaped);
        long endOriginal = System.nanoTime();
        long originalTime = TimeUnit.NANOSECONDS.toMillis(endOriginal - startOriginal);

        // 测试新实现
        long startNew = System.nanoTime();
        String newEscaped = newEscapeSpecialChar(longString);
        String newResult = newDealSpecialChar(newEscaped);
        long endNew = System.nanoTime();
        long newTime = TimeUnit.NANOSECONDS.toMillis(endNew - startNew);

        // 计算性能比例
        double ratio = (double) newTime / originalTime;

        System.out.println("原始实现处理时间: " + originalTime + "ms");
        System.out.println("新实现处理时间: " + newTime + "ms");
        System.out.println("性能比例(新/原): " + ratio);

        // 检查结果长度
        System.out.println("原始实现结果长度: " + originalResult.length());
        System.out.println("新实现结果长度: " + newResult.length());

        // 检查是否有双重转义
        int originalDoubleEscapeCount = countOccurrences(originalResult, "\\\\\'");
        int newDoubleEscapeCount = countOccurrences(newResult, "\\\\\'");

        System.out.println("原始实现中双重转义的数量: " + originalDoubleEscapeCount);
        System.out.println("新实现中双重转义的数量: " + newDoubleEscapeCount);
    }

    /**
     * 计算子字符串在字符串中出现的次数
     */
    private int countOccurrences(String str, String subStr) {
        int count = 0;
        int lastIndex = 0;

        while (lastIndex != -1) {
            lastIndex = str.indexOf(subStr, lastIndex);

            if (lastIndex != -1) {
                count++;
                lastIndex += subStr.length();
            }
        }

        return count;
    }
}
