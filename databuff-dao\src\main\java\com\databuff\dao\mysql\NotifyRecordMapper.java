package com.databuff.dao.mysql;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.databuff.entity.NotifyRecord;
import com.databuff.entity.NotifyRecordParams;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;

/**
 * @author:TianMing
 * @date: 2023/12/21
 * @time: 14:33
 */
@Mapper
public interface NotifyRecordMapper  extends BaseMapper<NotifyRecord> {

    @Select(
            "<script>" +
                    "SELECT  r1.*\n" +
                    "FROM df_notify_record r1\n" +
                    "where 1=1" +
                    "<when test='queryParamDTO.respPolicyName!=null and queryParamDTO.respPolicyName!=&apos;&apos; '> and r1.resp_policy_name like binary CONCAT('%', #{queryParamDTO.respPolicyName},'%')</when> " +
                    "<when test='queryParamDTO.rcvNames!=null and queryParamDTO.rcvNames!=&apos;&apos; '> and (r1.rcv_names like binary CONCAT('%', #{queryParamDTO.rcvNames},'%') or r1.rcv_ug_names like binary CONCAT('%', #{queryParamDTO.rcvNames},'%') )</when> " +
                    "<when test='queryParamDTO.alertDesc!=null and queryParamDTO.alertDesc!=&apos;&apos; '> and r1.alert_desc like binary CONCAT('%', #{queryParamDTO.alertDesc},'%')</when> " +
                    "<when test='queryParamDTO.method!=null and queryParamDTO.method!=&apos;&apos; '> and r1.method=#{queryParamDTO.method}</when> " +
                    "<when test='queryParamDTO.result!=null and queryParamDTO.result!=&apos;&apos; '> and r1.result=#{queryParamDTO.result}</when> " +
                    "<when test='queryParamDTO.errMsg!=null and queryParamDTO.errMsg!=&apos;&apos; '> and r1.err_msg like binary CONCAT('%', #{queryParamDTO.errMsg},'%')</when> " +
                    "<when test='queryParamDTO.alertType!=null and queryParamDTO.alertType!=&apos;&apos; '> and r1.alert_type=#{queryParamDTO.alertType}</when> " +
                    "<when test='queryParamDTO.apiKey!=null and queryParamDTO.apiKey!=&apos;&apos; '> and r1.api_key=#{queryParamDTO.apiKey}</when> " +
                    "<when test='queryParamDTO.from!=null'> and r1.notice_time &gt;= #{queryParamDTO.from} </when> " +
                    "<when test='queryParamDTO.to!=null '> and r1.notice_time &lt; #{queryParamDTO.to} </when> " +
                    "<when test='queryParamDTO.alarmId!=null and queryParamDTO.alarmId!=&apos;&apos; '> and r1.alarm_id=#{queryParamDTO.alarmId}</when> " +
                    "<if test='!domainManagerStatus'> AND r1.gid IS NULL </if> " +
                    "<if test='domainManagerStatus'> AND r1.gid IS NOT NULL </if> " +
                    "<if test='!allEntityPermission'>" +
                    "   <if test='gids != null and gids.size() &gt; 0'>" +
                    "       AND (r1.gid IN " +
                    "       <foreach item='gid' collection='gids' open='(' separator=',' close=')'>" +
                    "           #{gid}" +
                    "       </foreach>)" +
                    "   </if>" +
                    "   <if test='gids == null or gids.size() == 0'>" +
                    "       AND 1=0" +
                    "   </if>" +
                    "</if>" +
                    "<when test='queryParamDTO.sortField!=null and queryParamDTO.sortField!=&apos;&apos; '>\n" +
                    " order by ${queryParamDTO.sortField} ${queryParamDTO.sortOrder}\n" +
                    "</when>" +
                    "</script>")
    List<NotifyRecord> pageAll(@Param("queryParamDTO") NotifyRecordParams queryParamDTO,
                               @Param("allEntityPermission") boolean allEntityPermission,
                               @Param("domainManagerStatus") boolean domainManagerStatus,
                               @Param("gids") Collection<String> gids);


    @Select(
            "<script>" +
                    "SELECT  count(0)\n" +
                    "FROM df_notify_record r1 USE INDEX(idx_notify_api_time_gid)\n" +
                    "where 1=1" +
                    "<when test='queryParamDTO.respPolicyName!=null and queryParamDTO.respPolicyName!=&apos;&apos; '> and r1.resp_policy_name like binary CONCAT('%', #{queryParamDTO.respPolicyName},'%')</when> " +
                    "<when test='queryParamDTO.rcvNames!=null and queryParamDTO.rcvNames!=&apos;&apos; '> and (r1.rcv_names like binary CONCAT('%', #{queryParamDTO.rcvNames},'%') or r1.rcv_ug_names like binary CONCAT('%', #{queryParamDTO.rcvNames},'%') )</when> " +
                    "<when test='queryParamDTO.alertDesc!=null and queryParamDTO.alertDesc!=&apos;&apos; '> and r1.alert_desc like binary CONCAT('%', #{queryParamDTO.alertDesc},'%')</when> " +
                    "<when test='queryParamDTO.method!=null and queryParamDTO.method!=&apos;&apos; '> and r1.method=#{queryParamDTO.method}</when> " +
                    "<when test='queryParamDTO.result!=null and queryParamDTO.result!=&apos;&apos; '> and r1.result=#{queryParamDTO.result}</when> " +
                    "<when test='queryParamDTO.errMsg!=null and queryParamDTO.errMsg!=&apos;&apos; '> and r1.err_msg like binary CONCAT('%', #{queryParamDTO.errMsg},'%')</when> " +
                    "<when test='queryParamDTO.alertType!=null and queryParamDTO.alertType!=&apos;&apos; '> and r1.alert_type=#{queryParamDTO.alertType}</when> " +
                    "<when test='queryParamDTO.apiKey!=null and queryParamDTO.apiKey!=&apos;&apos; '> and r1.api_key=#{queryParamDTO.apiKey}</when> " +
                    "<when test='queryParamDTO.from!=null'> and r1.notice_time &gt;= #{queryParamDTO.from} </when> " +
                    "<when test='queryParamDTO.to!=null '> and r1.notice_time &lt; #{queryParamDTO.to} </when> " +
                    "<when test='queryParamDTO.alarmId!=null and queryParamDTO.alarmId!=&apos;&apos; '> and r1.alarm_id=#{queryParamDTO.alarmId}</when> " +
                    "<if test='!domainManagerStatus'> AND r1.gid IS NULL </if> " +
                    "<if test='domainManagerStatus'> AND r1.gid IS NOT NULL </if> " +
                    "<if test='!allEntityPermission'>" +
                    "   <if test='gids != null and gids.size() &gt; 0'>" +
                    "       AND (r1.gid IN " +
                    "       <foreach item='gid' collection='gids' open='(' separator=',' close=')'>" +
                    "           #{gid}" +
                    "       </foreach>)" +
                    "   </if>" +
                    "   <if test='gids == null or gids.size() == 0'>" +
                    "       AND 1=0" +
                    "   </if>" +
                    "</if>" +
                    "<when test='queryParamDTO.sortField!=null and queryParamDTO.sortField!=&apos;&apos; '>\n" +
                    " order by ${queryParamDTO.sortField} ${queryParamDTO.sortOrder}\n" +
                    "</when>" +
                    "</script>")
    long countByTime(@Param("queryParamDTO") NotifyRecordParams queryParamDTO,
                     @Param("allEntityPermission") boolean allEntityPermission,
                     @Param("domainManagerStatus") boolean domainManagerStatus,
                     @Param("gids") Collection<String> gids);
}
