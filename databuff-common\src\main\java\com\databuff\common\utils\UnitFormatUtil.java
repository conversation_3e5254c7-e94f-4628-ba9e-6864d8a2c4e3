package com.databuff.common.utils;


import java.text.CharacterIterator;
import java.text.StringCharacterIterator;
import java.util.HashMap;

import static com.databuff.common.utils.TimeUtil.ONE_DAY_S;

/**
 * 人类可读工具类
 * @package com.dctech.andrea.util
 * @company: dacheng
 * @author: zlh
 * @createDate: 2021/3/8
 */
public class UnitFormatUtil {
    private static HashMap<String,String> siFormat;
    private static HashMap<String,Double> timeFormat;
    private static HashMap<String,Double> byteFormat;
    static {
        siFormat = new HashMap<>(8);
        siFormat.put("garbage collection","gc");
        siFormat.put("garbage collections","gc");
        siFormat.put("thread","");
        siFormat.put("threads","");
        siFormat.put("packet","pkt");
        siFormat.put("packets","pkt");
        siFormat.put("request","req");
        siFormat.put("requests","req");
        timeFormat = new HashMap<>(8);
        timeFormat.put("nanosecond",1e-9);
        timeFormat.put("nanoseconds",1e-9);
        timeFormat.put("ns",1e-9);
        timeFormat.put("microsecond",0.000001);
        timeFormat.put("microseconds",0.000001);
        timeFormat.put("μs",0.000001);
        timeFormat.put("millisecond",0.001);
        timeFormat.put("milliseconds",0.001);
        timeFormat.put("ms",0.001);
        timeFormat.put("second",1.0);
        timeFormat.put("seconds",1.0);
        timeFormat.put("s",1.0);
        byteFormat = new HashMap<>(8);
        byteFormat.put("bit",0.125);
        byteFormat.put("bits",0.125);
        byteFormat.put("TiB",1099511627776.0);
        byteFormat.put("GiB",1073741824.0);
        byteFormat.put("mebibyte",1048576.0);
        byteFormat.put("mebibytes",1048576.0);
        byteFormat.put("MiB",1048576.0);
        byteFormat.put("kibibyte",1024.0);
        byteFormat.put("kibibytes",1024.0);
        byteFormat.put("KiB",1024.0);
        byteFormat.put("B",1.0);
        byteFormat.put("byte",1.0);
        byteFormat.put("bytes",1.0);
    }

    /**
     * 可读性格式化
     * @param value
     * @return
     */
    public static String humanReadableFormat(Double value, String format) {
       return humanReadableFormat(value, format,  null);
    }

    /**
     * 可读性格式化
     * @param value
     * @return
     */
    public static String humanReadableFormat(Double value, String unit, String format) {
        // 校验seconds
        if (value == Double.POSITIVE_INFINITY || value == Double.NEGATIVE_INFINITY){
            return null;
        }
        if ("".equals(unit) || unit == null){
            // 默认以1000进制处理
            return  humanReadableByteCountSI(value,"");
        }
        if ("%".equals(unit) || "percent".equals(unit)){
            // %
            return  humanReadableByteCountSI(value,"")+"%";
        }
        if ("fractions".equals(unit) || "fraction".equals(unit)){
            // fractions(%)
            return  humanReadableByteCountSI(value*100,"")+"%";
        }
        if (siFormat.containsKey(unit)){
            // 其他缩写单位
            return  humanReadableByteCountSI(value,siFormat.get(unit));
        }
        if (timeFormat.containsKey(unit)){
            // 时间,统一格式化到秒
            value = value * timeFormat.get(unit);
            return  humanReadableSeconds(value);
        }
        if (byteFormat.containsKey(unit)){
            // Bytes,统一格式化到B
            value = value * byteFormat.get(unit);
            return  humanReadableByteCountBin(value,format);
        }
        if ("°C".equals(unit)){
            // 摄氏度
            return  humanReadableByteCountSI(value,"°C");
        }
        if (unit.startsWith("!")){
            // 带!默认以1000进制处理，且去掉!
            return  humanReadableByteCountSI(value,unit.replace("!",""));
        }
        if ("uptime".equals(unit)){
            // uptime
            return  humanReadableUptime(value.longValue());
        }
        return  humanReadableByteCountSI(value,unit);
    }

    /**
     * 时间格式化
     * @param value 秒级
     * @return
     */
    private static String humanReadableSeconds(Double value) {
        if (value < 0.000001){
            return String.format("%.2f"+ "ns",value/1e-9);
        }
        if (value < 0.001){
            return String.format("%.2f"+ "us",value/0.000001);
        }
        if (value < 1){
            return String.format("%.2f"+ "ms",value/0.001);
        }
        if (value < 60){
            return String.format("%.2f"+ "s",value);
        }
        if (value < 3600){
            return String.format("%.2f"+ "min",value/60);
        }
        if (value < 86400){
            return String.format("%.2f"+ "h",value/3600);
        }
        if (value < 2592000){
            return String.format("%.2f"+ "d",value/86400);
        }
        if (value < 31536000){
            return String.format("%.2f"+ "M",value/2592000);
        }
        return String.format("%.2f"+ "y",value/31536000);
    }

    /**
     * uptime格式可读
     * @param seconds
     * @return
     */
    private static String humanReadableUptime(long seconds) {
        long days = seconds / (60 * 60 * 24);
        long hours = (seconds % (60 * 60 * 24)) / (60 * 60);
        long minutes = (seconds % (60 * 60)) / 60;
        return String.format("%2ddays, %02d:%02d:%02d", days,hours, minutes, seconds % 60);
    }

    /**
     * 秒或毫秒可读
     * @param value
     * @param format
     * @return
     */
    private String humanReadableSeconds(double value, String format) {
        // 处理毫秒
        double seconds = "ms".equals(format) ? value/1000 : value;
        double ms = 0.001;
        if (seconds < ms) {
            return "<1 ms";
        }
        return  timeFormat(seconds);
    }

    private String timeFormat(double seconds) {
        double dayStamp = ONE_DAY_S;
        Double Y = Math.floor(seconds / dayStamp / 365);
        Double M = Math.floor((seconds / dayStamp % 365) / 30);
        Double D = Math.floor((seconds / dayStamp % 365) % 30);
        if (Y != 0){
            return Y.longValue()+"Y "+M.longValue()+"M "+D.longValue()+"D";
        }
        Double h = Math.floor(seconds / 3600 % 24);
        if (M != 0){
            return M.longValue()+"M "+D.longValue()+"D "+h.longValue()+"h";
        }
        Double m = Math.floor(seconds / 60 % 60);
        if (D != 0){
            return D.longValue()+"D "+h.longValue()+"h "+m.longValue()+"m";
        }
        Double s = Math.floor(seconds % 60);
        if (h != 0){
            return h.longValue()+"h "+m.longValue()+"m "+s.longValue()+"s";
        }
        Double S = Math.floor(seconds * 1000 % 1000);
        if (m != 0){
            return m.longValue()+"m "+s.longValue()+"s "+S.longValue()+"ms";
        }
        if (s != 0){
            return s.longValue()+"s "+S.longValue()+"ms";
        }else {
            return S.longValue()+"ms";
        }
    }

    /**
     * bits进制可读或十进制可读
     * @param value
     * @param format
     * @return
     */
    private static String humanReadableByteCountSI(double value, String format) {
        long SI = 1000;
        if (-SI < value && value < SI) {
            return String.format("%.2f"+ format,value);
        }
        CharacterIterator ci = new StringCharacterIterator("kMGTPE");
        while (value <= -999_950 || value >= 999_950) {
            value /= 1000;
            ci.next();
        }
        return String.format("%.1f%c"+ format, value / 1000.0, ci.current());
    }

    /**
     * bytes可读
     * @param bytes
     * @param format
     * @return
     */
    private static String humanReadableByteCountBin(Double bytes, String format) {
        Double absB = bytes == Double.MIN_VALUE ? Double.MAX_VALUE : Math.abs(bytes);
        double BIN = 1024d;
        if (absB < BIN) {
            return String.format("%.2f%s", bytes, format);
        }
        // 保留两位小数
        long value = absB.longValue();
        CharacterIterator ci = new StringCharacterIterator("KMGTPE");
        for (int i = 40; i >= 0 && absB > 0xfffccccccccccccL >> i; i -= 10) {
            value >>= 10;
            ci.next();
        }
        value *= Long.signum(bytes.longValue());
        return String.format("%.1f%c", value  / 1024.0, ci.current());
    }
}
