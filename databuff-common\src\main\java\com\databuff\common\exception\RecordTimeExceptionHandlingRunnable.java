package com.databuff.common.exception;


import com.databuff.common.threadLocal.NowTimeThreadLocal;

public class RecordTimeExceptionHandlingRunnable extends ExceptionHandlingRunnable implements Runnable {

    public RecordTimeExceptionHandlingRunnable(Runnable delegate) {
        super(delegate);
    }

    @Override
    public void run() {
        try {
            super.run();
        } finally {
            NowTimeThreadLocal.remove();
        }
    }
}