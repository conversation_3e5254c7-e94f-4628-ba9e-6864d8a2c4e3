package com.databuff.entity;

import com.databuff.common.utils.DateUtils;
import com.databuff.common.utils.TimeUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author:TianMing
 * @date: 2021/9/8
 * @time: 11:06
 */
@Data
public class TraceSearch implements SearchParamSetter{
    /**
     * 每页数量
     */
    @ApiModelProperty("每页数量")
    protected Integer size = 15;
    /**
     * 偏移量 从0开始
     */
    @ApiModelProperty("偏移量 从0开始")
    protected Integer offset = 0;
    @ApiModelProperty(value = "开始时间", example = "2021-07-15 00:00:00")
    private String fromTime;
    @ApiModelProperty(value = "结束时间", example = "2021-07-15 23:59:59")
    private String toTime;
    @ApiModelProperty("排序字段")
    private String sortField;
    @ApiModelProperty("排序方式 asc desc")
    private String sortOrder;

    private Map<String, String> tags;

    @ApiModelProperty("调用链id")
    private String traceId;
    @ApiModelProperty("调用链SpanId")
    private String spanId;
    @ApiModelProperty("调用链Span父Id")
    private String parentId;
    @ApiModelProperty("调用链Span父Id")
    private Integer isParent;

    @ApiModelProperty("容器ID")
    private String containerId;

    @ApiModelProperty("来源服务id")
    private String srcServiceId;
    List<String> srcServiceIds;
    @ApiModelProperty("来源服务实例")
    private String srcServiceInstance;
    List<String> srcServiceInstances;
    @ApiModelProperty("服务id")
    private String serviceId;
    List<String> serviceIds;
    @ApiModelProperty("服务实例")
    private String serviceInstance;
    List<String> serviceInstances;
    @ApiModelProperty("服务端目标服务id")
    private String dstServiceId;
    List<String> dstServiceIds;
    @ApiModelProperty("服务端目标服务实例")
    private String dstServiceInstance;
    List<String> dstServiceInstances;
    @ApiModelProperty("资源接口")
    private String resource;
    @ApiModelProperty("接口别名")
    private String alias;
    List<String> resources;
    private String resourceQuery;
    @ApiModelProperty("接口名称")
    private String rootResource;
    List<String> rootResources;
    @ApiModelProperty("接口名称模糊查询")
    private String rootResourceQuery;
    @ApiModelProperty("折线图间隔 (秒级)")
    private Integer interval;
    @ApiModelProperty("错误类型")
    private String errorType;
    private String errorTypeQuery;
    List<String> errorTypes;
    @ApiModelProperty("左侧需要查询字段列表")
    private List<String> queryParams = new ArrayList<>();

    @ApiModelProperty("es 查询语法查询")
    private String query;

    @ApiModelProperty("模糊查询traceid查询")
    private String fuzzyTraceId;
    @ApiModelProperty("批量traceid查询")
    List<String> traceIds;

    @ApiModelProperty("调用链SpanIds")
    private List<String> spanIds;
    @ApiModelProperty("调用链parentIds")
    private List<String> parentIds;
    @ApiModelProperty("模糊查询trace名查询")
    private String fuzzyTraceName;
    @ApiModelProperty("模糊查询服务名称查询")
    private String fuzzyServiceName;
    @ApiModelProperty
    private List<ResourceAlias> aliasList;


    @ApiModelProperty("apm分析统计模型参数")
    private ApmSearch apmSearch;

    @ApiModelProperty("入口")
    private Integer isIn;
    @ApiModelProperty("出口")
    private Integer isOut;
    @ApiModelProperty("错误")
    private Integer error;
    @ApiModelProperty("慢接口")
    private Integer slow;
    @ApiModelProperty("apiKey")
    private String apiKey;

    private Long minDuration;
    private Long maxDuration;

    private String operationName;
    List<String> operationNames;
    private String httpHost;
    List<String> httpHosts;
    private String method;
    List<String> methods;
    private String statusCode;
    List<String> statusCodes;
    private String url;
    List<String> urls;
    private String host;
    List<String> hosts;


    private String spanName;
    List<String> spanNames;

    private String type;
    List<String> types;

    private String dbType;
    List<String> dbTypes;
    @ApiModelProperty("状态码")
    private String httpCode;
    List<String> httpCodes;
    @ApiModelProperty("topic查询")
    private String topic;
    List<String> topics;
    @ApiModelProperty("分组查询")
    private String group;
    List<String> groups;
    @ApiModelProperty("partition")
    private String partition;
    List<String> partitions;
    @ApiModelProperty("broker查询")
    private String broker;
    List<String> brokers;
    @ApiModelProperty("数据库查询")
    private String sqlDatabase;
    List<String> sqlDatabases;
    @ApiModelProperty("SQL内容查询")
    private String sqlContent;
    List<String> sqlContents;
    @ApiModelProperty("数据库操作")
    private String sqlOperation;
    List<String> sqlOperations;
    @ApiModelProperty("命令")
    private String command;
    List<String> commands;
    @ApiModelProperty("索引")
    private String indices;
    List<String> indicess;
    @ApiModelProperty("服务组件分类 service.rpc,service.http,service.mq,service.redis,service.db,service.es ;trace入口 service.trace")
    private String componentType;

    @ApiModelProperty("查询百分位点")
    private Double percentile;

    @ApiModelProperty("分组字段")
    private String groupByField;
    @ApiModelProperty("时间聚合桶字段")
    private String timeBucket;
    private Set<Long> minutes;
    private Set<Long> hours;
    private Long minMinute;
    private Long maxMinute;

    @ApiModelProperty("环境标签1")
    private List<String> envTag1s;
    @ApiModelProperty("环境标签2")
    private List<String> envTag2s;

    @ApiModelProperty("存在异常错误")
    private Integer isException;
    @ApiModelProperty("异常服务id（可能是客户端服务或者应用服务）")
    private String exceptionServiceId;
    @ApiModelProperty("异常服务实例（可能是客户端服务或者应用服务）")
    private String exceptionServiceInstance;
    @ApiModelProperty("异常服务ids（可能是客户端服务或者应用服务）")
    private List<String> exceptionServiceIds;

    public Long getFromTimeVul() {
        if (StringUtils.isBlank(this.fromTime)) {
            return DateUtils.strToDate("yyyy-MM-dd HH:mm:ss", this.fromTime).getTime() - TimeUtil.OUT_DAY_MS_LONG;
        }
        return DateUtils.strToDate("yyyy-MM-dd HH:mm:ss", this.fromTime).getTime();
    }

    public Long getToTimeVul() {
        return DateUtils.strToDate("yyyy-MM-dd HH:mm:ss", this.toTime).getTime();
    }

    public TraceSearch cloneSelf() {
        TraceSearch traceSearch = new TraceSearch();
        BeanUtils.copyProperties(this, traceSearch);
        return traceSearch;
    }

    public Map<String, String> getTags() {
        return tags;
    }

    public void setTags(Map<String, String> tags) {
        this.tags = tags;
    }
}
