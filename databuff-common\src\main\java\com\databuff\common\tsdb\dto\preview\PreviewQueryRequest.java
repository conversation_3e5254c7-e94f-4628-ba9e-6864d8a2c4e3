package com.databuff.common.tsdb.dto.preview;

import com.databuff.common.tsdb.dto.detect.DetectQueryRequest;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel("指标数据预览请求参数")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PreviewQueryRequest {

    @ApiModelProperty(value = "规则参数")
    private RuleDTO rule;

    @ApiModelProperty(value = "查询请求参数对象，包含查询条件和权限信息", example = "包含完整的查询条件配置")
    private DetectQueryRequest query;

    @ApiModelProperty(value = "起始时间戳，单位为毫秒")
    private Long start;

    @ApiModelProperty(value = "结束时间戳，单位为毫秒")
    private Long end;

    @ApiModelProperty(value = "查询数据的时间间隔，单位为秒")
    private Integer interval;


    /**
     * 准备请求，将顶层参数同步到内部的query对象及其所有子查询中。
     * 这是一个链式调用，确保所有层级的参数都得到更新。
     * 此方法应该在Controller接收到请求后，传递给Service之前调用。
     */
    public void prepare() {
        if (this.query == null) {
            // 如果前端没有提供query对象，创建一个空的，虽然这种情况很少见
            this.query = new DetectQueryRequest();
        }

        // 将本对象的顶层参数同步到下一级的DetectQueryRequest
        this.query.syncTopLevelParams(this.start, this.end, this.interval);
    }

}
