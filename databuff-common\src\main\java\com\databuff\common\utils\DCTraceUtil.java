package com.databuff.common.utils;

import com.databuff.common.model.DCTrace;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

public class DCTraceUtil {

    public static byte[] serialize(DCTrace dcTrace, int length) {
        List<byte[]> spans = dcTrace.getSpans();
        int spanSize = spans.size();
        ByteBuffer byteBuffer = ByteBuffer.allocate(length);
        byteBuffer.putLong(dcTrace.getEndTime());
        byteBuffer.putLong(dcTrace.getDtsReceiveTime());
        //span size
        byteBuffer.putInt(spanSize);
        byteBuffer.putInt(dcTrace.getTraceId().length());
        byteBuffer.put(dcTrace.getTraceId().getBytes(StandardCharsets.UTF_8));
        for (int i = 0; i < spanSize; i++) {
            byte[] span = spans.get(i);
            byteBuffer.putInt(span.length);
            byteBuffer.put(span);
        }
        return byteBuffer.array();
    }

    public static DCTrace deserialize(byte[] data) {
        DCTrace dcTrace = new DCTrace();
        ByteBuffer byteBuffer = ByteBuffer.wrap(data);
        dcTrace.setEndTime(byteBuffer.getLong());
        dcTrace.setDtsReceiveTime(byteBuffer.getLong());
        int spanSize = byteBuffer.getInt();

        byte[] traceIdBytes = new byte[byteBuffer.getInt()];
        byteBuffer.get(traceIdBytes);
        dcTrace.setTraceId(new String(traceIdBytes, StandardCharsets.UTF_8));

        List<byte[]> spans = new ArrayList<>(spanSize);
        for (int i = 0; i < spanSize; i++) {
            byte[] span = new byte[byteBuffer.getInt()];
            byteBuffer.get(span);
            spans.add(span);
        }
        dcTrace.setSpans(spans);
        return dcTrace;
    }

    public static String getTraceId(byte[] data) {
        ByteBuffer byteBuffer = ByteBuffer.wrap(data);
        byteBuffer.position(20);
        int len = byteBuffer.getInt();
        byte[] traceIdArr = new byte[len];
        byteBuffer.get(traceIdArr);
        return new String(traceIdArr, StandardCharsets.UTF_8);
    }

}
