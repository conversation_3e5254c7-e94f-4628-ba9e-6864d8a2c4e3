package com.databuff.common.exception;


public class ErrorCode {
    /**
     * 登陆模块
     */
    /**
     * 帐号密码错误
     */
    public static final int LOGIN_USERPASS_ERROR = 401;
    /**
     * 用户不存在
     */
    public static final int LOGIN_USER_NULL_ERROR = 402;
    /**
     * 用户已锁定
     */
    public static final int LOGIN_USER_WAS_LOCKED = 4002;
    /**
     * 验证码输入错误
     */
    public static final int LOGIN_CAPT_ERROR = 403;
    /**
     * 验证码失效
     */
    public static final int LOGIN_CAPT_STATUS_ERROR = 404;
    /**
     * 用户名已存在
     */
    public static final int CREATE_USER_ISPER = 405;
    /**
     * 修改密码旧密码错误
     */
    public static final int LOGIN_OLDUSERPASS_ERROR = 406;
    /**
     * 角色已存在
     */
    public static final int CREATE_ROLE_ISPER = 407;
    /**
     * token无效/token认证失败
     */
    public static final int LOGIN_TOKEN_ERROR = 3000;
    /**
     * 当前Subject没有此请求所需权限
     */
    public static final int USER_TOKEN_NO_ACCESS = 3001;
    /**
     * 前Subject是匿名Subject，请先登录
     */
    public static final int USER_NO_LOGIN = 3002;
    /**
     * license授权过期
     */
    public static final int LICENSE_HAS_EXPIRED = 3003;

    /**
     * 通用异常状态
     */
    public static final int COMMON_ERROR_STATUS = 505;
    /**
     * 删除数据集时，数据集下还有指标异常
     */
    public static final int COMMON_ERROR_DEL_DATASET = 501;
    /**
     * 通用成功状态
     */
    public static final int COMMON_SUCCESS_STATUS = 200;

}
