package com.databuff.dao.mysql;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.databuff.entity.SpaceMapLayout;
import com.databuff.entity.TraceServiceEntity;
import com.databuff.entity.extend.ServiceSearch;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @author:TianMing
 * @date: 2022/6/29
 * @time: 20:48
 */
@Mapper
@Repository("traceServiceMapper")
public interface TraceServiceMapper extends BaseMapper {

    List<TraceServiceEntity> listService(ServiceSearch search);

    List<String> listServiceIds(ServiceSearch search);

    TraceServiceEntity serviceInfo(@Param("id") String serviceId);

    int updateService(TraceServiceEntity service);

    int updateServiceDescribeAndTag(TraceServiceEntity service);

    int insertService(TraceServiceEntity service);

    int updateServiceName(TraceServiceEntity service);

    String getServiceTags(@Param("apikey") String apikey, @Param("fromTime") String fromTime, @Param("toTime") String toTime);

    TraceServiceEntity selectByName(@Param("name") String name, @Param("id") String serviceId, @Param("apikey") String apiKey);

    List<TraceServiceEntity> listAll();
    List<TraceServiceEntity> notVirtualSvcs();

    int deleteByTime(@Param("fromTime") String sevenDayTime);

    List<TraceServiceEntity> listIdMap(@Param("apikey") String apiKey);

    List<TraceServiceEntity> getServicesByIds(@Param("apiKey") String apiKey, @Param("ids") List<String> ids);

    int updateServiceUpdateTime(@Param("id") String serviceId);

    List<Map<String, Long>> getServiceTypeCount(ServiceSearch search);

    List<TraceServiceEntity> findServices(@Param("serviceIds") Collection<String> serviceIds);

    int saveSpaceMapLayout(SpaceMapLayout spaceMapLayout);

    int updateSpaceMapLayout(SpaceMapLayout spaceMapLayout);
    SpaceMapLayout getSpaceMapLayout(SpaceMapLayout spaceMapLayout);
}
