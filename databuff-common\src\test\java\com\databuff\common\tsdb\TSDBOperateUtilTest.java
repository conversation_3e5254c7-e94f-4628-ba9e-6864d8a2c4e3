package com.databuff.common.tsdb;

import com.databuff.common.tsdb.builder.QueryBuilder;
import com.databuff.common.tsdb.factory.TSDBOperatorFactory;
import com.databuff.common.tsdb.impl.TSDBReaderImpl;
import com.databuff.common.tsdb.impl.TSDBWriterImpl;
import com.databuff.common.tsdb.model.*;
import com.databuff.common.tsdb.pool.TSDBConnectPool;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TSDBOperateUtilTest {

    @Mock
    private TSDBConnectPool mockPool;

    @Mock
    private TSDBReaderImpl mockReader;

    @Mock
    private TSDBWriterImpl mockWriter;

    @Mock
    private TSDBOperatorFactory mockFactory;

    private TSDBOperateUtil tsdbOperateUtil;

    @BeforeEach
    void setUp() {
        when(mockFactory.createReader()).thenReturn(mockReader);
        when(mockFactory.createWriter()).thenReturn(mockWriter);
        tsdbOperateUtil = new TSDBOperateUtil(mockFactory);

        // 使用反射设置私有字段，确保使用我们的mock对象
        try {
            java.lang.reflect.Field readerField = TSDBOperateUtil.class.getDeclaredField("tsdbReader");
            readerField.setAccessible(true);
            readerField.set(tsdbOperateUtil, mockReader);

            java.lang.reflect.Field writerField = TSDBOperateUtil.class.getDeclaredField("tsdbWriters");
            writerField.setAccessible(true);
            writerField.set(tsdbOperateUtil, Lists.newArrayList(mockWriter));
        } catch (Exception e) {
            fail("Failed to set up test: " + e.getMessage());
        }
    }

    @Test
    void testBuildFullSql() {
        // Arrange
        QueryBuilder mockBuilder = mock(QueryBuilder.class);
        String expectedSql = "SELECT * FROM measurement";

        when(mockReader.buildFullSql(any())).thenReturn(expectedSql);

        // Act
        String result = tsdbOperateUtil.buildFullSql(mockBuilder);

        // Assert
        assertEquals(expectedSql, result);
        verify(mockReader).buildFullSql(mockBuilder);
    }

    @Test
    void testBuildWhereSql() {
        // Arrange
        QueryBuilder mockBuilder = mock(QueryBuilder.class);
        String expectedSql = "WHERE time > now() - 1h";

        when(mockReader.buildWhereSql(any())).thenReturn(expectedSql);

        // Act
        String result = tsdbOperateUtil.buildWhereSql(mockBuilder);

        // Assert
        assertEquals(expectedSql, result);
        verify(mockReader).buildWhereSql(mockBuilder);
    }

    @Test
    void testExecuteQuery() {
        // Arrange
        QueryBuilder mockBuilder = mock(QueryBuilder.class);
        TSDBResultSet expectedResult = new TSDBResultSet();

        when(mockReader.executeQuery(any(QueryBuilder.class))).thenReturn(expectedResult);

        // Act
        TSDBResultSet result = tsdbOperateUtil.executeQuery(mockBuilder);

        // Assert
        assertNotNull(result);
        assertEquals(expectedResult, result);
        verify(mockReader).executeQuery(mockBuilder);
    }

    @Test
    void testExecuteQueryForAllGroups() {
        // Arrange
        QueryBuilder mockBuilder = mock(QueryBuilder.class);
        List<TSDBSeries> expectedSeries = new ArrayList<>();
        TSDBSeries series1 = new TSDBSeries();
        series1.setName("cpu");
        Map<String, String> tags1 = new HashMap<>();
        tags1.put("host", "server01");
        series1.setTags(tags1);

        TSDBSeries series2 = new TSDBSeries();
        series2.setName("cpu");
        Map<String, String> tags2 = new HashMap<>();
        tags2.put("host", "server02");
        series2.setTags(tags2);

        expectedSeries.add(series1);
        expectedSeries.add(series2);

        when(mockReader.executeQueryForAllGroups(any(QueryBuilder.class))).thenReturn(expectedSeries);

        // Act
        List<TSDBSeries> result = tsdbOperateUtil.executeQueryForAllGroups(mockBuilder);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(expectedSeries, result);
        verify(mockReader).executeQueryForAllGroups(mockBuilder);
    }

    @Test
    void testExecuteQueryForAllGroupsEmptyResult() {
        // Arrange
        QueryBuilder mockBuilder = mock(QueryBuilder.class);
        List<TSDBSeries> expectedSeries = Collections.emptyList();

        when(mockReader.executeQueryForAllGroups(any(QueryBuilder.class))).thenReturn(expectedSeries);

        // Act
        List<TSDBSeries> result = tsdbOperateUtil.executeQueryForAllGroups(mockBuilder);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(mockReader).executeQueryForAllGroups(mockBuilder);
    }

    @Test
    void testExecuteQueryForOneGroup() {
        // Arrange
        QueryBuilder mockBuilder = mock(QueryBuilder.class);
        TSDBSeries expectedSeries = new TSDBSeries();
        expectedSeries.setName("cpu");
        Map<String, String> tags = new HashMap<>();
        tags.put("host", "server01");
        expectedSeries.setTags(tags);
        List<String> columns = Arrays.asList("time", "usage");
        expectedSeries.setColumns(columns);
        List<List<Object>> values = new ArrayList<>();
        values.add(Arrays.asList(1625097600000L, 45.5));
        values.add(Arrays.asList(1625097660000L, 48.2));
        expectedSeries.setValues(values);

        when(mockReader.executeQueryForOneGroup(any(QueryBuilder.class))).thenReturn(expectedSeries);

        // Act
        TSDBSeries result = tsdbOperateUtil.executeQueryForOneGroup(mockBuilder);

        // Assert
        assertNotNull(result);
        assertEquals("cpu", result.getName());
        assertEquals(tags, result.getTags());
        assertEquals(columns, result.getColumns());
        assertEquals(values, result.getValues());
        verify(mockReader).executeQueryForOneGroup(mockBuilder);
    }

    @Test
    void testExecuteQueryForOneGroupNullResult() {
        // Arrange
        QueryBuilder mockBuilder = mock(QueryBuilder.class);

        when(mockReader.executeQueryForOneGroup(any(QueryBuilder.class))).thenReturn(null);

        // Act
        TSDBSeries result = tsdbOperateUtil.executeQueryForOneGroup(mockBuilder);

        // Assert
        assertNull(result);
        verify(mockReader).executeQueryForOneGroup(mockBuilder);
    }

    @Test
    void testShowTagValues() {
        // Arrange
        QueryBuilder mockBuilder = mock(QueryBuilder.class);
        Map<String, Set<String>> expectedValues = new HashMap<>();
        Set<String> values = new HashSet<>();
        values.add("server01");
        expectedValues.put("host", values);

        when(mockReader.showTagValues(any(QueryBuilder.class))).thenReturn(expectedValues);

        // Act
        Map<String, Set<String>> result = tsdbOperateUtil.showTagValues(mockBuilder);

        // Assert
        assertNotNull(result);
        assertEquals(expectedValues, result);
        verify(mockReader).showTagValues(mockBuilder);
    }

    @Test
    void testShowTagValuesWithParameters() {
        // Arrange
        String field = "usage";
        String databaseName = "testDB";
        String measurement = "cpu";
        List<Where> wheres = new ArrayList<>();
        Collection<String> keys = Arrays.asList("host", "region");
        Long start = 1000L;
        Long end = 2000L;

        Map<String, Set<String>> expectedValues = new HashMap<>();
        Set<String> values = new HashSet<>();
        values.add("server01");
        expectedValues.put("host", values);

        when(mockReader.showTagValues(anyString(), anyString(), anyString(), anyList(), anyCollection(), anyLong(), anyLong())).thenReturn(expectedValues);

        // Act
        Map<String, Set<String>> result = tsdbOperateUtil.showTagValues(field, databaseName, measurement, wheres, keys, start, end);

        // Assert
        assertNotNull(result);
        assertEquals(expectedValues, result);
        verify(mockReader).showTagValues(field, databaseName, measurement, wheres, keys, start, end);
    }

    @Test
    void testShowPeriodTagValuesWithTimeOffset() {
        // Arrange
        String field = "usage";
        String databaseName = "testDB";
        String measurement = "cpu";
        List<Where> wheres = new ArrayList<>();
        Collection<String> keys = Arrays.asList("host", "region");
        // period in seconds (3600 seconds = 1 hour)
        Integer period = 3600;
        // timeOffset in seconds
        Integer timeOffset = 0;

        Map<String, Set<String>> expectedValues = new HashMap<>();
        Set<String> values = new HashSet<>();
        values.add("server01");
        expectedValues.put("host", values);

        // Verify the correct method is called with the exact parameters
        when(mockReader.showPeriodTagValues(
                eq(field),
                eq(databaseName),
                eq(measurement),
                eq(wheres),
                eq(keys),
                eq(period),
                eq(timeOffset)
        )).thenReturn(expectedValues);

        // Act
        Map<String, Set<String>> result = tsdbOperateUtil.showPeriodTagValues(
                field, databaseName, measurement, wheres, keys, period, timeOffset);

        // Assert
        assertNotNull(result);
        assertEquals(expectedValues, result);
        verify(mockReader).showPeriodTagValues(
                field, databaseName, measurement, wheres, keys, period, timeOffset);
    }

    @Test
    void testShowPeriodTagValuesWithoutTimeOffset() {
        // Arrange
        String field = "usage";
        String databaseName = "testDB";
        String measurement = "cpu";
        List<Where> wheres = new ArrayList<>();
        Collection<String> keys = Arrays.asList("host", "region");
        // period in seconds (3600 seconds = 1 hour)
        Integer period = 3600;

        Map<String, Set<String>> expectedValues = new HashMap<>();
        Set<String> values = new HashSet<>();
        values.add("server01");
        expectedValues.put("host", values);

        // Verify the correct method is called with the exact parameters
        when(mockReader.showPeriodTagValues(
                eq(field),
                eq(databaseName),
                eq(measurement),
                eq(wheres),
                eq(keys),
                eq(period)
        )).thenReturn(expectedValues);

        // Act
        Map<String, Set<String>> result = tsdbOperateUtil.showPeriodTagValues(
                field, databaseName, measurement, wheres, keys, period);

        // Assert
        assertNotNull(result);
        assertEquals(expectedValues, result);
        verify(mockReader).showPeriodTagValues(
                field, databaseName, measurement, wheres, keys, period);
    }

    @Test
    void testShowPeriodTagValuesWithNegativePeriod() {
        // Arrange
        String field = "usage";
        String databaseName = "testDB";
        String measurement = "cpu";
        List<Where> wheres = new ArrayList<>();
        Collection<String> keys = Arrays.asList("host", "region");
        // Negative period should cause an IllegalArgumentException
        Integer period = -3600;

        // Set up the mock to throw an exception for negative period
        when(mockReader.showPeriodTagValues(
                anyString(), anyString(), anyString(), anyList(), anyCollection(), eq(period), anyInt()
        )).thenThrow(new IllegalArgumentException("period必须为非负数"));

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            tsdbOperateUtil.showPeriodTagValues(field, databaseName, measurement, wheres, keys, period, 0);
        });

        // Verify the exception message
        assertEquals("period必须为非负数", exception.getMessage());

        // Verify the method was called with the correct parameters
        verify(mockReader).showPeriodTagValues(
                field, databaseName, measurement, wheres, keys, period, 0);
    }

    @Test
    void testShowPeriodTagValuesWithNegativeTimeOffset() {
        // Arrange
        String field = "usage";
        String databaseName = "testDB";
        String measurement = "cpu";
        List<Where> wheres = new ArrayList<>();
        Collection<String> keys = Arrays.asList("host", "region");
        Integer period = 3600;
        // Negative timeOffset should cause an IllegalArgumentException
        Integer timeOffset = -60;

        // Set up the mock to throw an exception for negative timeOffset
        when(mockReader.showPeriodTagValues(
                anyString(), anyString(), anyString(), anyList(), anyCollection(), anyInt(), eq(timeOffset)
        )).thenThrow(new IllegalArgumentException("timeOffset必须为非负数"));

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            tsdbOperateUtil.showPeriodTagValues(field, databaseName, measurement, wheres, keys, period, timeOffset);
        });

        // Verify the exception message
        assertEquals("timeOffset必须为非负数", exception.getMessage());

        // Verify the method was called with the correct parameters
        verify(mockReader).showPeriodTagValues(
                field, databaseName, measurement, wheres, keys, period, timeOffset);
    }

    @Test
    void testCreateDatabase() {
        // Arrange
        String databaseName = "testDB";
        when(mockWriter.createDatabase(databaseName)).thenReturn(true);
        when(mockWriter.createDatabase(databaseName, null)).thenReturn(true);

        // Act
        boolean result = tsdbOperateUtil.createDatabase(databaseName);

        // Assert
        assertTrue(result);
        verify(mockWriter).createDatabase(databaseName);
    }

    @Test
    void testCreateDatabaseWithTSDBDatabaseInfo() {
        // Arrange
        TSDBDatabaseInfo databaseInfo = new TSDBDatabaseInfo();
        databaseInfo.setDatabaseName("testDB");
        databaseInfo.setUserName("testUser");
        databaseInfo.setPassword("testPassword");
        databaseInfo.setShard(2);
        databaseInfo.setReplication(1);
        databaseInfo.setKeepDay(30);
        databaseInfo.setInterval(60);

        when(mockWriter.createDatabase(any(TSDBDatabaseInfo.class))).thenReturn(true);

        // Act
        boolean result = tsdbOperateUtil.createDatabase(databaseInfo);

        // Assert
        assertTrue(result);
        verify(mockWriter).createDatabase(any(TSDBDatabaseInfo.class));
    }

    @Test
    void testCreateDatabaseWithStringAndIntervalParams() {
        // Arrange
        String databaseName = "testDB";
        Integer interval = 120;
        when(mockWriter.createDatabase(anyString(), any(Integer.class))).thenReturn(true);

        // Act
        boolean result = tsdbOperateUtil.createDatabase(databaseName, interval);

        // Assert
        assertTrue(result);
        verify(mockWriter).createDatabase(anyString(), any(Integer.class));
    }

    @Test
    void testCreateDatabaseWithIntervalFailure() {
        // Arrange
        String databaseName = "testDB";
        Integer interval = 120;

        // Mock the createDatabase method with interval parameter to return false
        when(mockWriter.createDatabase(anyString(), any(Integer.class))).thenReturn(false);

        // Act
        boolean result = tsdbOperateUtil.createDatabase(databaseName, interval);

        // Assert
        assertFalse(result);
        verify(mockWriter).createDatabase(anyString(), any(Integer.class));
    }

    @Test
    void testCreateDatabaseWithNoWriters() {
        // Arrange
        String databaseName = "testDB";

        // 清空写入器列表
        try {
            java.lang.reflect.Field writerField = TSDBOperateUtil.class.getDeclaredField("tsdbWriters");
            writerField.setAccessible(true);
            writerField.set(tsdbOperateUtil, new ArrayList<>());
        } catch (Exception e) {
            fail("Failed to set up test: " + e.getMessage());
        }

        // Act
        boolean result = tsdbOperateUtil.createDatabase(databaseName);

        // Assert
        assertFalse(result);
        verify(mockWriter, never()).createDatabase(anyString());

        // 恢复写入器列表
        try {
            java.lang.reflect.Field writerField = TSDBOperateUtil.class.getDeclaredField("tsdbWriters");
            writerField.setAccessible(true);
            List<TSDBWriter> writers = new ArrayList<>();
            writers.add(mockWriter);
            writerField.set(tsdbOperateUtil, writers);
        } catch (Exception e) {
            fail("Failed to restore test environment: " + e.getMessage());
        }
    }

    @Test
    void testWritePoint() {
        // Arrange
        String databaseName = "testDB";
        String measurement = "cpu";
        long timestamp = System.currentTimeMillis();

        // 使用正确的构造函数创建TSDBPoint
        TSDBPoint point = new TSDBPoint(measurement, timestamp);

        when(mockWriter.writePoint(anyString(), any())).thenReturn(true);

        // Act
        boolean result = tsdbOperateUtil.writePoint(databaseName, point);

        // Assert
        assertTrue(result);
        verify(mockWriter).writePoint(databaseName, point);
    }

    @Test
    void testWritePoints() {
        // Arrange
        String databaseName = "testDB";
        List<TSDBPoint> points = new ArrayList<>();

        // 使用正确的构造函数创建TSDBPoint
        String measurement = "cpu";
        long timestamp = System.currentTimeMillis();
        points.add(new TSDBPoint(measurement, timestamp));

        when(mockWriter.writePoints(anyString(), anyList())).thenReturn(true);

        // Act
        boolean result = tsdbOperateUtil.writePoints(databaseName, points);

        // Assert
        assertTrue(result);
        verify(mockWriter).writePoints(databaseName, points);
    }

    @Test
    void testClose() throws Exception {
        // Act
        tsdbOperateUtil.close();

        // Assert
        verify(mockReader).close();
        verify(mockWriter).close();
    }

    @Test
    void testGetConfigs() {
        // Arrange
        Map<String, Object> expectedConfigs = new HashMap<>();
        expectedConfigs.put("url", "localhost:8086");

        when(mockReader.getConfigs()).thenReturn(expectedConfigs);

        // Act
        Map<String, Object> result = tsdbOperateUtil.getConfigs();

        // Assert
        assertNotNull(result);
        assertEquals(expectedConfigs, result);
        verify(mockReader).getConfigs();
    }

    @SneakyThrows
    @Test
    void testAutoCloseable() {
        // Test that the util implements AutoCloseable and can be used with try-with-resources
        assertDoesNotThrow(() -> {
            try (TSDBOperateUtil testUtil = new TSDBOperateUtil(mockFactory)) {
                // Do something with the util
                assertNotNull(testUtil);

                // Set the mock reader and writer for the test
                java.lang.reflect.Field readerField = TSDBOperateUtil.class.getDeclaredField("tsdbReader");
                readerField.setAccessible(true);
                readerField.set(testUtil, mockReader);

                java.lang.reflect.Field writerField = TSDBOperateUtil.class.getDeclaredField("tsdbWriters");
                writerField.setAccessible(true);
                writerField.set(testUtil, Lists.newArrayList(mockWriter));
            }
        });

        verify(mockReader).close();
        verify(mockWriter).close();
    }
}
