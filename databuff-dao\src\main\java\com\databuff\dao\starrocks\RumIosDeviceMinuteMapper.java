package com.databuff.dao.starrocks;

import com.databuff.entity.dto.TimeValue;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Mapper
@Repository
public interface RumIosDeviceMinuteMapper {
    List<TimeValue> getDeviceCount(@Param("appId") Integer appId,
                                  @Param("fromTime") Date fromTime,
                                  @Param("toTime") Date toTime,
                                  @Param("timeBucket") String timeBucket);
}
