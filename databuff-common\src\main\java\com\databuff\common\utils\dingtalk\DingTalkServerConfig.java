package com.databuff.common.utils.dingtalk;

import lombok.Data;

import java.io.Serializable;

/**
 *  dc_notice_dingtalk_config
 * <AUTHOR> 2021-03-24
 */
@Data
public class DingTalkServerConfig implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 应用的唯一标识key
     */
    private String appkey;

    /**
     * 应用的密钥
     */
    private String appsecret;
    /**
     * 发送消息时使用的微应用的AgentID
     */
    private Long agentId ;

    /**
     * 是否启用 1启用0不启用
     */
    private Integer enable;



    public DingTalkServerConfig() {
    }

}
