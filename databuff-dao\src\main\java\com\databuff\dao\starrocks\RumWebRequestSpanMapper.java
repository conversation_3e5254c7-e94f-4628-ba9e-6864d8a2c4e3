package com.databuff.dao.starrocks;

import com.databuff.entity.rum.web.RumWebRequestTraceDetailDto;
import com.databuff.entity.rum.web.RumWebRequestTraceDetailResponseDto;
import com.databuff.entity.rum.web.RumWebRequestTraceListDto;
import com.databuff.entity.rum.web.RumWebRequestTraceListSearchCriteria;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface RumWebRequestSpanMapper {

    List<RumWebRequestTraceListDto> getRequestTraceList(RumWebRequestTraceListSearchCriteria criteria);

    RumWebRequestTraceDetailResponseDto getRequestTraceDetail(RumWebRequestTraceDetailDto criteria);

}

