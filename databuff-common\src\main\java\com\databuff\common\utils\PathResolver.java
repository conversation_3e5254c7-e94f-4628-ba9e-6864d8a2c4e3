package com.databuff.common.utils;

import org.apache.commons.lang3.SystemUtils;
import java.nio.file.Path;
import java.nio.file.Paths;

public class PathResolver {

    /**
     * 根据操作系统和配置路径解析适当的源映射存储路径。
     *
     * <p>此方法确定应存储源映射的实际文件系统路径，
     * 考虑到操作系统和潜在的文件系统限制。</p>
     *
     * <p>行为如下：</p>
     * <ul>
     *   <li>在Windows 开发环境上：源映射存储在系统临时目录中，附加配置路径。</li>
     *   <li>在macOS 开发环境上：方法尝试使用配置路径。如果不可写，则回退到使用"/tmp"目录加上配置路径。</li>
     *   <li>在其他系统（假定为Linux/Unix） 生产环境上：按原样使用配置路径。</li>
     * </ul>
     *
     * @param configuredPath 应用程序属性中配置的路径，通常以"/data/dc/dcgl/dacheng/staticDir/sourcemap"开头。
     * @return 表示应存储源映射的已解析文件系统路径的字符串。
     * @throws SecurityException 如果存在安全管理器，且其checkPropertyAccess方法不允许访问Windows上的系统临时目录。
     */
    public static String resolveRelativePath(String configuredPath) {
        if (SystemUtils.IS_OS_WINDOWS) {
            String tempDir = System.getProperty("java.io.tmpdir");
            return Paths.get(tempDir, configuredPath).toString();
        } else if (SystemUtils.IS_OS_MAC) {
            Path path = Paths.get(configuredPath);
            if (path.toFile().canWrite()) {
                return configuredPath;
            } else {
                return Paths.get("/tmp", configuredPath).toString();
            }
        } else {
            // For Linux and other Unix-like systems
            return configuredPath;
        }
    }
}
