package com.databuff.common.tsdb.dto.preview;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("规则配置DTO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RuleDTO {
    @ApiModelProperty(value = "检测方式", example = "changePoint")
    private String way;

    @ApiModelProperty(value = "动态基线", example = "1")
    private double baselineScope;

    @ApiModelProperty(value = "比较符（>、<、>=、<=）", example = ">")
    private String comparison;

    @ApiModelProperty(value = "阈值配置", required = true)
    private ThresholdsDTO thresholds;
}