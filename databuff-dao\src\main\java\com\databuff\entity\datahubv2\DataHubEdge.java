
package com.databuff.entity.datahubv2;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.databuff.handler.TimestampToLongTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "datahub_edge", autoResultMap = true)
public class DataHubEdge {
    @TableId(value = "edge_id", type = IdType.AUTO)
    private Integer edgeId;

    @TableField(value = "pipeline_id")
    private Integer pipelineId;

    private Integer source;

    private Integer target;

    private String config;

    @TableField(value = "created_at", typeHandler = TimestampToLongTypeHandler.class)
    private Long createdAt;



    /* Getter 和 Setter 方法 */

    public Integer getEdgeId() {
        return edgeId;
    }

    public void setEdgeId(Integer edgeId) {
        this.edgeId = edgeId;
    }

    public Integer getPipelineId() {
        return pipelineId;
    }

    public void setPipelineId(Integer pipelineId) {
        this.pipelineId = pipelineId;
    }

    public Integer getTarget() {
        return target;
    }

    public void setTarget(Integer target) {
        this.target = target;
    }

    public String getConfig() {
        return config;
    }

    public void setConfig(String config) {
        this.config = config;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }
}