package com.databuff.common.tsdb.dto.detect;

import com.alibaba.fastjson.parser.ParserConfig;
import com.alibaba.fastjson.serializer.SerializeConfig;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

@Configuration
public class FastJsonConfiguration {

    @PostConstruct
    public void initFastJsonConfig() {
        // 注册反序列化器
        ParserConfig.getGlobalInstance()
                .putDeserializer(MultiDetectQueryRequest.class,
                        new MultiDetectQueryRequestDeserializerFastjson());

        // 注册序列化器
        SerializeConfig.getGlobalInstance()
                .put(MultiDetectQueryRequest.class,
                        new MultiDetectQueryRequestSerializerFastjson());
    }
}
