package com.databuff.common.utils.dingtalk;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.constants.Constant;
import com.databuff.common.exception.CustomException;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import com.fasterxml.jackson.core.type.TypeReference;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.util.List;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
/**
 * @author:TianMing
 * @date: 2021/12/17
 * @time: 14:08
 */
@Slf4j
public class DingTalkUtils {


    private static final String ACCESS_TOKEN_URL = "https://oapi.dingtalk.com/gettoken";

    private static final String GETUSERID_BY_MOBILE_URL = "https://oapi.dingtalk.com/user/get_by_mobile";

    private static final String GETUSER_BY_USERID_URL = "https://oapi.dingtalk.com/user/get";

    private static final String SEND_NOTICE_MESSAGE_URL = "https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2";

    private static final String GET_SEND_RESULT = "https://oapi.dingtalk.com/topapi/message/corpconversation/getsendresult";



    public static String getAccessToken(String appkey, String appsecret) {
        String url = ACCESS_TOKEN_URL + "?appkey=" + appkey + "&appsecret=" + appsecret;
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet request = new HttpGet(url);
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                HttpEntity entity = response.getEntity();
                String result = EntityUtils.toString(entity);
                ObjectMapper mapper = new ObjectMapper();
                JsonNode jsonNode = mapper.readTree(result);
                if (jsonNode.get("errcode").asInt() != 0) {
                    throw new CustomException(jsonNode.get("errmsg").asText());
                }
                return jsonNode.get("access_token").asText();
            }
        } catch (Exception e) {
            throw new CustomException(e.getMessage());
        }
    }

    public static String getUserIdByMobile(String accessToken, String mobile) {
        String url = GETUSERID_BY_MOBILE_URL + "?access_token=" + accessToken + "&mobile=" + mobile;
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet request = new HttpGet(url);
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                HttpEntity entity = response.getEntity();
                String result = EntityUtils.toString(entity);
                ObjectMapper mapper = new ObjectMapper();
                JsonNode jsonNode = mapper.readTree(result);
                if (jsonNode.get("errcode").asInt() != 0) {
                    throw new CustomException(jsonNode.get("errmsg").asText());
                }
                return jsonNode.get("userid").asText();
            }
        } catch (Exception e) {
            throw new CustomException(e.getMessage());
        }
    }


    public static void sendNoticeMessage(NotifyDingTalkConfig dingConfig, String accessToken, String title, String text, String textType, List<String> useridList) {
        Long agentId = dingConfig.getDingAgentId();
        String url = SEND_NOTICE_MESSAGE_URL + "?access_token=" + accessToken;

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost post = new HttpPost(url);
            post.setHeader(Constant.CONTENT_TYPE, Constant.CONTENT_TYPE_JSON);

            ObjectMapper mapper = new ObjectMapper();
            ObjectNode requestJson = mapper.createObjectNode();
            requestJson.put("agent_id", agentId);
            requestJson.put("userid_list", String.join(",", useridList));
            requestJson.put("to_all_user", false);

            ObjectNode msg = requestJson.putObject("msg");
            if ("markdown".equals(textType)) {
                msg.put("msgtype", "markdown");
                ObjectNode markdown = msg.putObject("markdown");
                markdown.put("title", title);
                markdown.put("text", text);
            } else {
                msg.put("msgtype", "text");
                ObjectNode textNode = msg.putObject("text");
                textNode.put("content", text);
            }

            post.setEntity(new StringEntity(requestJson.toString(), "UTF-8"));

            try (CloseableHttpResponse response = httpClient.execute(post)) {
                HttpEntity entity = response.getEntity();
                String result = EntityUtils.toString(entity);
                JsonNode jsonNode = mapper.readTree(result);
                if (jsonNode.get("errcode").asInt() != 0) {
                    throw new CustomException(jsonNode.get("errmsg").asText());
                }
            }
        } catch (Exception e) {
            throw new CustomException(e.getMessage());
        }
    }

    public static String getSendResult(Long taskId, NotifyDingTalkConfig dingConfig, String accessToken) {
        CloseableHttpClient client = HttpClients.createDefault();
        try {
            // 构建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("task_id", taskId);
            requestBody.put("agent_id", dingConfig.getDingAgentId());

            StringEntity entity = new StringEntity(requestBody.toString(), "UTF-8");

            // 创建POST请求
            HttpPost post = new HttpPost(GET_SEND_RESULT + "?access_token=" + accessToken);
            post.setEntity(entity);
            post.setHeader(Constant.CONTENT_TYPE, Constant.CONTENT_TYPE_JSON);

            // 执行请求
            try (CloseableHttpResponse response = client.execute(post)) {
                HttpEntity responseEntity = response.getEntity();
                String responseString = EntityUtils.toString(responseEntity, "UTF-8");

                // 解析响应
                JSONObject responseJson = JSONObject.parseObject(responseString);
                if (responseJson.getInteger("errcode") != 0) {
                    log.error("getsendresultRequest error: {}", responseJson.getString("errmsg"));
                    throw new CustomException(responseJson.getString("errmsg"));
                }

                // 返回结果
                return responseJson.getString("body");
            }
        } catch (Exception e) {
            log.error("getsendresultRequest error: {}", e);
            throw new CustomException(e.getMessage());
        } finally {
            try {
                client.close();
            } catch (Exception e) {
                log.error("Error closing HttpClient: {}", e.getMessage());
            }
        }
}

    /**
     * 群机器人发送消息
     * 自定义关键词
     * 1.最多可以设置10个关键词，消息中至少包含其中1个关键词才可以发送成功。 例如添加了一个自定义关键词：监控报警，则这个机器人所发送的消息，必须包含监控报警这个词，才能发送成功。
     * 2.加签 把timestamp+"\n"+密钥当做签名字符串，使用HmacSHA256算法计算签名，然后进行Base64 encode，最后再把签名参数再进行urlEncode，得到最终的签名（需要使用UTF-8字符集）。
     * 3.IP地址（段） 设定后，只有来自IP地址范围内的请求才会被正常处理。支持两种设置方式：IP地址和IP地址段，暂不支持IPv6地址白名单
     *
     *
     * 每个机器人每分钟最多发送20条消息到群里，如果超过20条，会限流10分钟。
     * 注意 如果你有大量发消息的场景（譬如系统监控报警）可以将这些信息进行整合，通过markdown消息以摘要的形式发送到群里。
     *
     * @param secret 加签密钥
     * @param webhookUrl
     * @param title 首屏会话透出的展示内容
     * @param text 内容
     * @param textType 消息类型 markdown text
     * @param phoneList 被@人的手机号列表。
     * @throws Exception
     */
    public static void sendRobotMessage(String secret, String webhookUrl, String title, String text, String textType, List<String> phoneList) throws Exception {
        if (secret != null && !secret.isEmpty()) {
            long timestamp = System.currentTimeMillis();
            String sign = getSign(timestamp, secret);
            webhookUrl = webhookUrl + "&timestamp=" + timestamp + "&sign=" + sign;
        }

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost post = new HttpPost(webhookUrl);
            post.setHeader(Constant.CONTENT_TYPE, Constant.CONTENT_TYPE_JSON);

            ObjectMapper mapper = new ObjectMapper();
            ObjectNode requestJson = mapper.createObjectNode();
            requestJson.put("msgtype", textType);

            if (phoneList != null && !phoneList.isEmpty()) {
                ObjectNode atNode = requestJson.putObject("at");
                atNode.putArray("atMobiles").addAll(mapper.convertValue(phoneList, new TypeReference<List<JsonNode>>() {}));
                text += "\n\n------\n\n";
                StringBuilder textBuilder = new StringBuilder(text);
                for (String phone : phoneList) {
                    textBuilder.append("@").append(phone);
                }
                text = textBuilder.toString();
            }

            if ("markdown".equals(textType)) {
                ObjectNode markdown = requestJson.putObject("markdown");
                markdown.put("title", title);
                markdown.put("text", text);
            } else {
                ObjectNode textNode = requestJson.putObject("text");
                textNode.put("content", text);
            }

            post.setEntity(new StringEntity(requestJson.toString(), "UTF-8"));

            try (CloseableHttpResponse response = httpClient.execute(post)) {
                HttpEntity entity = response.getEntity();
                String result = EntityUtils.toString(entity);
                JsonNode jsonNode = mapper.readTree(result);
                if (jsonNode.get("errcode").asInt() != 0) {
                    throw new CustomException(jsonNode.get("errmsg").asText());
                }
            }
        } catch (Exception e) {
            throw new CustomException(e.getMessage());
        }
    }

    /**
     * 把timestamp+"\n"+密钥当做签名字符串，使用HmacSHA256算法计算签名，然后进行Base64 encode，最后再把签名参数再进行urlEncode，得到最终的签名
     * @param timestamp
     * @param secret
     * @return
     * @throws Exception
     */
    private static String getSign (Long timestamp,String secret) throws Exception {
        String stringToSign = timestamp + "\n" + secret;
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(secret.getBytes("UTF-8"), "HmacSHA256"));
        byte[] signData = mac.doFinal(stringToSign.getBytes("UTF-8"));
        String sign = URLEncoder.encode(new String(Base64.encodeBase64(signData)),"UTF-8");
        return sign ;
    }
}
