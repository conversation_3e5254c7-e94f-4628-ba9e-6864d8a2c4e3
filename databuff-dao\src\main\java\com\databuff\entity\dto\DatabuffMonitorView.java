package com.databuff.entity.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.databuff.common.tsdb.dto.detect.MultiDetectQueryRequest;
import com.databuff.handler.MultiDetectQueryRequestHandler;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.*;

/**
 * <AUTHOR>
 * @description
 * @date 2021-08-30
 */

@Data
@ToString
@TableName(value = "dc_databuff_monitor")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DatabuffMonitorView {

	private Integer interval;

	private Boolean hasAnnotate;

	private Long id;

	/**
	 * 检测规则ids列表，推荐监控模板专用
	 */
	private volatile Collection<Long> monitorIds;

	public Collection<Long> getMonitorIds() {
		if (monitorIds == null) {
			synchronized (this) {
				if (monitorIds == null) {
					monitorIds = new HashSet<>();
				}
			}
		}
		return monitorIds;
	}


	/**
	 * 域ID
	 */
	private String gid;

	/**
	 * 页面展示id
	 */
	private String idView;
	/**
	 * 监控规则名称
	 */
	@TableField(value = "rule_name")
	private String ruleName;

	/**
	 * 监控规则名称 兼容前端传参,删除会引起bug
	 *
	 * @return
	 * <AUTHOR>
	 */
	public String getName() {
		return ruleName;
	}

	/**
	 * 监控规则名称 兼容前端传参,删除会引起bug
	 *
	 * @return
	 * <AUTHOR>
	 */
	public void setName(String ruleName) {
		this.ruleName = ruleName;
	}

	/**
	 * 检测目标类型：
	 * 服务:service，服务实例:serviceInstance，资源:resource，指标:metric
	 */
	@TableField(value = "target")
	private String target;

	/**
	 * 监控标签
	 */
	private String tags;

	/**
	 * 监控对象 monitor_object
	 */
	private String monitorObject;

	/**
	 * 监控类型
	 */
	private String classification;

	/**
	 * 检测类型
	 */
	private String type;

	/**
	 * 优先级
	 */
	private Integer priority;

	/**
	 * 是否分组0否1是
	 */
	private Integer multi;

	/**
	 * 是否公共的0否1是,默认为0 monitor_public
	 */
	private Integer monitorPublic = 0;

	@ApiModelProperty(value = "启停，布尔类型，默认为开启状态")
	@TableField("enabled")
	private Boolean enabled;

	@ApiModelProperty(value = "是否为系统内置监控规则", example = "true")
	@TableField(value = "`system`")
	private Boolean system;

	public Boolean getSystem() {
		if (system == null) {
			return false;
		}
		return system;
	}

	/**
	 * 监控事件信息模版
	 */
	private String message;

	/**
	 * 监控查询条件
	 */
	@TableField(typeHandler = MultiDetectQueryRequestHandler.class)
	private MultiDetectQueryRequest query;

	/**
	 * 监控参数
	 */
	private String options;

	/**
	 * 监控其他参数
	 */
	private String params;

	/**
	 * 告警通知参数
	 */
	private String monitorNotice;

	/**
	 * *************以下为状态和静默计划****************
	 */
	/**
	 * 监控状态id
	 */
	private Long statusId;

	/**
	 * 分组
	 */
	private String group;

	/**
	 * 状态0正常1无数据2警告3紧急
	 */
	private Integer status;

	/**
	 * 触发时间单位秒
	 */
	private Long triggerTime;

	/**
	 * 触发时长单位秒
	 */
	private Long triggerDuration;

	/**
	 * 开始时间秒
	 */
	private Long start;

	/**
	 * 结束时间秒
	 */
	private Long end;

	/**
	 * 是否静默0F1T
	 */
	private Integer silence;
	/**
	 * 静默停止时间，永久时间为2099年
	 */
	private Long stopTime;


	/**
	 * 静默超过多少秒 v2.7.2静默计划在用
	 */
	private Long silenceElapsed;
	/**
	 * 静默还剩多少秒 v2.7.2静默计划在用
	 */
	private Long silenceLeft;
	/**
	 * 静默ids v2.7.2静默计划在用
	 */
	private List<Long> silenceIds;
	/**
	 * 被取消静默ids v2.7.2静默计划在用
	 */
	private List<Long> cancelSilenceIds;


	@TableField(value = "create_time")
	private Date createTime;

	@TableField(value = "update_time")
	private Date updateTime;

	private String apiKey;
	/**
	 * 内置监控是否默认开启0F 1T ,default_open
	 */
	private int defaultOpen = 0;
	/**
	 * 内置监控更新类型 0 删除，1更新或新增
	 */
	private int updateType = 1;
	/**
	 * 监控涉及到故障场景规则id列表
	 */
	private Set<Integer> faultIds;

	@ApiModelProperty(value = "创建者ID")
	@TableField(value = "creator_id")
	private Long creatorId;

	@ApiModelProperty(value = "创建者")
	@TableField(value = "creator")
	private String creator;

	@ApiModelProperty(value = "最后一次修改者ID")
	@TableField(value = "editor_id")
	private Long editorId;

	@ApiModelProperty(value = "最后一次修改者")
	@TableField(value = "editor")
	private String editor;

    private String serviceIds;

    private String serviceInstances;

    private String hosts;

    /**
	 * 已经关闭的指标
	 */
	private List<String> closeMetrics;
}
