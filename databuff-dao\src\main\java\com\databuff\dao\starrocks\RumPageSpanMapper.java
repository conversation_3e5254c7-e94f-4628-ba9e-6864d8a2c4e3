package com.databuff.dao.starrocks;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.databuff.entity.dto.WebSearchCriteria;
import com.databuff.entity.rum.starrocks.RumPageSpan;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface RumPageSpanMapper extends BaseMapper<RumPageSpan> {

    @Insert("INSERT INTO dc_rum_page_span (startTime, pageId, traceId, spanId, parentId, appId, sessionId, service, locationHref, processedLocationHref, httpUrl, start, end, duration) VALUES (#{startTime}, #{pageId}, #{traceId}, #{spanId}, #{parentId}, #{appId}, #{sessionId}, #{service}, #{locationHref}, #{processedLocationHref}, #{httpUrl}, #{start}, #{end}, #{duration})")
    void insertRumSlowPageSpan(RumPageSpan rumPageSpan);

    @Select("SELECT * FROM dc_rum_page_span WHERE spanId = #{spanId}")
    RumPageSpan selectRumSlowPageSpanById(Long spanId);

    @Update("UPDATE dc_rum_page_span SET startTime = #{startTime}, pageId = #{pageId}, traceId = #{traceId}, parentId = #{parentId}, appId = #{appId}, sessionId = #{sessionId}, service = #{service}, locationHref = #{locationHref}, processedLocationHref = #{processedLocationHref}, httpUrl = #{httpUrl}, start = #{start}, end = #{end}, duration = #{duration} WHERE spanId = #{spanId}")
    void updateRumSlowPageSpan(RumPageSpan rumPageSpan);

    @Delete("DELETE FROM dc_rum_page_span WHERE spanId = #{spanId}")
    void deleteRumSlowPageSpan(Long spanId);

    @Select("<script>" +
            "SELECT * FROM dc_rum_page_span WHERE 1=1" +
            "<if test='appId != null'> AND app_id = #{appId}</if>" +
            "<if test='pageId != null'> AND page_id = #{pageId}</if>" +
            "<if test='traceId != null'> AND trace_id = #{traceId}</if>" +
            "<if test='processedLocationHref != null'> AND processed_location_href LIKE CONCAT('%', #{processedLocationHref}, '%')</if>" +
            "<if test='locationHref != null'> AND location_href LIKE CONCAT('%', #{locationHref}, '%')</if>" +
            "<if test='service != null'> AND service LIKE CONCAT('%', #{service}, '%')</if>" +
            "<if test='fromTime != null'> AND startTime &gt;= #{fromTime}</if>" +
            "<if test='toTime != null'> AND startTime &lt; #{toTime}</if>" +
            "</script>")
    List<RumPageSpan> searchRumSlowPageSpansWithFilters(WebSearchCriteria searchCriteria);
}