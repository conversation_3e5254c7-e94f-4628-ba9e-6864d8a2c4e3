package com.databuff.common.metric;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class WrapData implements Serializable {
    String[] columns;
    List<List<Number>> values;
    String[] units;
    Map<String, String> tags;
}
