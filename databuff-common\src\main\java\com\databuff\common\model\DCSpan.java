package com.databuff.common.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class DCSpan implements Serializable {

    private static final long serialVersionUID = 1L;

    @JSONField(name = "hours")
    private Long hours;

    @JSONField(name = "minutes")
    private Long minutes;

    @JSONField(name = "startTime")
    private String startTime;

    @JSONField(name = "longStartTime")
    private Long longStartTime;

    @JSONField(name = "is_parent")
    private Integer is_parent;

    @JSONField(name = "slow")
    private Integer slow;

    @JSONField(name = "trace_id")
    private String trace_id;

    @JSONField(name = "span_id")
    private String span_id;

    @JSONField(name = "parent_id")
    private String parent_id;

    @JSONField(name = "df-api-key")
    private String dfApiKey;

    @JSONField(name = "resource")
    private String resource;

    @JSONField(name = "envTag1")
    private String envTag1;

    @JSONField(name = "envTag2")
    private String envTag2;

    @JSONField(name = "service")
    private String service;

    @JSONField(name = "serviceId")
    private String serviceId;

    @JSONField(name = "serviceInstance")
    private String serviceInstance;

    @JSONField(name = "serviceType")
    private String serviceType;

    @JSONField(name = "srcEnvTag1")
    private String srcEnvTag1;

    @JSONField(name = "srcEnvTag2")
    private String srcEnvTag2;

    @JSONField(name = "srcService")
    private String srcService;

    @JSONField(name = "srcServiceId")
    private String srcServiceId;

    @JSONField(name = "srcServiceInstance")
    private String srcServiceInstance;

    @JSONField(name = "srcServiceType")
    private String srcServiceType;

    @JSONField(name = "dstEnvTag1")
    private String dstEnvTag1;

    @JSONField(name = "dstEnvTag2")
    private String dstEnvTag2;

    @JSONField(name = "dstService")
    private String dstService;

    @JSONField(name = "dstServiceId")
    private String dstServiceId;

    @JSONField(name = "dstServiceInstance")
    private String dstServiceInstance;

    @JSONField(name = "dstServiceType")
    private String dstServiceType;

    @JSONField(name = "end")
    private Long end;

    @JSONField(name = "hostName")
    private String hostName;

    @JSONField(name = "error")
    private Integer error;

    @JSONField(name = "type")
    private String type;

    @JSONField(name = "duration")
    private Long duration;

    @JSONField(name = "isIn")
    private Integer isIn;

    @JSONField(name = "start")
    private Long start;

    @JSONField(name = "host_id")
    private String hostId;

    @JSONField(name = "reportService")
    private String reportService;

    @JSONField(name = "meta")
    private Map<String, String> meta;

    @JSONField(name = "name")
    private String name;

    @JSONField(name = "isOut")
    private int isOut;

    @JSONField(name = "metrics")
    private Map<String, String> metrics;

    @JSONField(name = "user-agent")
    private String userAgent;

    @JSONField(name = "meta.http.status_code")
    private int meta_http_status_code;

    @JSONField(name = "meta.error.type")
    private String meta_error_type;

    @JSONField(name = "meta.peer.hostname")
    private String meta_peer_hostname;

    @JSONField(name = "meta.http.method")
    private String meta_http_method;

    @JSONField(name = "meta.http.url")
    private String meta_http_url;

    @JSONField(name = "meta.db.type")
    private String meta_db_type;

    @JSONField(name = "meta.db.instance")
    private String meta_db_instance;

    @JSONField(name = "meta.db.operation")
    private String meta_db_operation;

    @JSONField(name = "meta.mq.topic")
    private String meta_mq_topic;

    @JSONField(name = "meta.mq.group")
    private String meta_mq_group;

    @JSONField(name = "meta.mq.broker")
    private String meta_mq_broker;

    @JSONField(name = "metrics.mq.partition")
    private String metrics_mq_partition;

    @JSONField(name = "meta.root.resource")
    private String meta_root_resource;

    @JSONField(name = "meta.indices")
    private String meta_indices;

    @JSONField(name = "virtual")
    private boolean virtual;

    @JSONField(name = "slowTime")
    protected Long slowTime;

    @JSONField(name = "collectErrorAndSlowTrace")
    private Integer collectErrorAndSlowTrace;

    protected Long cpuTime;
    protected String serviceCode;
    protected String clientReportService;
    protected String apiKey;
    protected String errorType;
    protected Long isSlow = 0L;
    protected Long isVerySlow = 0L;

    protected List<String> biz_pid_id;
    protected List<String> client_biz_pid_id;
    protected List<String> server_biz_pid_id;

    private Integer level;

    public Long getIsSlow() {
        if (null == slowTime) {
            return 0L;
        }
        if (duration >= slowTime * 1000 * 1000 && duration < slowTime * 1000 * 1000 * 4) {
            return 1L;
        }
        return 0L;
    }

    public Long getIsVerySlow() {
        if (null == slowTime) {
            return 0L;
        }
        if (duration > slowTime * 1000 * 1000 * 4) {
            return 1L;
        }
        return 0L;
    }

    public String getApiKey() {
        return null == apiKey ? dfApiKey : apiKey;
    }

    public int groupBy() {
        return trace_id.hashCode() % 64;
    }

}
