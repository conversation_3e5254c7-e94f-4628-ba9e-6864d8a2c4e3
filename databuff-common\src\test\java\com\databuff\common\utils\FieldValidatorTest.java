package com.databuff.common.utils;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

class FieldValidatorTest {

    @Test
    void testRumValidatorWithNullFields() {
        TestObject testObj = new TestObject(null, null);

        boolean hasErrors = FieldValidator.createRumValidator("TestClass", "testMethod")
                .validate("name", testObj.getName())
                .validate("age", testObj.getAge())
                .hasErrors();

        assertTrue(hasErrors);
    }

    @Test
    void testRumValidatorWithValidFields() {
        TestObject testObj = new TestObject("John", 25);

        boolean hasErrors = FieldValidator.createRumValidator("TestClass", "testMethod")
                .validate("name", testObj.getName())
                .validate("age", testObj.getAge())
                .hasErrors();

        assertFalse(hasErrors);
    }

    @Test
    void testSimpleValidatorWithNullFields() {
        TestObject testObj = new TestObject(null, null);

        boolean hasErrors = FieldValidator.createSimpleValidator("SimpleTest")
                .validate("name", testObj.getName())
                .validate("age", testObj.getAge())
                .hasErrors();

        assertTrue(hasErrors);
    }

    private static class TestObject {
        private final String name;
        private final Integer age;

        TestObject(String name, Integer age) {
            this.name = name;
            this.age = age;
        }

        public String getName() {
            return name;
        }

        public Integer getAge() {
            return age;
        }
    }
}