package com.databuff.common.tsdb.model;

import com.databuff.moredb.proto.Common;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Representation of a TSDB database Point.
 */
public class TSDBPoint {

    private String database;
    private String measurement;
    private long timestamp;
    private TimeUnit timeUnit = TimeUnit.MILLISECONDS;
    private Map<String, String> tags;
    private Map<String, Object> fields;
    private Map<String, Common.FieldType> fieldTypes;

    public TSDBPoint(String measurement, long timestamp) {
        this.measurement = measurement;
        this.timestamp = timestamp;
        this.fields = new HashMap<>();
        this.tags = new HashMap<>();
    }

    public TSDBPoint(String measurement, long timestamp, Map<String, String> tags, Map<String, Object> fields) {
        this.measurement = measurement;
        this.timestamp = timestamp;
        this.tags = tags;
        this.fields = fields;
    }

    public TSDBPoint(String database, String measurement, long timestamp, Map<String, String> tags, Map<String, Object> fields) {
        this.database = database;
        this.measurement = measurement;
        this.timestamp = timestamp;
        this.tags = tags;
        this.fields = fields;
    }

    public TSDBPoint(String database, String measurement, long timestamp, Map<String, String> tags, Map<String, Object> fields, Map<String, Common.FieldType> fieldTypes) {
        this.database = database;
        this.measurement = measurement;
        this.timestamp = timestamp;
        this.tags = tags;
        this.fields = fields;
        this.fieldTypes = fieldTypes;
    }

    public String getDatabase() {
        return database;
    }

    public void setDatabase(String database) {
        this.database = database;
    }

    public String getMeasurement() {
        return measurement;
    }

    public void setMeasurement(String measurement) {
        this.measurement = measurement;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public Map<String, String> getTags() {
        return tags;
    }

    public void setTags(Map<String, String> tags) {
        this.tags = tags;
    }

    public Map<String, Object> getFields() {
        return fields;
    }

    public void setFields(Map<String, Object> fields) {
        this.fields = fields;
    }

    public TimeUnit getTimeUnit() {
        return timeUnit;
    }

    public void setTimeUnit(TimeUnit timeUnit) {
        this.timeUnit = timeUnit;
    }

    public void setFieldTypes(Map<String, Common.FieldType> fieldTypes) {
        this.fieldTypes = fieldTypes;
    }

    public Map<String, Common.FieldType> getFieldTypes() {
        return fieldTypes;
    }
}
