package com.databuff.dao.mysql.datahub;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.databuff.entity.datahubv2.DataHubRemappingEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface DataHubRemappingMapper extends BaseMapper<DataHubRemappingEntity> {

    @Select("select name from datahub_remapping")
    List<String> selectNameList();
}
