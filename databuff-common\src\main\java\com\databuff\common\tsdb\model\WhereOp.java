package com.databuff.common.tsdb.model;

import java.util.HashMap;
import java.util.Map;

public enum WhereOp {
    EQ("=", "等于"),          // 等于
    NEQ("!=", "不等于"),      // 不等于
    LT("<", "小于"),          // 小于
    GT(">", "大于"),          // 大于
    LTE("<=", "小于等于"),     // 小于等于
    GTE(">=", "大于等于"),     // 大于等于
    LIKE("LIKE", "模糊匹配"),  // 模糊匹配
    NOT_LIKE("NOTLIKE", "反向模糊匹配"), // 反向模糊匹配
    START_WITH("STARTWITH", "以某个值开头"), // 以某个值开头
    END_WITH("ENDWITH", "以某个值结尾"), // 以某个值结尾
    REGEX("REGEX", "正则匹配"), // 正则匹配
    IN("INLIST", "IN 语句"),      // IN 语句
    NOT_IN("NOTINLIST", "NOT IN 语句"), // NOT IN 语句
    IS("EMPTY", "为空"),      // IS NULL
    IS_NOT("NOTEMPTY", "不为空"); // IS NOT NULL

    // 存储符号值（如"="、"!="等）
    private final String symbol;
    private final String description;

    // 构造器
    private WhereOp(String symbol, String description) {
        this.symbol = symbol;
        this.description = description;
    }

    // 获取符号值
    public String getSymbol() {
        return symbol;
    }

    // 获取描述
    public String getDescription() {
        return description;
    }

    private static final Map<String, WhereOp> symbolToOp = new HashMap<>();

    static {
        for (WhereOp op : values()) {
            symbolToOp.put(op.symbol, op);
        }
    }

    public static WhereOp fromSymbol(String value) {
        if (value == null) {
            return EQ;
        }
        String trimmed = value.trim().toUpperCase();
        return symbolToOp.getOrDefault(trimmed, EQ);
    }
}
