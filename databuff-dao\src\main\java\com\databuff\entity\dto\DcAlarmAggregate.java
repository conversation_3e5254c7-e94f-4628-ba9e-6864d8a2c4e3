package com.databuff.entity.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@ApiModel(description = "告警聚合信息")
@Entity
@Table(name = "dc_alarm_aggregate")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DcAlarmAggregate {

    @ApiModelProperty(value = "告警时间戳")
    @Column(name = "time")
    private String time;

    @ApiModelProperty(value = "API密钥")
    @Column(name = "apiKey")
    private String apiKey;

    @ApiModelProperty(value = "告警ID")
    @Column(name = "id")
    private String id;

    @ApiModelProperty(value = "域ID")
    @Column(name = "gid")
    private String gid;

    @ApiModelProperty(value = "业务系统列表")
    @Column(name = "busName")
    private String busName;

    @ApiModelProperty(value = "主机列表")
    @Column(name = "host")
    private String host;

    @ApiModelProperty(value = "服务ID列表")
    @Column(name = "serviceId")
    private String serviceId;

    @ApiModelProperty(value = "服务实例列表")
    @Column(name = "serviceInstance")
    private String serviceInstance;

    @ApiModelProperty(value = "检测类型")
    @Column(name = "classification")
    private String classification;

    @ApiModelProperty(value = "告警等级")
    @Column(name = "level")
    private Integer level;

    @ApiModelProperty(value = "告警处理状态")
    @Column(name = "status")
    private Integer status;

    @ApiModelProperty(value = "描述")
    @Column(name = "description")
    private String description;

    @ApiModelProperty(value = "当前事件数求和")
    @Column(name = "eventCnt")
    private Integer eventCnt;
}