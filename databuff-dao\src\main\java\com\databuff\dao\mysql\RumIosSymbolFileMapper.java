package com.databuff.dao.mysql;

import com.databuff.entity.rum.mysql.IosSymbolFile;
import com.databuff.entity.rum.web.IosSymbolSearchCriteria;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface RumIosSymbolFileMapper {
    String getDsymPathByUuid(@Param("uuid") String uuid);

    void insert(IosSymbolFile symbolFile);

    List<IosSymbolFile> selectAll();

    List<IosSymbolFile> getSymbolFiles(IosSymbolSearchCriteria search);

    int deleteSymbolFiles(@Param("ids") List<Long> ids);

    List<IosSymbolFile> getSymbolFilesByIds(@Param("ids") List<Long> ids);

    IosSymbolFile getSymbolFileById(@Param("id") Long id);

    IosSymbolFile getByUuidAndAppId(@Param("uuid") String uuid, @Param("appId") Integer appId);

    int updateByUuid(IosSymbolFile symbolFile);

}
