package com.databuff.common.tsdb.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 * TSDB动态配置类
 * 支持运行时刷新配置
 * 继承 GenericObjectPoolConfig 用于配置连接池
 */
@RefreshScope
@Data
@Slf4j
public class TSDBRefreshScopeConfig extends GenericObjectPoolConfig {

    // 数据库类型
    @Value("${tsdb.db:moredb}")
    private String db;

    // 连接配置
    @Value("${tsdb.url:moredb:2890}")
    private String url;

    @Value("${tsdb.api:http://moredb:8080/api}")
    private String api;

    @Value("${tsdb.user:databuff}")
    private String user;

    @Value("${tsdb.password:databuff666}")
    private String password;

    // 通用配置
    @Value("${tsdb.duration:30d}")
    private String duration;

    @Value("${tsdb.max-total:100}")
    private int maxTotal;

    @Value("${tsdb.max-idle:50}")
    private int maxIdle;

    @Value("${tsdb.min-idle:10}")
    private int minIdle;

    @Value("${tsdb.max-wait-millis:3000}")
    private long maxWaitMillis;

    @Value("${tsdb.soft-min-evictable-idle-time-millis:600000}")
    private long softMinEvictableIdleTimeMillis;

    @Value("${tsdb.test-on-borrow:false}")
    private boolean testOnBorrow;

    @Value("${tsdb.test-while-idle:true}")
    private boolean testWhileIdle;

    @Value("${tsdb.time-between-eviction-runs-millis:120000}")
    private long timeBetweenEvictionRunsMillis;

    @Value("${tsdb.queryTimeout:60000}")
    private long queryTimeout;

    @Value("${tsdb.shard:2}")
    private Integer shard;

    @Value("${tsdb.replication:1}")
    private Integer replication;

    @Value("${tsdb.interval:60}")
    private Integer interval;

    /**
     * SQL打印开关
     * 控制是否在日志中打印TSDB查询SQL
     */
    @Value("${tsdb.sql.print.enabled:true}")
    private boolean sqlPrintEnabled;

    /**
     * 构造函数
     * 初始化连接池配置
     */
    public TSDBRefreshScopeConfig() {
        super();
        log.info("创建TSDBRefreshScopeConfig实例");
    }

    /**
     * 在属性设置后调用此方法初始化连接池配置
     * 将配置属性设置到父类 GenericObjectPoolConfig 中
     */
    public void initPoolConfig() {
        // 池大小配置
        this.setMaxTotal(maxTotal);
        this.setMaxIdle(maxIdle);
        this.setMinIdle(minIdle);
        this.setMaxWaitMillis(maxWaitMillis);

        // 验证配置
        this.setTestOnBorrow(testOnBorrow); // 借用对象前验证
        this.setTestWhileIdle(testWhileIdle); // 空闲时验证

        // 驱逐配置
        this.setSoftMinEvictableIdleTimeMillis(softMinEvictableIdleTimeMillis);
        this.setTimeBetweenEvictionRunsMillis(timeBetweenEvictionRunsMillis);

        // 其他配置
        this.setJmxEnabled(false);

        log.info("初始化连接池配置: maxTotal={}, maxIdle={}, minIdle={}, testOnBorrow={}, testWhileIdle={}",
                maxTotal, maxIdle, minIdle, testOnBorrow, testWhileIdle);
    }
}
