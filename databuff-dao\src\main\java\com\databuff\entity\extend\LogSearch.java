package com.databuff.entity.extend;

import com.databuff.common.utils.DateUtils;
import com.databuff.common.utils.TimeUtil;
import com.databuff.entity.SearchParamSetter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @author:TianMing
 * @date: 2022/11/28
 * @time: 11:07
 */
@Data
public class LogSearch implements SearchParamSetter {
    @ApiModelProperty("关键字搜索")
    private String query ;
    @ApiModelProperty("主机")
    private List<String> hosts ;
    @ApiModelProperty("服务")
    private List<String> services;
    @ApiModelProperty("服务实例")
    private List<String> serviceInstances;
    @ApiModelProperty("状态")
    private List<String> status ;
    @ApiModelProperty("服务ids")
    private List<String> serviceIds;
    @ApiModelProperty("调用链id")
    private String traceId;
    @ApiModelProperty("spanid")
    private String spanId;
    @ApiModelProperty("uid")
    private String uid;
    @ApiModelProperty("uids")
    private List<String> uids;
    @ApiModelProperty(value = "开始时间",example = "2021-07-15 00:00:00")
    private String fromTime;
    @ApiModelProperty(value = "结束时间",example = "2021-07-15 23:59:59")
    private String toTime;

    @ApiModelProperty(value = "开始时间纳秒")
    private Long fromTimeNs;
    @ApiModelProperty(value = "结束时间纳秒")
    private Long toTimeNs;

    @ApiModelProperty("排序字段（ts）")
    private String sortField = "ts";
    @ApiModelProperty("排序方式 asc desc")
    private String sortOrder = "desc";

    @ApiModelProperty("scrollId")
    private String scrollId;
    @ApiModelProperty("每页数量")
    private Integer size = 15;
    @ApiModelProperty("每页数量")
    private Integer offset = 0;


    @ApiModelProperty("管理域权限服务ids")
    private List<String> permissionSvcIds;
    @ApiModelProperty("管理域权限主机")
    private List<String> permissionHosts ;
    @ApiModelProperty("环境标签1")
    private List<String> envTag1s;
    @ApiModelProperty("环境标签2")
    private List<String> envTag2s;

    private String apiKey;
    public Long getFromTimeVul(){
        if (StringUtils.isBlank(this.fromTime)){
            return DateUtils.strToDate("yyyy-MM-dd HH:mm:ss", this.fromTime).getTime() - TimeUtil.OUT_DAY_MS_LONG;
        }
        return DateUtils.strToDate("yyyy-MM-dd HH:mm:ss",this.fromTime).getTime();
    }
    public Long getToTimeVul(){
        return DateUtils.strToDate("yyyy-MM-dd HH:mm:ss",this.toTime).getTime();
    }
}
