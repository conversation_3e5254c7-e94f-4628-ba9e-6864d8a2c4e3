package com.databuff.common.utils;

import java.io.File;
import java.net.URISyntaxException;
import java.net.URL;

public class TestResourceUtil {

    public static String getResourcePath(String relativePath) {
        try {
            URL resource = TestResourceUtil.class.getClassLoader().getResource(relativePath);
            if (resource == null) {
                throw new IllegalArgumentException("Resource file not found: " + relativePath);
            }
            return new File(resource.toURI()).getPath();
        } catch (URISyntaxException e) {
            throw new RuntimeException("Failed to get resource file path", e);
        }
    }
}
