package com.databuff.common.constants;


import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Value;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static com.databuff.common.constants.Constant.Trace.*;
import static com.databuff.common.constants.TSDBIndex.*;

/**
 * 登录常量
 *
 * <AUTHOR> @date 2019/3/3 16:03
 */
public class Constant {
    public static final String CURRENT_MONITOR_COUNT = "current.monitor.count";
    public static final String TSDB_QUERY_EXCEPTION = "tsdb.query.exception";

    public static final String JWT_FILTER_REFUSE = "jwt.filter.refuse";
    public static final String JWT_FILTER_TIME_DIFF = "jwt.filter.time.diff";
    public static final String AAU_CHECK_ERROR = "authError";
    public static final String AAU_LOG_HOST_REFUSE = "authRefuse";
    public static final String AAU_LOG_NO_HOST_ID = "noHostId";

    public static final String AAU_LOG_NO_HOST_MEM = "noHostMem";
    public static final String AAU_LOG_OUT_OF_UNIT = "outOfUnit";
    public static final String AAU_LOG_UNIT_CHECK_ERROR = "unitCheckError";
    public static final String JWT_LOG_CHECK_ERROR = "checkError";
    public static final String JWT_LOG_CHECK_AGENT_NO_APIKEY = "agentNoApiKey";
    public static final String JWT_LOG_CHECK_PLATFORM_NO_APIKEY = "platformNoApiKey";

    // Redis key前缀定义
    public static final String AGENT_AUTH_UNIT_TOTAL = "AAU:Total:"; // 总授权单位数key前缀
    public static final String AGENT_AUTH_UNIT_LIST = "AAU:List:";  // 授权单位列表key前缀
    public static final String AGENT_AUTH_UNIT_REMAIN = "AAU:Remain:"; // 剩余授权单位key前缀
    public static final String AGENT_AUTH_FEATURES = "AAU:Features:"; // 功能权限


    public static final String EVENT_AUTO_RECOVERY = "EVENT_AUTO_RECOVERY";
    public static final String ALARM_CONFIG = "ALARM_CONFIG";
    public static final String ALARM_CREATED_CNT = "alarm.created.cnt";
    public static final String EVENT_CREATED_CNT = "event.created.cnt";

    public static final String UNIT_CONFIG = "UNIT_CONFIG";

    public static final String GROUP_AUTH_CONFIG = "GROUP_AUTH_CONFIG";
    public static final String METRIC_VAL = "metricsVal";

    public static final String VIRTUAL = "virtual";
    public static final String REMOTE = "remote";

    /**
     * redis-OK
     */
    public static final String OK = "OK";

    public static final String SUCCESS = "success";

    public static final String FAIL = "fail";

    public static final List<String> SORT_ORDER = Arrays.asList(new String[]{
            "DESC", "ASC", "desc", "asc"
    });

    /**
     * redis-key-前缀-shiro:cache:
     */
    public static final String PREFIX_SHIRO_CACHE = "databuff:cache:";

    /**
     * redis-key-前缀-shiro:refresh_token:
     */
    public static final String PREFIX_SHIRO_REFRESH_TOKEN = "databuff:refresh_token:";
    public static final String PREFIX_SHIRO_REFRESH_IP = "databuff:refresh_ip:";
    public static final String PREFIX_SHIRO_REFRESH_CID = "databuff:refresh_cid:";
    /**
     * 页面过期时间缓存
     */
    public static final String REFRESH_TOKEN_EXPIRETIME = "databuff:refreshTokenExpireTime";
    /**
     * 验证码前缀
     */
    public static final String VERIFY_CODE_PREFIX = "databuff:verify_code:";
    /**
     * 有效apiKey缓存:
     */
    public static final String SAAS_API_KEY_CATCH = "saas:apikey:";
    /**
     * JWT-account:
     */
    public static final String ACCOUNT = "account";
    /**
     * JWT-currentTimeMillis:
     */
    public static final String CURRENT_TIME_MILLIS = "currentTimeMillis";
    /**
     * DomainManagerInterceptor-gid:
     */
    public static final String AGI = "agi";
    /**
     * DomainManagerInterceptor-Authorization:
     */
    public static final String AUTHORIZATION = "Authorization";
    /**
     * 慢服务定义 超过500 ms
     */
    public static final Long SLOW_TIME = 500L;

    public static final String SEPARATOR = "#&#";

    /**
     * agent离线时间 5分钟
     */
    public static final long OFFLINE_TIME = 300L;
    public static final String AGENT_ANOMALY = "Anomaly";
    public static final String AGENT_ANOMALY_CN = "异常";
    public static final String AGENT_OFFLINE = "OffLine";
    public static final String AGENT_OFFLINE_CN = "离线";
    public static final String AGENT_ONLINE = "OnLine";
    public static final String AGENT_ONLINE_CN = "在线";

    public static final String AGENT_PRELOAD = "agent:preload:";


    public static class Zookeeper {
        public static final String LOCK_KEY_HOME_PAGE_METRICS_TIMER = "/zk/lock/homePageMetricsTimer";

    }

    public static class Redis {
        public static final String HOST = "host";
        public static final String PORT = "port";
        public static final String PWD = "";
        protected static final ConcurrentHashMap CONFIG = new ConcurrentHashMap();

        public static void put(Object key, Object value) {
            CONFIG.put(key, value);
        }

        public static Object get(Object key) {
            return CONFIG.get(key);
        }
    }

    public static class Kafka {
        public static final String CONSUMER = "127.0.0.1:9092";
        public static final String PRODUCER = "127.0.0.1:9092";
        public static final String ZOOKEEPER_CONNECT = "127.0.0.1:2181";
        public static final String TRACE_GROUP_ID = "f1_trace_group_id";
        public static final String METRIC_GROUP_ID = "f1_metric_group_id";
        public static final String PROCESS1_GROUP_ID = "df_1_process_group_id";
        public static final String OTHER_RESOURCE_GROUP_ID = "df_other_resource_group_id";
        public static final String NPM_STATS_CONSUMER_GROUP_ID = "dc_npm_stats_consumer";
        public static final String NPM_DNS_CONSUMER_GROUP_ID = "dc_npm_dns_consumer";
        public static final String STANDARD_METRIC_CONSUMER_GROUP_ID = "flink_metric_writer_group";
        /**
         * 日志数据
         */
        public static final String LOG_TOPIC = "dc_databuff_log";

        /**
         * npm统计数据
         */
        public static final String NPM_STATS_TOPIC = "dc_databuff_npm_stats";

        public static final String NPM_DNS_TOPIC = "dc_databuff_npm_dns";
        /**
         * <<<<<<< HEAD
         * =======
         * 指标数据
         */
        public static final String METRIC_TOPIC = "dc_databuff_metric";
        /**
         * >>>>>>> b4cb5786e (数据模型修改)
         * 进程数据
         */
        public static final String PROCESS_TOPIC = "dc_databuff_process";
        /**
         * 容器数据
         */
        public static final String CONTAINER_TOPIC = "dc_databuff_container";
        /**
         * k8s数据
         */
        public static final String K8S_TOPIC = "dc_databuff_k8s";

        /**
         * trace调用链数据
         */
        public static final String TRACE_TOPIC = "dc_databuff_trace";
        public static final String TRACE_TO_FILL_TOPIC = "dc_databuff_trace_to_fill";
        public static final String TRACE_FILLED_TOPIC = "dc_databuff_trace_filled";
        public static final String TRACE_COLLECTOR_TOPIC = "dc_databuff_trace_collector";
        public static final String PROFILING_ORIGIN_TOPIC = "dc_databuff_profiling_origin";
        public static final String PROFILING_HOTSPOT_TOPIC = "dc_databuff_profiling_hotspot";
        public static final String PROFILING_STACK_TOPIC = "dc_databuff_profiling_stack";
        public static final String PROFILING_STACK_BASE_TOPIC = "dc_databuff_profiling_stack_base";

        public static final String STANDARD_METRIC_TOPIC = "dc_standard_metric";

        /**
         * rum数据
         */
        public static final String RUM_TOPIC = "rum_data";
        public static final String RUM_TOPIC_GROUP_ID = "rum-span-analyse-group";
        public static final String RUM_LOGS_TOPIC = "rum_logs";
        public static final String RUM_IOS_TOPIC = "rum_ios_data";
        public static final String RUM_IOS_LOGS_TOPIC = "rum_ios_logs";
        public static final String RUM_ANDROID_TOPIC = "rum_android_data";
        public static final String RUM_ANDROID_LOGS_TOPIC = "rum_android_logs";
        public static final String RUM_APP_TOPIC_GROUP_ID = "rum-app-span-analyse-group";

    }

    public static class TS_DB {
        public static final String MOREDB = "moredb";
        public static final String OPENGEMINI = "opengemini";
    }

    /**
     * 定义MORE_DB数据库的连接参数、API端点及配置选项。
     */
    public static class MORE_DB {
        public static final String URL = "http://moredb:2890";
        public static final String API = "http://moredb:8080/api";
        public static final String USER = "databuff";
        public static final String PASSWORD = "databuff666";
        public static final int QUERY_TIMEOUT = 30000;
        public static final String DURATION = "30d";
        public static final int SHARD = 2;
        public static final int REPLICATION = 1;
        public static final int INTERVAL = 60;
        public static final boolean JMX_ENABLED = false;
    }

    public static class Npm {

        public static final String DIRECTION = "direction";
        public static final String OUTGOING = "outgoing";
        public static final String MATCH_TYPE = "matchType";
        public static final String MATCH_TYPE_FULL_MATH = "clientFullMatch";
        public static final String MATCH_TYPE_VIP_MATCH = "clientVipMatch";
        public static final String MATCH_TYPE_SERVER_MATCH = "serverMatch";

        public static final String LADDR_IP = "laddr_ip";
        public static final String LADDR_PORT = "laddr_port";
        public static final String LADDR_CID = "laddr_cid";
        public static final String LADDR_CNAME = "laddr_cname";
        public static final String LADDR_HOSTNAME = "laddr_hostname";
        public static final String LADDR_PID = "laddr_pid";
        public static final String LADDR_PNAME = "laddr_pname";
        public static final String LADDR_CLUSTER_ID = "laddr_cluster_id";
        public static final String LADDR_POD_NAME = "laddr_pod_name";
        public static final String LADDR_DEPLOYMENT = "laddr_deployment";
        public static final String LADDR_SVC_ID = "laddr_svc_id";
        public static final String LADDR_SVC_NAME = "laddr_svc_name";
        public static final String LADDR_SVC_INSTANCE = "laddr_svc_instance";

        public static final String RADDR_IP = "raddr_ip";
        public static final String RADDR_PORT = "raddr_port";
        public static final String RADDR_CID = "raddr_cid";
        public static final String RADDR_CNAME = "raddr_cname";
        public static final String RADDR_HOSTNAME = "raddr_hostname";
        public static final String RADDR_PID = "raddr_pid";
        public static final String RADDR_PNAME = "raddr_pname";
        public static final String RADDR_CLUSTER_ID = "raddr_cluster_id";
        public static final String RADDR_POD_NAME = "raddr_pod_name";
        public static final String RADDR_DEPLOYMENT = "raddr_deployment";
        public static final String RADDR_SVC_ID = "raddr_svc_id";
        public static final String RADDR_SVC_NAME = "raddr_svc_name";
        public static final String RADDR_SVC_INSTANCE = "raddr_svc_instance";

        public static final String IP = "ip";
        public static final String PORT = "port";
        public static final String CID = "cid";
        public static final String CNAME = "cname";
        public static final String HOSTNAME = "hostname";
        public static final String PID = "pid";
        public static final String PNAME = "pname";
        public static final String CLUSTER_ID = "clusterId";
        public static final String POD_NAME = "podName";
        public static final String DEPLOYMENT = "deployment";

        public static final String SRC_IP = "srcIp";
        public static final String SRC_PORT = "srcPort";
        public static final String SRC_CID = "srcCid";
        public static final String SRC_CNAME = "srcCname";
        public static final String SRC_HOSTNAME = "srcHostname";
        public static final String SRC_PID = "srcPid";
        public static final String SRC_PNAME = "srcPname";
        public static final String SRC_CLUSTER_ID = "srcClusterId";
        public static final String SRC_POD_NAME = "srcPodName";
        public static final String SRC_DEPLOYMENT = "srcDeployment";

        public static final String FIELD_EPHEMERAL_PORT = "ephemeral_port";
        public static final String FIELD_RTT = "rtt";
        public static final String FIELD_RTT_VAR = "rtt_var";

        public static final String IP_TYPE = "ip_type";

        public static final Map<String, String> IN_COMING_MAPPING = new HashMap<>();
        public static final Map<String, String> OUT_GOING_MAPPING = new HashMap<>();

        static {
            IN_COMING_MAPPING.put(LADDR_IP, IP);
            IN_COMING_MAPPING.put(LADDR_PORT, PORT);
            IN_COMING_MAPPING.put(LADDR_CID, CID);
            IN_COMING_MAPPING.put(LADDR_HOSTNAME, HOSTNAME);
            IN_COMING_MAPPING.put(LADDR_CNAME, CNAME);
            IN_COMING_MAPPING.put(LADDR_PID, PID);
            IN_COMING_MAPPING.put(LADDR_PNAME, PNAME);
            IN_COMING_MAPPING.put(LADDR_CLUSTER_ID, CLUSTER_ID);
            IN_COMING_MAPPING.put(LADDR_POD_NAME, POD_NAME);
            IN_COMING_MAPPING.put(LADDR_DEPLOYMENT, DEPLOYMENT);
            IN_COMING_MAPPING.put(LADDR_SVC_ID, SERVICE_ID);
            IN_COMING_MAPPING.put(LADDR_SVC_NAME, SERVICE);
            IN_COMING_MAPPING.put(LADDR_SVC_INSTANCE, SERVICE_INSTANCE);
            IN_COMING_MAPPING.put(RADDR_IP, SRC_IP);
            IN_COMING_MAPPING.put(RADDR_PORT, SRC_PORT);
            IN_COMING_MAPPING.put(RADDR_CID, SRC_CID);
            IN_COMING_MAPPING.put(RADDR_HOSTNAME, SRC_HOSTNAME);
            IN_COMING_MAPPING.put(RADDR_CNAME, SRC_CNAME);
            IN_COMING_MAPPING.put(RADDR_PID, SRC_PID);
            IN_COMING_MAPPING.put(RADDR_PNAME, SRC_PNAME);
            IN_COMING_MAPPING.put(RADDR_CLUSTER_ID, SRC_CLUSTER_ID);
            IN_COMING_MAPPING.put(RADDR_POD_NAME, SRC_POD_NAME);
            IN_COMING_MAPPING.put(RADDR_DEPLOYMENT, SRC_DEPLOYMENT);
            IN_COMING_MAPPING.put(RADDR_SVC_ID, SRC_SERVICE_ID);
            IN_COMING_MAPPING.put(RADDR_SVC_NAME, SRC_SERVICE);
            IN_COMING_MAPPING.put(RADDR_SVC_INSTANCE, SRC_SERVICE_INSTANCE);

            OUT_GOING_MAPPING.put(LADDR_IP, SRC_IP);
            OUT_GOING_MAPPING.put(LADDR_PORT, SRC_PORT);
            OUT_GOING_MAPPING.put(LADDR_CID, SRC_CID);
            OUT_GOING_MAPPING.put(LADDR_HOSTNAME, SRC_HOSTNAME);
            OUT_GOING_MAPPING.put(LADDR_CNAME, SRC_CNAME);
            OUT_GOING_MAPPING.put(LADDR_PID, SRC_PID);
            OUT_GOING_MAPPING.put(LADDR_PNAME, SRC_PNAME);
            OUT_GOING_MAPPING.put(LADDR_CLUSTER_ID, SRC_CLUSTER_ID);
            OUT_GOING_MAPPING.put(LADDR_POD_NAME, SRC_POD_NAME);
            OUT_GOING_MAPPING.put(LADDR_DEPLOYMENT, SRC_DEPLOYMENT);
            OUT_GOING_MAPPING.put(LADDR_SVC_ID, SRC_SERVICE_ID);
            OUT_GOING_MAPPING.put(LADDR_SVC_NAME, SRC_SERVICE);
            OUT_GOING_MAPPING.put(LADDR_SVC_INSTANCE, SRC_SERVICE_INSTANCE);
            OUT_GOING_MAPPING.put(RADDR_IP, IP);
            OUT_GOING_MAPPING.put(RADDR_PORT, PORT);
            OUT_GOING_MAPPING.put(RADDR_CID, CID);
            OUT_GOING_MAPPING.put(RADDR_HOSTNAME, HOSTNAME);
            OUT_GOING_MAPPING.put(RADDR_CNAME, CNAME);
            OUT_GOING_MAPPING.put(RADDR_PID, PID);
            OUT_GOING_MAPPING.put(RADDR_PNAME, PNAME);
            OUT_GOING_MAPPING.put(RADDR_CLUSTER_ID, CLUSTER_ID);
            OUT_GOING_MAPPING.put(RADDR_POD_NAME, POD_NAME);
            OUT_GOING_MAPPING.put(RADDR_DEPLOYMENT, DEPLOYMENT);
            OUT_GOING_MAPPING.put(RADDR_SVC_ID, SERVICE_ID);
            OUT_GOING_MAPPING.put(RADDR_SVC_NAME, SERVICE);
            OUT_GOING_MAPPING.put(RADDR_SVC_INSTANCE, SERVICE_INSTANCE);
        }
    }

    public static class Trace {
        public static final String TRACE_ID = "trace_id";
        public static final String SPAN_ID = "span_id";
        public static final String ERROR = "error";
        public static final String DURATION = "duration";
        public static final String NAME = "name";
        public static final String START = "start";
        public static final String END = "end";
        public static final String GENERATE_SERVICE = "generate.service";
        public static final String GENERATE_IP = "generate.ip";
        public static final String COL_SCOPE_NAME = "col_scope_name";
        public static final String JAVA_VERSION = "java.version";
        public static final String JAVA_VENDOR = "java.vendor";
        public static final String SPAN_KIND_CLIENT = "client";
        public static final String IS_SLOW = "is_slow";
        public static final String COMPONENT = "component";
        public static final String LOW_APIKEY = "apikey";
        public static final String ERRORTYPE = "errorType";
        public static final String SERVICE = "service";
        public static final String SERVICE_CODE = "serviceCode";
        public static final String SERVICE_ID = "serviceId";
        public static final String SERVICE_INSTANCE = "serviceInstance";
        public static final String SRC_SERVICE = "srcService";
        public static final String SRC_SERVICE_INSTANCE = "srcServiceInstance";
        public static final String SRC_SERVICE_ID = "srcServiceId";
        public static final String BIZ_EVENT_NAME = "bizEventName";
        public static final String BIZ_EVENT_ID = "bizEventId";
        public static final String APP_ID = "appId";
        public static final String APP_NAME = "appName";
        public static final String PROBLEM_ID = "problemId";
        public static final String INFLUENCESERVICE_COUNT = "influenceServiceCount";
        public static final String PROBLEM_SERVICE = "problemService";
        public static final String ISSUE_ID = "issueId";
        public static final String RESOURCE = "resource";
        public static final String INDICES = "indices";
        public static final String HTTP_URL = "http.url";
        public static final String DATA_SOURCE = "data.source";
        public static final String SDK_LANGUAGE = "sdkLanguage";
        public static final String PROCESS_RUNTIME_NAME = "processRuntimeName";
        public static final String PROCESS_RUNTIME_VERSION = "processRuntimeVersion";
        public static final String SQL_DATABASE = "sqlDatabase";

        public static final String REMOTE_ARRRESS_NAME = "remoteAddress";

        public static final String SERVER_SERVICE = "serverService";
        public static final String SERVER_SERVICE_ID = "server_service_id";
        public static final String SERVER_SERVICE_INSTANCE = "serverServiceInstance";
        public static final String SERVICE_TYPE_DB = "db";
        public static final String SERVICE_TYPE_WEB = "web";
        public static final String SERVICE_TYPE_CACHE = "cache";
        public static final String SERVICE_TYPE_MQ = "mq";
        public static final String SERVICE_TYPE_CUSTOM = "custom";
        public static final String SERVICE_TYPE_REMOTE = "remote";
        public static final String ROOT_SERVICE_TYPE_SERVICE = "SERVICE";
        public static final String ROOT_SERVICE_TYPE_RUM = "RUM";
        //业务父系统id-系统id
        public static final String BIZ_PID_ID = "biz_pid_id";
        //server业务父系统id-系统id

        public static final String SERVICE_TYPE = "service_type";

        public static final String ERROR_TYPE = "error.type";
        public static final String K8S_CLUSTER_ID = "k8s.clusterId";
        public static final String K8S_NAMESPACE = "k8s.namespace";
        public static final String K8S_POD_NAME = "k8s.podName";
        public static final String K8S_CONTAINER_NAME = "k8s.containerName";
        public static final String K8S_CONTAINER_ID = "k8s.containerId";
        public static final String K8S_HOST_NAME = "k8s.hostName";
        public static final String HOSTNAME = "hostName";
        public static final String HOST_NAME = "hostname";
        public static final String HOST_IP = "hostIp";
        public static final String PROCESS_PID = "process.pid";
        public static final String ALL_COMPONENT = "all.component";
        public static final String SYSTEM_HEARTBEAT = "system.heartbeat";
        public static final String IS_IN = "isIn";
        public static final String IS_OUT = "isOut";
        public static final String IS_IN_TRUE = "1";
        public static final String LANGUAGE = "language";
        public static final String ENTRY_PATH_ID = "entryPathId";
        public static final String PARENT_PATH_ID = "parentPathId";
        public static final String PATH_ID = "pathId";
        public static final String ENTRY_INTERFACE_PATH_ID = "entryInterfacePathId";
        public static final String TIME = "time";

        public static final String ROOT_RESOURCE = "root.resource";
        public static final String ROOT_TYPE = "root.type";
        public static final String ROOT_NAME = "root.name";
        public static final String HTTP_METHOD = "http.method";
        public static final String HTTP_STATUS_CODE = "http.status_code";

        public static final String DB_UPDATE_ROWS = "db.updateRows";
        public static final String DB_SELECT_RETURN_ROWS = "database.select.return.rows";

        public static final String TRACE_RECORD_E2E_DURATUIB_NS = "record.e2e_duration_ns";
        public static final String TRACE_CPU_TIME_COST_NS = "cpu.time.cost.ns";

        public static final String TRACE_METRIC_RESP_BODY_LENGTH = "response.body.length";
        public static final String TRACE_METRIC_REQ_BODY_LENGTH = "request.body.length";
        public static final String TRACE_METRIC_MQ_BODY_LENGTH = "mq.body.length";

        public static final String TRACE_META_OPERATION = "operation";
        public static final String TRACE_META_CONFIG_TYPE = "config.type";

        public static final String TRACE_AGENT_SOURCE = "agent.source";
        public static final String TRACE_DATABUFF_JAVA = "databuffJavaAgent";
        public static final String TRACE_NEXT_AGENT = "NextAgent";
        public static final String TRACE_DATA_HUB = "dataHub";
        public static final String DATA_HUB_SOURCE_OTLP = "otlp";
        public static final String TRACE_DATA_SOURCE_OTLP = "OpenTelemetry";
        public static final String DATA_HUB_SOURCE_SKY = "skywalking";
        public static final String TRACE_DATA_SOURCE_SKY = "SkyWalking";
        public static final String TRACE_DATA_SOURCE_DATABUFF = "Databuff";

        /**
         * nacos
         */
        public static final String TRACE_SERVICE_NACOS = "nacos";
        public static final String TRACE_NAME_OUT_NACOS_QUERY = "nacos";
        /**
         * memcached
         */
        public static final String TRACE_SERVICE_MEMCACHED = "memcached";
        public static final String TRACE_NAME_OUT_MEMCACHED_QUERY = "memcached.query";

        /**
         * couchbase
         */
        public static final String TRACE_SERVICE_COUCHBASE = "couchbase";
        public static final String TRACE_NAME_OUT_COUCHBASE_CALL = "couchbase.call";

        /**
         * cassandra
         */
        public static final String TRACE_SERVICE_CASSANDRA = "cassandra";
        public static final String TRACE_NAME_OUT_CASSANDRA_EXECUTE = "cassandra.execute";

        /**
         * mongo
         */
        public static final String TRACE_SERVICE_MONGO = "mongo";
        public static final String TRACE_NAME_OUT_MONGO_QUERY = "mongo.query";
        public static final String TRACE_NAME_OUT_MONGODB_QUERY = "mongodb.query";

        public static final String TRACE_RECORD_E2E_DURATION_NS = "record.e2e_duration_ns";

        /**
         * kafka
         */
        public static final String TRACE_SERVICE_KAFKA = "kafka";
        public static final String TRACE_NAME_IN_KAFKA_CONSUME = "kafka.consume";
        public static final String TRACE_NAME_OUT_KAFKA_PRODUCE = "kafka.produce";
        public static final String TRACE_COMPONENT_KAFKA = "kafka";
        public static final String TRACE_KAFKA_TOPIC = "topic";
        public static final String TRACE_KAFKA_BROKER = "broker";

        /**
         * rabbitmq
         */
        public static final String TRACE_SERVICE_RABBITMQ = "rabbitmq";
        public static final String TRACE_NAME_IN_RABBITMQ_CONSUME = "amqp.consume";
        public static final String TRACE_NAME_OUT_RABBITMQ_PRODUCE = "amqp.produce";
        public static final String TRACE_COMPONENT_RABBITMQ = "rabbitmq";
        public static final String TRACE_RABBITMQ_TOPIC = "topic";
        public static final String TRACE_RABBITMQ_BROKER = "broker";

        /**
         * es
         */
        public static final String TRACE_SERVICE_ES = "elasticsearch";
        public static final String TRACE_NAME_OUT_ES_REST_QUERY = "elasticsearch.rest.query";
        public static final String TRACE_NAME_OUT_ES_QUERY = "elasticsearch.query";

        /**
         * redis
         */
        public static final String TRACE_SERVICE_REDIS = "redis";
        public static final String TRACE_NAME_OUT_REDIS_QUERY = "redis.query";
        public static final String TRACE_NAME_OUT_REDIS_COMMAND = "redis.command";

        /**
         * mysql
         */
        public static final String TRACE_SERVICE_MYSQL = "mysql";
        public static final String TRACE_NAME_OUT_MYSQL_QUERY = "mysql.query";
        /**
         * gaussdb
         */
        public static final String TRACE_SERVICE_GAUSSDB = "gaussdb";
        public static final String TRACE_NAME_OUT_GAUSSDB_QUERY = "gaussdb.query";
        /**
         * oracle
         */
        public static final String TRACE_SERVICE_ORACLE = "oracle";
        public static final String TRACE_NAME_OUT_ORACLE_QUERY = "oracle.query";

        /**
         * postgresql
         */
        public static final String TRACE_SERVICE_POSTGRESQL = "postgresql";
        public static final String TRACE_NAME_OUT_POSTGRESQL_QUERY = "postgresql.query";
        public static final String TRACE_NAME_OUT_POSTGRE_QUERY = "postgres.query";

        /**
         * sqlserver
         */
        public static final String TRACE_SERVICE_SQLSERVER = "sqlserver";
        public static final String TRACE_NAME_OUT_SQLSERVER_QUERY = "sqlserver.query";
        public static final String TRACE_NAME_OUT_SQL_SERVER_QUERY = "sql-server.query";

        /**
         * db2
         */
        public static final String TRACE_SERVICE_DB2 = "db2";
        public static final String TRACE_NAME_OUT_DB2_QUERY = "db2.query";

        /**
         * h2
         */
        public static final String TRACE_SERVICE_H2 = "h2";
        public static final String TRACE_NAME_OUT_H2_QUERY = "h2.query";

        /**
         * derby
         */
        public static final String TRACE_SERVICE_DERBY = "derby";
        public static final String TRACE_NAME_OUT_DERBY_QUERY = "derby.query";

        /**
         * hsqldb
         */
        public static final String TRACE_SERVICE_HSQLDB = "hsqldb";
        public static final String TRACE_NAME_OUT_HSQLDB_QUERY = "hsqldb.query";

        /**
         * oceanbase
         */
        public static final String TRACE_SERVICE_OCEANBASE = "oceanbase";
        public static final String TRACE_NAME_OUT_OCEANBASE_QUERY = "oceanbase.query";

        /**
         * http
         */
        public static final String TRACE_NAME_OUT_OK_HTTP_REQUEST = "okhttp.request";
        public static final String TRACE_NAME_OUT_HTTP_REQUEST = "http.request";
        public static final String TRACE_NAME_IN_ASPNET_REQUEST = "aspnet.request";
        public static final String TRACE_NAME_IN_ASPNET_MVC_REQUEST = "aspnet-mvc.request";
        public static final String TRACE_NAME_IN_ASPNET_WEBAPI_REQUEST = "aspnet-webapi.request";
        public static final String TRACE_NAME_IN_ASPNET_CORE_REQUEST = "aspnet_core.request";
        public static final String TRACE_NAME_IN_ASPNET_CORE_MVC_REQUEST = "aspnet_core_mvc.request";
        public static final String TRACE_NAME_IN_SERVLET_REQUEST = "servlet.request";
        public static final String TRACE_NAME_IN_AKKA_HTTP_REQUEST = "akka-http.request";
        public static final String TRACE_NAME_IN_AXWAY_REQUEST = "axway.request";
        public static final String TRACE_NAME_IN_FINATRA_REQUEST = "finatra.request";
        public static final String TRACE_NAME_IN_GRIZZLY_REQUEST = "grizzly.request";
        public static final String TRACE_NAME_IN_MICRONAUT_CONTROLLER = "micronaut-controller";
        public static final String TRACE_NAME_IN_NETTY_REQUEST = "netty.request";
        public static final String TRACE_NAME_IN_PLAY_REQUEST = "play.request";
        public static final String TRACE_NAME_IN_RATPACK_HANDLER = "ratpack.handler";
        public static final String TRACE_NAME_IN_RESTLET_HTTP_REQUEST = "restlet-http.request";
        public static final String TRACE_NAME_IN_SPRAY_HTTP_REQUEST = "spray-http.request";
        public static final String TRACE_NAME_IN_SYNAPSE_REQUEST = "synapse.request";
        public static final String TRACE_NAME_IN_UNDERTOW_HTTP_REQUEST = "undertow-http.request";
        public static final String TRACE_NAME_IN_VERTX_ROUTE_HANDLER = "vertx.route-handler";


        /**
         * dubbo
         */
        public static final String TRACE_NAME_DUBBO = "dubbo";
        public static final String TRACE_NAME_OUT_DUBBO_CALL = "dubbo.call";
        public static final String TRACE_NAME_IN_DUBBO_SERVICE = "dubbo.service";

        /**
         * sofarpc
         */
        public static final String TRACE_NAME_SOFARPC = "sofarpc";
        public static final String TRACE_NAME_OUT_SOFARPC_CALL = "sofarpc.call";
        public static final String TRACE_NAME_IN_SOFARPC_SERVICE = "sofarpc.service";

        /**
         * grpc
         */
        public static final String TRACE_NAME_GRPC = "grpc";
        public static final String TRACE_NAME_OUT_GRPC_CLIENT = "grpc.client";
        public static final String TRACE_NAME_OUT_GRPC_REQUEST = "grpc.request";
        public static final String TRACE_NAME_IN_GRPC_SERVER = "grpc.server";

        /**
         * rocketmq
         */
        public static final String TRACE_SERVICE_ROCKETMQ = "rocketmq";
        public static final String TRACE_NAME_OUT_ROCKETMQ_PRODUCER = "rocketmq.produce";
        public static final String TRACE_NAME_IN_ROCKETMQ_CONSUME = "rocketmq.consume";
        public static final String TRACE_TOPIC = "topic";
        public static final String TRACE_BROKER_NAME = "broker";
        public static final String TRACE_COMPONENT_ROCKETMQ = "apache-rocketmq";

        /**
         * sofamq
         */
        public static final String TRACE_SERVICE_SOFAMQ = "sofamq";
        public static final String TRACE_NAME_OUT_SOFAMQ_PRODUCER = "sofamq.produce";
        public static final String TRACE_NAME_IN_SOFAMQ_CONSUME = "sofamq.consume";
        public static final String TRACE_COMPONENT_SOFAMQ = "sofamq";

        /**
         * 池 name
         */
        public static final String POOL_GET_OBJECT = "object_pool.borrow";
        public static final String POOL_GET_DB = "datasource.getConnection";
        public static final String POOL_GET_HTTP = "http_connection_pool.getConnection";

        public static final String SKY_DRUID_POOL_GET_DB_RESOURCE = "Druid/Connection/getConnection";
        public static final String SKY_HIKARICP_POOL_GET_DB_RESOURCE = "HikariCP/Connection/getConnection";
        public static final Set<String> SKY_POOL_GET_DB_RESOURCES = Sets.newHashSet(SKY_DRUID_POOL_GET_DB_RESOURCE, SKY_HIKARICP_POOL_GET_DB_RESOURCE);
        /**
         * remote type
         */
        public static final String REMOTE_TYPE_HTTP = "Http";
        public static final String REMOTE_TYPE_DUBBO = "Dubbo";
        public static final String REMOTE_TYPE_GRPC = "Grpc";
        public static final String REMOTE_TYPE_SOFARPC = "Sofarpc";


        /**
         * span type
         */
        public static final String HTTP_CLIENT = "http";
        public static final String HTTP_SERVER = "web";
        public static final String RPC = "rpc";
        public static final String CACHE = "cache";
        public static final String SOAP = "soap";
        public static final String SQL = "sql";
        public static final String MONGO = "mongodb";
        public static final String CASSANDRA = "cassandra";
        public static final String COUCHBASE = "db"; // Using generic for now.
        public static final String REDIS = "redis";
        public static final String MEMCACHED = "memcached";
        public static final String ELASTICSEARCH = "elasticsearch";
        public static final String HIBERNATE = "hibernate";
        public static final String AEROSPIKE = "aerospike";
        public static final String QUEUE = "queue";
        public static final String GRAPHQL = "graphql";
        public static final String CONFIG = "config";


        public static final Set<String> TRACE_POOL_GET_NAMES = Sets.newHashSet(POOL_GET_OBJECT, POOL_GET_DB, POOL_GET_HTTP);
        public static final Set<String> SUPPORTED_ES_NAMES = Sets.newHashSet(TRACE_NAME_OUT_ES_REST_QUERY, TRACE_NAME_OUT_ES_QUERY);
        public static final Set<String> SUPPORTED_REDIS_NAMES = Sets.newHashSet(TRACE_NAME_OUT_REDIS_QUERY, TRACE_NAME_OUT_REDIS_COMMAND);
        public static final Set<String> SUPPORTED_REDIS_POOL_OBJ_TYPES = Sets.newHashSet("redis.clients.jedis.Jedis", "redis.clients.jedis.BinaryJedis");
        public static final Map<String, OutConfig> TRACE_OUT_MIDDLEWARE_CONFIGS = new HashMap<>();
        public static final Set<String> TRACE_OUT_NAMES = new HashSet<>();
        public static final Map<String, String> TRACE_TYPE_METRIC_NAME_MAP = new HashMap<>();
        public static final Set<String> TRACE_OUT_NAMES_HTTP = Sets.newHashSet(TRACE_NAME_OUT_HTTP_REQUEST, TRACE_NAME_OUT_OK_HTTP_REQUEST);
        public static final Set<String> TRACE_OUT_NAMES_RPC = Sets.newHashSet(TRACE_NAME_OUT_DUBBO_CALL, TRACE_NAME_OUT_SOFARPC_CALL, TRACE_NAME_OUT_GRPC_CLIENT, TRACE_NAME_OUT_GRPC_REQUEST);
        public static final Set<String> TRACE_OUT_NAMES_MQ = Sets.newHashSet(TRACE_NAME_OUT_ROCKETMQ_PRODUCER, TRACE_NAME_OUT_SOFAMQ_PRODUCER, TRACE_NAME_OUT_KAFKA_PRODUCE, TRACE_NAME_OUT_RABBITMQ_PRODUCE);

        public static final Set<String> TRACE_POOL_NAMES = Sets.newHashSet(POOL_GET_HTTP, POOL_GET_DB, POOL_GET_OBJECT);
        public static final Map<String, String> REMOTE_TYPE_METRIC_NAME_MAP = new HashMap<>();
        public static final Set<String> TRACE_OUT_MIDDLEWARE_NAMES = new HashSet<>();
        public static final Set<String> TRACE_IN_NAMES = Sets.newHashSet(TRACE_NAME_IN_SERVLET_REQUEST, TRACE_NAME_IN_KAFKA_CONSUME,
                TRACE_NAME_IN_DUBBO_SERVICE, TRACE_NAME_IN_SOFARPC_SERVICE, TRACE_NAME_IN_AKKA_HTTP_REQUEST, TRACE_NAME_IN_AXWAY_REQUEST,
                TRACE_NAME_IN_FINATRA_REQUEST, TRACE_NAME_IN_GRIZZLY_REQUEST, TRACE_NAME_IN_MICRONAUT_CONTROLLER, TRACE_NAME_IN_NETTY_REQUEST,
                TRACE_NAME_IN_PLAY_REQUEST, TRACE_NAME_IN_RATPACK_HANDLER, TRACE_NAME_IN_RESTLET_HTTP_REQUEST, TRACE_NAME_IN_SPRAY_HTTP_REQUEST,
                TRACE_NAME_IN_SYNAPSE_REQUEST, TRACE_NAME_IN_UNDERTOW_HTTP_REQUEST, TRACE_NAME_IN_VERTX_ROUTE_HANDLER, TRACE_NAME_IN_GRPC_SERVER,
                TRACE_NAME_IN_ROCKETMQ_CONSUME, TRACE_NAME_IN_RABBITMQ_CONSUME, TRACE_NAME_IN_SOFAMQ_CONSUME,
                TRACE_NAME_IN_ROCKETMQ_CONSUME, TRACE_NAME_IN_RABBITMQ_CONSUME, TRACE_NAME_IN_SOFAMQ_CONSUME,
                TRACE_NAME_IN_ASPNET_REQUEST, TRACE_NAME_IN_ASPNET_MVC_REQUEST, TRACE_NAME_IN_ASPNET_WEBAPI_REQUEST, TRACE_NAME_IN_ASPNET_CORE_REQUEST,
                TRACE_NAME_IN_ASPNET_CORE_MVC_REQUEST);
        public static final Set<String> TRACE_IN_NAMES_HTTP = Sets.newHashSet(TRACE_NAME_IN_SERVLET_REQUEST, TRACE_NAME_IN_AKKA_HTTP_REQUEST, TRACE_NAME_IN_AXWAY_REQUEST,
                TRACE_NAME_IN_GRIZZLY_REQUEST, TRACE_NAME_IN_MICRONAUT_CONTROLLER, TRACE_NAME_IN_NETTY_REQUEST, TRACE_NAME_IN_PLAY_REQUEST, TRACE_NAME_IN_RATPACK_HANDLER,
                TRACE_NAME_IN_RESTLET_HTTP_REQUEST, TRACE_NAME_IN_SPRAY_HTTP_REQUEST, TRACE_NAME_IN_SYNAPSE_REQUEST, TRACE_NAME_IN_UNDERTOW_HTTP_REQUEST,
                TRACE_NAME_IN_VERTX_ROUTE_HANDLER, TRACE_NAME_IN_ASPNET_REQUEST, TRACE_NAME_IN_ASPNET_MVC_REQUEST, TRACE_NAME_IN_ASPNET_WEBAPI_REQUEST, TRACE_NAME_IN_ASPNET_CORE_REQUEST,
                TRACE_NAME_IN_ASPNET_CORE_MVC_REQUEST);

        public static final Set<String> TRACE_IN_NAMES_PRC = Sets.newHashSet(TRACE_NAME_IN_GRPC_SERVER, TRACE_NAME_IN_DUBBO_SERVICE, TRACE_NAME_IN_SOFARPC_SERVICE);

        public static final Set<String> TRACE_IN_NAMES_MQ = Sets.newHashSet(TRACE_NAME_IN_ROCKETMQ_CONSUME, TRACE_NAME_IN_SOFAMQ_CONSUME, TRACE_NAME_IN_KAFKA_CONSUME, TRACE_NAME_IN_RABBITMQ_CONSUME);

        public static final Set<String> SUPPORTED_DB_NAME = Sets.newHashSet(TRACE_NAME_OUT_MYSQL_QUERY, TRACE_NAME_OUT_ORACLE_QUERY, TRACE_NAME_OUT_POSTGRESQL_QUERY, TRACE_NAME_OUT_POSTGRE_QUERY, TRACE_NAME_OUT_SQL_SERVER_QUERY, TRACE_NAME_OUT_SQLSERVER_QUERY, TRACE_NAME_OUT_DB2_QUERY, TRACE_NAME_OUT_H2_QUERY, TRACE_NAME_OUT_HSQLDB_QUERY, TRACE_NAME_OUT_DERBY_QUERY, TRACE_NAME_OUT_OCEANBASE_QUERY, TRACE_NAME_OUT_GAUSSDB_QUERY, TRACE_NAME_OUT_MONGO_QUERY, TRACE_NAME_OUT_MONGO_QUERY);


        static {
            TRACE_OUT_MIDDLEWARE_CONFIGS.put(TRACE_NAME_OUT_NACOS_QUERY, new OutConfig(SERVICE_TYPE_CUSTOM, TRACE_SERVICE_NACOS, TRACE_SERVICE_NACOS));
            TRACE_OUT_MIDDLEWARE_CONFIGS.put(TRACE_NAME_OUT_MEMCACHED_QUERY, new OutConfig(SERVICE_TYPE_CACHE, TRACE_SERVICE_MEMCACHED, TRACE_SERVICE_MEMCACHED));
            TRACE_OUT_MIDDLEWARE_CONFIGS.put(TRACE_NAME_OUT_COUCHBASE_CALL, new OutConfig(SERVICE_TYPE_DB, TRACE_SERVICE_COUCHBASE, TRACE_SERVICE_COUCHBASE));
            TRACE_OUT_MIDDLEWARE_CONFIGS.put(TRACE_NAME_OUT_CASSANDRA_EXECUTE, new OutConfig(SERVICE_TYPE_DB, TRACE_SERVICE_CASSANDRA, TRACE_SERVICE_CASSANDRA));
            TRACE_OUT_MIDDLEWARE_CONFIGS.put(TRACE_NAME_OUT_MONGO_QUERY, new OutConfig(SERVICE_TYPE_DB, TRACE_SERVICE_MONGO, TRACE_SERVICE_MONGO));
            TRACE_OUT_MIDDLEWARE_CONFIGS.put(TRACE_NAME_OUT_MONGODB_QUERY, new OutConfig(SERVICE_TYPE_DB, TRACE_SERVICE_MONGO, TRACE_SERVICE_MONGO));
            TRACE_OUT_MIDDLEWARE_CONFIGS.put(TRACE_NAME_OUT_KAFKA_PRODUCE, new OutConfig(SERVICE_TYPE_MQ, TRACE_SERVICE_KAFKA, TRACE_SERVICE_KAFKA));
            TRACE_OUT_MIDDLEWARE_CONFIGS.put(TRACE_NAME_OUT_ROCKETMQ_PRODUCER, new OutConfig(SERVICE_TYPE_MQ, TRACE_SERVICE_ROCKETMQ, TRACE_SERVICE_ROCKETMQ));
            TRACE_OUT_MIDDLEWARE_CONFIGS.put(TRACE_NAME_OUT_SOFAMQ_PRODUCER, new OutConfig(SERVICE_TYPE_MQ, TRACE_SERVICE_SOFAMQ, TRACE_SERVICE_SOFAMQ));
            TRACE_OUT_MIDDLEWARE_CONFIGS.put(TRACE_NAME_OUT_RABBITMQ_PRODUCE, new OutConfig(SERVICE_TYPE_MQ, TRACE_SERVICE_RABBITMQ, TRACE_SERVICE_RABBITMQ));
            TRACE_OUT_MIDDLEWARE_CONFIGS.put(TRACE_NAME_OUT_ES_REST_QUERY, new OutConfig(SERVICE_TYPE_DB, TRACE_SERVICE_ES, TRACE_SERVICE_ES));
            TRACE_OUT_MIDDLEWARE_CONFIGS.put(TRACE_NAME_OUT_ES_QUERY, new OutConfig(SERVICE_TYPE_DB, TRACE_SERVICE_ES, TRACE_SERVICE_ES));
            TRACE_OUT_MIDDLEWARE_CONFIGS.put(TRACE_NAME_OUT_REDIS_QUERY, new OutConfig(SERVICE_TYPE_CACHE, TRACE_SERVICE_REDIS, TRACE_SERVICE_REDIS));
            TRACE_OUT_MIDDLEWARE_CONFIGS.put(TRACE_NAME_OUT_REDIS_COMMAND, new OutConfig(SERVICE_TYPE_CACHE, TRACE_SERVICE_REDIS, TRACE_SERVICE_REDIS));
            TRACE_OUT_MIDDLEWARE_CONFIGS.put(TRACE_NAME_OUT_MYSQL_QUERY, new OutConfig(SERVICE_TYPE_DB, TRACE_SERVICE_MYSQL, TRACE_SERVICE_MYSQL));
            TRACE_OUT_MIDDLEWARE_CONFIGS.put(TRACE_NAME_OUT_GAUSSDB_QUERY, new OutConfig(SERVICE_TYPE_DB, TRACE_SERVICE_GAUSSDB, TRACE_SERVICE_GAUSSDB));
            TRACE_OUT_MIDDLEWARE_CONFIGS.put(TRACE_NAME_OUT_ORACLE_QUERY, new OutConfig(SERVICE_TYPE_DB, TRACE_SERVICE_ORACLE, TRACE_SERVICE_ORACLE));
            TRACE_OUT_MIDDLEWARE_CONFIGS.put(TRACE_NAME_OUT_POSTGRESQL_QUERY, new OutConfig(SERVICE_TYPE_DB, TRACE_SERVICE_POSTGRESQL, TRACE_SERVICE_POSTGRESQL));
            TRACE_OUT_MIDDLEWARE_CONFIGS.put(TRACE_NAME_OUT_POSTGRE_QUERY, new OutConfig(SERVICE_TYPE_DB, TRACE_SERVICE_POSTGRESQL, TRACE_SERVICE_POSTGRESQL));
            TRACE_OUT_MIDDLEWARE_CONFIGS.put(TRACE_NAME_OUT_SQLSERVER_QUERY, new OutConfig(SERVICE_TYPE_DB, TRACE_SERVICE_SQLSERVER, TRACE_SERVICE_SQLSERVER));
            TRACE_OUT_MIDDLEWARE_CONFIGS.put(TRACE_NAME_OUT_SQL_SERVER_QUERY, new OutConfig(SERVICE_TYPE_DB, TRACE_SERVICE_SQLSERVER, TRACE_SERVICE_SQLSERVER));
            TRACE_OUT_MIDDLEWARE_CONFIGS.put(TRACE_NAME_OUT_DB2_QUERY, new OutConfig(SERVICE_TYPE_DB, TRACE_SERVICE_DB2, TRACE_SERVICE_DB2));
            TRACE_OUT_MIDDLEWARE_CONFIGS.put(TRACE_NAME_OUT_H2_QUERY, new OutConfig(SERVICE_TYPE_DB, TRACE_SERVICE_H2, TRACE_SERVICE_H2));
            TRACE_OUT_MIDDLEWARE_CONFIGS.put(TRACE_NAME_OUT_DERBY_QUERY, new OutConfig(SERVICE_TYPE_DB, TRACE_SERVICE_DERBY, TRACE_SERVICE_DERBY));
            TRACE_OUT_MIDDLEWARE_CONFIGS.put(TRACE_NAME_OUT_HSQLDB_QUERY, new OutConfig(SERVICE_TYPE_DB, TRACE_SERVICE_HSQLDB, TRACE_SERVICE_HSQLDB));
            TRACE_OUT_MIDDLEWARE_CONFIGS.put(TRACE_NAME_OUT_OCEANBASE_QUERY, new OutConfig(SERVICE_TYPE_DB, TRACE_SERVICE_OCEANBASE, TRACE_SERVICE_OCEANBASE));

            TRACE_OUT_MIDDLEWARE_NAMES.addAll(TRACE_OUT_MIDDLEWARE_CONFIGS.keySet());
            TRACE_OUT_NAMES.addAll(TRACE_OUT_MIDDLEWARE_CONFIGS.keySet());
            TRACE_OUT_NAMES.add(TRACE_NAME_OUT_HTTP_REQUEST);
            TRACE_OUT_NAMES.add(TRACE_NAME_OUT_OK_HTTP_REQUEST);
            TRACE_OUT_NAMES.add(TRACE_NAME_OUT_DUBBO_CALL);
            TRACE_OUT_NAMES.add(TRACE_NAME_OUT_SOFARPC_CALL);
            TRACE_OUT_NAMES.add(TRACE_NAME_OUT_GRPC_CLIENT);

            TRACE_TYPE_METRIC_NAME_MAP.put(ELASTICSEARCH, TSDB_METRIC_ES_TABLE_NAME);
            TRACE_TYPE_METRIC_NAME_MAP.put(HIBERNATE, TSDB_METRIC_DB_TABLE_NAME);
            TRACE_TYPE_METRIC_NAME_MAP.put(AEROSPIKE, TSDB_METRIC_DB_TABLE_NAME);
            TRACE_TYPE_METRIC_NAME_MAP.put(GRAPHQL, TSDB_METRIC_DB_TABLE_NAME);
            TRACE_TYPE_METRIC_NAME_MAP.put(COUCHBASE, TSDB_METRIC_DB_TABLE_NAME);
            TRACE_TYPE_METRIC_NAME_MAP.put(CASSANDRA, TSDB_METRIC_DB_TABLE_NAME);
            TRACE_TYPE_METRIC_NAME_MAP.put(MONGO, TSDB_METRIC_DB_TABLE_NAME);
            TRACE_TYPE_METRIC_NAME_MAP.put(SQL, TSDB_METRIC_DB_TABLE_NAME);
            TRACE_TYPE_METRIC_NAME_MAP.put(REDIS, TSDB_METRIC_REDIS_TABLE_NAME);
            TRACE_TYPE_METRIC_NAME_MAP.put(HTTP_CLIENT, TSDB_METRIC_HTTP_TABLE_NAME);
            TRACE_TYPE_METRIC_NAME_MAP.put(HTTP_SERVER, TSDB_METRIC_HTTP_TABLE_NAME);
            TRACE_TYPE_METRIC_NAME_MAP.put(RPC, TSDB_METRIC_RPC_TABLE_NAME);
            TRACE_TYPE_METRIC_NAME_MAP.put(SOAP, TSDB_METRIC_RPC_TABLE_NAME);
            TRACE_TYPE_METRIC_NAME_MAP.put(QUEUE, TSDB_METRIC_MQ_TABLE_NAME);
            TRACE_TYPE_METRIC_NAME_MAP.put(CONFIG, TSDB_METRIC_CONFIG_TABLE_NAME);
            TRACE_OUT_NAMES.add(TRACE_NAME_OUT_GRPC_REQUEST);


            REMOTE_TYPE_METRIC_NAME_MAP.put(TRACE_NAME_OUT_HTTP_REQUEST, REMOTE_TYPE_HTTP);
            REMOTE_TYPE_METRIC_NAME_MAP.put(TRACE_NAME_OUT_OK_HTTP_REQUEST, REMOTE_TYPE_HTTP);
            REMOTE_TYPE_METRIC_NAME_MAP.put(TRACE_NAME_OUT_DUBBO_CALL, REMOTE_TYPE_DUBBO);
            REMOTE_TYPE_METRIC_NAME_MAP.put(TRACE_NAME_OUT_GRPC_CLIENT, REMOTE_TYPE_GRPC);
            REMOTE_TYPE_METRIC_NAME_MAP.put(TRACE_NAME_OUT_GRPC_REQUEST, REMOTE_TYPE_GRPC);
            REMOTE_TYPE_METRIC_NAME_MAP.put(TRACE_NAME_OUT_SOFARPC_CALL, REMOTE_TYPE_SOFARPC);

        }

        public static final String PEER_HOSTNAME = "peer.hostname";
        public static final String PEER_PORT = "peer.port";
        public static final String DB_PORT = "db.port";
        public static final String DB_TYPE = "db.type";
        public static final String DB_INSTANCE = "db.instance";
        public static final String DB_OPERATION = "db.operation";
        public static final String TAG_CLIENT_SERVICE = "client.service";
        public static final String TAG_CLIENT_IP = "client.ip";
        public static final String TAG_SERVER_SERVICE = "server.service";
        public static final String TAG_SERVER_IP = "server.ip";

        public static final String SERVICE_INSTANCE_METRIC = "service.instance.metricsVal";
        public static final String TYPE_SERVICE_INSTANCE = "service.instance";

        public static final String COMPONENT_TOMCAT_SERVER = "tomcat-server";
        public static final String COMPONENT_APACHE_DUBBO = "apache-dubbo";
        public static final String COMPONENT_ALIBABA_DUBBO = "alibaba-dubbo";
        public static final String COMPONENT_JETTY_SERVER = "jetty-server";
        public static final String DTS_RECEIVE_TIME = "dtsReceiveTime";
        public static final String POOL_NAME = "pool.name";
        public static final String POOL_TYPE = "pool.type";
        public static final String OBJ_TYPE = "obj.type";
        public static final String HTTP_HOSTNAME = "http.hostname";
        public static final String PARTITION = "partition";
        public static final String LAST_SPAN_NAME = "lastSpanName";
        public static final String LAST_SPAN_RESOURCE = "lastSpanResource";
        public static final String LAST_DB_TYPE = "lastDbType";
    }

    public static class Metric {
        public static final String DATABUFF_DATABASE = "databuff_database";
        public static final String DATABUFF_MEASUREMENT = "databuff_measurement";
        public static final String METRIC_API_KEY = "apiKey";
        public static final String TAG = "tag";
        public static final String FIELDS = "fields";
        public static final String FIELDTYPES = "fieldTypes";
        public static final String TIMESTAMP = "timestamp";
        public static final String DEFAULT_FIELD = "metricsVal";
        public static final String DATABASE_APM_METRIC = "apm_metric";
        public static final String DATABASE_INFRASTRUCTURE = "infrastructure";
        public static final String DATABASE_DATABUFF = "databuff";
        public static final String MEASUREMENT_SERVICE_HEALTH_STATUS = "service.health_status";
        public static final String SKIP = "skip";
        public static final String FIELD_MAX_DURATION = "maxDuration";
        public static final String FIELD_MIN_DURATION = "minDuration";
        public static final String TAG_VIRTUAL_SERVICE = "virtualService";
    }

    public static class Root {
        public static final String CASE_TYPE_DESC = "caseTypeDesc";
        public static final String NO_CASE_TYPE = "无异常";
    }

    /**
     * Span service 中间件匹配规则
     */
    public static final Map<String, String> PLUGIN_MACH_SERVICE = new HashMap<>();

    static {
        PLUGIN_MACH_SERVICE.put("mysql", "mysql(.*?)|mariadb");
        PLUGIN_MACH_SERVICE.put("mongodb", "mongo(.*?)");
        PLUGIN_MACH_SERVICE.put("redis", "(.*?)redis(.*?)|jedis(.*?)");
        PLUGIN_MACH_SERVICE.put("postgresql", "pg(.*?)|(.*?)postgre(.*?)");
        PLUGIN_MACH_SERVICE.put("elasticsearch", "elasticsearch(.*?)");
        PLUGIN_MACH_SERVICE.put("oracle", "oracle(.*?)");
        PLUGIN_MACH_SERVICE.put("sqlserver", "sqlserver(.*?)");
        PLUGIN_MACH_SERVICE.put("memcache", "(.*?)memcache(.*?)");
        PLUGIN_MACH_SERVICE.put("kafka", "kafka(.*?)");
        PLUGIN_MACH_SERVICE.put("consul", "consul(.*?)");
        PLUGIN_MACH_SERVICE.put("sqlite", "sqlite(.*?)");
        PLUGIN_MACH_SERVICE.put("aerospike", "aerospike(.*?)");
        PLUGIN_MACH_SERVICE.put("tomcat", "tomcat(.*?)");
    }


    public static final String DF_API_KEY = "api_key";
    public static final String DF_API_KEY_VALUE = "NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4";


    public static final String AUTH_PLUGIN_LIST = "AUTH_PLUGIN_LIST";
    //授权 单位数
    public static final String AUTH_UNITS = "AUTH_UNITS";
    //授权 语言
    public static final String AUTH_LANGS = "AUTH_LANGS";
    //授权 功能特征
    public static final String AUTH_FEATURES = "AUTH_FEATURES";
    /**
     * agent上报数据header
     */
    //agent上报数据当时时间戳毫秒
    public static final String TIME_NOW = "time-now";
    //agent所在与dts服务器时差
    public static final String TIME_DIFF = "time-diff";
    public static final String AGENT_HOSTNAME = "x-dd-hostname";
    public static final String AGENT_HOSTNAME2 = "df-hostname";
    public static final String AGENT_HOST_ID = "hostId";
    public static final String CONTENT_TYPE = "Content-Type";
    public static final String CONTENT_TYPE_MSGPACK = "application/msgpack";
    public static final String CONTENT_TYPE_JSON = "application/json";
    public static final String AGENT_LAST_UPDATE_TIME = "lastUpdateTime";
    public static final String AGENT_HOST_MEM = "hostMem";
    public static final String AGENT_HOST_TAG = "hosttag";
    public static final String AGENT_FEATURE_TAG = "host-feature";
    public static final String HOST = "host";
    public static final String PNAME = "pname";
    public static final String CONTAINER = "container";
    public static final String BUSINESS_NAME = "businessName";
    public static final String API_KEY = "dd-api-key";
    public static final String API_KEY2 = "df-api-key";
    public static final String USER_AGENT = "user-agent";
    public static final String RUM_API_KEY = "api_key";
    public static final String HOST_ID = "hostid";
    public static final String HOST_G_ID = "host_id";
    public static final String CONTAINER_ID = "container_id";
    public static final String CLUSTER_ID = "X-DF-Orchestrator-ClusterID";
    public static final String IS_K8S = "isk8s";
    public static final String SEC_CH_UA = "sec-ch-ua";
    public static final String HOST_IP = "hostip";
    //agent环境 [host  、 docker、  kubernetes]
    public static final String CONTENT_LENGTH = "content-length";
    public static final String DEFAULT_BROKER_NAME = "127.0.0.1";

    public static final String MONITOR = "monitor";
    public static final String CONVERGENCE = "convergence";
    public static final String ACTION = "action";
    public static final String SILENCE = "silence";

    public static final Map<String, MetricTagFieldInfo> METRIC_TAG_CN_EN = new HashMap<>();

    public static String ENV_TAG1 = "envTag1";
    public static String ENV_TAG2 = "envTag2";
    public static String SRC_ENV_TAG1 = "srcEnvTag1";
    public static String SRC_ENV_TAG2 = "srcEnvTag2";
    //环境标签
    public static final Map<String, String> ENV_MAP = new HashMap<>();

    static {
        ENV_MAP.put(ENV_TAG1, "");
        ENV_MAP.put(ENV_TAG2, "");

        METRIC_TAG_CN_EN.put("host", new MetricTagFieldInfo("host", "主机名称", "infra", 1));
        METRIC_TAG_CN_EN.put("container_name", new MetricTagFieldInfo("container_name", "容器名称", "infra", 2));
        METRIC_TAG_CN_EN.put("pname", new MetricTagFieldInfo("pname", "进程组名称", "infra", 3));
        METRIC_TAG_CN_EN.put("elastic_cluster", new MetricTagFieldInfo("elastic_cluster", "ES集群", "infra", 2));
        METRIC_TAG_CN_EN.put("kube_container_name", new MetricTagFieldInfo("kube_container_name", "K8s容器名称", "infra", 3));
        METRIC_TAG_CN_EN.put("device_name", new MetricTagFieldInfo("device_name", "磁盘分区", "infra", 3));
        METRIC_TAG_CN_EN.put("pod_name", new MetricTagFieldInfo("pod_name", "Pod名称", "infra", 2));
        METRIC_TAG_CN_EN.put("core", new MetricTagFieldInfo("core", "CPU核", "infra", 3));
        METRIC_TAG_CN_EN.put("sqlDatabase", new MetricTagFieldInfo("sqlDatabase", "数据库库名", "apm", 3));
        METRIC_TAG_CN_EN.put("sqlContent", new MetricTagFieldInfo("sqlContent", "数据库sql", "apm", 4));
        METRIC_TAG_CN_EN.put("service", new MetricTagFieldInfo("service", "服务名称", "apm", 1));
        METRIC_TAG_CN_EN.put("serviceInstance", new MetricTagFieldInfo("serviceInstance", "服务实例名称", "apm", 2));
        METRIC_TAG_CN_EN.put("url", new MetricTagFieldInfo("sqlDatabase", "URL", "apm", 3));
        METRIC_TAG_CN_EN.put("durationRange", new MetricTagFieldInfo("durationRange", "响应时间分层", "apm", 5));
        METRIC_TAG_CN_EN.put("httpCode", new MetricTagFieldInfo("httpCode", "状态码", "apm", 3));
        METRIC_TAG_CN_EN.put("businessName", new MetricTagFieldInfo("businessName", "业务系统名称", "apm", 0));
    }


    public static class Notify {
        public static final Integer MAX_SMS_MSG_LENGTH = 20;
        public static final String MAIL = "mail";
        public static final String SMS = "sms";
        public static final String WECHAT = "wechat";
        public static final String DINGTALK = "dingtalk";
        public static final String WEBHOOK = "webhook";
        public static final String SOCKET = "socket";

        //字符编码集
        public static final String CHARSET = "utf-8";

        /**
         * 钉钉应用的唯一标识key
         */
        public static final String appkey = "dingn5bsz8ouzrpietnh";

        /**
         * 钉钉应用的密钥
         */
        public static final String appsecret = "Ry-E8wvKRJqUU0n7QRGwVVBG6dB1BwtY4spgkk7flhKGRrihGOf6_SrNMI0q05Yn";
        /**
         * 钉钉AgentID
         */
        public static final Long agentId = 1410724978L;

    }

    public static final Set<String> SNMP_MEASUREMENTS = Sets.newHashSet("snmp", "snmp.tcp", "snmp.udp", "snmp.ip", "snmp.interface");

    public static final String ROOTS = "roots";
    public static final String START_TIME = "startTime";
    public static final String END_TIME = "endTime";
    public static final String SERVICE_TYPE = "serviceType";
    public static final String SERVICE_TYPE_RUM = "RUM";
    public static final String ABNORMAL_DETAIL = "abnormalDetail";
    public static final String ABNORMAL_FIRST_INDEX = "abnormalFirstIndex";
    public static final String ABNORMAL_LAST_INDEX = "abnormalLastIndex";
    public static final String ALARM = "alarm";
    public static final String SERVICE_ICON_TYPE = "serviceIconType";
    public static final String CASE_TYPE = "caseType";
    public static final String COLUMNS = "columns";
    public static final String COLUMNS_DESC = "columnsDesc";
    public static final String CHILDREN = "children";
    public static final String URL = "url";
    public static final String COLUMN_SERVICE_INSTANCE = "serviceInstance";
    public static final String COLUMN_RESOURCE = "resource";
    public static final String COLUMN_SRC_SERVICE_INSTANCE = "srcServiceInstance";
    public static final String COLUMN_SQL_OPERATION = "sqlOperation";
    public static final String COLUMN_SQL_CONTENT = "sqlContent";
    public static final String COLUMN_COMMAND = "command";
    public static final String COLUMN_TOPIC = "topic";
    public static final String COLUMN_BROKER = "broker";
    public static final String COLUMN_PARTITION = "partition";
    public static final String COLUMN_EXCEPTION_NAME = "exceptionName";
    public static final String COLUMN_POOL_NAME = "poolName";
    public static final String COLUMN_THREAD_POOL_NAME = "threadPoolName";
    public static final String COLUMN_ROOT_RESOURCE = "rootResource";
    public static final String COLUMN_LOCAL_METHOD = "localMethod";
    public static final String COLUMN_DOMAIN = "domain";
    public static final String COLUMN_ISP = "isp";
    public static final String COLUMN_PROCESSED_PATH = "processedPath";
    public static final String COLUMN_REQUEST_TYPE = "requestType";
    public static final String COLUMN_STATUS_CODE = "statusCode";

    public static final String SHAKE_TYPE = "shakeType";

    public static final String CASE_TYPE_EXCEPTION = "Exception";
    public static final String CASE_TYPE_GC = "GC";
    public static final String CASE_TYPE_HTTP_CLIENT = "HTTP_CLIENT";
    public static final String CASE_TYPE_HTTP_CLIENT_EXECUTE = "HTTP_CLIENT_EXECUTE";
    public static final String CASE_TYPE_HTTP_SERVER = "HTTP_SERVER";
    public static final String CASE_TYPE_HTTP_SERVER_PAYLOAD = "HTTP_SERVER_PAYLOAD";
    public static final String CASE_TYPE_RPC_CLIENT = "RPC_CLIENT";
    public static final String CASE_TYPE_RPC_SERVER = "RPC_SERVER";
    public static final String CASE_TYPE_RPC_SERVER_PAYLOAD = "RPC_SERVER_PAYLOAD";
    public static final String CASE_TYPE_MQ_CLIENT = "MQ_CLIENT";
    public static final String CASE_TYPE_MQ_SERVER = "MQ_SERVER";
    public static final String CASE_TYPE_DB_CLIENT = "DB_CLIENT";
    public static final String CASE_TYPE_DB_CLIENT_ROWS = "DB_CLIENT_ROWS";
    public static final String CASE_TYPE_REDIS_CLIENT = "REDIS_CLIENT";
    public static final String CASE_TYPE_REDIS_CLIENT_PAYLOAD = "REDIS_CLIENT_PAYLOAD";
    public static final String CASE_TYPE_HOST = "Host";
    public static final String CASE_TYPE_SERVICE_INSTANCE_CPU = "SERVICE_INSTANCE_CPU";
    public static final String CASE_TYPE_SERVICE_INSTANCE_MEMORY = "SERVICE_INSTANCE_MEMORY";
    public static final String CASE_TYPE_SERVICE_INSTANCE = "SERVICE_INSTANCE";
    public static final String CASE_TYPE_DB_CONNECTION_POOL = "DB_CONNECTION_POOL";
    public static final String CASE_TYPE_OBJECT_POOL = "OBJECT_POOL";
    public static final String CASE_TYPE_HTTP_CONNECTION_POOL = "HTTP_CONNECTION_POOL";
    public static final String CASE_TYPE_ES_CLIENT = "ES_CLIENT";
    public static final String CASE_TYPE_CONFIG_CLIENT = "CONFIG_CLIENT";
    public static final String CASE_TYPE_THREAD_POOL_WAIT = "THREAD_POOL_WAIT";
    public static final String CASE_TYPE_THREAD_POOL_EXECUTE = "THREAD_POOL_EXECUTE";
    public static final String CASE_TYPE_LOCAL = "LOCAL";
    public static final String CASE_TYPE_RUM_WEB_REQUEST = "RUM_WEB_REQUEST";
    public static final String CASE_TYPE_RUM_WEB_REQUEST_SERVER = "RUM_WEB_REQUEST_SERVER";
    public static final String CASE_TYPE_RUM_WEB_REQUEST_NET = "RUM_WEB_REQUEST_NET";
    public static final String CASE_TYPE_RUM_WEB_JS_ERROR = "RUM_WEB_JS_ERROR";

    public static final Set<String> CASE_TYPE_SERVICE_IN_BASE = Sets.newHashSet(CASE_TYPE_HTTP_SERVER, CASE_TYPE_RPC_SERVER, CASE_TYPE_MQ_SERVER);
    public static final Set<String> CASE_TYPE_SERVICE_IN_PAYLOAD = Sets.newHashSet(CASE_TYPE_HTTP_SERVER_PAYLOAD, CASE_TYPE_RPC_SERVER_PAYLOAD);
    public static final Set<String> CASE_TYPE_SERVICE_GC = Sets.newHashSet(CASE_TYPE_GC);
    public static final Set<String> CASE_TYPE_SERVICE_RESOURCE = Sets.newHashSet(CASE_TYPE_SERVICE_INSTANCE_CPU, CASE_TYPE_SERVICE_INSTANCE_MEMORY);
    public static final String CASE_TYPE_POD_NET_RETRANSMITS = "POD_NET_RETRANSMITS";
    public static final String CASE_TYPE_POD_NET_RTT = "POD_NET_RTT";

    public static final String TYPE_SERVICE_HTTP = "service.http";
    public static final String TYPE_SERVICE_DB = "service.db";
    public static final String TYPE_SERVICE_REDIS = "service.redis";
    public static final String TYPE_SERVICE_RPC = "service.rpc";
    public static final String TYPE_SERVICE_MQ = "service.mq";
    public static final String TYPE_SERVICE_ES = "service.es";
    public static final String TYPE_SERVICE_CONFIG = "service.config";
    public static final String ST_WEB = "web";
    public static final String ST_DB = "db";
    public static final String ST_CACHE = "cache";
    public static final String ST_MQ = "mq";
    public static final String ST_CUSTOM = "custom";
    public static final String VIEW_TYPE_DB_CONN = "dbConn";
    public static final String VIEW_TYPE_OBJECT = "object";
    public static final String VIEW_TYPE_HTTP_CONN = "httpConn";
    public static final String METRIC_TYPE_POOL_GET = "poolGet";
    public static final String METRIC_TYPE_THREAD = "thread";


    public static final Map<String, Set<String>> COMPONENT_IN_CASE_TYPES = new HashMap<>();
    public static final Map<String, Set<String>> COMPONENT_OUT_CASE_TYPES = new HashMap<>();
    public static final Map<String, String> COMPONENT_OUT_RESOURCE_KEY = new HashMap<>();
    public static final Map<String, Set<String>> SERVICE_PAGE_CASE_TYPES = new HashMap<>();
    public static final Map<String, Set<String>> SERVICE_RESOURCE_PAGE_CASE_TYPES = new HashMap<>();

    static {
        COMPONENT_IN_CASE_TYPES.put(TYPE_SERVICE_HTTP, Sets.newHashSet(CASE_TYPE_HTTP_SERVER, CASE_TYPE_HTTP_SERVER_PAYLOAD));
        COMPONENT_IN_CASE_TYPES.put(TYPE_SERVICE_RPC, Sets.newHashSet(CASE_TYPE_RPC_SERVER, CASE_TYPE_RPC_SERVER_PAYLOAD));
        COMPONENT_IN_CASE_TYPES.put(TYPE_SERVICE_MQ, Sets.newHashSet(CASE_TYPE_MQ_SERVER));

        COMPONENT_OUT_CASE_TYPES.put(TYPE_SERVICE_DB, Sets.newHashSet(CASE_TYPE_DB_CLIENT));
        COMPONENT_OUT_CASE_TYPES.put(TYPE_SERVICE_MQ, Sets.newHashSet(CASE_TYPE_MQ_CLIENT));
        COMPONENT_OUT_CASE_TYPES.put(TYPE_SERVICE_REDIS, Sets.newHashSet(CASE_TYPE_REDIS_CLIENT));
        COMPONENT_OUT_CASE_TYPES.put(TYPE_SERVICE_ES, Sets.newHashSet(CASE_TYPE_ES_CLIENT));
        COMPONENT_OUT_CASE_TYPES.put(TYPE_SERVICE_CONFIG, Sets.newHashSet(CASE_TYPE_CONFIG_CLIENT));
        COMPONENT_OUT_CASE_TYPES.put(TYPE_SERVICE_HTTP, Sets.newHashSet(CASE_TYPE_HTTP_CLIENT));
        COMPONENT_OUT_CASE_TYPES.put(TYPE_SERVICE_RPC, Sets.newHashSet(CASE_TYPE_RPC_CLIENT));

        COMPONENT_OUT_RESOURCE_KEY.put(TYPE_SERVICE_DB, "sql");
        COMPONENT_OUT_RESOURCE_KEY.put(TYPE_SERVICE_MQ, "topic");
        COMPONENT_OUT_RESOURCE_KEY.put(TYPE_SERVICE_REDIS, "command");
        COMPONENT_OUT_RESOURCE_KEY.put(TYPE_SERVICE_ES, "indices");

        Set<String> baseCaseTypes = Sets.newHashSet(CASE_TYPE_HTTP_SERVER, CASE_TYPE_RPC_SERVER, CASE_TYPE_MQ_SERVER,
                CASE_TYPE_HTTP_SERVER_PAYLOAD, CASE_TYPE_RPC_SERVER_PAYLOAD);

        SERVICE_PAGE_CASE_TYPES.put("tab-baseinfo", baseCaseTypes);

        SERVICE_PAGE_CASE_TYPES.put("tab-resource", CASE_TYPE_SERVICE_RESOURCE);

        SERVICE_PAGE_CASE_TYPES.put("tab-jvm", CASE_TYPE_SERVICE_GC);

        SERVICE_RESOURCE_PAGE_CASE_TYPES.put("tab-baseinfo", baseCaseTypes);
    }

    public static final String PATH_SERVICE_ANALYSIS = "/databuff/appMonitor/serviceAnalysis?";
    public static final String PATH_SERVICE_CALL = "/databuff/appMonitor/serviceCall?";
    public static final String PATH_SERVICE_ERRORS = "/databuff/appMonitor/errors?";
    public static final String PATH_SERVICE_ERROR_DETAIL = "/databuff/appMonitor/errorDetail?";
    public static final String PATH_SERVICE_DETAIL = "/databuff/appMonitor/serviceDetail?";
    public static final String PATH_SERVICE_INSTANCE = "/databuff/appMonitor/serviceInstance?";
    public static final String PATH_SERVICE_THREAD_POOL = "/databuff/appMonitor/threadPool?";
    public static final String PATH_SERVICE_HOT_METHODS = "/databuff/appMonitor/hotMethods?";
    public static final String PATH_RESOURCE_DETAIL = "/databuff/appMonitor/resourceDetail?";
    public static final String PATH_RUM_DETAIL = "/databuff/rum/request?";
    public static final String PATH_RUM_JS_ERROR = "/databuff/rum/jsError?";
    public static final String PATH_NET = "/databuff/npm/analysis?";
    public static final String PARAMS_JOIN = "&";
    public static final String PARAMS_SN = "sn=";
    public static final String PARAMS_ST = "st=";
    public static final String PARAMS_SID = "sid=";
    public static final String PARAMS_TYPE = "tabType=";
    public static final String PARAMS_FROM_TIME = "fromTime=";
    public static final String PARAMS_TO_TIME = "toTime=";
    public static final String PARAMS_SI = "si=";
    public static final String PARAMS_RESOURCE = "resourceQuery=";
    public static final String PARAMS_COMPONENT_TYPE = "componentType=";
    public static final String PARAMS_SRC_SID = "srcSid=";
    public static final String PARAMS_SRC_SN = "srcSn=";
    public static final String PARAMS_SRC_ST = "srcSt=";
    public static final String PARAMS_SRC_SERVICE_INSTANCE = "srcServiceInstance=";
    public static final String PARAMS_SERVICE_INSTANCE = "serviceInstance=";
    public static final String PARAMS_SQL_OPERATION_QUERY = "sqlOperationQuery=";
    public static final String PARAMS_TOPIC_QUERY = "topicQuery=";
    public static final String PARAMS_BROKER_QUERY = "brokerQuery=";
    public static final String PARAMS_PARTITION_QUERY = "partitionQuery=";
    public static final String PARAMS_EXCEPTION = "exception=";
    public static final String PARAMS_POOL_NAME = "poolName=";
    public static final String PARAMS_VIEW_TYPE = "viewType=";
    public static final String PARAMS_ROOT_RESOURCE_QUERY = "rootResourceQuery=";
    public static final String PARAMS_METRIC_TYPE = "metricType=";
    public static final String PARAMS_PROFILING_RESOURCE = "resource=";
    public static final String PARAMS_PROFILING_HOT_METHOD = "hotMethod=";
    public static final String PARAMS_ACTIVE_NAME = "activeName=";
    public static final String PARAMS_ENDPOINT = "endpoint=";
    public static final String PARAMS_APP_ID = "appId=";
    public static final String PARAMS_DOMAIN = "domain=";
    public static final String PARAMS_ISP = "isp=";
    public static final String PARAMS_PROCESSED_PATH = "processedPath=";
    public static final String PARAMS_REQUEST_TYPE = "requestType=";
    public static final String PARAMS_STATUS_CODE = "statusCode=";

    public static final String STATUS_ANALYSE_IN_PROGRESS = "分析中";
    public static final String STATUS_ANALYSE_FAILED = "分析失败";
    public static final String STATUS_ANALYSE_SUCCESS = "分析成功";

    //service,host,business,k8s_cluster,k8s_namespace,k8s_workload
    public static class EntityType {
        public static final String SERVICE = "service";
        public static final String HOST = "host";
        public static final String BUSINESS = "business";
        public static final String K8S_NAMESPACE = "namespace";
    }

    public static final String DETAILS = "details";

    public static final Map<String, String> METRIC_CASE_TYPE_MAPPING = new HashMap<>();

    static {
        METRIC_CASE_TYPE_MAPPING.put("jvm.gc.minor_collection_count", CASE_TYPE_GC);
        METRIC_CASE_TYPE_MAPPING.put("jvm.gc.minor_collection_time", CASE_TYPE_GC);
        METRIC_CASE_TYPE_MAPPING.put("service.cpu.usage_pct", CASE_TYPE_SERVICE_INSTANCE_CPU);
        METRIC_CASE_TYPE_MAPPING.put("service.mem.usage_pct", CASE_TYPE_SERVICE_INSTANCE_MEMORY);
    }

    //动态配置下发路径
    public static final String JAVAAGENT_PATH = "/javaagent";

}
