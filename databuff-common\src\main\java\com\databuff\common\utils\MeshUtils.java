package com.databuff.common.utils;

/**
 * 动态计算mesh，databuff采集数据最小粒度为1分钟
 */
public class MeshUtils {

    /**
     * 动态计算查询粒度（秒），仅支持 60秒 或 600秒
     *
     * @param startMs     查询起始时间（毫秒）
     * @param endMs       查询结束时间（毫秒）
     * @param maxPoints   允许的最大点数（默认建议：500）
     * @return 60 或 600（秒）
     */
    public static int calculateInterval(long startMs, long endMs, int maxPoints) {
        long durationSec = (endMs - startMs) / 1000;
        long pointCountWith60s = durationSec / 60;
        // 如果1分钟粒度下，点数未超过最大限制，就用60s；否则返回10分钟 moredb只有60s和600s粒度
        return pointCountWith60s <= maxPoints ? 60 : 600;
    }

    /**
     * 使用默认最大点数500
     */
    public static int calculateInterval(long startMs, long endMs) {
        return calculateInterval(startMs, endMs, 500);
    }

}
