package com.databuff.dao.starrocks.profiling.v3;

import com.databuff.entity.profiling.v3.ProfilingSearchParamsV2;
import com.databuff.entity.profiling.v3.ProfilingHotspotMethodVO;
import com.databuff.entity.profiling.v3.ProfilingStackDTO;
import com.databuff.handler.StringArrayToListTypeHandler;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface ProfilingStackMapper {

    @Select({"<script>",
            "SELECT DISTINCT serviceInstance ",
            "FROM dc_profiling_stack ",
            "WHERE serviceId = #{serviceId} ",
            "AND createDt BETWEEN #{fromTime} AND #{toTime}",
            "</script>"
    })
    List<String> getBasicServiceInstance(@Param("serviceId") String serviceId,
                                         @Param("fromTime") String fromTime,
                                         @Param("toTime") String toTime);

    /**
     * 查询指定条件的 ProfilingStackDTO 列表
     *
     * @param params 查询参数，包括开始时间、结束时间、服务、主机、服务ID、服务实例、资源、线程ID等
     * @return ProfilingStackDTO 列表
     */
    @Select({"<script>",
            "SELECT ",
            "    dps.excerptId, ",
            "    dps.baseExcerptId, ",
            "    dps.service, ",
            "    dps.serviceId, ",
            "    dps.serviceInstance, ",
            "    dps.resource, ",
            "    dps.resourceType, ",
            "    dps.eventType, ",
            "    dps.onOperation, ",
            "    dps.rsFlagIndex, ",
            "    dps.stackTrace, ",
            "    dps.frameTypeIds, ",
            "    temp.samples, ",
            "    temp.instanceAlloc ",
            "FROM ",
            "    dc_profiling_stack dps ",
            "INNER JOIN ( ",
            "    SELECT ",
            "        excerptId, ",
            "        COUNT(1) AS samples, ",
            "        SUM(instanceAlloc) AS instanceAlloc ",
            "    FROM ",
            "        dc_profiling_hotspot dph ",
            "    WHERE ",
            "        (nanoTimestamp &gt;= #{params.startTimeNS} AND nanoTimestamp &lt;= #{params.endTimeNS}) ",
            "        AND (dph.time &gt;= #{params.startTime} AND dph.time &lt;= #{params.endTime}) ",
            "<if test='params.traceId != null'> AND traceId = #{params.traceId} </if>",
            "    GROUP BY excerptId ",
            ") AS temp ",
            "ON ",
            "    dps.excerptId = temp.excerptId ",
            "WHERE ",
            "    dps.excerptId IS NOT NULL ",
            "    AND temp.excerptId IS NOT NULL ",
            "    AND temp.samples &gt; 0 ",
            "    AND (dps.createDt &gt;= #{params.startDate} AND dps.createDt &lt;= #{params.endDate}) ",
            "<if test='params.service != null'> AND dps.service = #{params.service} </if>",
            "<if test='params.serviceId != null'> AND dps.serviceId = #{params.serviceId} </if>",
            "<if test='params.serviceInstance != null'> AND dps.serviceInstance = #{params.serviceInstance} </if>",
            "<if test='params.resource != null'> AND dps.resource LIKE CONCAT('%', #{params.resource}, '%') </if>",
            "<if test='params.eventType != null'> AND dps.eventType = #{params.eventType} </if>",
            "<if test='params.onOperation != null'> AND dps.onOperation = #{params.onOperation} </if>",
            "<if test='params.envTag1s != null and params.envTag1s.size() > 0'>",
            "    AND dps.envTag1 IN ",
            "    <foreach collection='params.envTag1s' item='envTag1' open='(' close=')' separator=','>",
            "        #{envTag1}",
            "    </foreach>",
            "</if>",
            "<if test='params.envTag2s != null and params.envTag2s.size() > 0'>",
            "    AND dps.envTag2 IN ",
            "    <foreach collection='params.envTag2s' item='envTag2' open='(' close=')' separator=','>",
            "        #{envTag2}",
            "    </foreach>",
            "</if>",
            "ORDER BY ",
            "    temp.samples DESC, ",
            "    dps.rsFlagIndex DESC",
            "</script>"
    })
    @Results({
            @Result(column = "stackTrace", property = "stackTrace", typeHandler = StringArrayToListTypeHandler.class)
    })
    List<ProfilingStackDTO> search(@Param("params") ProfilingSearchParamsV2 params);

    /**
     * 查询前5个热点方法
     *
     * @param params 查询参数，包括开始时间、结束时间、服务、主机、服务ID、服务实例、资源、线程ID等
     * @return ProfilingHotspotMethodVO 列表
     */
    @Select({"<script>",
            "SELECT ",
            "    dps.hotspotMethod, ",
            "    sum(temp.samples) as samples ",
            "FROM ",
            "    dc_profiling_stack dps ",
            "INNER JOIN ( ",
            "    SELECT ",
            "        excerptId, ",
            "        COUNt(1) AS samples ",
            "    FROM ",
            "        dc_profiling_hotspot dph ",
            "    WHERE ",
            "        (nanoTimestamp &gt;= #{params.startTimeNS} AND nanoTimestamp &lt;= #{params.endTimeNS}) ",
            "        AND (dph.time &gt;= #{params.startTime} AND dph.time &lt;= #{params.endTime}) ",
            "<if test='params.traceId != null'> AND traceId = #{params.traceId} </if>",
            "    GROUP BY ",
            "        excerptId ",
            ") AS temp ",
            "ON ",
            "    dps.excerptId = temp.excerptId ",
            "WHERE ",
            "    dps.excerptId IS NOT NULL ",
            "    AND temp.excerptId IS NOT NULL ",
            "    AND temp.samples &gt; 0 ",
            "    AND (dps.createDt &gt;= #{params.startDate} AND dps.createDt &lt;= #{params.endDate}) ",
            "<if test='params.service != null'> AND dps.service = #{params.service} </if>",
            "<if test='params.serviceId != null'> AND dps.serviceId = #{params.serviceId} </if>",
            "<if test='params.serviceInstance != null'> AND dps.serviceInstance = #{params.serviceInstance} </if>",
            "<if test='params.resource != null'> AND dps.resource LIKE CONCAT('%', #{params.resource}, '%') </if>",
            "<if test='params.envTag1s != null and params.envTag1s.size() > 0'>",
            "    AND dps.envTag1 IN ",
            "    <foreach collection='params.envTag1s' item='envTag1' open='(' close=')' separator=','>",
            "        #{envTag1}",
            "    </foreach>",
            "</if>",
            "<if test='params.envTag2s != null and params.envTag2s.size() > 0'>",
            "    AND dps.envTag2 IN ",
            "    <foreach collection='params.envTag2s' item='envTag2' open='(' close=')' separator=','>",
            "        #{envTag2}",
            "    </foreach>",
            "</if>",
            "GROUP BY dps.hotspotMethod ",
            "ORDER BY samples DESC LIMIT 0, 5 ",
            "</script>"
    })
    List<ProfilingHotspotMethodVO> top(@Param("params") ProfilingSearchParamsV2 params);

    /**
     * 检查是否存在符合条件的热点方法记录
     *
     * @param params 查询参数，包括开始时间、结束时间、资源、服务ID、服务实例、主机等
     * @return 如果存在记录则返回true，否则返回false
     */
    @Select({"<script>",
            "SELECT ",
            "    EXISTS (",
            "        SELECT 1 ",
            "        FROM ",
            "            dc_profiling_hotspot dph ",
            "        INNER JOIN ",
            "            dc_profiling_stack dps ",
            "        ON ",
            "            dph.excerptId = dps.excerptId ",
            "        WHERE ",
            "            (nanoTimestamp &gt;= #{params.startTimeNS} AND nanoTimestamp &lt;= #{params.endTimeNS}) ",
            "            AND (dph.time &gt;= #{params.startTime} AND dph.time &lt;= #{params.endTime}) ",
            "            AND (dps.createDt &gt;= #{params.startDate} AND dps.createDt &lt;= #{params.endDate}) ",
            "            AND serviceId = #{params.serviceId} ",
            "            AND dph.traceId = #{params.traceId} ",
            "    ) AS record_exists",
            "</script>"
    })
    boolean existsHotspotMethod(@Param("params") ProfilingSearchParamsV2 params);

    @Select({"<script>",
            "SELECT DISTINCT ${field} ",
            "FROM dc_profiling_stack ",
            "WHERE 1=1 AND ${field} IS NOT NULL ",
            "<if test='serviceId != null'> AND serviceId = #{serviceId} </if>",
            "<if test='fromTime != null'> AND createDt &gt;= #{fromTime} </if>",
            "<if test='toTime != null'> AND createDt &lt;= #{toTime} </if>",
            "</script>"
    })
    List<String> getTagsByField(@Param("field") String field,
                                @Param("serviceId") String serviceId,
                                @Param("fromTime") String fromTime,
                                @Param("toTime") String toTime);
}