package com.databuff.entity.dto;

import com.databuff.service.DomainManagerObjService;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Collection;
import java.util.Date;
import java.util.List;

@ApiModel(description = "查询参数")
@Data
public class CommonSearchParams {

    @ApiModelProperty(value = "域ID列表", example = "[40,34]")
    private List<String> gids;

    /**
     * 获取域ID列表
     * @return
     */
    public Collection<String> getGids() {
        if (gids == null || gids.isEmpty()) {
            return DomainManagerObjService.getGidFromThread();
        }
        return gids;
    }

    @ApiModelProperty(value = "apiKey", example = "NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4")
    private String apiKey;

    @ApiModelProperty(value = "关键词", example = "1")
    private String keyword;

    @ApiModelProperty(value = "是否启用", example = "true")
    private Boolean enabled;

    @ApiModelProperty(value = "排序字段", example = "updatedTime")
    protected String sortField;

    @ApiModelProperty(value = "排序方向", example = "DESC")
    protected Sort.Direction sortOrder = Sort.Direction.DESC;

    @ApiModelProperty(value = "页码", example = "1")
    protected Integer pageNum = 1;

    @ApiModelProperty(value = "分页数", example = "10")
    protected Integer pageSize = 10;

    @ApiModelProperty(value = "ID列表", example = "[1, 2, 3]")
    private List<Long> ids;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date fromTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date toTime;

    @ApiModelProperty(value = "间隔(秒)", example = "60")
    private Integer interval;

    public void setSortOrder(String sortOrder) {
        if (sortOrder != null) {
            this.sortOrder = Sort.Direction.valueOf(sortOrder.toUpperCase());
        }
    }
}