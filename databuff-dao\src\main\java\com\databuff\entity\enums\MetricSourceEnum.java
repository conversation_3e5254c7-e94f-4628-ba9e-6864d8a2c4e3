package com.databuff.entity.enums;

import lombok.Getter;

/**
 * 指标来源枚举
 * 预置/自定义/第三方平台等
 */
@Getter
public enum MetricSourceEnum {
    
    // 系统内置
    SYSTEM_BUILT_IN("SYSTEM_BUILT_IN", "系统内置"),  // 系统默认提供的指标
    
    // 用户自定义
    USER_DEFINED("USER_DEFINED", "用户自定义"),      // 用户通过界面或API自定义的指标
    USER_IMPORTED("USER_IMPORTED", "用户导入"),      // 用户通过导入方式添加的指标
    
    // 第三方平台
    THIRD_PARTY_CLOUD("THIRD_PARTY_CLOUD", "云服务商"),  // AWS, Azure, GCP等云服务商提供的指标
    THIRD_PARTY_APM("THIRD_PARTY_APM", "APM工具"),      // Datadog, New Relic等APM工具提供的指标
    THIRD_PARTY_DB("THIRD_PARTY_DB", "数据库监控"),      // 数据库监控工具提供的指标
    THIRD_PARTY_NETWORK("THIRD_PARTY_NETWORK", "网络监控"), // 网络监控工具提供的指标
    
    // 集成来源
    INTEGRATION_API("INTEGRATION_API", "API集成"),      // 通过API集成的指标
    INTEGRATION_AGENT("INTEGRATION_AGENT", "Agent集成"), // 通过Agent采集的指标
    INTEGRATION_LOG("INTEGRATION_LOG", "日志集成"),      // 从日志中提取的指标
    INTEGRATION_TRACE("INTEGRATION_TRACE", "链路集成"),  // 从分布式追踪中提取的指标
    
    // 社区贡献
    COMMUNITY_PLUGIN("COMMUNITY_PLUGIN", "社区插件"),    // 社区贡献的插件提供的指标
    COMMUNITY_TEMPLATE("COMMUNITY_TEMPLATE", "社区模板"), // 社区贡献的模板中的指标
    
    // 兼容旧版本的枚举值
    PRESET("PRESET", "预置"),
    CUSTOM("CUSTOM", "自定义"),
    THIRD_PARTY("THIRD_PARTY", "第三方平台");
    
    private final String code;
    private final String name;
    
    MetricSourceEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    /**
     * 根据编码获取枚举
     */
    public static MetricSourceEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (MetricSourceEnum source : values()) {
            if (source.getCode().equals(code)) {
                return source;
            }
        }
        return null;
    }
    
    /**
     * 根据名称获取枚举
     */
    public static MetricSourceEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        for (MetricSourceEnum source : values()) {
            if (source.getName().equals(name)) {
                return source;
            }
        }
        return null;
    }
}
