package com.databuff.common.tsdb.dto.detect;

import com.alibaba.fastjson.annotation.JSONField;
import com.databuff.common.tsdb.dto.explore.QueryRequest;
import com.databuff.common.tsdb.dto.preview.ThresholdsDTO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 查询请求数据传输对象，包含查询条件、时间范围及间隔信息。
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DetectQueryRequest {

    @ApiModelProperty(value = "是否拥有所有权限", example = "true")
    private Boolean allPermission;

    /**
     * 设置是否拥有所有权限，并同步到所有非空的查询请求中。
     *
     * @param allPermission 新的权限状态（可能为null）
     */
    public void setAllPermission(Boolean allPermission) {
        this.allPermission = allPermission;
        // 同步权限状态到所有非空查询请求
        for (QueryRequest query : this.getNonNullQueries()) {
            query.setAllPermission(allPermission);
        }
    }

    @ApiModelProperty(value = "是否开启领域管理状态", example = "false")
    private Boolean domainManagerStatusOpen;

    /**
     * 设置是否开启领域管理状态，并同步到所有非空的查询请求中。
     *
     * @param domainManagerStatusOpen 新的领域管理状态（可能为null）
     */
    public void setDomainManagerStatusOpen(Boolean domainManagerStatusOpen) {
        this.domainManagerStatusOpen = domainManagerStatusOpen;
        // 同步领域管理状态到所有非空查询请求
        for (QueryRequest query : this.getNonNullQueries()) {
            query.setDomainManagerStatusOpen(domainManagerStatusOpen);
        }
    }

    @ApiModelProperty(value = "组ID集合，用于过滤查询条件", example = "[\"group1\", \"group2\"]")
    private Collection<String> gids;

    /**
     * 设置组ID集合，并同步到所有非空的查询请求中。
     *
     * @param gids 新的组ID集合（可能为null）
     */
    public void setGids(Collection<String> gids) {
        this.gids = gids;
        // 同步组ID集合到所有非空查询请求
        for (QueryRequest query : this.getNonNullQueries()) {
            query.setGids(gids);
        }
    }

    @ApiModelProperty(value = "是否包含注释信息，默认为false", example = "true")
    private boolean hasAnnotate;

    /**
     * 设置是否包含注释信息，并同步到所有非空的查询请求中。
     *
     * @param hasAnnotate 新的注释状态
     */
    public void setHasAnnotate(boolean hasAnnotate) {
        this.hasAnnotate = hasAnnotate;
        // 同步注释状态到所有非空查询请求
        for (QueryRequest query : this.getNonNullQueries()) {
            query.setHasAnnotate(hasAnnotate);
        }
    }

    @ApiModelProperty(value = "API密钥，用于身份认证", example = "your_api_key_123")
    private String apiKey;

    /**
     * 设置API密钥，并同步到所有非空的查询请求中。
     *
     * @param apiKey 新的API密钥（可能为null）
     */
    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
        // 同步API密钥到所有非空查询请求
        for (QueryRequest query : this.getNonNullQueries()) {
            query.setApiKey(apiKey);
        }
    }

    @ApiModelProperty(value = "起始时间戳，单位为毫秒", example = "1609459200000")
    private Long start;

    /**
     * 设置起始值，并同步到所有非空的查询请求中。
     *
     * @param start 要设置的起始值
     */
    public void setStart(Long start) {
        this.start = start;
        if (start == null) {
            return;
        }
        // 同步起始值到所有非空查询请求
        for (QueryRequest nonNullQuery : this.getNonNullQueries()) {
            nonNullQuery.setStart(start);
        }
    }

    @ApiModelProperty(value = "结束时间戳，单位为毫秒", example = "1609462800000")
    private Long end;

    /**
     * 设置结束时间并同步更新所有非空查询请求的结束时间。
     * @param end 新的结束时间值（可能为null）
     */
    public void setEnd(Long end) {
        this.end = end;
        if (end == null) {
            return;
        }
        // 同步更新所有非空查询请求的结束时间
        for (QueryRequest nonNullQuery : this.getNonNullQueries()) {
            nonNullQuery.setEnd(end);
        }
    }

    @ApiModelProperty(value = "查询数据的时间间隔，单位为秒", example = "60")
    private Integer interval;

    /**
     * 设置查询时间间隔（单位为秒），并同步到所有非空的查询请求中。
     *
     * @param interval 新的时间间隔值（单位为秒，可能为null）
     */
    public void setInterval(Integer interval) {
        this.interval = interval;
        if (interval == null) {
            return;
        }
        // 同步时间间隔到所有非空查询请求（转换为Long类型）
        for (QueryRequest query : this.getNonNullQueries()) {
            query.setInterval(interval);
        }
    }

    @ApiModelProperty(value = "最近时间区间，单位为秒", example = "3600")
    private Integer period;
    /**
     * 设置最近时间区间（单位为秒），并同步到所有非空的查询请求中。
     *
     * @param period 新的时间区间值（可能为null）
     */
    public void setPeriod(Integer period) {
        this.period = period;
        // 同步最近时间区间到所有非空查询请求
        for (QueryRequest query : this.getNonNullQueries()) {
            query.setPeriod(period);
        }
    }

    @ApiModelProperty(value = "检测方式", example = "threshold/baseline...")
    private String way;


    @ApiModelProperty(value = "动态基线", example = "1")
    @JsonProperty("baselineScope")
    @JSONField(name = "baselineScope")
    private double baselineScope;

    @ApiModelProperty(value = "任务触发时间(毫秒)", example = "60")
    private long triggerTime;

    @ApiModelProperty(value = "波动检测专用，同比于n分钟前", example = "60")
    @JsonProperty("comparePeriod")
    @JSONField(name = "comparePeriod")
    private Integer comparePeriod;

    @ApiModelProperty(value = "波动检测专用，同比于n分钟前，数值增加量", example = "数值增加量/数值减少量")
    private String fluctuate;

    @ApiModelProperty(value = "无数据时间阈值（分钟）", example = "12")
    @JsonProperty("no_data_timeframe")
    @JSONField(name = "no_data_timeframe")
    private Integer noDataTimeframe;

    /**
     * 获取无数据时间范围的值。如果内部存储的noDataTimeframe为null，则返回默认值0，否则返回其实际存储值。
     *
     * @return 无数据时间范围值（null时返回0）
     */
    public Integer getNoDataTimeframe() {
        if (noDataTimeframe == null) {
            return 0;
        }
        return noDataTimeframe;
    }


    @ApiModelProperty(value = "比较符（>、<、>=、<=）", example = ">")
    private String comparison;

    @ApiModelProperty(value = "时间聚合方式（avg/sum/max/min）", example = "avg")
    @JsonProperty("time_aggregator")
    @JSONField(name = "time_aggregator")
    private String timeAggregator;

    @ApiModelProperty(value = "延迟评估时间（秒）", example = "0")
    @JsonProperty("evaluation_delay")
    @JSONField(name = "evaluation_delay")
    private Integer evaluationDelay;

    /**
     * 获取评估延迟时间，若未设置则返回默认值0。
     *
     * @return 评估延迟时间（若未设置则返回0）
     */
    public Integer getEvaluationDelay() {
        // 如果评估延迟未设置，则返回默认值0
        if (evaluationDelay == null) {
            return 0;
        }
        return evaluationDelay;
    }


    @ApiModelProperty(value = "连续周期数", example = "3")
    @JsonProperty("continuous_n")
    @JSONField(name = "continuous_n")
    private Integer continuousN;

    @ApiModelProperty(value = "底层单位（如percent表示百分比）", example = "percent")
    private String unit;

    @ApiModelProperty(value = "显示单位", example = "%")
    @JsonProperty("view_unit")
    @JSONField(name = "view_unit")
    private String viewUnit;

    @JsonProperty("unit")
    @JSONField(name = "unit")
    public void setUnit(String unit) {
        this.unit = unit;
        if (thresholds == null) {
            return;
        }
        thresholds.setUnit(unit);
    }

    @ApiModelProperty(value = "阈值配置", required = true)
    private ThresholdsDTO thresholds;

    @ApiModelProperty(value = "突变检测，指标数据低于30%时，生成跳过事件", example = "true")
    @JsonProperty("less_data_timeframe")
    @JSONField(name = "less_data_timeframe")
    private Boolean lessDataTimeframe;

    @ApiModelProperty(value = "单位换算比例（前端用）", example = "0.01")
    @JsonProperty("_scale")
    @JSONField(name = "_scale")
    private Double scale;

    @ApiModelProperty(value = "是否启用连续检测", example = "true")
    private Boolean continuous;

    @ApiModelProperty(value = "是否需要完整窗口数据", example = "false")
    @JsonProperty("require_full_window")
    @JSONField(name = "require_full_window")
    private Boolean requireFullWindow;

    @ApiModelProperty(value = "查询表达式对象A", example = "{\"metric\":\"cpu_usage\"}")
    @JsonProperty("A")
    @JSONField(name = "A")
    private QueryRequest A;

    @ApiModelProperty(value = "查询表达式对象B", example = "{\"metric\":\"memory_usage\"}")
    @JsonProperty("B")
    @JSONField(name = "B")
    private QueryRequest B;

    @ApiModelProperty(value = "查询表达式对象C", example = "{\"metric\":\"disk_io\"}")
    @JsonProperty("C")
    @JSONField(name = "C")
    private QueryRequest C;

    @ApiModelProperty(value = "查询表达式对象D", example = "{\"metric\":\"network_traffic\"}")
    @JsonProperty("D")
    @JSONField(name = "D")
    private QueryRequest D;

    @ApiModelProperty(value = "查询表达式对象E", example = "{\"metric\":\"system_load\"}")
    @JsonProperty("E")
    @JSONField(name = "E")
    private QueryRequest E;

    @ApiModelProperty(value = "查询表达式对象F", example = "{\"metric\":\"custom_metric_f\"}")
    @JsonProperty("F")
    @JSONField(name = "F")
    private QueryRequest F;

    @ApiModelProperty(value = "查询表达式对象G", example = "{\"metric\":\"custom_metric_g\"}")
    @JsonProperty("G")
    @JSONField(name = "G")
    private QueryRequest G;

    @ApiModelProperty(value = "查询表达式对象H", example = "{\"metric\":\"custom_metric_h\"}")
    @JsonProperty("H")
    @JSONField(name = "H")
    private QueryRequest H;

    @ApiModelProperty(value = "查询表达式对象I", example = "{\"metric\":\"custom_metric_i\"}")
    @JsonProperty("I")
    @JSONField(name = "I")
    private QueryRequest I;

    @ApiModelProperty(value = "查询表达式对象J", example = "{\"metric\":\"custom_metric_j\"}")
    @JsonProperty("J")
    @JSONField(name = "J")
    private QueryRequest J;

    @ApiModelProperty(value = "查询表达式对象K", example = "{\"metric\":\"custom_metric_k\"}")
    @JsonProperty("K")
    @JSONField(name = "K")
    private QueryRequest K;

    @ApiModelProperty(value = "查询表达式对象L", example = "{\"metric\":\"custom_metric_l\"}")
    @JsonProperty("L")
    @JSONField(name = "L")
    private QueryRequest L;

    @ApiModelProperty(value = "查询表达式对象M", example = "{\"metric\":\"custom_metric_m\"}")
    @JsonProperty("M")
    @JSONField(name = "M")
    private QueryRequest M;

    @ApiModelProperty(value = "查询表达式对象N", example = "{\"metric\":\"custom_metric_n\"}")
    @JsonProperty("N")
    @JSONField(name = "N")
    private QueryRequest N;

    @ApiModelProperty(value = "查询表达式对象O", example = "{\"metric\":\"custom_metric_o\"}")
    @JsonProperty("O")
    @JSONField(name = "O")
    private QueryRequest O;

    @ApiModelProperty(value = "查询表达式对象P", example = "{\"metric\":\"custom_metric_p\"}")
    @JsonProperty("P")
    @JSONField(name = "P")
    private QueryRequest P;

    @ApiModelProperty(value = "查询表达式对象Q", example = "{\"metric\":\"custom_metric_q\"}")
    @JsonProperty("Q")
    @JSONField(name = "Q")
    private QueryRequest Q;

    @ApiModelProperty(value = "查询表达式对象R", example = "{\"metric\":\"custom_metric_r\"}")
    @JsonProperty("R")
    @JSONField(name = "R")
    private QueryRequest R;

    @ApiModelProperty(value = "查询表达式对象S", example = "{\"metric\":\"custom_metric_s\"}")
    @JsonProperty("S")
    @JSONField(name = "S")
    private QueryRequest S;

    @ApiModelProperty(value = "查询表达式对象T", example = "{\"metric\":\"custom_metric_t\"}")
    @JsonProperty("T")
    @JSONField(name = "T")
    private QueryRequest T;

    @ApiModelProperty(value = "查询表达式对象U", example = "{\"metric\":\"custom_metric_u\"}")
    @JsonProperty("U")
    @JSONField(name = "U")
    private QueryRequest U;

    @ApiModelProperty(value = "查询表达式对象V", example = "{\"metric\":\"custom_metric_v\"}")
    @JsonProperty("V")
    @JSONField(name = "V")
    private QueryRequest V;

    @ApiModelProperty(value = "查询表达式对象W", example = "{\"metric\":\"custom_metric_w\"}")
    @JsonProperty("W")
    @JSONField(name = "W")
    private QueryRequest W;

    @ApiModelProperty(value = "查询表达式对象X", example = "{\"metric\":\"custom_metric_x\"}")
    @JsonProperty("X")
    @JSONField(name = "X")
    private QueryRequest X;

    @ApiModelProperty(value = "查询表达式对象Y", example = "{\"metric\":\"custom_metric_y\"}")
    @JsonProperty("Y")
    @JSONField(name = "Y")
    private QueryRequest Y;

    @ApiModelProperty(value = "查询表达式对象Z", example = "{\"metric\":\"custom_metric_z\"}")
    @JsonProperty("Z")
    @JSONField(name = "Z")
    private QueryRequest Z;

    @ApiModelProperty(value = "查询表达式字符串（如PromQL语法）", example = "a + b + c")
    private String expr;

    @ApiModelProperty(value = "表达式名称", example = "自定义指标名")
    private String exprName;

    /**
     * 获取所有非空的 A-Z 查询对象列表。
     */
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    public Collection<QueryRequest> getNonNullQueries() {
        Collection<QueryRequest> nonNullList = new ArrayList<>();
        if (A != null) nonNullList.add(A);
        if (B != null) nonNullList.add(B);
        if (C != null) nonNullList.add(C);
        if (D != null) nonNullList.add(D);
        if (E != null) nonNullList.add(E);
        if (F != null) nonNullList.add(F);
        if (G != null) nonNullList.add(G);
        if (H != null) nonNullList.add(H);
        if (I != null) nonNullList.add(I);
        if (J != null) nonNullList.add(J);
        if (K != null) nonNullList.add(K);
        if (L != null) nonNullList.add(L);
        if (M != null) nonNullList.add(M);
        if (N != null) nonNullList.add(N);
        if (O != null) nonNullList.add(O);
        if (P != null) nonNullList.add(P);
        if (Q != null) nonNullList.add(Q);
        if (R != null) nonNullList.add(R);
        if (S != null) nonNullList.add(S);
        if (T != null) nonNullList.add(T);
        if (U != null) nonNullList.add(U);
        if (V != null) nonNullList.add(V);
        if (W != null) nonNullList.add(W);
        if (X != null) nonNullList.add(X);
        if (Y != null) nonNullList.add(Y);
        if (Z != null) nonNullList.add(Z);
        return nonNullList;
    }

    /**
     * 获取包含非空DetectQueryRequest对象的映射表。
     * 该方法遍历A到Z的DetectQueryRequest对象，将非空的对象按字母键存入Map中。
     * 每个键对应如下：
     * "A" -> A
     * "B" -> B
     * ...
     * "Z" -> Z
     *
     * @return 包含非空请求对象的Map，键为字母字符串，值为对应的QueryRequest实例
     */
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    public Map<String, QueryRequest> getNonNullQueriesMap() {
        Map<String, QueryRequest> map = new HashMap<>();
        if (A != null) map.put("A", A);
        if (B != null) map.put("B", B);
        if (C != null) map.put("C", C);
        if (D != null) map.put("D", D);
        if (E != null) map.put("E", E);
        if (F != null) map.put("F", F);
        if (G != null) map.put("G", G);
        if (H != null) map.put("H", H);
        if (I != null) map.put("I", I);
        if (J != null) map.put("J", J);
        if (K != null) map.put("K", K);
        if (L != null) map.put("L", L);
        if (M != null) map.put("M", M);
        if (N != null) map.put("N", N);
        if (O != null) map.put("O", O);
        if (P != null) map.put("P", P);
        if (Q != null) map.put("Q", Q);
        if (R != null) map.put("R", R);
        if (S != null) map.put("S", S);
        if (T != null) map.put("T", T);
        if (U != null) map.put("U", U);
        if (V != null) map.put("V", V);
        if (W != null) map.put("W", W);
        if (X != null) map.put("X", X);
        if (Y != null) map.put("Y", Y);
        if (Z != null) map.put("Z", Z);
        return map;
    }

    /**
     * 收集所有非空查询请求中的查询条件并返回结果集合。
     *
     * @return 包含所有查询条件字符串的集合
     */
    public Collection<String> findBy() {
        Collection<String> result = new HashSet<>();
        for (QueryRequest queryRequest : this.getNonNullQueries()) {
            result.addAll(queryRequest.getBy());
        }
        return result;
    }

    /**
     * 收集所有检测查询请求中定义的指标名称。
     * <p>
     * 该方法遍历所有非空的检测查询请求及其嵌套的查询请求，提取每个查询请求中的指标名称，
     * 并返回所有唯一指标名称的集合。
     *
     * @return 包含所有唯一指标名称的集合
     */
    public Collection<String> findMetrics() {
        Collection<QueryRequest> queries = this.getNonNullQueries();
        if (queries == null) {
            return new HashSet<>();
        }
        return queries.stream()
                .filter(Objects::nonNull)
                .map(QueryRequest::getMetric)
                .filter(Objects::nonNull)
                .collect(Collectors.toCollection(HashSet::new));
    }

    public String findFirstMetric() {
        Collection<QueryRequest> queries = this.getNonNullQueries();
        if (queries == null) {
            return null;
        }
        return queries.stream()
                .filter(Objects::nonNull)
                .map(QueryRequest::getMetric)
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
    }

    /**
     * 查询表达式字符串，用于高级查询条件（如PromQL语法）。
     * 比如：a+b+c+d+e
     */
    public String getExpr() {
        return this.expr;
    }

    public void setExpr(String expr) {
        this.expr = expr;
    }


    /**
     * 【新增的核心方法】
     * 准备所有的子查询请求，确保它们继承了顶层的公共参数。
     * 这个方法应该在执行任何查询之前被调用。
     */
    public void prepareChildQueries() {
        // 获取所有非空的子查询请求
        Collection<QueryRequest> queries = this.getNonNullQueries();
        if (queries.isEmpty()) {
            return;
        }

        // 将顶层参数强制同步到每个子查询中
        for (QueryRequest query : queries) {
            // 如果子查询没有自己的值，就使用顶层的值
            if (query.getStart() == null && this.start != null) {
                query.setStart(this.start);
            }
            if (query.getEnd() == null && this.end != null) {
                query.setEnd(this.end);
            }
            if (query.getInterval() == null && this.interval != null) {
                query.setInterval(this.interval);
            }
            if (query.getPeriod() == null && this.period != null) {
                query.setPeriod(this.period);
            }
            if (query.getApiKey() == null && this.apiKey != null) {
                query.setApiKey(this.apiKey);
            }
            if (query.getAllPermission() == null && this.allPermission != null) {
                query.setAllPermission(this.allPermission);
            }
            if (query.getDomainManagerStatusOpen() == null && this.domainManagerStatusOpen != null) {
                query.setDomainManagerStatusOpen(this.domainManagerStatusOpen);
            }
            if (query.getGids() == null && this.gids != null) {
                query.setGids(this.gids);
            }
            if (!query.isHasAnnotate() && this.hasAnnotate) {
                query.setHasAnnotate(this.hasAnnotate);
            }
        }
    }

    public void syncTopLevelParams(Long start, Long end, Integer interval) {
        // 如果自身的字段是null，就使用上层传来的值
        if (this.start == null && start != null) {
            this.start = start;
        }
        if (this.end == null && end != null) {
            this.end = end;
        }
        if (this.interval == null && interval != null) {
            this.interval = interval;
        }
        // 调用自己的准备方法，将参数继续传递下去
        this.prepareChildQueries();
    }
}
