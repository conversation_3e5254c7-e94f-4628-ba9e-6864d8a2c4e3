package com.databuff.entity.page;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分页结果
 * @param <T> 数据类型
 */
@Data
public class PagedResult<T,K>  {
    @ApiModelProperty(value = "数据")
    private T data;

    @ApiModelProperty(value = "最后的键,用于回传翻页")
    private K lastKey;

    @ApiModelProperty(value = "页面大小")
    private int pageSize = 10;

    @ApiModelProperty(value = "总数")
    private long total;
}