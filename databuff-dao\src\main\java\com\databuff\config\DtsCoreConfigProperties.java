package com.databuff.config; // Or your chosen package for config properties

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "dts.config.amnesty")
@Getter
@Setter
public class DtsCoreConfigProperties {

    /**
     * Redis特赦标记在目标日期的次日几点过期 (e.g., 2 for 2 AM).
     */
    private int flagExpireAtNextDayHour = 2;

    /**
     * 在 {@link #flagExpireAtNextDayHour} 基础上增加的安全余量秒数 for flag TTL.
     */
    private int flagExpireAtSafetyMarginSeconds = 3600; // 1 hour ,只要dts集群没挂那么久就正常

    /**
     * Redis DEL 操作（如删除 dayFailKey 或特赦标记）的最大重试次数.
     */
    private int redisDelRetryCount = 3;

    /**
     * Redis DEL 操作的重试间隔毫秒数.
     */
    private long redisDelRetryIntervalMs = 200;

    /**
     * LocalCacheService.evictEntriesForApp 执行耗时的警告阈值（毫秒）.
     */
    private long cacheEvictionWarningThresholdMs = 500;

    /**
     * (可选, 如果采用受限清理) LocalCacheService 单次调用 evictEntriesForApp 时，
     * 通过 removeIf 清理的最大条目数限制.
     * 如果为0或负数，则表示不限制（清理所有匹配项）.
     */
    private int cacheEvictionLimitPerCall = 0; // Default to no limit

}