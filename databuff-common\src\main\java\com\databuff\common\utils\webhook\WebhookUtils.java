package com.databuff.common.utils.webhook;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.databuff.common.constants.Constant;
import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import com.sun.net.httpserver.HttpServer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.net.InetSocketAddress;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.Map;
import java.util.Scanner;

@Slf4j
@Component
public class WebhookUtils {


    public static void main(String[] args) {

//        String webhookUrl = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d0e4c19b-716f-4bdd-b216-ebaedc41981a";
//        String webhookMethod = "POST";
//        String webhookHeaders = "[\"key:d0e4c19b-716f-4bdd-b216-ebaedc41981a\"]";
//        String webhookHeaders = "[\"Authorization:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************.rnuWRsv7_7ezeSsg2hq7SLkt9fck2vhqIGJbX7kF-UE\", \"cid:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************.qyu3Z0jkSWZ2MmqmEBHZv3zVi0BH8Wc4vE1CHoI_SWE\", \"Content-Type:application/json\"]";
//        String content = "测试测试测试";

        String webhookUrl = "http://**************:18880/webhook";
        String webhookMethod = "POST";
        String webhookHeaders = "[\"Authorization:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************.X72VLLHPymjEbP02kfW8ZNa8iXFXokjIiOtBGbv4vb4\", \"cid:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************.bpttbXQfFvXICjiXzOC8cX8lgFvkbYDzjRYcnYyDO0I\"]";


        JSONObject testObj = new JSONObject();
        testObj.put("msgtype", "text");
        JSONObject textContent = new JSONObject();
        textContent.put("content", "测试内容");
        testObj.put("text", textContent);


        Map<String,String> params = new HashMap<>();
        params.put("esid", "dc_monitor_event_20230621@_doc@f394be09dd5c43299M83");
        params.put("apiKey", "NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4");
        params.put("group", "host:host162");
        params.put("lastTragerTime", "**********");
        params.put("firstTragerTime", "**********");
        params.put("triggerTime", "**********");

        sendWebhookRequests(webhookUrl, webhookMethod, webhookHeaders, testObj.toJSONString(), params);
    }

    public static String sendWebhookRequests(String webhookUrl, String webhookMethod, String webhookHeaders, String content) {
        return sendWebhookRequests(webhookUrl,webhookMethod,webhookHeaders,content,null);
    }

    public static String sendWebhookRequests(String webhookUrl, String webhookMethod, String webhookHeaders, String content, Map<String, String> params) {
        URIBuilder uri;
        try {
            uri = new URIBuilder(webhookUrl);
        } catch (URISyntaxException e) {
            if (e.getMessage()==null){
                log.error("webhook uri转化错误", e);
                return "webhook uri转化错误";
            } else {
                log.error("webhook uri转化错误: {}", e.getMessage(), e);
                return "webhook uri转化错误,"+e.getMessage();
            }
        }
        JSONArray webhookHeadersArr = JSONArray.parseArray(webhookHeaders);
        try (
                CloseableHttpClient closeableHttpClient = HttpClients.createDefault();
                ){
            switch (webhookMethod) {
                case "POST": {
                    HttpPost httpPost = new HttpPost(uri.build());
                    for (int i=0; i<webhookHeadersArr.size(); i++) {
                        String kvpair = (String)webhookHeadersArr.get(i);
                        String[] kvPairs = kvpair.split(":");
                        NameValuePair nameValuePair = new NameValuePair() {
                            @Override
                            public String getName() {
                                return kvPairs[0];
                            }

                            @Override
                            public String getValue() {
                                return kvPairs[1];
                            }
                        };

                        httpPost.addHeader(nameValuePair.getName(), nameValuePair.getValue());
                    }
                    httpPost.addHeader(Constant.CONTENT_TYPE, "application/json;charset=utf-8");

                    if (!MapUtils.isEmpty(params)) {
                        for (Map.Entry<String, String> entry : params.entrySet()) {
                            if (entry == null) {
                                continue;
                            }
                            httpPost.addHeader(entry.getKey(), entry.getValue());
                        }
                    }

                    StringEntity alertStringEntity = new StringEntity(content, "UTF-8");
                    httpPost.setEntity(alertStringEntity);
                    CloseableHttpResponse httpResponse = closeableHttpClient.execute(httpPost);
                    HttpEntity entity = httpResponse.getEntity();
                    if(httpResponse.getStatusLine().getStatusCode()==200){
                        return "ok";
                    }
                    InputStream inputStream = entity.getContent();
                    int available = inputStream.available();

                    byte[] avail = new byte[available];
                    inputStream.read(avail);

                    return new String(avail, "UTF-8");
                }

                case "GET": {

                    if (!MapUtils.isEmpty(params)) {
                        for (Map.Entry<String, String> entry : params.entrySet()) {
                            if (entry == null) {
                                continue;
                            }
                            uri.addParameter(entry.getKey(), entry.getValue());
                        }
                    }

                    HttpGet httpGet = new HttpGet(uri.build());

                    for (int i=0; i<webhookHeadersArr.size(); i++) {
                        String kvpair = (String)webhookHeadersArr.get(i);
                        String[] kvPairs = kvpair.split(":");
                        NameValuePair nameValuePair = new NameValuePair() {
                            @Override
                            public String getName() {
                                return kvPairs[0];
                            }

                            @Override
                            public String getValue() {
                                return kvPairs[1];
                            }
                        };

                        httpGet.addHeader(nameValuePair.getName(), nameValuePair.getValue());
                    }

                    CloseableHttpResponse httpResponse = closeableHttpClient.execute(httpGet);
                    HttpEntity entity = httpResponse.getEntity();
                    if(httpResponse.getStatusLine().getStatusCode()==200){
                        return "ok";
                    }
                    InputStream inputStream = entity.getContent();
                    int available = inputStream.available();

                    byte[] avail = new byte[available];
                    inputStream.read(avail);

                    return new String(avail, "UTF-8");
                }

                case "PUT": {
                    HttpPut httpPut = new HttpPut(uri.build());

                    for (int i=0; i<webhookHeadersArr.size(); i++) {
                        String kvpair = (String)webhookHeadersArr.get(i);
                        String[] kvPairs = kvpair.split(":");
                        NameValuePair nameValuePair = new NameValuePair() {
                            @Override
                            public String getName() {
                                return kvPairs[0];
                            }

                            @Override
                            public String getValue() {
                                return kvPairs[1];
                            }
                        };

                        httpPut.addHeader(nameValuePair.getName(), nameValuePair.getValue());
                    }
                        CloseableHttpResponse httpResponse = closeableHttpClient.execute(httpPut);
                        HttpEntity entity = httpResponse.getEntity();
                        if(httpResponse.getStatusLine().getStatusCode()==200){
                            return "ok";
                        }
                        InputStream inputStream = entity.getContent();
                        int available = inputStream.available();

                        byte[] avail = new byte[available];
                        inputStream.read(avail);

                    return new String(avail, "UTF-8");
                }
            }
        } catch (Exception e) {
            if (e.getMessage()==null){
                log.error("webhook发送错误", e);
                return "webhook发送错误";
            } else {
                log.error("webhook发送错误: {}", e.getMessage(), e);
                return "webhook发送错误,"+e.getMessage();
            }
        }
        return "ok";
    }



    public static class WebhookHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            String requestMethod = exchange.getRequestMethod();
            if (requestMethod.equalsIgnoreCase("POST")) { // 只处理 POST 请求
                InputStream requestBody = exchange.getRequestBody();
                Scanner scanner = new Scanner(requestBody, "UTF-8").useDelimiter("\\A");
                String payload = scanner.hasNext() ? scanner.next() : "";
                System.out.println("收到 Webhook 消息：" + payload);
                exchange.sendResponseHeaders(200, 0); // 返回 200 OK
            } else {
                exchange.sendResponseHeaders(405, 0); // 返回 405 Method Not Allowed
            }
            exchange.close();
        }
    }

    //用于测试webhook接收
//    @PostConstruct
    public void start() throws IOException {
        log.info("persistence timer start");
        int port = 18880; // 监听的端口号
        HttpServer server = HttpServer.create(new InetSocketAddress(port), 0);
        server.createContext("/webhook", new WebhookHandler()); // 设置 Webhook 的路径和处理程序
        server.setExecutor(null); // 使用默认的线程池
        server.start(); // 启动服务器
        System.out.println("Webhook 接收程序已启动，监听端口：" + port);
    }
}
