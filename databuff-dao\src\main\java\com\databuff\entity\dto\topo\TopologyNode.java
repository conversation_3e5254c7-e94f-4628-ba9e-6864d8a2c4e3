package com.databuff.entity.dto.topo;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.util.Date;

/**
 * 拓扑节点数据访问对象（DAO）。
 * 该类用于表示拓扑图中的节点，并包含节点的详细信息。
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(description = "拓扑节点实体")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Table(name = "topology_node")
public class TopologyNode {

    @ApiModelProperty(value = "apiKey", example = "your-api-key")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    private String apiKey;
    /**
     * 节点的唯一标识符。
     */
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    private String id;

    /**
     * 节点的名称。
     */
    @TableField(value = "`name`")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    private String name;

    /**
     * 节点所属的组的唯一标识符。
     */
    @TableField(value = "`group_id`")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    private String groupId;

    /**
     * 节点的类型。
     */
    @TableField(value = "`node_type`")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    private String nodeType;

    /**
     * 节点的描述信息。
     */
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    private String description;

    /**
     * 上游节点的集合，表示与当前节点直接相连的上游节点的ID。
     */
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    private JSONArray upstreamNodes;

    /**
     * 下游节点的集合，表示与当前节点直接相连的下游节点的ID。
     */
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    private JSONArray downstreamNodes;

    /**
     * 与节点相关的度量元数据列表。
     */
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    private JSONArray metricMetaData;

    /**
     * 节点的创建时间。
     */
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    private Date createTime;

    /**
     * 节点的更新时间。
     */
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    private Date updateTime;
}