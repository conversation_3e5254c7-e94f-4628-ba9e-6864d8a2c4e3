package com.databuff.dao.starrocks;

import com.databuff.entity.BaseSearch;
import com.databuff.entity.Container;
import com.databuff.entity.ProcessEntity;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author:TianMing
 * @date: 2024/1/23
 * @time: 14:41
 */
@Mapper
@Repository
public interface InfrastructureSrMapper extends DataCleanupMapper {


    Long containerCount(BaseSearch search);

    Long processCount(BaseSearch search);


    List<ProcessEntity> processNames(BaseSearch search);

    List<ProcessEntity> processList(BaseSearch search);

    ProcessEntity getProcessInfo(BaseSearch search);

    Container getContainerInfo(BaseSearch search);

    List<Container> containerNames(BaseSearch search);

    List<Container> containerList(BaseSearch search);

    List<String> processDistinctTags(BaseSearch search);

    List<String> containersDistinctTags(BaseSearch search);

}
