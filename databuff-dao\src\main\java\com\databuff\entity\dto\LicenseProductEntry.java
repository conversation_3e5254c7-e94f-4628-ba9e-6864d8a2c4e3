package com.databuff.entity.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 授权产品
 *
 * @company: dacheng
 * @Author: zlh
 * @CreateDate: 2020/6/2
 */
@Getter
@Setter
public class LicenseProductEntry {
    private Integer id;
    //产品名称
    private String licenseProductName;
    //产品版本号
    private String licenseProductVersion;
    //序列号
    private String licenseProductSerialnum;
    //0未授权 1已授权
    private Integer licenseProductStatus;
    //授权开始时间
    private Long licenseProductStarttime;
    /**
     * 有效期截止时间
     */
    private Long licenseProductEndtime;
    /**
     * 提醒有效授权时间
     */
    private Long remindTime;
    //授权文件路径
    private String licenseProductPath;
    //授权model
    private List<LicenseModelEntry> modelEntryList;
    private Date createTime;
    private Date updateTime;
    //产品版本号
    private String productVersion;

    //到期限制，默认1，如果为0则平台不过期，只是用基本功能
    private Integer expireLimit = 1;
}
