package com.databuff.dao.mysql;

import com.alibaba.fastjson.JSONObject;
import com.databuff.entity.DatabuffMetricTypeDto;
import com.databuff.entity.MetricsCore;
import com.databuff.typehandler.AggregationMethodTypeHandler;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

@Mapper
public interface MetricsCoreMapper {
    // 根据id查询指标
    @Select("SELECT * FROM dc_databuff_metrics_core WHERE id = #{id}")
    @Results({
            @Result(column = "tags", property = "tags", javaType = JSONObject.class),
            @Result(column = "fields", property = "fields", javaType = JSONObject.class),
            @Result(column = "metric_type", property = "metricType"),
            @Result(column = "metric_source", property = "metricSource")
    })
    MetricsCore findById(@Param("id") Integer id);

    // 插入新的指标
    @Insert("INSERT INTO dc_databuff_metrics_core(type1, type2, type3, app, `database`, measurement, `desc`, tagKey, tagValue, fields, isOpen, metric_type, metric_source) VALUES(#{type1}, #{type2}, #{type3}, #{app}, #{database}, #{measurement}, #{desc}, #{tagKey}, #{tagValue}, #{fields}, #{isOpen}, #{metricType}, #{metricSource})")
    void insert(MetricsCore dcMetricsCore);

    // 更新指标，只更新非null字段
    @Update({
            "<script>",
            "UPDATE dc_databuff_metrics_core",
            "<set>",
            "<if test='type1 != null'>type1 = #{type1},</if>",
            "<if test='type2 != null'>type2 = #{type2},</if>",
            "<if test='type3 != null'>type3 = #{type3},</if>",
            "<if test='app != null'>app = #{app},</if>",
            "<if test='database != null'>`database` = #{database},</if>",
            "<if test='measurement != null'>measurement = #{measurement},</if>",
            "<if test='desc != null'>`desc` = #{desc},</if>",
            "<if test='tagKey != null'>tagKey = #{tagKey},</if>",
            "<if test='tagValue != null'>tagValue = #{tagValue},</if>",
            "<if test='fields != null'>fields = #{fields},</if>",
            "<if test='isOpen != null'>isOpen = #{isOpen},</if>",
            "<if test='metricType != null'>metric_type = #{metricType},</if>",
            "<if test='metricSource != null'>metric_source = #{metricSource},</if>",
            "</set>",
            "WHERE id = #{id}",
            "</script>"
    })
    void update(MetricsCore dcMetricsCore);

    // 根据id删除指标
    @Delete("DELETE FROM dc_databuff_metrics_core WHERE id = #{id}")
    void delete(@Param("id") Integer id);

    @Select({
            "<script>",
            "SELECT * FROM dc_databuff_metrics_core",
            "WHERE 1=1",
            "<if test='isOpen != null'>AND isOpen = #{isOpen}</if>",
            "<if test='type1 != null'>AND type1 = #{type1}</if>",
            "<if test='type2 != null'>AND type2 = #{type2}</if>",
            "<if test='type3 != null'>AND type3 = #{type3}</if>",
            "<if test='app != null'>AND app = #{app}</if>",
            "<if test='database != null'>AND `database` = #{database}</if>",
            "<if test='measurement != null'>AND measurement = #{measurement}</if>",
            "<if test='metricType != null'>AND metric_type = #{metricType}</if>",
            "<if test='metricSource != null'>AND metric_source = #{metricSource}</if>",
            "<if test='builtin != null'>AND builtin = #{builtin}</if>",
            "</script>"
    })
    @Results({
            @Result(column = "tags", property = "tags", javaType = JSONObject.class),
            @Result(column = "fields", property = "fields", javaType = JSONObject.class),
            @Result(column = "tagKey", property = "tagKey", javaType = JSONObject.class),
            @Result(column = "tagValue", property = "tagValue", javaType = JSONObject.class),
            @Result(column = "metric_type", property = "metricType"),
            @Result(column = "metric_source", property = "metricSource")
    })
    List<MetricsCore> findByConditions(@Param("type1") String type1, @Param("type2") String type2, @Param("type3") String type3,
                                       @Param("app") String app, @Param("database") String database, @Param("measurement") String measurement,
                                       @Param("isOpen") Boolean isOpen, @Param("metricType") String metricType,
                                       @Param("metricSource") String metricSource, @Param("basicJudgment") String basicJudgment,
                                       @Param("builtin") Boolean builtin);


    /**
     * 直接更新type1字段。
     *
     * @param oldType1 原始type1值，用于WHERE条件筛选需要更新的记录。
     * @param newType1 新的type1值，将替换符合条件记录的当前type1值。
     * @return 受影响的记录行数。
     */
    @Update("UPDATE dc_databuff_metrics_core SET type1 = #{newType1} WHERE type1 = #{oldType1}")
    int updateType1(@Param("oldType1") String oldType1, @Param("newType1") String newType1);

    /**
     * 直接更新type2字段，根据type1条件筛选记录。
     *
     * @param oldType1 用于WHERE条件的type1值，筛选需要更新的记录。
     * @param newType2 新的type2值，将替换符合条件记录的当前type2值。
     * @return 受影响的记录行数。
     */
    @Update("UPDATE dc_databuff_metrics_core SET type2 = #{newType2} WHERE type1 = #{oldType1}")
    int updateType2(@Param("oldType1") String oldType1, @Param("newType2") String newType2);

    /**
     * 直接更新type3字段，根据type1和type2条件筛选记录。
     *
     * @param oldType1 用于WHERE条件的type1值。
     * @param oldType2 用于WHERE条件的type2值。
     * @param newType3 新的type3值，将替换符合条件记录的当前type3值。
     * @return 受影响的记录行数。
     */
    @Update("UPDATE dc_databuff_metrics_core SET type3 = #{newType3} WHERE type1 = #{oldType1} AND type2 = #{oldType2}")
    int updateType3(@Param("oldType1") String oldType1, @Param("oldType2") String oldType2, @Param("newType3") String newType3);

    /**
     * 根据type1、type2、type3查询不重复的app、database和measurement
     *
     * @param type1 类型1
     * @param type2 类型2
     * @param type3 类型3
     * @return 包含app、database和measurement的列表
     */
    @Select({
            "<script>",
            "SELECT DISTINCT app, `database`, measurement FROM dc_databuff_metrics_core",
            "WHERE 1=1",
            "<if test='type1 != null'>AND type1 = #{type1}</if>",
            "<if test='type2 != null'>AND type2 = #{type2}</if>",
            "<if test='type3 != null'>AND type3 = #{type3}</if>",
            "</script>"
    })
    List<Map<String, Object>> findDistinctAppDatabaseMeasurementByTypes(@Param("type1") String type1, @Param("type2") String type2, @Param("type3") String type3);


    /**
     * 根据app查询所有指标核心配置.
     *
     * @param app 应用名称
     * @return 指标核心配置列表
     */
    @Select("SELECT * FROM dc_databuff_metrics_core WHERE app = #{app}")
    @Results({
            @Result(column = "tags", property = "tags", javaType = JSONObject.class),
            @Result(column = "fields", property = "fields", javaType = JSONObject.class),
            @Result(column = "tagKey", property = "tagKey", javaType = JSONObject.class),
            @Result(column = "tagValue", property = "tagValue", javaType = JSONObject.class),
            @Result(column = "metric_type", property = "metricType"),
            @Result(column = "metric_source", property = "metricSource")
    })
    List<MetricsCore> findByApp(@Param("app") String app);

    /**
     * [新增] 根据 type1 查询已存在的 database，LIMIT 1 保证只返回一个，提高效率
     * @param type1 一级分类名称
     * @return 对应的database名称，如果不存在则返回null
     */
    @Select("SELECT `database` FROM dc_databuff_metrics_core WHERE type1 = #{type1} LIMIT 1")
    String findDatabaseByType1(@Param("type1") String type1);

    /**
     * [新增] 根据三级完整分类查询已存在的 app
     * @param type1 一级分类名称
     * @param type2 二级分类名称
     * @param type3 三级分类名称
     * @return 对应的app名称，如果不存在则返回null
     */
    @Select("SELECT app FROM dc_databuff_metrics_core WHERE type1 = #{type1} AND type2 = #{type2} AND type3 = #{type3} LIMIT 1")
    String findAppByTypes(@Param("type1") String type1, @Param("type2") String type2, @Param("type3") String type3);

    /**
     * [新增] 获取 custom database 的最大ID。
     * 使用 IFNULL 处理第一次无记录的情况，返回0。
     * 使用 REGEXP 保证只匹配 "custom" + 数字的格式。
     * @return 当前最大的custom编号，没有则返回0
     */
    @Select("SELECT IFNULL(MAX(CAST(SUBSTRING(`database`, 7) AS UNSIGNED)), 0) " +
            "FROM dc_databuff_metrics_core WHERE `database` REGEXP '^custom[0-9]+$'")
    Integer findMaxCustomDatabaseId();

    /**
     * [新增] 获取 custom app 的最大ID。
     * 逻辑同上。
     * @return 当前最大的custom编号，没有则返回0
     */
    @Select("SELECT IFNULL(MAX(CAST(SUBSTRING(app, 7) AS UNSIGNED)), 0) " +
            "FROM dc_databuff_metrics_core WHERE app REGEXP '^custom[0-9]+$'")
    Integer findMaxCustomAppId();

}