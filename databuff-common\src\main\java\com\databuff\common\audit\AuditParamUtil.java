package com.databuff.common.audit;

import com.google.common.collect.Lists;

import java.util.Collection;

public class AuditParamUtil {
    private static final ThreadLocal<Collection<AuditEntity>> threadLocalParams = new ThreadLocal<>();

    public static void setThreadLocal(AuditEntity param) {
        threadLocalParams.set(Lists.newArrayList(param));
    }

    public static void setThreadLocals(Collection<AuditEntity> params) {
        threadLocalParams.set(params);
    }
    public static void set(String entityId, String entityName, String description) {
        AuditEntity.builder()
                .id(entityId)
                .name(entityName)
                .desc(description)
                .build();
    }

    public static void set(String entityId, String entityName) {
        AuditEntity.builder()
                .id(entityId)
                .name(entityName)
                .build();
    }

    public static void set(String entityName) {
        AuditEntity.builder()
                .name(entityName)
                .build();
    }
    public static void set(Object entityName) {
        if (entityName == null) {
            return;
        }
        AuditEntity.builder()
                .name(entityName.toString())
                .build();
    }

    public static Collection<AuditEntity> get() {
        return threadLocalParams.get();
    }

    public static void remove() {
        threadLocalParams.remove();
    }
}