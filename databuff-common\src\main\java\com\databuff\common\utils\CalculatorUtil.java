package com.databuff.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.metric.WrapData;
import com.google.common.collect.Lists;
import com.sun.istack.NotNull;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 加减乘除带括号表达式求值算法
 *
 * @package com.databuff.webapp.util
 * @company: dacheng
 * @author: zlh
 * @createDate: 2021/9/8
 */
public class CalculatorUtil {
    /**
     * 表达式字符合法性校验正则模式,只支持+ - * / ()
     */
    private static final Pattern EXPRESSION_PATTERN = Pattern.compile("[A-Z0-9.+\\-/*()= ]+");
    private static final Pattern EXPRESSION_PATTERN_SINGLE = Pattern.compile("[A-Z0-9.+\\-()= ]+");
    private static final Pattern LETTER_PATTERN = Pattern.compile("[A-Z]+");

//    private Map<String, Map<Object,Double>> valMap;

    /**
     * 运算符优先级map
     */
    private static final Map<String, Integer> OPT_PRIORITY_MAP = new HashMap<String, Integer>() {
        private static final long serialVersionUID = 6968472606692771458L;

        {
            put("(", 0);
            put("+", 1);
            put("-", 1);
            put("*", 2);
            put("/", 2);
            put(")", 3);
            put("=", 9);
        }
    };

    /**
     * 输入加减乘除表达式字符串，返回计算结果
     *
     * @param expression 表达式字符串
     * @return 返回计算结果
     */
    public static Map<Object, Double> executeExprOfHistogram(String expression, Map<String, Map<Object, Double>> valMap) {
        if (valMap == null || valMap.isEmpty()) {
            return new HashMap<>(0);
        }
        //线程安全问题
//         valMap = valMap;
        // 非空校验
        if (null == expression || "".equals(expression.trim()) || expression.trim().length() == 1) {
            Iterator<String> iterator = valMap.keySet().iterator();
            return valMap.get(iterator.next());
        }
        String key = executeExpression(expression, valMap);
        return valMap.get(key);
    }

    /**
     * 根据数值映射进行分组
     *
     * @param doubleMap 数值映射，键为时间戳和标签的拼接字符串，值为对应的数值
     * @return 一个Map，键为标签的Map，值为时间戳和数值的有序Map
     * 输入：doubleMap = {1634726400¥{“city”:“Beijing”}: 10.0, 1634730000¥{“city”:“Beijing”}: 12.0, 1634726400¥{“city”:“Shanghai”}: 8.0, 1634730000¥{“city”:“Shanghai”}: 9.0}
     * 输出：groupMap = {{city=Beijing}={1634726400=10.0, 1634730000=12.0}, {city=Shanghai}={1634726400=8.0, 1634730000=9.0}}
     * <p>
     * 输入：doubleMap = null
     * 输出：groupMap = {}
     */
    @NotNull
    public static Map<Map, Map<Object, Double>> group(Map<Object, Double> doubleMap) {
        // <分组，<秒，val>>
        Map<Map, Map<Object, Double>> groupMap = new HashMap<>(16);
        for (Map.Entry<Object, Double> entry : doubleMap.entrySet()) {
            String[] keys = entry.getKey().toString().split("¥");
            String groupKey = keys[1];
            Map<String, String> tag = new HashMap<>();
            JSONObject jsonObject = JSONObject.parseObject(groupKey);

            // 为null是未分组情况
            if (jsonObject != null) {
                for (Map.Entry<String, Object> e : jsonObject.entrySet()) {
                    //获取键和值
                    String key = e.getKey();
                    Object value = e.getValue();
                    //把值转换成String类型
                    String valueStr = String.valueOf(value);
                    //把键值对放入Map中
                    tag.put(key, valueStr);
                }
            }

            if (groupMap.containsKey(tag)) {
                groupMap.get(tag).put(keys[0], entry.getValue());
            } else {
                Map<Object, Double> treeMap = new TreeMap<>();
                treeMap.put(keys[0], entry.getValue());
                groupMap.put(tag, treeMap);
            }

        }
        return groupMap;
    }

    @NotNull
    public static List<WrapData> groupV2(Map<Object, Double> doubleMap, String expr, String unit) {
        List<WrapData> wrapDataList = new ArrayList<>();
        Map<Map, Map<Object, Double>> groupMap = group(doubleMap);

        for (Map.Entry<Map, Map<Object, Double>> entry : groupMap.entrySet()) {
            if (entry == null) {
                continue;
            }

            List<List<Number>> values = new ArrayList<>();
            for (Map.Entry<Object, Double> doubleEntry : entry.getValue().entrySet()) {
                List<Number> ddd = Lists.newArrayList(Long.parseLong((String) doubleEntry.getKey()), doubleEntry.getValue());
                values.add(ddd);
            }
            wrapDataList.add(WrapData.builder()
                    .columns(new String[]{"time", expr})
                    .units(new String[]{"ms", unit})
                    .tags(entry.getKey())
                    .values(values)
                    .build());
        }
        return wrapDataList;
    }

    /**
     * 输入加减乘除表达式字符串，返回单位计算结果
     *
     * @param expression 表达式字符串
     * @return 返回计算结果
     */
    public static String executeExprOfUnit(String expression, Map<String, String> valMap) {
        if (valMap == null || valMap.isEmpty()) {
            return null;
        }
        // 非空校验
        Iterator<String> iterator = valMap.keySet().iterator();
        if (null == expression || "".equals(expression.trim()) || expression.trim().length() == 1) {
            return valMap.get(iterator.next());
        }
        Matcher matcher = EXPRESSION_PATTERN_SINGLE.matcher(expression);
        if (!matcher.matches()) {
            return null;
        }
        // 纯加减法
        return valMap.get(iterator.next());
    }

    /**
     * 表达式计算逻辑
     *
     * @param expression
     * @return
     */
    private static String executeExpression(String expression, Map<String, Map<Object, Double>> valMap) {
        // 表达式字符合法性校验
        Matcher matcher = EXPRESSION_PATTERN.matcher(expression);
        if (!matcher.matches()) {
            throw new IllegalArgumentException("表达式含有非法字符！");
        }

        // 运算符栈
        Stack<String> optStack = new Stack<>();
        // 表达式值栈
        Stack<String> objectStack = new Stack<>();
        // 当前读取的字符追加器
        StringBuilder curBuilder = new StringBuilder(16);

        // 遍历表达式字符，判断进行何种计算
        for (int i = 0; i < expression.length(); i++) {
            char c = expression.charAt(i);
            if (c != ' ') {
                boolean matches = LETTER_PATTERN.matcher(String.valueOf(c)).matches();
                boolean isNumber = c >= '0' && c <= '9';
                // 数值、字母型
                if (isNumber || c == '.' || matches) {
                    curBuilder.append(c);
                } else {
                    // 当前追加器是否有值，有就压栈
                    if (curBuilder.length() > 0) {
                        objectStack.push(curBuilder.toString());
                        curBuilder.delete(0, curBuilder.length());
                    }
                    String curOpt = String.valueOf(c);
                    if (optStack.isEmpty()) {
                        // 栈顶为空
                        optStack.push(curOpt);
                    } else {
                        // 括号与等号特殊处理
                        if ("(".equals(curOpt)) {
                            // 直接压栈
                            optStack.push(curOpt);
                        } else if (")".equals(curOpt)) {
                            // 碰到右括号，进行二元运算
                            directCalc(optStack, objectStack, true, valMap);
                        } else if ("=".equals(curOpt)) {
                            // 进行最后计算，返回计算值
                            directCalc(optStack, objectStack, false, valMap);
                            return objectStack.pop();
                        } else {
                            // 当前运算符为加减乘除之一，要与栈顶运算符比较，判断是否要进行一次二元计算
                            compareAndCalc(optStack, objectStack, curOpt, valMap);
                        }
                    }
                }
            }
        }
        // 数值、字母型结尾，即不以=号结尾
        if (curBuilder.length() > 0) {
            objectStack.push(curBuilder.toString());
        }
        // 进行最后计算，返回计算值
        directCalc(optStack, objectStack, false, valMap);
        return objectStack.pop();
    }

    /**
     * 二元运算（递归比较并计算），否则当前运算符入栈
     *
     * @param optStack 运算符栈
     * @param objStack 数值、字母栈
     * @param curOpt   当前运算符
     */
    private static void compareAndCalc(Stack<String> optStack, Stack<String> objStack, String curOpt, Map<String, Map<Object, Double>> valMap) {
        // 比较当前运算符和栈顶运算符的优先级
        String peekOpt = optStack.peek();
        int priority = getPriority(peekOpt, curOpt);
        if (priority == -1 || priority == 0) {
            // 栈顶运算符优先级大或同级，触发一次二元运算
            String opt = optStack.pop();
            String obj2 = objStack.pop();
            String obj1 = objStack.pop();
            String obj3 = mapPointCalc(opt, obj1, obj2, valMap);

            // 计算结果当做操作数入栈
            objStack.push(obj3);

            // 运算完栈顶还有运算符，则还需要再次触发一次比较判断是否需要再次二元计算
            if (optStack.empty()) {
                optStack.push(curOpt);
            } else {
                compareAndCalc(optStack, objStack, curOpt, valMap);
            }
        } else {
            // 当前运算符优先级高，则直接入栈
            optStack.push(curOpt);
        }
    }

    /**
     * 遇到右括号和等号执行的连续计算操作（递归计算）
     *
     * @param optStack
     * @param objStack
     * @param isBracket
     */
    private static void directCalc(Stack<String> optStack, Stack<String> objStack, boolean isBracket, Map<String, Map<Object, Double>> valMap) {
        String opt = optStack.pop();
        String obj2 = objStack.pop();
        String obj1 = objStack.pop();
        String obj3 = mapPointCalc(opt, obj1, obj2, valMap);

        // 计算结果当做操作数入栈
        objStack.push(obj3);

        if (isBracket) {
            if ("(".equals(optStack.peek())) {
                // 括号类型则遇左括号停止计算，同时将左括号从栈中移除
                optStack.pop();
            } else {
                directCalc(optStack, objStack, isBracket, valMap);
            }
        } else {
            if (!optStack.empty()) {
                // 等号类型只要栈中还有运算符就继续计算
                directCalc(optStack, objStack, isBracket, valMap);
            }
        }
    }

    /**
     * 根据操作符和参数执行计算
     *
     * @param opt     操作符（如 "+", "-"）
     * @param obj1    第一个操作数（字符串形式）
     * @param obj2    第二个操作数（字符串形式）
     * @param valMap  存储变量值的映射表
     * @return 计算结果字符串
     */
    private static String mapPointCalc(String opt, String obj1, String obj2, Map<String, Map<Object, Double>> valMap) {
        boolean isExpressionMatch1 = EXPRESSION_PATTERN.matcher(obj1).matches();
        boolean isExpressionMatch2 = EXPRESSION_PATTERN.matcher(obj2).matches();

        // 空值检查
        if (obj1 == null || obj2 == null) {
            throw new IllegalArgumentException("操作数不能为空");
        }

        // 合并条件分支
        if (isExpressionMatch1 && isExpressionMatch2) {
            boolean isLetter1 = LETTER_PATTERN.matcher(obj1).matches();
            boolean isLetter2 = LETTER_PATTERN.matcher(obj2).matches();
            if (!isLetter1 && !isLetter2
                    && NumberUtils.isCreatable(obj1) && NumberUtils.isCreatable(obj2)) {
                return numberCalc(opt, obj1, obj2);
            } else {
                return letterCalc(opt, obj1, obj2, valMap);
            }
        } else if (!isExpressionMatch1 && !isExpressionMatch2) {
            return numberCalc(opt, obj1, obj2);
        } else {
            if (isExpressionMatch1) {
                return letterNumberCalc(opt, obj1, obj2, valMap);
            } else {
                return numberLetterCalc(opt, obj1, obj2, valMap);
            }
        }
    }


    /**
     * 一个数字一个字母的计算
     *
     * @param opt
     * @param obj1
     * @param obj2
     * @return
     */
    private static String numberLetterCalc(String opt, String obj1, String obj2, Map<String, Map<Object, Double>> valMap) {
        Map<Object, Double> doubleMap = valMap.get(obj2);
        Double obj1Value = Double.parseDouble(obj1);

        for (Map.Entry<Object, Double> entry : doubleMap.entrySet()) {
            double result = 0.0;
            Double entryValue = entry.getValue();

            if (entryValue == null || obj1Value == null) {
                doubleMap.put(entry.getKey(), result);
                continue;
            }

            switch (opt) {
                case "+":
                    result = obj1Value + entryValue;
                    break;
                case "-":
                    result = obj1Value - entryValue;
                    break;
                case "*":
                    result = obj1Value * entryValue;
                    break;
                case "/":
                    if (entryValue != 0.0) {
                        result = obj1Value / entryValue;
                    }
                    break;
            }
            doubleMap.put(entry.getKey(), result);
        }

        return obj2;
    }

    /**
     * 一个字母一个数字的计算
     *
     * @param opt
     * @param obj1
     * @param obj2
     * @return
     */
    private static String letterNumberCalc(String opt, String obj1, String obj2, Map<String, Map<Object, Double>> valMap) {
        final Map<Object, Double> doubleMap = valMap.get(obj1);
        final Double doubleVal = Double.parseDouble(obj2);

        for (Map.Entry<Object, Double> entry : doubleMap.entrySet()) {
            double result = 0.0;
            final Object key = entry.getKey();
            Double entryValue = entry.getValue();

            if (entryValue == null || doubleVal == null) {
                doubleMap.put(key, result);
                continue;
            }

            switch (opt) {
                case "+":
                    result = entryValue + doubleVal;
                    break;
                case "-":
                    result = entryValue - doubleVal;
                    break;
                case "*":
                    result = entryValue * doubleVal;
                    break;
                case "/":
                    if (doubleVal != 0.0) {
                        result = entryValue / doubleVal;
                    }
                    break;
            }

            doubleMap.put(key, result);
        }

        return obj1 + opt + obj2;
    }

    /**
     * 都是数字的计算
     *
     * @param opt
     * @param obj1
     * @param obj2
     * @return
     */
    private static String numberCalc(String opt, String obj1, String obj2) {
        BigDecimal bigDecimal1 = new BigDecimal(obj1);
        BigDecimal bigDecimal2 = new BigDecimal(obj2);
        BigDecimal resultBigDecimal = new BigDecimal(0);
        switch (opt) {
            case "+":
                resultBigDecimal = bigDecimal1.add(bigDecimal2);
                break;
            case "-":
                resultBigDecimal = bigDecimal1.subtract(bigDecimal2);
                break;
            case "*":
                resultBigDecimal = bigDecimal1.multiply(bigDecimal2);
                break;
            case "/":
                double doubleVal = bigDecimal2.doubleValue();
                if (Double.isNaN(doubleVal) || Double.isInfinite(doubleVal) || doubleVal == 0.0) {
                    break;
                }
                resultBigDecimal = bigDecimal1.divide(bigDecimal2, 10, BigDecimal.ROUND_HALF_DOWN);
                break;
            default:
                break;
        }
        return resultBigDecimal.toPlainString();
    }

    /**
     * 检查给定字符串是否为有效的数值字符串，支持整数、小数和负数。
     *
     * @param str 需要检查的字符串
     * @return true 如果字符串是有效的数值格式；false 否则
     */
    private static boolean isNumericString(String str) {
        // 检查字符串是否为null或空，直接返回false
        if (str == null || str.isEmpty()) {
            return false;
        }
        return str.matches("^-?\\d+(\\.\\d+)?$");
    }


    /**
     * 都是字母的二元计算
     *
     * @param opt
     * @param obj1
     * @param obj2
     * @return
     */
    private static String letterCalc(String opt, String obj1, String obj2, Map<String, Map<Object, Double>> valMap) {
        Map<Object, Double> doubleMap1 = valMap.getOrDefault(obj1, new HashMap<>(0));
        Map<Object, Double> doubleMap2 = valMap.getOrDefault(obj2, new HashMap<>(0));
        Map<Object, Double> order1;
        Map<Object, Double> order2;
        // 结果map
        Map<Object, Double> resMap = new LinkedHashMap<>(32);

        Double obj1DefValue = null;
        if (isNumericString(obj1)) {
            obj1DefValue = new BigDecimal(obj1).doubleValue();
        }

        Double obj2DefValue = null;
        if (isNumericString(obj2)) {
            obj2DefValue = new BigDecimal(obj2).doubleValue();
        }

        // 取并集，用最小的map进行遍历计算
        if (doubleMap1.size() < doubleMap2.size()) {
            order1 = doubleMap1;
            order2 = doubleMap2;
        } else {
            order1 = doubleMap2;
            order2 = doubleMap1;
        }
        Set<Map.Entry<Object, Double>> entrySet = order1.entrySet();
        if (entrySet.isEmpty()) {
            entrySet = order2.entrySet();
        }
        switch (opt) {
            case "+":
                for (Map.Entry<Object, Double> entry : entrySet) {
                    Object key = entry.getKey();
                    // 如果都存在key，进行计算
                    if (order2.containsKey(key)) {
                        try {
                            resMap.put(key, doubleMap1.getOrDefault(key, obj1DefValue) + doubleMap2.getOrDefault(key, obj2DefValue));
                        } catch (Exception e) {
                            resMap.put(key, null);
                        }
                    }
                }
                break;
            case "-":
                for (Map.Entry<Object, Double> entry : entrySet) {
                    Object key = entry.getKey();
                    // 如果都存在key，进行计算
                    if (order2.containsKey(key)) {
                        try {
                            resMap.put(key, doubleMap1.getOrDefault(key, obj1DefValue) - doubleMap2.getOrDefault(key, obj2DefValue));
                        } catch (Exception e) {
                            resMap.put(key, null);
                        }
                    }
                }
                break;
            case "*":
                for (Map.Entry<Object, Double> entry : entrySet) {
                    Object key = entry.getKey();
                    // 如果都存在key，进行计算
                    if (order2.containsKey(key)) {
                        try {
                            resMap.put(key, doubleMap1.getOrDefault(key, obj1DefValue) * doubleMap2.getOrDefault(key, obj2DefValue));
                        } catch (Exception e) {
                            resMap.put(key, null);
                        }
                    }
                }
                break;
            case "/":
                for (Map.Entry<Object, Double> entry : entrySet) {
                    Object key = entry.getKey();
                    // 如果都存在key，进行计算
                    if (order2.containsKey(key)) {
                        try {

                            Double doubleVal = doubleMap2.getOrDefault(key, obj2DefValue);
                            if (Double.isNaN(doubleVal) || Double.isInfinite(doubleVal) || doubleVal == 0.0) {
                                resMap.put(key, 0.0);
                                continue;
                            }
                            resMap.put(key, doubleMap1.getOrDefault(key, obj1DefValue) / doubleMap2.getOrDefault(key, obj2DefValue));
                        } catch (Exception e) {
                            resMap.put(key, null);
                        }
                    }
                }
                break;
            default:
                break;
        }
        final String key = obj1 + opt + obj2;
        valMap.put(key, resMap);
        return key;
    }

    /**
     * priority = 0  表示两个运算符同级别
     * priority = 1  第二个运算符级别高，负数则相反
     *
     * @param opt1
     * @param opt2
     * @return
     */
    private static int getPriority(String opt1, String opt2) {
        return OPT_PRIORITY_MAP.get(opt2) - OPT_PRIORITY_MAP.get(opt1);
    }


    /**
     * 获取指标数值
     *
     * @param metricValue
     * @return
     */
    public static Number getMetricValue(Object metricValue) {
        if (metricValue == null) {
            return null;
        }
        if (metricValue instanceof String && NumberUtils.isParsable(metricValue.toString())) {
            metricValue = NumberUtils.createNumber(metricValue.toString());
        } else {
            if (!(metricValue instanceof Number)) {
                return null;
            }
        }
        return (Number) metricValue;
    }
}
