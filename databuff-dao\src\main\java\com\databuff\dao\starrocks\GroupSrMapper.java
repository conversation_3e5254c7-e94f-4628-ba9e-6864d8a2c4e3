package com.databuff.dao.starrocks;

import com.databuff.entity.GroupRuleParamsEntity;
import com.databuff.entity.K8sEntity;
import com.databuff.entity.domainObj.DomainManagerSearch;
import com.databuff.entity.domainObj.K8sNamespaceObj;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface GroupSrMapper {
    List<K8sNamespaceObj> getNamespacesByRules(@Param("rules") List<List<GroupRuleParamsEntity>> rules, @Param("apiKey") String apiKey);
    List<K8sNamespaceObj> getNamespaces(DomainManagerSearch search);
    List<K8sEntity> getNamespaceData(@Param("apiKey") String apiKey, @Param("clusterIdNamespaces") List<String> clusterIdNamespaces);

}
