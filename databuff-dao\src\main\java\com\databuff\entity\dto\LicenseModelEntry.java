package com.databuff.entity.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 授权模块
 * @company: dacheng
 * @Author: zlh
 * @CreateDate: 2020/6/2
 */
@Getter
@Setter
public class LicenseModelEntry {
    private Integer id;
    //授权产品id
    private Integer licenseProductId;
    //模块名称
    private String licenseModelName;
    //模块唯一识别
    private String licenseModelIdentify;
    private String licenseModelFunction;
    //授权开始时间
    private Long licenseModelStarttime;
    /**授权结束时间*/
    private Long licenseModelEndtime;
    private Date createTime;
    private Date updateTime;
}
