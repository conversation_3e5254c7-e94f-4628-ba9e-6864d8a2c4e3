package com.databuff.common.tsdb.builder;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.tsdb.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

// 具体的 OpenGemini 查询构建类
@Slf4j
public class OpenGeminiQueryBuilder extends AbstractQueryBuilder {

    // OpenGemini 聚合函数映射
    public static final Map<AggFun, String> FUNCTIONS = new HashMap<>();
    // OpenGemini WHERE 操作符映射
    public static final Map<WhereOp, String> OPERATORS = new HashMap<>();
    // OpenGemini TOP聚合函数映射
    public static final Map<AggFun, String> TOP_FUNCTIONS = new HashMap<>();
    private static final String[] SPECIAL_CHARACTERS = {"'"};

    static {
        // OpenGemini 不支持 t_max 等函数，使用标准聚合函数
        TOP_FUNCTIONS.put(AggFun.AVG, "mean");
        TOP_FUNCTIONS.put(AggFun.MEAN, "mean");
        TOP_FUNCTIONS.put(AggFun.SUM, "sum");
        TOP_FUNCTIONS.put(AggFun.MAX, "max");
        TOP_FUNCTIONS.put(AggFun.MIN, "min");
        TOP_FUNCTIONS.put(AggFun.LAST, "last");

        FUNCTIONS.put(AggFun.AVG, "mean");
        FUNCTIONS.put(AggFun.MEAN, "mean");
        FUNCTIONS.put(AggFun.COUNT, "count");
        FUNCTIONS.put(AggFun.SUM, "sum");
        FUNCTIONS.put(AggFun.PERCENTILE, "percentile");
        FUNCTIONS.put(AggFun.MAX, "max");
        FUNCTIONS.put(AggFun.MIN, "min");
        FUNCTIONS.put(AggFun.LAST, "last");

        // WHERE 操作符映射
        OPERATORS.put(WhereOp.EQ, "=");
        OPERATORS.put(WhereOp.NEQ, "!=");
        OPERATORS.put(WhereOp.LT, "<");
        OPERATORS.put(WhereOp.GT, ">");
        OPERATORS.put(WhereOp.LTE, "<=");
        OPERATORS.put(WhereOp.GTE, ">=");
        OPERATORS.put(WhereOp.LIKE, "=~");
        OPERATORS.put(WhereOp.NOT_LIKE, "!~");
        OPERATORS.put(WhereOp.REGEX, "=~");
        OPERATORS.put(WhereOp.IN, "IN");
        OPERATORS.put(WhereOp.NOT_IN, "NOT IN");
        OPERATORS.put(WhereOp.IS, "IS NULL");
        OPERATORS.put(WhereOp.IS_NOT, "IS NOT NULL");
    }

    public OpenGeminiQueryBuilder(QueryBuilder queryBuilder) {
        super(queryBuilder);
    }

    @Override
    public void appendDatabaseSpecificLogic(StringBuilder query, AggFun tsAgg, AggFun valAgg, AggFun topAgg, JSONObject otherParam) {
        // 添加 OpenGemini 特定的 SQL 语法逻辑
        List<Aggregation> aggregations = queryBuilder.getAggregations();
        if (aggregations == null || aggregations.isEmpty()) {
            return;
        }

        // 检查是否需要使用嵌套聚合函数
        boolean needsNestedAggregation = (tsAgg != null && valAgg != null) ||
                                       (tsAgg != null && topAgg != null) ||
                                       (valAgg != null && topAgg != null);

        if (!needsNestedAggregation) {
            // 如果不需要嵌套聚合，使用简单的聚合函数
            StringBuilder selectFields = new StringBuilder();
            for (Aggregation aggregation : aggregations) {
                String field = aggregation.getField();
                String alias = aggregation.getAlias();
                if (field == null || field.trim().isEmpty()) {
                    throw new IllegalArgumentException("Field name cannot be null or empty.");
                }

                // 判断字段是否已经被引号包裹
                boolean isAlreadyQuoted = (field.startsWith("\"") && field.endsWith("\"")) ||
                                         (field.startsWith("'") && field.endsWith("'"));

                // 判断字段是否是公式或聚合函数
                boolean isFormulaOrFunction = field.contains("(") || field.contains(")") ||
                                             field.contains("+") || field.contains("-") ||
                                             field.contains("*") || field.contains("/");

                String processedField;
                if (isAlreadyQuoted || isFormulaOrFunction) {
                    // 如果字段已经被引号包裹或是公式/聚合函数，不需要再次包裹
                    processedField = field;
                } else {
                    // 单独的字段名需要用双引号包裹
                    processedField = "\"" + field + "\"";
                }

                String aggField = processedField;
                // 应用单个聚合函数
                if (tsAgg != null) {
                    String upperFunction = FUNCTIONS.getOrDefault(tsAgg, tsAgg.name());
                    aggField = upperFunction + "(" + processedField + ")";
                } else if (valAgg != null) {
                    String upperFunction = FUNCTIONS.getOrDefault(valAgg, valAgg.name());
                    aggField = upperFunction + "(" + processedField + ")";
                } else if (topAgg != null) {
                    String upperFunction = TOP_FUNCTIONS.getOrDefault(topAgg, topAgg.name());
                    aggField = upperFunction + "(" + processedField + ")";
                }

                if (alias == null || alias.trim().isEmpty()) {
                    selectFields.append(aggField).append(",");
                } else {
                    // 避免别名与 SQL 关键字冲突
                    selectFields.append(aggField).append(" AS \"").append(alias).append("\",");
                }
            }
            query.append(selectFields, 0, selectFields.length() - 1);
        } else {
            // 使用子查询来实现嵌套聚合
            // 为每个聚合函数创建一个子查询层
            for (Aggregation aggregation : aggregations) {
                String field = aggregation.getField();
                String alias = aggregation.getAlias() != null ? aggregation.getAlias() : field;

                if (field == null || field.trim().isEmpty()) {
                    throw new IllegalArgumentException("Field name cannot be null or empty.");
                }

                // 构建最内层查询 - 使用 tsAgg
                if (tsAgg != null && (valAgg != null || topAgg != null)) {
                    String innerFunction = FUNCTIONS.getOrDefault(tsAgg, tsAgg.name());
                    String innerAlias = "inner_value";

                    // 判断字段是否已经被引号包裹
                    boolean isAlreadyQuoted = (field.startsWith("\"") && field.endsWith("\"")) ||
                                             (field.startsWith("'") && field.endsWith("'"));

                    // 判断字段是否是公式或聚合函数
                    boolean isFormulaOrFunction = field.contains("(") || field.contains(")") ||
                                                 field.contains("+") || field.contains("-") ||
                                                 field.contains("*") || field.contains("/");

                    String processedField;
                    if (isAlreadyQuoted || isFormulaOrFunction) {
                        // 如果字段已经被引号包裹或是公式/聚合函数，不需要再次包裹
                        processedField = field;
                    } else {
                        // 单独的字段名需要用双引号包裹
                        processedField = "\"" + field + "\"";
                    }

                    String innerQuery = innerFunction + "(" + processedField + ") AS " + innerAlias;

                    // 构建中间层查询 - 使用 valAgg
                    if (valAgg != null && topAgg != null) {
                        String middleFunction = FUNCTIONS.getOrDefault(valAgg, valAgg.name());
                        String middleAlias = "middle_value";
                        String middleQuery = middleFunction + "(" + innerAlias + ") AS " + middleAlias;

                        // 构建最外层查询 - 使用 topAgg
                        String outerFunction = TOP_FUNCTIONS.getOrDefault(topAgg, topAgg.name());
                        query.append(outerFunction).append("(").append(middleAlias).append(")");

                        // 添加子查询
                        query.append(" FROM (SELECT ").append(middleQuery)
                             .append(" FROM (SELECT ").append(innerQuery)
                             .append(" FROM \"").append(queryBuilder.getMeasurement()).append("\"");

                        // 添加 GROUP BY 子句到内层查询
                        if (queryBuilder.getInterval() != null && queryBuilder.getInterval() > 0) {
                            query.append(" GROUP BY time(")
                                 .append(queryBuilder.getInterval())
                                 .append(queryBuilder.getIntervalUnit() != null ? queryBuilder.getIntervalUnit() : "s")
                                 .append(")");
                        }

                        query.append(") "); // 关闭内层查询

                        // 可以在中间层添加 GROUP BY 子句，如果需要的话
                        query.append(") "); // 关闭中间层查询
                    } else if (valAgg != null) {
                        // 只有 tsAgg 和 valAgg
                        String outerFunction = FUNCTIONS.getOrDefault(valAgg, valAgg.name());
                        query.append(outerFunction).append("(").append(innerAlias).append(")");

                        // 添加子查询
                        query.append(" FROM (SELECT ").append(innerQuery)
                             .append(" FROM \"").append(queryBuilder.getMeasurement()).append("\"");

                        // 添加 GROUP BY 子句到内层查询
                        if (queryBuilder.getInterval() != null && queryBuilder.getInterval() > 0) {
                            query.append(" GROUP BY time(")
                                 .append(queryBuilder.getInterval())
                                 .append(queryBuilder.getIntervalUnit() != null ? queryBuilder.getIntervalUnit() : "s")
                                 .append(")");
                        }

                        query.append(") "); // 关闭内层查询
                    } else if (topAgg != null) {
                        // 只有 tsAgg 和 topAgg
                        String outerFunction = TOP_FUNCTIONS.getOrDefault(topAgg, topAgg.name());
                        query.append(outerFunction).append("(").append(innerAlias).append(")");

                        // 添加子查询
                        query.append(" FROM (SELECT ").append(innerQuery)
                             .append(" FROM \"").append(queryBuilder.getMeasurement()).append("\"");

                        // 添加 GROUP BY 子句到内层查询
                        if (queryBuilder.getInterval() != null && queryBuilder.getInterval() > 0) {
                            query.append(" GROUP BY time(")
                                 .append(queryBuilder.getInterval())
                                 .append(queryBuilder.getIntervalUnit() != null ? queryBuilder.getIntervalUnit() : "s")
                                 .append(")");
                        }

                        query.append(") "); // 关闭内层查询
                    }
                } else {
                    // 只有一个聚合函数，不需要子查询
                    String aggFunction = "";
                    if (tsAgg != null) {
                        aggFunction = FUNCTIONS.getOrDefault(tsAgg, tsAgg.name());
                    } else if (valAgg != null) {
                        aggFunction = FUNCTIONS.getOrDefault(valAgg, valAgg.name());
                    } else if (topAgg != null) {
                        aggFunction = TOP_FUNCTIONS.getOrDefault(topAgg, topAgg.name());
                    }

                    // 判断字段是否已经被引号包裹
                    boolean isAlreadyQuoted = (field.startsWith("\"") && field.endsWith("\"")) ||
                                             (field.startsWith("'") && field.endsWith("'"));

                    // 判断字段是否是公式或聚合函数
                    boolean isFormulaOrFunction = field.contains("(") || field.contains(")") ||
                                                 field.contains("+") || field.contains("-") ||
                                                 field.contains("*") || field.contains("/");

                    String processedField;
                    if (isAlreadyQuoted || isFormulaOrFunction) {
                        // 如果字段已经被引号包裹或是公式/聚合函数，不需要再次包裹
                        processedField = field;
                    } else {
                        // 单独的字段名需要用双引号包裹
                        processedField = "\"" + field + "\"";
                    }

                    query.append(aggFunction).append("(").append(processedField).append(")");
                }

                // 添加别名
                if (alias != null && !alias.trim().isEmpty()) {
                    query.append(" AS \"").append(alias).append("\"");
                }

                // 只处理第一个聚合，因为子查询结构复杂
                break;
            }
        }
    }

    @Override
    public String getSpecificAggregationFunction(Aggregation aggregation) {
        AggFun function = aggregation.getFunction();
        String field = aggregation.getField();
        //  处理单引号 opengemini field 不支持单引号
        field = field.replaceAll("'", "\"");

        // 判断字段是否已经被引号包裹
        boolean isAlreadyQuoted = (field.startsWith("\"") && field.endsWith("\"")) ||
                                 (field.startsWith("'") && field.endsWith("'"));

        // 判断字段是否是公式或聚合函数
        boolean isFormulaOrFunction = field.contains("(") || field.contains(")") ||
                                     field.contains("+") || field.contains("-") ||
                                     field.contains("*") || field.contains("/");

        if (function == null) {
            // 如果字段已经被引号包裹或是公式/聚合函数，不需要再次包裹
            if (isAlreadyQuoted || isFormulaOrFunction) {
                return field + aggregation.formatAlias("");
            } else {
                // 单独的字段名需要用双引号包裹
                return "\"" + field + "\"" + aggregation.formatAlias("");
            }
        }

        if (function == AggFun.PERCENTILE) {
            // 目前opengemini不支持，为不影响自测，暂时屏蔽不查，等天明出方案
            return null;
            //百分位的特殊处理
//            return FUNCTIONS.getOrDefault(function, function.name()) + "(" + aggregation.getField() + ")" + aggregation.formatAlias("");
        }

        // opengemini 不支持avg转为mean
        if (function == AggFun.AVG) {
            function = AggFun.MEAN;
        }

        String functionName = FUNCTIONS.getOrDefault(function, function.name());

        // 如果字段已经被引号包裹或是公式/聚合函数，不需要再次包裹
        if (isAlreadyQuoted || isFormulaOrFunction) {
            return functionName + "(" + field + ")" + aggregation.formatAlias("");
        } else {
            return functionName + "(\"" + field + "\")" + aggregation.formatAlias("");
        }
    }

    @Override
    public void appendSpecificWheres(StringBuilder query) {
        if (queryBuilder.getWheres() != null && !queryBuilder.getWheres().isEmpty()) {
            query.append(queryBuilder.getWheres().stream()
                    .map(this::convertWhereConditions)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.joining(" AND ")));
        }
    }

    @Override
    public void appendSpecificInterval(StringBuilder query) {
        Integer interval = queryBuilder.getInterval();
        String intervalUnit = queryBuilder.getIntervalUnit();
        if (interval != null && interval != 0) {
            List<String> groupBy = queryBuilder.getGroupBy();
            if (groupBy != null && !groupBy.isEmpty()) {
                query.append(",time(").append(interval).append(intervalUnit).append(") ");
            } else {
                query.append(" GROUP BY time(").append(interval).append(intervalUnit).append(") ");
            }
        }
    }

    private String convertWhereConditions(Where condition) {
        if (condition == null) {
            return "";
        }
        // 处理嵌套 WHERE 语句
        if (CollectionUtils.isNotEmpty(condition.getSubConditions())) {
            String operator = Optional.ofNullable(condition.getLogicalOperator())
                    .map(Enum::name)
                    .orElse("AND");
            List<String> parts = condition.getSubConditions().stream()
                    .map(this::convertWhereConditions)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(parts)) {
                return "";
            }
            return "(" + String.join(" " + operator + " ", parts) + ")";
        }
        if (StringUtils.isBlank(condition.getField())) {
            return "";
        }
        WhereOp whereOp = condition.getOperator();
        String operator = OPERATORS.getOrDefault(whereOp, condition.getOperator().name());
        operator = operator.trim();
        String field = condition.getField();
        Object value = condition.getValue();

        // 处理字段名，OpenGemini 要求字段名用双引号包裹，除了 TIME 字段
        // 对于非TIME字段，添加::tag后缀以适配OpenGemini的tag语法
        String quotedField = "TIME".equalsIgnoreCase(field) ? field : "\"" + field + "\"::tag";

        if (value == null) {
            value = "NULL"; // 处理 null 值，防止 SQL 语法错误
        } else if (value instanceof Boolean) {
            value = String.format("%s", (Boolean) value ? 1 : 0);
        }
        switch (whereOp) {
            case LIKE:
                // OpenGemini 使用正则表达式语法 =~ 进行模糊匹配
                // 使用辅助方法处理正则表达式中的所有特殊字符
                String escapedLikeValue = escapeRegexSpecialChars(value.toString());
                return quotedField + " =~ /.*" + escapedLikeValue + ".*/ ";

            case NOT_LIKE:
                // OpenGemini 使用正则表达式语法 !~ 进行反向模糊匹配
                // 使用辅助方法处理正则表达式中的所有特殊字符
                String escapedNotLikeValue = escapeRegexSpecialChars(value.toString());
                return quotedField + " !~ /.*" + escapedNotLikeValue + ".*/ ";

            case START_WITH:
                // 以某个值开头的正则表达式
                // 使用辅助方法处理正则表达式中的所有特殊字符
                String escapedStartValue = escapeRegexSpecialChars(value.toString());
                return quotedField + " =~ /^" + escapedStartValue + ".*/ ";

            case END_WITH:
                // 以某个值结尾的正则表达式
                // 使用辅助方法处理正则表达式中的所有特殊字符
                String escapedEndValue = escapeRegexSpecialChars(value.toString());
                return quotedField + " =~ /.*" + escapedEndValue + "$/ ";
            case REGEX:
                // 直接使用正则表达式，确保正则表达式中的特殊字符被正确处理
                // 使用辅助方法处理正则表达式中的所有特殊字符
                String escapedRegex = escapeRegexSpecialChars(value.toString());
                return quotedField + " =~ /" + escapedRegex + "/ ";
            case IS:
                // OpenGemini 中检查字段是否为空（相当于 IS NULL）
                return quotedField + " = ''";
            case IS_NOT:
                // OpenGemini 中检查字段是否不为空（相当于 IS NOT NULL）
                return quotedField + " != ''";
            case IN:
            case NOT_IN:
                return formatInCondition(field, whereOp, value);
            default:
                if ("TIME".equalsIgnoreCase(field)) {
                    // 处理时间戳，如果是数字且是13位毫秒级时间戳，直接乘以1000000转为纳秒级
                    String timeValue = value.toString();
                    if (timeValue.matches("\\d+") && timeValue.length() == 13) {
                        // 将毫秒时间戳转换为纳秒时间戳（乘以1000000）
                        try {
                            long millisTimestamp = Long.parseLong(timeValue);
                            long nanoTimestamp = millisTimestamp * 1000000;
                            return field + " " + operator + " " + nanoTimestamp;
                        } catch (NumberFormatException e) {
                            // 如果转换失败，记录日志并使用原始值
                            log.error("Failed to convert millisecond timestamp to nanosecond timestamp: {}", timeValue, e);
                        }
                    }
                    return field + " " + operator + " " + value;
                }
                // 处理字符串类型值，需要添加适当的引号并转义特殊字符
                return quotedField + " " + operator + " " + formatValueForSQL(value.toString());
        }
    }

    private String formatInCondition(String field, WhereOp whereOp, Object value) {
        // 处理字段名，OpenGemini 要求字段名用双引号包裹，除了 TIME 字段
        String quotedField = "TIME".equalsIgnoreCase(field) ? field : "\"" + field + "\"";

        if (value == null) {
            return "";
        }

        List<Object> values;
        if (value instanceof String) {
            // 字符串类型，按逗号分割
            values = Arrays.stream(((String) value).split(","))
                    .collect(Collectors.toList());
        } else if (value instanceof Collection<?>) {
            // 集合类型，直接使用
            values = new ArrayList<>((Collection<?>) value);
        } else {
            // 单个值，如 Number 类型
            values = Collections.singletonList(value);
        }

        if (values.isEmpty()) {
            return "";
        }

        // 对于 IN 操作符，使用标准 IN 语法
        if (whereOp == WhereOp.IN) {
            return quotedField + " IN (" +
                    values.stream()
                            .map(val -> formatSingleValue(val))
                            .collect(Collectors.joining(", ")) + ")";
        }
        // 对于 NOT IN 操作符，转换为多个 != 条件
        else {
            // 处理异常情况：如果values为空，返回空字符串
            if (values.isEmpty()) {
                return "";
            }

            // 正常情况：将多个!=条件用括号包裹
            return "(" + values.stream()
                    .map(val -> quotedField + " != " + formatSingleValue(val))
                    .collect(Collectors.joining(" AND ")) + ")";
        }
    }

    /**
     * 格式化单个值，根据类型进行不同处理
     *
     * @param value 需要格式化的值
     * @return 格式化后的值
     */
    private String formatSingleValue(Object value) {
        if (value == null) {
            return "";
        } else if (value instanceof Number) {
            // 数字类型也用单引号包裹
            return "'" + value + "'";
        } else if (value instanceof Boolean) {
            // 布尔类型转为 true/false 并用单引号包裹
            return "'" + ((Boolean) value ? "true" : "false") + "'";
        } else {
            // 字符串类型，需要处理转义
            return dealSpecialChar(escapeSpecialChar(value.toString()));
        }
    }

    /**
     * 处理特殊字符，确保字符串中的单引号被正确转义，并包裹在单引号中。
     * 避免对已经转义的单引号进行二次转义。
     * 优化版本，提高性能：
     * 1. 添加快速路径，对于不包含特殊字符的字符串直接返回
     * 2. 预分配 StringBuilder 空间，减少扩容操作
     * 3. 使用字符级别处理，减少中间字符串生成
     *
     * @param value 需要处理的原始字符串
     * @return 处理后的字符串，以单引号包裹，内部的单引号被转义为反斜杠加单引号（即每个单引号替换为 \\'）
     */
    private String dealSpecialChar(String value) {
        // 快速路径：空值处理
        if (value == null || value.isEmpty()) {
            return "''";
        }

        // 快速路径：如果不包含单引号和反斜杠，直接包裹并返回
        // 使用indexOf而不是contains，避免创建正则表达式
        if (value.indexOf('\'') == -1 && value.indexOf('\\') == -1) {
            return "'" + value + "'";
        }

        // 预分配空间减少扩容操作
        int length = value.length();
        StringBuilder result = new StringBuilder(length + 10);
        result.append('\''); // 开始单引号

        // 直接遍历字符串，避免创建字符数组
        for (int i = 0; i < length; i++) {
            char c = value.charAt(i);

            // 检查是否是已经转义的单引号 (\') - 不需要再次转义
            if (c == '\\' && i + 1 < length) {
                char next = value.charAt(i + 1);
                if (next == '\'') {
                    // 已转义的单引号，直接复制
                    result.append('\\').append('\'');
                    i++; // 跳过已处理的字符
                } else {
                    // 普通反斜杠，直接复制
                    result.append(c);
                }
            } else if (c == '\'') {
                // 未转义的单引号，需要转义
                result.append('\\').append('\'');
            } else {
                // 其他字符直接添加
                result.append(c);
            }
        }

        result.append('\''); // 结束单引号
        return result.toString();
    }

    /**
     * 转义特殊字符处理
     * 避免对已经转义的特殊字符进行二次转义
     * 优化版本，提高性能
     *
     * @param input 需要处理的输入字符串
     * @return 转义后的字符串
     */
    public String escapeSpecialChar(String input) {
        if (StringUtils.isBlank(input)) {
            return input;
        }

        // 快速路径：如果不包含单引号和反斜杠，直接返回
        if (!input.contains("'") && !input.contains("\\")) {
            return input;
        }

        // 使用字符数组而不是集合，提高查找效率
        char[] specialChars = {'\''};  // 只有单引号需要转义

        StringBuilder result = new StringBuilder(input.length() + 10); // 预分配空间减少扩容
        char[] chars = input.toCharArray();

        for (int i = 0; i < chars.length; i++) {
            // 处理已转义的单引号
            if (chars[i] == '\\' && i + 1 < chars.length && chars[i + 1] == '\'') {
                result.append("\\\'"); // 保持原有的转义
                i++; // 跳过已处理的字符
            }
            // 处理未转义的单引号
            else if (chars[i] == '\'') {
                result.append("\\\'"); // 转义单引号
            }
            // 其他字符直接添加
            else {
                result.append(chars[i]);
            }
        }

        return result.toString();
    }

    /**
     * 处理正则表达式中的斜杠转义
     * 如果字符串已经被引号包裹，则去除引号并对内容中的斜杠进行转义
     * 如果字符串没有被引号包裹，则直接对斜杠进行转义
     *
     * @param value 需要处理的字符串
     * @return 处理后的字符串，斜杠已被正确转义
     */
    private String escapeRegexSlashes(String value) {
        if (StringUtils.isBlank(value)) {
            return value;
        }

        // 检查值是否已经被引号包裹
        boolean isQuoted = (value.startsWith("'") && value.endsWith("'")) ||
                (value.startsWith("\"") && value.endsWith("\""));

        if (isQuoted) {
            // 如果已经被引号包裹，去除引号并对斜杠进行转义
            String content = value.substring(1, value.length() - 1);
            return content.replace("/", "\\/");
        } else {
            // 如果没有被引号包裹，直接对斜杠进行转义
            return value.replace("/", "\\/");
        }
    }

    private String formatValueForSQL(String value) {
        String escapeStr = escapeSpecialChar(value);
        // 如果包含 `"`，则用单引号 `'` 包裹，否则用双引号 `"`
        return dealSpecialChar(escapeStr);
    }

    /**
     * 处理正则表达式中的所有特殊字符转义
     * 避免对已经转义的特殊字符进行二次转义
     * 优化版本，提高性能
     *
     * @param value 需要处理的字符串
     * @return 处理后的字符串，特殊字符已被正确转义
     */
    private String escapeRegexSpecialChars(String value) {
        // 快速路径：空值处理
        if (value == null || value.isEmpty()) {
            return value;
        }

        // 快速路径：如果不包含特殊字符和反斜杠，直接返回
        // 使用静态字符集合避免重复创建
        final String SPECIAL_CHARS = ".*+?^$()[]{}|/;:,=<>\\";
        boolean containsSpecial = false;

        // 快速检查是否包含特殊字符
        int valueLen = value.length();
        for (int i = 0; i < valueLen; i++) {
            if (SPECIAL_CHARS.indexOf(value.charAt(i)) >= 0) {
                containsSpecial = true;
                break;
            }
        }

        if (!containsSpecial) {
            return value; // 不包含特殊字符，直接返回
        }

        // 检查值是否已经被引号包裹
        boolean isQuoted = (valueLen > 1) &&
                ((value.charAt(0) == '\'' && value.charAt(valueLen - 1) == '\'') ||
                        (value.charAt(0) == '"' && value.charAt(valueLen - 1) == '"'));

        // 如果被引号包裹，去除引号
        int startIndex = isQuoted ? 1 : 0;
        int endIndex = isQuoted ? valueLen - 1 : valueLen;

        // 初始化特殊字符查找表 - 使用静态数组避免重复创建
        boolean[] isSpecial = new boolean[128]; // ASCII范围内的字符映射表
        for (int i = 0; i < SPECIAL_CHARS.length(); i++) {
            char c = SPECIAL_CHARS.charAt(i);
            if (c < 128) {
                isSpecial[c] = true;
            }
        }

        // 预分配空间减少扩容操作 - 最坏情况下每个字符都需要转义，所以预分配2倍空间
        StringBuilder result = new StringBuilder((endIndex - startIndex) * 2);

        // 直接遍历字符串，避免创建字符数组
        for (int i = startIndex; i < endIndex; i++) {
            char currentChar = value.charAt(i);

            // 处理已转义的特殊字符
            if (currentChar == '\\' && i + 1 < endIndex) {
                char nextChar = value.charAt(i + 1);

                // 如果下一个字符是特殊字符，保持原有转义
                if (nextChar < 128 && isSpecial[nextChar]) {
                    result.append(currentChar).append(nextChar);
                    i++; // 跳过已处理的字符
                } else {
                    // 转义反斜杠本身
                    result.append("\\\\");
                }
            }
            // 处理未转义的特殊字符
            else if (currentChar < 128 && isSpecial[currentChar]) {
                result.append('\\').append(currentChar);
            }
            // 其他字符直接添加
            else {
                result.append(currentChar);
            }
        }

        return result.toString();
    }

    /**
     * 构建显示标签值的查询映射
     *
     * @return 查询映射
     */
    public Map<String, String> buildShowTagValueQueryMap() {
        Map<String, String> result = new HashMap<>();
        List<String> groupBy = queryBuilder.getGroupBy();
        if (groupBy == null || groupBy.isEmpty()) {
            return result;
        }

        for (String tag : groupBy) {
            String sql = buildShowTagValueQuery(tag);
            result.put(tag, sql);
        }
        return result;
    }

    /**
     * 构建显示标签值的查询
     *
     * @param tag 标签
     * @return 查询语句
     */
    private String buildShowTagValueQuery(String tag) {
        String measurement = queryBuilder.getMeasurement();
        String escapedMeasurement = measurement.replace("\"", "\\\"");
        StringBuilder sql = new StringBuilder();

        // OpenGemini 使用双引号而不是单引号来包裹测量名称和标签名称
        sql.append("SHOW TAG VALUES FROM \"").append(escapedMeasurement).append("\" WITH KEY = \"").append(tag).append("\"");

        // 添加 WHERE 条件
        String whereSql = buildWhere();
        if (StringUtils.isNotBlank(whereSql)) {
            sql.append(whereSql);
        }

        // 添加 LIMIT
        Long limit = queryBuilder.getLimit();
        if (limit != null && limit > 0) {
            sql.append(" LIMIT ").append(limit);
        }

        return sql.toString();
    }

    // 添加LIMIT 和 OFFSET
    @Override
    public void appendLimitAndOffset(StringBuilder query) {
        if (queryBuilder.getOpenGeminiLimit() != null && queryBuilder.getOpenGeminiLimit() > 0) {
            query.append(" LIMIT ").append(queryBuilder.getOpenGeminiLimit());
        }
        if (queryBuilder.getOpengeminiOffset() != null && queryBuilder.getOpengeminiOffset() > 0) {
            query.append(" OFFSET ").append(queryBuilder.getOpengeminiOffset());
        }
    }

    /**
     * 重写buildQuery方法，支持OpenGemini的TOP/BOTTOM函数
     * 当OrderBy不为空时，根据排序方向使用TOP或BOTTOM函数构建嵌套查询
     * 当limit和offset不为0时，添加分页查询
     * 当排序字段是"time"时，使用两次查询的方法：
     * 1. 第一次查询获取tag值
     * 2. 第二次查询将tag值作为条件拼接到SQL中
     */
    @Override
    public String buildQuery() {
        //检验
        queryBuilder.validate();

        // 检查是否有OrderBy字段
        List<OrderBy> orderBy = queryBuilder.getOrderBy();
        if (orderBy == null || orderBy.isEmpty()) {
            // 如果没有OrderBy，使用父类的标准查询构建逻辑
            queryBuilder.setQueryType(QueryBuilder.QueryType.STANDARD);
            String baseQuery = super.buildQuery();
            return baseQuery;
        }

        // 分离time字段和非time字段的排序
        List<OrderBy> nonTimeOrderBy = new ArrayList<>();
        List<OrderBy> timeOrderBy = new ArrayList<>();

        for (OrderBy order : orderBy) {
            if ("time".equalsIgnoreCase(order.getField())) {
                timeOrderBy.add(order);
            } else {
                nonTimeOrderBy.add(order);
            }
        }

        // 根据排序字段类型设置查询类型并构建查询
        final Integer interval = queryBuilder.getInterval();
        if (interval != null && interval > 0 && !nonTimeOrderBy.isEmpty()) {
            // 同时有time和非time字段排序
            queryBuilder.setQueryType(QueryBuilder.QueryType.TOPN_MAX_TIME);
            return buildMixedOrderQuery(nonTimeOrderBy);
        } else if (!timeOrderBy.isEmpty()) {
            // 只有time字段排序
            queryBuilder.setQueryType(QueryBuilder.QueryType.MIXED_ORDER);
            return buildTimeOnlyOrderQuery(timeOrderBy);
        } else {
            // 只有非time字段排序
            queryBuilder.setQueryType(QueryBuilder.QueryType.NON_TIME_ONLY_ORDER);
            return buildNonTimeOnlyOrderQuery(nonTimeOrderBy);
        }
    }

    /**
     * 构建同时包含time和非time字段排序的查询
     * 这种情况下使用两次查询的方法，第一次查询获取tag值
     *
     * @param nonTimeOrderBy 非time字段排序列表
     * @return 构建的SQL查询字符串
     */
    private String buildMixedOrderQuery(List<OrderBy> nonTimeOrderBy) {
        // 构建第一次查询，获取排序字段的前几条数据的tag值
        StringBuilder firstQuery = new StringBuilder("SELECT ");

        // 选择第一个非time排序字段作为排序条件
        OrderBy firstNonTimeOrder = nonTimeOrderBy.get(0);
        String orderField = firstNonTimeOrder.getField();
        boolean isAsc = firstNonTimeOrder.isAsc();

        // 根据排序方向选择top或bottom函数
        String selectorFunction = isAsc ? "bottom" : "top";
        int limit = queryBuilder.getLimit() != null ? queryBuilder.getLimit().intValue() : 5; // 默认取5条

        // 构建第一次查询的SELECT部分
        firstQuery.append(selectorFunction).append("(\"").append(orderField).append("\", ").append(limit).append(") AS \"").append(orderField).append("\", ");
        firstQuery.append("*::tag");

        // 构建子查询
        firstQuery.append(" FROM (SELECT ");

        // 添加聚合函数
        boolean foundOrderField = false;
        // 添加判断复杂公式的逻辑
        for (Aggregation agg : queryBuilder.getAggregations()) {
            if (agg == null) {
                continue;
            }
            final String field = agg.getField();
            if (field.equals(orderField) || agg.getAlias().equals(orderField)) {
                // 判断是否为复杂公式
                boolean isComplex = field.contains("(") || field.contains(")") ||
                        field.contains(",") || field.contains("+") ||
                        field.contains("-") || field.contains("*") ||
                        field.contains("/");

                if (isComplex) {
                    agg.setFunction(null); // 复杂公式不设置聚合函数
                } else {
                    agg.setFunction(AggFun.MAX); // 简单字段设置MAX
                }
                firstQuery.append(getSpecificAggregationFunction(agg));
                foundOrderField = true;
                break;
            }
        }
        // 如果没有找到对应的聚合函数，则添加默认的sum聚合
        if (!foundOrderField) {
            firstQuery.append("sum(\"").append(orderField).append("\") AS \"").append(orderField).append("\"");
        }

        // 添加测量值
        firstQuery.append(" FROM \"").append(queryBuilder.getMeasurement()).append("\"");

        // 添加WHERE条件
        firstQuery.append(buildWhere());

        // 添加GROUP BY
        if (queryBuilder.getGroupBy() != null && !queryBuilder.getGroupBy().isEmpty()) {
            firstQuery.append(" GROUP BY ");
            firstQuery.append(queryBuilder.getGroupBy().stream()
                    .map(field -> "\"" + field.trim() + "\"")
                    .collect(Collectors.joining(", ")));
        }

        firstQuery.append(")");

        // 返回第一次查询的SQL
        return firstQuery.toString();
    }

    /**
     * 构建只有time字段排序的查询
     * 这种情况下使用标准查询
     *
     * @param timeOrderBy time字段排序列表
     * @return 构建的SQL查询字符串
     */
    private String buildTimeOnlyOrderQuery(List<OrderBy> timeOrderBy) {
        // 使用标准查询
        String baseQuery = super.buildQuery();

        // 检查是否需要分页查询
        if (needsPagination()) {
            return addPaginationLayer(baseQuery);
        }

        return baseQuery;
    }

    /**
     * 构建只有非time字段排序的查询
     * 这种情况下使用多层嵌套查询方法
     * 当需要分页时，添加额外的分页查询层
     *
     * @param nonTimeOrderBy 非time字段排序列表
     * @return 构建的SQL查询字符串
     */
    private String buildNonTimeOnlyOrderQuery(List<OrderBy> nonTimeOrderBy) {
        // 构建基本的多层嵌套查询
        String baseQuery = buildMultiLevelSelectorQuery(nonTimeOrderBy);

        // 检查是否需要分页查询
        if (needsPagination()) {
            // 添加分页层
            return addNonTimeOrderPaginationLayer(baseQuery);
        }

        return baseQuery;
    }

    /**
     * 构建多层嵌套查询，每个非time排序字段对应一层
     *
     * @param orderByList 非time排序字段列表
     * @return 构建的SQL查询字符串
     */
    private String buildMultiLevelSelectorQuery(List<OrderBy> orderByList) {
        if (orderByList == null || orderByList.isEmpty()) {
            return super.buildQuery();
        }

        // 构建最内层查询
        StringBuilder innerQuery = new StringBuilder("SELECT ");
        appendAggregations(innerQuery);
        appendMeasurement(innerQuery);
        innerQuery.append(buildWhere());
        appendGroupBy(innerQuery);
        appendSpecificInterval(innerQuery);

        // 递归构建嵌套查询，从最内层开始
        String currentQuery = innerQuery.toString();

        // 如果有分页，使用更大的限制数，以确保分页后有足够的数据
        // 如果没有分页，使用原始的limit
        int topLimit;
        if (needsPagination()) {
            // 当需要分页时，在top/bottom函数中使用更大的限制数
            // 计算方式：limit + offset，以确保有足够的数据进行分页
            Long limit = queryBuilder.getLimit();
            Long offset = queryBuilder.getOffset();
            topLimit = (limit != null ? limit.intValue() : 5) + (offset != null ? offset.intValue() : 0);
            // 不再设置最小值，确保topLimit始终等于limit+offset
        } else {
            // 如果不需要分页，使用原始的limit
            topLimit = queryBuilder.getLimit() != null ? queryBuilder.getLimit().intValue() : 5; // 默认取5条
        }

        // 从最后一个排序字段开始，逐层向外构建
        for (int i = orderByList.size() - 1; i >= 0; i--) {
            OrderBy order = orderByList.get(i);
            String field = order.getField();
            boolean isAsc = order.isAsc();

            // 构建当前层的查询
            StringBuilder currentLayerQuery = new StringBuilder("SELECT ");

            // 根据排序方向选择函数
            String selectorFunction = isAsc ? "bottom" : "top";
            currentLayerQuery.append(selectorFunction).append("(\"").append(field).append("\", ").append(topLimit).append(") AS \"").append(field).append("\", ");
            currentLayerQuery.append("* ");
            currentLayerQuery.append("FROM (").append(currentQuery).append(")");
            // 更新当前查询为新构建的层
            currentQuery = currentLayerQuery.toString();
        }
        currentQuery = currentQuery + " LIMIT " + topLimit;

        return currentQuery;
    }

    /**
     * 在外层查询中添加时间范围条件
     */
    private void appendTimeRangeConditions(StringBuilder sql) {
        // 使用AbstractQueryBuilder中的通用方法
        AbstractQueryBuilder.appendTimeRangeConditions(sql, queryBuilder);
    }

    /**
     * 检查是否需要分页查询
     * 当limit和offset不为0时，需要添加分页查询
     *
     * @return 是否需要分页查询
     */
    private boolean needsPagination() {
        return (queryBuilder.getLimit() != null && queryBuilder.getLimit() > 0) ||
                (queryBuilder.getOffset() != null && queryBuilder.getOffset() > 0);
    }

    /**
     * 添加分页查询层
     * 在原查询外层添加一层子查询，并应用LIMIT和OFFSET
     * 在OpenGemini中，当GROUP BY和LIMIT同时在一个查询中使用时会导致查询结果不正确
     * 因此需要先在子查询中应用LIMIT和OFFSET，然后在外层查询中应用GROUP BY
     * 在LIMIT和OFFSET之前添加所有ORDER BY字段（除了time字段），确保分页结果的排序一致性
     *
     * @param baseQuery 原始查询语句
     * @return 添加分页后的查询语句
     */
    private String addPaginationLayer(String baseQuery) {
        // 先在内层查询中添加LIMIT和OFFSET
        StringBuilder innerQuery = new StringBuilder();
        innerQuery.append(baseQuery);

        // 如果原查询中已经有GROUP BY，则需要先将其包裹在子查询中
        StringBuilder paginatedQuery = new StringBuilder();
        paginatedQuery.append("SELECT * FROM (").append(innerQuery).append(") ");

        // 添加时间范围条件
        appendTimeRangeConditions(paginatedQuery);

        // 在LIMIT和OFFSET之前添加所有非time字段的ORDER BY
        List<OrderBy> orderBy = queryBuilder.getOrderBy();
        if (orderBy != null && !orderBy.isEmpty()) {
            // 过滤出非time字段的排序
            List<OrderBy> nonTimeOrderBy = orderBy.stream()
                    .filter(order -> !"time".equalsIgnoreCase(order.getField()))
                    .collect(Collectors.toList());

            if (!nonTimeOrderBy.isEmpty()) {
                paginatedQuery.append(" ORDER BY ");
                paginatedQuery.append(nonTimeOrderBy.stream()
                        .map(order -> "\"" + order.getField().trim() + "\" " + (order.isAsc() ? "ASC" : "DESC"))
                        .collect(Collectors.joining(", ")));
            }
        }

        // 添加LIMIT和OFFSET
        Long openGeminiLimit = queryBuilder.getOpenGeminiLimit();
        if (openGeminiLimit != null && openGeminiLimit > 0) {
            paginatedQuery.append(" LIMIT ").append(openGeminiLimit);
        }
        Long opengeminiOffset = queryBuilder.getOpengeminiOffset();
        if (opengeminiOffset != null && opengeminiOffset > 0) {
            paginatedQuery.append(" OFFSET ").append(opengeminiOffset);
        }

        // 在最外层添加另一层查询，并应用GROUP BY
        StringBuilder finalQuery = new StringBuilder();
        finalQuery.append("SELECT * FROM (").append(paginatedQuery).append(") ");

        // 添加时间范围条件
        appendTimeRangeConditions(finalQuery);

        // 在外层添加GROUP BY语句，保持与内层一致
        if (queryBuilder.getGroupBy() != null && !queryBuilder.getGroupBy().isEmpty()) {
            finalQuery.append(" GROUP BY ");
            finalQuery.append(queryBuilder.getGroupBy().stream()
                    .map(field -> "\"" + field.trim() + "\"")
                    .collect(Collectors.joining(", ")));
        }

        // 在最外层添加time字段的ORDER BY（如果有的话）
        if (orderBy != null && !orderBy.isEmpty()) {
            // 过滤出time字段的排序
            List<OrderBy> timeOrderBy = orderBy.stream()
                    .filter(order -> "time".equalsIgnoreCase(order.getField()))
                    .collect(Collectors.toList());

            if (!timeOrderBy.isEmpty()) {
                finalQuery.append(" ORDER BY ");
                finalQuery.append(timeOrderBy.stream()
                        .map(order -> "\"time\" " + (order.isAsc() ? "ASC" : "DESC"))
                        .collect(Collectors.joining(", ")));
            }
        }

        return finalQuery.toString();
    }

    /**
     * 为非time字段排序的查询添加分页查询层
     * 在原查询外层添加一层子查询，并应用LIMIT和OFFSET
     * 这个方法专门处理非time字段排序的情况，与addPaginationLayer方法类似，但结构更适合top/bottom函数查询
     *
     * @param baseQuery 原始查询语句（包含top/bottom函数的嵌套查询）
     * @return 添加分页后的查询语句
     */
    private String addNonTimeOrderPaginationLayer(String baseQuery) {
        // 构建外层查询，包含分页参数
        StringBuilder paginatedQuery = new StringBuilder();
        paginatedQuery.append("SELECT * FROM (");
        paginatedQuery.append(baseQuery);
        paginatedQuery.append(") ");

        // 添加时间范围条件
        appendTimeRangeConditions(paginatedQuery);

        // 添加LIMIT和OFFSET
        Long limit = queryBuilder.getLimit();
        Long offset = queryBuilder.getOffset();

        if (limit != null && limit > 0) {
            paginatedQuery.append(" LIMIT ").append(limit);
        }

        if (offset != null && offset > 0) {
            paginatedQuery.append(" OFFSET ").append(offset);
        }

        return paginatedQuery.toString();
    }
}