package com.databuff.dao.mysql;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.databuff.entity.XxlJobLog;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

@Mapper
@Repository
public interface XxlJobLogMapper extends BaseMapper<XxlJobLog> {

    @Delete("Delete From xxl_job_log WHERE DATE(trigger_time) <= DATE(DATE_SUB(NOW(),INTERVAL 2 day))")
    void clearLog();

}
