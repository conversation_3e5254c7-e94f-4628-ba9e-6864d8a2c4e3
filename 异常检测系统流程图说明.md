# DataBuff SkyWalking 异常检测系统流程图说明

## 概述

本文档包含了DataBuff SkyWalking异常检测系统的两个重要流程图：
1. **整体架构流程图** - 展示系统的完整数据流和组件关系
2. **五种检测方法对比流程图** - 详细对比五种异常检测方法的处理流程

## 整体架构流程图说明

### 系统分层架构

#### 1. 数据输入层 (蓝色)
- **指标数据**: 原始的时间序列指标数据
- **MetricAggregator**: 指标聚合器，负责数据预处理和聚合
- **聚合时间序列数据**: 经过聚合处理的时间序列数据，格式为`Map<Map, Map<Object, Double>>`

#### 2. 检测引擎层 (紫色)
- **检测方式选择**: 根据配置的`way`参数选择具体的检测方法
- **五种检测器**: 
  - ThresholdAlarmCheck (阈值检测)
  - DynamicBaselineCheck (动态基线检测)
  - MutationCheck (波动检测)
  - StatusAlarmCheck (状态检测)
  - ChangePointAlarmCheck (突变检测)

#### 3. 事件生成层 (绿色)
- **EventEntity创建**: 将检测结果封装为事件实体
- **事件属性设置**: 设置状态、值、阈值等属性
- **检测方式标记**: 使用DetectionType枚举标记检测方式

#### 4. 数据处理层 (绿色)
- **无数据处理**: 处理无数据场景的告警逻辑
- **标签增强**: 为事件添加更多的标签信息
- **Redis计数器**: 记录无数据累积次数
- **本地缓存**: 缓存标签查询结果

#### 5. 输出层 (橙色)
- **事件映射**: 将处理结果转换为标准格式
- **告警事件**: 最终发送到消息队列的告警事件

### 外部依赖 (粉色)
- **Redis缓存**: 存储基线数据、计数器等
- **外部算法服务**: 突变检测使用的算法API
- **分布式锁**: 确保基线计算的线程安全

### 关键数据流

1. **数据输入流**: 指标数据 → MetricAggregator → 聚合数据 → 检测引擎
2. **检测处理流**: 检测引擎 → 具体检测器 → 事件生成 → 数据处理
3. **结果输出流**: 数据处理 → 事件映射 → 告警事件输出
4. **缓存交互流**: 检测器 ↔ Redis缓存 ↔ 外部服务

## 五种检测方法对比流程图说明

### 颜色编码说明

- **红色系**: 阈值检测 (ThresholdAlarmCheck)
- **绿色系**: 动态基线检测 (DynamicBaselineCheck)  
- **橙色系**: 波动检测 (MutationCheck)
- **蓝色系**: 状态检测 (StatusAlarmCheck)
- **紫色系**: 突变检测 (ChangePointAlarmCheck)
- **灰色系**: 通用流程

### 各检测方法特点对比

#### 阈值检测 (红色)
- **适用场景**: 固定阈值监控，如CPU使用率、内存使用率
- **核心特点**: 简单直接，支持多种聚合方式和比较运算符
- **关键步骤**: 时间聚合 → 阈值比较 → 连续性检查 → 状态判断
- **配置参数**: critical/warning阈值、comparison运算符、continuous连续性

#### 动态基线检测 (绿色)
- **适用场景**: 具有周期性规律的指标，如业务流量、API调用量
- **核心特点**: 基于历史数据动态计算基线，适应业务变化
- **关键步骤**: 缓存检查 → 基线计算 → 偏离度计算 → 异常判断
- **数据要求**: 至少2016个历史数据点（7天*20%数据量）

#### 波动检测 (橙色)
- **适用场景**: 关注数据变化趋势，如同比增长、环比变化
- **核心特点**: 比较当前与历史数据的变化幅度
- **关键步骤**: 历史数据获取 → 数据聚合 → 波动计算 → 阈值判断
- **计算方式**: valUp/valDown(绝对变化)、yoyUp/yoyDown(相对变化)

#### 状态检测 (蓝色)
- **适用场景**: 状态类指标监控，如服务健康状态、资源状态
- **核心特点**: 统计异常状态数量，支持多级状态分类
- **关键步骤**: 数据完整性检查 → 状态分类统计 → 计数比较 → 状态判断
- **状态分级**: 0=正常、1=警告、2=严重、3=致命

#### 突变检测 (紫色)
- **适用场景**: 检测时间序列中的突然变化点，如异常流量突增
- **核心特点**: 依赖外部算法服务，支持复杂的统计学算法
- **关键步骤**: 数据格式化 → API调用 → 结果解析 → 事件创建
- **外部依赖**: 需要配置外部算法服务URL和超时参数

### 通用处理流程

所有检测方法都遵循统一的后处理流程：

1. **EventEntity创建**: 将检测结果封装为标准事件对象
2. **检测方式标记**: 使用DetectionType枚举标识检测方法
3. **事件属性填充**: 设置value、threshold、status、abnormalTime等属性
4. **结果返回**: 返回`Map<Object, Object>`格式的检测结果

### 状态码统一标准

所有检测方法使用统一的状态码：
- **0**: 正常状态，无需告警
- **2**: 次要告警，warning级别触发
- **3**: 重要告警，critical级别触发

### 配置参数对比

| 检测方法 | 核心参数 | 特殊参数 | 外部依赖 |
|---------|---------|---------|---------|
| 阈值检测 | critical/warning、comparison | continuous、continuous_n | 无 |
| 动态基线 | critical/warning、baselineScope | 历史数据要求 | Redis缓存 |
| 波动检测 | critical/warning、fluctuate | comparePeriod | 无 |
| 状态检测 | critical/warning | fullWindow | 无 |
| 突变检测 | apiUrl、timeout | defTimeOffset | 外部算法API |

### 性能特点对比

| 检测方法 | 计算复杂度 | 内存占用 | 响应时间 | 准确性 |
|---------|-----------|---------|---------|--------|
| 阈值检测 | 低 | 低 | 快 | 中等 |
| 动态基线 | 高 | 中等 | 中等 | 高 |
| 波动检测 | 中等 | 中等 | 中等 | 中等 |
| 状态检测 | 低 | 低 | 快 | 中等 |
| 突变检测 | 高 | 低 | 慢 | 高 |

## 流程图使用指南

### 如何选择检测方法

1. **固定阈值场景**: 选择阈值检测，配置简单，响应快速
2. **周期性业务指标**: 选择动态基线检测，自适应能力强
3. **关注变化趋势**: 选择波动检测，支持多种变化计算方式
4. **状态类监控**: 选择状态检测，专门处理状态统计
5. **复杂异常模式**: 选择突变检测，依赖专业算法

### 配置最佳实践

1. **阈值设置**: critical > warning，避免告警风暴
2. **时间窗口**: 根据业务特点选择合适的检测周期
3. **连续性检查**: 对于波动较大的指标启用连续性检查
4. **缓存策略**: 合理设置缓存TTL，平衡性能和准确性
5. **外部依赖**: 配置合适的超时时间和重试机制

### 监控和调优

1. **性能监控**: 关注各检测方法的执行时间和资源消耗
2. **准确性评估**: 定期评估检测结果的准确性和误报率
3. **参数调优**: 根据业务反馈调整阈值和检测参数
4. **容量规划**: 根据数据量和检测频率进行容量规划
