package com.databuff.common.utils;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.alibaba.ttl.threadpool.TtlExecutors;
import com.databuff.common.exception.ExceptionHandlingRunnable;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class ThreadLocalExample {
    private static final ThreadLocal<String> threadLocal = new ThreadLocal<>();
    // 创建一个 InheritableThreadLocal 实例
    private static final InheritableThreadLocal<String> inheritableThreadLocal = new InheritableThreadLocal<>();

    public static ScheduledExecutorService scheduled = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread t = new Thread(r);
        t.setName("Scheduled-AuthGroupLoop");
        return t;
    });

    private static final TransmittableThreadLocal<String> USER_ID_THREAD_LOCAL = new TransmittableThreadLocal<>();

    private static final ExecutorService executorService = TtlExecutors.getTtlExecutorService(Executors.newFixedThreadPool(10));

    public static void processRequest(String userId) {
        USER_ID_THREAD_LOCAL.set(userId);

        executorService.submit(() -> {
            try {
                // 获取并打印用户ID
                String currentUserId = USER_ID_THREAD_LOCAL.get();
                System.out.println("User ID in async task: " + currentUserId);
            } finally {
                // 清理ThreadLocal值
//                USER_ID_THREAD_LOCAL.remove();
            }
        });
    }


    public static void main(String[] args) {
        // 设置 ThreadLocal 变量的值
        threadLocal.set("Hello, ThreadLocal!");
        // 设置主线程的值
        inheritableThreadLocal.set("Hello, InheritableThreadLocal!");

        // 获取 ThreadLocal 变量的值
        String value = threadLocal.get();
        System.out.println("ThreadLocal Value: " + value);

        String value3 = inheritableThreadLocal.get();
        System.out.println(" InheritableThreadLocal Value: " + value3);

        // 创建两个线程
        Thread t1 = new Thread(() -> {
            System.out.println("ThreadLocal 1: " + threadLocal.get());
            System.out.println("InheritableThreadLocal 1: " + inheritableThreadLocal.get());
        });
        // 启动线程
        t1.start();


        // 创建一个线程池
        ExecutorService executorService = Executors.newFixedThreadPool(1);

        // 提交一个任务
        executorService.submit(() -> {
            System.out.println("Child Thread Value1: " + threadLocal.get());
            // 子线程获取值
            System.out.println("Child Thread Value2: " + inheritableThreadLocal.get());
            inheritableThreadLocal.remove();
        });

        // 关闭线程池
        executorService.shutdown();

        scheduled.scheduleAtFixedRate(new ExceptionHandlingRunnable(() -> {
            System.out.println("Scheduled Thread Value1: " + threadLocal.get());
            System.out.println("Scheduled Thread Value2: " + inheritableThreadLocal.get());
        }), 1, 10, TimeUnit.SECONDS);


        threadLocal.remove();
        inheritableThreadLocal.remove();

        System.out.println("ThreadLocal end: " + threadLocal.get());
        System.out.println("inheritableThreadLocal end: " + inheritableThreadLocal.get());


        List<String> list = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            list.add(i + "");
        }
        list.parallelStream().forEach(ThreadLocalExample::processRequest);


    }

}
