# DataBuff SkyWalking 异常检测类代码集合

本文档包含了DataBuff SkyWalking告警系统中五种异常检测方法的完整代码实现。

## 目录

1. [基础接口和抽象类](#基础接口和抽象类)
2. [阈值检测 (ThresholdAlarmCheck)](#阈值检测)
3. [动态基线检测 (DynamicBaselineCheck)](#动态基线检测)
4. [波动检测 (MutationCheck)](#波动检测)
5. [状态检测 (StatusAlarmCheck)](#状态检测)
6. [突变检测 (ChangePointAlarmCheck)](#突变检测)
7. [基线计算工具类 (CalculateBaseline)](#基线计算工具类)
8. [事件实体类 (EventEntity)](#事件实体类)

## 基础接口和抽象类

### AlarmCheckOperatorV2 接口

```java
package com.databuff.metric;

import com.databuff.common.tsdb.dto.detect.DetectQueryRequest;
import com.databuff.common.tsdb.dto.explore.QueryRequest;
import com.databuff.entity.DatabuffMonitor;

import java.util.Collection;
import java.util.Map;
import java.util.Set;

public interface AlarmCheckOperatorV2 {

    /**
     * 检查结果后执行进一步的处理。
     * <p>
     * 它接收监控对象、聚合后的时序数据、查询请求、指标聚合器以及查询集合，
     * 并返回一个包含处理结果的映射。
     * </p>
     *
     * @param monitor           包含监控信息的对象。
     * @param map               聚合后的时序数据，键为时间序列，值为对应的指标值。
     * @param detectQueryRequest 包含检测查询信息的请求对象。
     * @param metricAggregator  用于执行指标聚合的工具类。
     * @param queries           查询集合。
     * @return 包含处理结果的映射。
     */
    Map<Object, Object> afterCheckResult(DatabuffMonitor monitor, Map<Map, Map<Object, Double>> map, DetectQueryRequest detectQueryRequest, MetricAggregator metricAggregator, Collection<QueryRequest> queries);

    /**
     * 无数据的情况下执行检查逻辑。
     * <p>
     * 它接收监控对象、键、无数据结果集合以及查询请求，
     * 并返回一个包含处理结果的映射。
     * </p>
     *
     * @param monitor           包含监控信息的对象。
     * @param key               唯一标识符，用于标记无数据的情况。
     * @param map               无数据结果集合。
     * @param detectQueryRequest 包含检测查询信息的请求对象。
     * @return 包含处理结果的映射。
     */
    Map<Object, Object> afterCheckNoDataResult(DatabuffMonitor monitor, String key, Set<Map<String, String>> map, DetectQueryRequest detectQueryRequest);

    /**
     * 此方法用于根据提供的查询向 eventsMap 添加更多标签。
     * 它迭代查询，对于每个查询，它检索相应的 MetricsQuery 对象。
     * 如果 MetricsQuery 对象不为 null，则它会检索数据库名称，如果不是默认数据库，则会将 apiKey 附加到该名称。
     * 然后它会迭代 eventsMap 中的条目。对于每个条目，它检查 EventEntity 对象是否不为 null 并且其状态是否大于 0。
     * 如果满足条件，它会根据 eventTag 条目创建 JSONObject 的集合，并创建一个 whereCondition HashSet。
     * 然后检查 redisKey 是否存在于 localTagCache 中。如果是，则会将缓存中的所有条目添加到 eventTag。
     * 如果没有，它会调用 listMetricMonitorTagValues 方法来检索标签值的映射并将它们添加到 eventTag。
     * 然后将 eventTag 放入 localTagCache。
     *
     * @param eventsMap 要处理的事件的映射。
     * @param queries   用于处理事件的查询列表。
     * @param gids
     * @return 带有附加标签的处理后的 eventsMap。
     */
    Map<Object, Object> needMoreTags(Map<Object, Object> eventsMap, Collection<QueryRequest> queries, Collection<String> gids);

}
```

## 阈值检测

### ThresholdAlarmCheck 类

```java
package com.databuff.metric.impl.alarmV2;

import com.databuff.common.tsdb.dto.detect.DetectQueryRequest;
import com.databuff.common.tsdb.dto.explore.QueryRequest;
import com.databuff.common.tsdb.dto.preview.ThresholdsDTO;
import com.databuff.entity.DatabuffMonitor;
import com.databuff.entity.EventEntity;
import com.databuff.metric.AlarmCheckOperatorV2;
import com.databuff.metric.MetricAggregator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Component
@Slf4j
public class ThresholdAlarmCheck extends BaseAlarmCheckV2 implements AlarmCheckOperatorV2 {

    private static final String SEPARATOR = ":";
    private static final String REDIS_PREFIX = "monitor:metric:";
    private static final int CRITICAL_STATUS = 3;
    private static final int WARNING_STATUS = 2;
    private static final int NORMAL_STATUS = 0;

    @Override
    public Map<Object, Object> afterCheckResult(DatabuffMonitor monitor, Map<Map, Map<Object, Double>> aggTimeSeries, DetectQueryRequest detectQueryRequest, MetricAggregator metricAggregator, Collection<QueryRequest> queries) {
        Map<Object, Object> ret = new HashMap<>(16);
        if (CollectionUtils.isEmpty(aggTimeSeries)) {
            return ret;
        }

        final Boolean lessDataTimeframe = detectQueryRequest.getLessDataTimeframe();
        if (lessDataTimeframe != null && lessDataTimeframe) {
            aggTimeSeries = this.filterAggTimeSeries(aggTimeSeries);
        }

        if (CollectionUtils.isEmpty(aggTimeSeries)) {
            return ret;
        }

        final String timeAggregator = detectQueryRequest.getTimeAggregator();
        final String comparison = detectQueryRequest.getComparison();
        final Boolean continuous = detectQueryRequest.getContinuous();
        final Integer continuous_n = detectQueryRequest.getContinuousN();
        final ThresholdsDTO thresholds = detectQueryRequest.getThresholds();

        /** 核心处理：获取符合阈值条件的聚合值
         *  参数说明：
         *  - timeAggregator: 时间维度聚合方式
         *  - comparison: 比较运算符(>,<等)
         *  - continuous: 是否要求连续触发
         *  - continuous_n: 连续触发次数阈值 */
        Map<Map, Map.Entry> aggValue = this.getAggValue(timeAggregator, aggTimeSeries, comparison, continuous, continuous_n, thresholds);
        if (CollectionUtils.isEmpty(aggValue)) {
            return ret;
        }

        // 检测方法
        final String way = detectQueryRequest.getWay();
        // 将检索到的"way"字符串转换为`EventEntity.DetectionType`类型的枚举值
        final EventEntity.DetectionType detectionType = EventEntity.DetectionType.valueOf(way);

        for (Map.Entry<Map, Map.Entry> entry : aggValue.entrySet()) {
            if (entry == null) {
                continue;
            }
            final Map<String, String> lineDataKey = entry.getKey();
            final Map.Entry<Long, Double> timeValue = entry.getValue();
            final EventEntity eventEntity = judgeThresholdStatus(thresholds, comparison, timeValue);
            if (eventEntity == null) {
                continue;
            }
            eventEntity.setWay(detectionType);
            ret.put(lineDataKey, eventEntity);
        }
        return ret;
    }

    /**
     * 阈值状态判断核心方法
     *
     * @param thresholds 包含critical/warning阈值的JSON对象
     * @param comparison 比较运算符(支持>/>=/</<=/==)
     * @param timeValue  指标数据Entry<时间戳, 值>
     * @return 包含三种状态的事件实体: - 3(重要)/2(次要)/0(正常)
     **/
    public EventEntity judgeThresholdStatus(ThresholdsDTO thresholds, String comparison, Map.Entry<Long, Double> timeValue) {
        final Double critical = thresholds.getCritical() == null ? null : thresholds.getCritical().doubleValue();
        final Double warning = thresholds.getWarning() == null ? null : thresholds.getWarning().doubleValue();
        if (critical == null && warning == null) {
            log.warn("阈值配置错误：critical和warning至少需要配置一个");
            return null;
        }

        final Double value = timeValue.getValue();
        final Long timestamp = timeValue.getKey();

        /** 阈值判断逻辑：
         * 1. 根据比较运算符选择条件检查策略
         * 2. 优先检查critical阈值
         * 3. 当critical阈值未触发时检查warning阈值
         * */
        int status = NORMAL_STATUS;
        String threshold = "";
        boolean conditionMet = false;

        switch (comparison) {
            case ">":
                conditionMet = checkCondition(value, critical, warning, (v, t) -> v > t);
                break;
            case ">=":
                conditionMet = checkCondition(value, critical, warning, (v, t) -> v >= t);
                break;
            case "<":
                conditionMet = checkCondition(value, critical, warning, (v, t) -> v < t);
                break;
            case "<=":
                conditionMet = checkCondition(value, critical, warning, (v, t) -> v <= t);
                break;
            default: // "=="
                conditionMet = checkCondition(value, critical, warning, (v, t) -> v.equals(t));
                break;
        }

        /** 状态分级逻辑：
         * - 当critical阈值存在且满足时，标记为重要告警(3)
         * - 否则当warning阈值存在且满足时，标记为次要告警(2)
         * - 都不满足时保持正常状态(0)
         * */
        if(conditionMet){
            status = (critical != null && checkCriticalCondition(value, critical, comparison)) ? CRITICAL_STATUS : WARNING_STATUS;
            threshold = (status == CRITICAL_STATUS) ? critical.toString() : warning.toString();
        }

        return EventEntity.builder()
                .way(EventEntity.DetectionType.threshold)
                .value(value)
                .status(status)
                .threshold(threshold)
                .abnormalTime(timestamp)
                .comparison(comparison)
                .build();
    }

    // 辅助方法：检查条件
    private boolean checkCondition(Double value, Double critical, Double warning, java.util.function.BiPredicate<Double, Double> predicate) {
        if (critical != null && predicate.test(value, critical)) {
            return true;
        }
        return warning != null && predicate.test(value, warning);
    }

    // 辅助方法：检查critical条件
    private boolean checkCriticalCondition(Double value, Double critical, String comparison) {
        switch (comparison) {
            case ">": return value > critical;
            case ">=": return value >= critical;
            case "<": return value < critical;
            case "<=": return value <= critical;
            default: return value.equals(critical);
        }
    }

    @Override
    public Map<Object, Object> afterCheckNoDataResult(DatabuffMonitor monitor, String mKey, Set<Map<String, String>> map, DetectQueryRequest detectQueryRequest) {
        Map<Object, Object> ret = new HashMap<>(16);
        // 无数据处理逻辑...
        return ret;
    }
}
```

## 动态基线检测

### DynamicBaselineCheck 类

```java
package com.databuff.metric.impl.alarmV2;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.databuff.common.tsdb.dto.detect.DetectQueryRequest;
import com.databuff.common.tsdb.dto.explore.QueryRequest;
import com.databuff.common.tsdb.dto.preview.ThresholdsDTO;
import com.databuff.entity.DatabuffMonitor;
import com.databuff.entity.EventEntity;
import com.databuff.metric.AlarmCheckOperatorV2;
import com.databuff.metric.MetricAggregator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 动态基线检测类
 * 基于历史数据计算动态基线，检测当前值是否偏离基线超过设定阈值
 * @author:yuzhili
 * @date: 2024/01/09
 */
@Component
@Slf4j
public class DynamicBaselineCheck extends BaseAlarmCheckV2 implements AlarmCheckOperatorV2 {

    private static final String SEPARATOR = ":";
    private static final String REDIS_PREFIX = "monitor:metric:";

    @Autowired
    private CalculateBaseline calculateBaseline;

    @Override
    public Map<Object, Object> afterCheckResult(DatabuffMonitor monitor, Map<Map, Map<Object, Double>> aggTimeSeries, DetectQueryRequest detectQueryRequest, MetricAggregator metricAggregator, Collection<QueryRequest> queries) {
        this.metricAggregator = metricAggregator;
        Map<Object, Object> ret = new HashMap<>(16);
        if (CollectionUtils.isEmpty(aggTimeSeries)) {
            return ret;
        }

        final Boolean lessDataTimeframe = detectQueryRequest.getLessDataTimeframe();
        if (lessDataTimeframe != null && lessDataTimeframe) {
            aggTimeSeries = this.filterAggTimeSeries(aggTimeSeries);
        }

        if (CollectionUtils.isEmpty(aggTimeSeries)) {
            return ret;
        }

        //要求周期内完整数据
        final boolean fullWindow = detectQueryRequest.getRequireFullWindow();
        final long period = detectQueryRequest.getPeriod();

        // 删除不满足条件的数据
        for (Iterator<Map.Entry<Map, Map<Object, Double>>> iterator = aggTimeSeries.entrySet().iterator(); iterator.hasNext(); ) {
            Map.Entry<Map, Map<Object, Double>> entry = iterator.next();
            if (entry == null || entry.getKey() == null || entry.getValue() == null) {
                iterator.remove();
                continue;
            }
            Map<Object, Double> retMap = entry.getValue();
            if (CollectionUtils.isEmpty(retMap)) {
                iterator.remove();
                continue;
            }
            if (fullWindow && !dataIntegrityCheck(period, entry)) {
                iterator.remove();
            }
        }

        final String comparison = detectQueryRequest.getComparison();
        final double baselineScope = detectQueryRequest.getBaselineScope();
        // 阈值
        final ThresholdsDTO thresholds = detectQueryRequest.getThresholds();

        // 根据时间聚合器和比较符获取分组的聚合值
        Map<Map, EventEntity> aggValue = this.getAggValue(aggTimeSeries, comparison, thresholds, baselineScope, queries);
        if (CollectionUtils.isEmpty(aggValue)) {
            return ret;
        }
        // 检测方法
        final String way = detectQueryRequest.getWay();
        // 将检索到的"way"字符串转换为`EventEntity.DetectionType`类型的枚举值
        final EventEntity.DetectionType detectionType = EventEntity.DetectionType.valueOf(way);
        // 对于每个 `EventEntity`，调用 `setWay` 方法，并以 `detectionType` 作为参数
        aggValue.values().forEach(eventEntity -> eventEntity.setWay(detectionType));
        ret.putAll(aggValue);
        return ret;
    }

    /**
     * 动态基线检测核心方法
     *
     * @param aggTimeSeries 聚合时间序列数据
     * @param comparison 比较运算符
     * @param thresholds 阈值配置
     * @param baselineScope 基线范围
     * @param queries 查询请求集合
     * @return 检测结果映射
     */
    protected Map<Map, EventEntity> getAggValue(Map<Map, Map<Object, Double>> aggTimeSeries, String comparison, ThresholdsDTO thresholds, double baselineScope, Collection<QueryRequest> queries) {
        Map<Map, EventEntity> result = new HashMap<>();

        for (Map.Entry<Map, Map<Object, Double>> lineData : aggTimeSeries.entrySet()) {
            if (lineData == null || lineData.getValue() == null || CollectionUtils.isEmpty(lineData.getValue().values())) {
                continue;
            }

            /**
             * lineDataKey 指的是时间序列对应的标签集合（触发对象）
             * @see {"host":"host193","source":"DataBuff","host_id":"fa9880f4582d8076a2c2a76290a8ed71"}
             */
            final Map<String, String> lineDataKey = lineData.getKey();
            final String group = lineDataKey.entrySet().stream()
                    .filter(Objects::nonNull)
                    .map(i -> i.getKey() + SEPARATOR + i.getValue())
                    .reduce((a, b) -> a + SEPARATOR + b)
                    .orElse(null);
            if (group == null) {
                log.warn("找不到触发对象，不计算动态基线");
                continue;
            }
            final String keyStr = REDIS_PREFIX + "baseline" + SEPARATOR + group + SEPARATOR + comparison + SEPARATOR + baselineScope;
            final String baselineJson = jedisService.getJson(keyStr);
            if (baselineJson == null) {
                calculateBaseline.processAsync(keyStr, queries, lineDataKey, metricAggregator, comparison, baselineScope);
                continue;
            }

            final JSONObject baselineResult = JSON.parseObject(baselineJson);
            if (baselineResult == null) {
                continue;
            }

            final double baseline = baselineResult.getDoubleValue("baseline");
            final double upperBound = baselineResult.getDoubleValue("upperBound");
            final double lowerBound = baselineResult.getDoubleValue("lowerBound");
            final double num = baselineResult.getDoubleValue("num");

            if (num < 2016 || baseline <= 0) {
                log.debug("基线数据不足，跳过检测");
                continue;
            }

            // 获取当前值
            final Collection<Double> values = lineData.getValue().values();
            final Double currentValue = values.stream()
                    .filter(Objects::nonNull)
                    .findFirst()
                    .orElse(null);

            if (currentValue == null) {
                continue;
            }

            // 计算偏离程度
            double deviation = Math.abs(currentValue - baseline) / baseline;

            EventEntity eventEntity = EventEntity.builder()
                    .way(EventEntity.DetectionType.baseline)
                    .value(currentValue)
                    .originalValue(baseline)
                    .build();

            // 判断是否超过阈值
            final Double critical = thresholds.getCritical() == null ? null : thresholds.getCritical().doubleValue();
            final Double warning = thresholds.getWarning() == null ? null : thresholds.getWarning().doubleValue();

            if (critical != null && deviation > critical) {
                eventEntity.setStatus(3);
                eventEntity.setThreshold(String.valueOf(critical));
            } else if (warning != null && deviation > warning) {
                eventEntity.setStatus(2);
                eventEntity.setThreshold(String.valueOf(warning));
            } else {
                eventEntity.setStatus(0);
            }

            result.put(lineDataKey, eventEntity);
        }

        return result;
    }

    @Override
    public Map<Object, Object> afterCheckNoDataResult(DatabuffMonitor monitor, String mKey, Set<Map<String, String>> map, DetectQueryRequest detectQueryRequest) {
        Map<Object, Object> ret = new HashMap<>(16);
        // 无数据处理逻辑...
        return ret;
    }
}
```

## 波动检测

### MutationCheck 类

```java
package com.databuff.metric.impl.alarmV2;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.tsdb.dto.detect.DetectQueryRequest;
import com.databuff.common.tsdb.dto.explore.QueryRequest;
import com.databuff.common.tsdb.dto.preview.ThresholdsDTO;
import com.databuff.entity.DatabuffMonitor;
import com.databuff.entity.EventEntity;
import com.databuff.metric.AlarmCheckOperatorV2;
import com.databuff.metric.MetricAggregator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

import static com.databuff.entity.EventEntity.DetectionType.mutation;

/**
 * 波动检测类
 * 通过比较当前周期与历史周期的数据变化，检测异常波动
 * 支持多种波动计算方式：数据增加量、减少量、同比增长、同比下降
 * @author:yuzhili
 * @date: 2024/01/09
 */
@Component
@Slf4j
public class MutationCheck extends BaseAlarmCheckV2 implements AlarmCheckOperatorV2 {

    private static final String SEPARATOR = ":";
    private static final String REDIS_PREFIX = "monitor:metric:";

    @Override
    public Map<Object, Object> afterCheckResult(DatabuffMonitor monitor, Map<Map, Map<Object, Double>> aggTimeSeries, DetectQueryRequest multiQueryDTO, MetricAggregator metricAggregator, Collection<QueryRequest> queries) {
        if (multiQueryDTO == null) {
            return new HashMap<>(16);
        }

        this.metricAggregator = metricAggregator;
        Map<Object, Object> ret = new HashMap<>(16);

        if (CollectionUtils.isEmpty(aggTimeSeries)) {
            return ret;
        }

        final Boolean lessDataTimeframe = multiQueryDTO.getLessDataTimeframe();
        if (lessDataTimeframe != null && lessDataTimeframe) {
            aggTimeSeries = this.filterAggTimeSeries(aggTimeSeries);
        }

        if (CollectionUtils.isEmpty(aggTimeSeries)) {
            return ret;
        }

        final String expr = multiQueryDTO.getExpr();
        final Integer comparePeriod = multiQueryDTO.getComparePeriod();
        final String fluctuate = multiQueryDTO.getFluctuate();

        // 设置时间偏移量，获取历史数据
        for (QueryRequest query : multiQueryDTO.getNonNullQueries()) {
            query.setTimeOffset(query.getTimeOffset() + comparePeriod);
        }

        // 获取历史周期的聚合数据
        final Map<Map, Map<Object, Double>> beforeAggTimeSeries = metricAggregator.aggResult(expr, multiQueryDTO.getNonNullQueriesMap());
        if (CollectionUtils.isEmpty(beforeAggTimeSeries)) {
            return ret;
        }

        //要求周期内完整数据
        final boolean fullWindow = multiQueryDTO.getRequireFullWindow();
        final long period = multiQueryDTO.getPeriod();

        // 删除不满足条件的数据
        for (Iterator<Map.Entry<Map, Map<Object, Double>>> iterator = aggTimeSeries.entrySet().iterator(); iterator.hasNext(); ) {
            Map.Entry<Map, Map<Object, Double>> entry = iterator.next();
            if (entry == null || entry.getKey() == null || entry.getValue() == null) {
                iterator.remove();
                continue;
            }
            Map<Object, Double> retMap = entry.getValue();
            if (CollectionUtils.isEmpty(retMap)) {
                iterator.remove();
                continue;
            }
            if (fullWindow && !dataIntegrityCheck(period, entry)) {
                iterator.remove();
            }
        }

        final String timeAggregator = multiQueryDTO.getTimeAggregator();
        // 阈值
        final ThresholdsDTO thresholds = multiQueryDTO.getThresholds();

        // 根据时间聚合器和比较符获取分组的聚合值
        Map<Map, EventEntity> aggValue = this.getAggValue(timeAggregator, aggTimeSeries, beforeAggTimeSeries, fluctuate, thresholds);
        if (CollectionUtils.isEmpty(aggValue)) {
            return ret;
        }
        ret.putAll(aggValue);
        return ret;
    }

    /**
     * 波动检测核心计算方法
     *
     * @param timeAggregator 时间聚合方式
     * @param aggTimeSeries 当前周期数据
     * @param beforeAggTimeSeries 历史周期数据
     * @param fluctuate 波动计算方式
     * @param thresholds 阈值配置
     * @return 检测结果
     */
    protected Map<Map, EventEntity> getAggValue(final String timeAggregator,
                                                final Map<Map, Map<Object, Double>> aggTimeSeries,
                                                final Map<Map, Map<Object, Double>> beforeAggTimeSeries,
                                                final String fluctuate, ThresholdsDTO thresholds) {
        Map<Map, EventEntity> result = new HashMap<>();
        if (thresholds == null) {
            log.warn("错误的参数配置：continuous_n或thresholds或lineDataValue");
            return result;
        }
        final Double critical = thresholds.getCritical() == null ? null : thresholds.getCritical().doubleValue();
        final Double warning = thresholds.getWarning() == null ? null : thresholds.getWarning().doubleValue();
        if (critical == null && warning == null) {
            log.warn("阈值配置错误：critical和warning至少需要配置一个");
            return null;
        }

        for (Map.Entry<Map, Map<Object, Double>> entry : aggTimeSeries.entrySet()) {
            if (entry == null) {
                continue;
            }
            final Map lineDataKey = entry.getKey();
            final Collection<Double> xValues = entry.getValue().values();

            // 获取对应的历史数据
            final Map<Object, Double> beforeData = beforeAggTimeSeries.get(lineDataKey);
            if (beforeData == null || CollectionUtils.isEmpty(beforeData.values())) {
                continue;
            }
            final Collection<Double> yValues = beforeData.values();

            // 获取最后一个时间戳
            final Long lastTimestamp = entry.getValue().keySet().stream()
                    .filter(Objects::nonNull)
                    .map(k -> Long.valueOf(k.toString()))
                    .max(Long::compareTo)
                    .orElse(System.currentTimeMillis() / 1000L);

            EventEntity eventEntity = EventEntity.builder().way(mutation).build();
            Double xMean;
            Double yMean;
            Double fluctuationValue = 0D;

            // 根据时间聚合方式计算聚合值
            switch (timeAggregator) {
                case "avg":
                case "mean":
                    xMean = xValues.stream().filter(Objects::nonNull).mapToDouble(t -> t).average().getAsDouble();
                    yMean = yValues.stream().filter(Objects::nonNull).mapToDouble(t -> t).average().getAsDouble();
                    fluctuationValue = getFluctuationValue(xMean, yMean, fluctuate);
                    break;
                case "sum":
                    xMean = xValues.stream().filter(Objects::nonNull).mapToDouble(t -> t).sum();
                    yMean = yValues.stream().filter(Objects::nonNull).mapToDouble(t -> t).sum();
                    fluctuationValue = getFluctuationValue(xMean, yMean, fluctuate);
                    break;
                case "max":
                    xMean = xValues.stream().filter(Objects::nonNull).mapToDouble(t -> t).max().getAsDouble();
                    yMean = yValues.stream().filter(Objects::nonNull).mapToDouble(t -> t).max().getAsDouble();
                    fluctuationValue = getFluctuationValue(xMean, yMean, fluctuate);
                    break;
                case "min":
                    xMean = xValues.stream().filter(Objects::nonNull).mapToDouble(t -> t).min().getAsDouble();
                    yMean = yValues.stream().filter(Objects::nonNull).mapToDouble(t -> t).min().getAsDouble();
                    fluctuationValue = getFluctuationValue(xMean, yMean, fluctuate);
                    break;
                default:
                    log.warn("不支持的时间聚合方式: {}", timeAggregator);
                    continue;
            }

            // 根据阈值判断状态
            if (critical != null && fluctuationValue > critical) {
                eventEntity.setStatus(3);
                eventEntity.setThreshold(String.valueOf(critical));
            } else if (warning != null && fluctuationValue > warning) {
                eventEntity.setStatus(2);
                eventEntity.setThreshold(String.valueOf(warning));
            } else {
                eventEntity.setStatus(0);
            }

            eventEntity.setValue(fluctuationValue);
            eventEntity.setAbnormalTime(lastTimestamp);
            result.put(lineDataKey, eventEntity);
        }
        return result;
    }

    /**
     * 波动值计算方法
     *
     * @param currentVal 当前值
     * @param previousVal 历史值
     * @param fluctuateType 波动计算类型
     * @return 计算后的波动值
     */
    private double getFluctuationValue(double currentVal, double previousVal, String fluctuateType) {
        if (fluctuateType == null) {
            return currentVal - previousVal;
        }
        switch (fluctuateType) {
            case "valUp": {
                // 数据增加量
                return currentVal - previousVal;
            }
            case "valDown": {
                // 数据减少量
                return previousVal - currentVal;
            }
            case "yoyUp": {
                // 同比增长率
                return (currentVal - previousVal) / (previousVal == 0 ? 1 : previousVal);
            }
            case "yoyDown": {
                // 同比下降率
                return (previousVal - currentVal) / (previousVal == 0 ? 1 : previousVal);
            }
            default:
                return currentVal - previousVal;
        }
    }

    @Override
    public Map<Object, Object> afterCheckNoDataResult(DatabuffMonitor monitor, String mKey, Set<Map<String, String>> map, DetectQueryRequest detectQueryRequest) {
        Map<Object, Object> ret = new HashMap<>(16);
        // 无数据处理逻辑...
        return ret;
    }
}
```

## 状态检测

### StatusAlarmCheck 类

```java
package com.databuff.metric.impl.alarmV2;

import com.databuff.common.tsdb.dto.detect.DetectQueryRequest;
import com.databuff.common.tsdb.dto.explore.QueryRequest;
import com.databuff.common.tsdb.dto.preview.ThresholdsDTO;
import com.databuff.entity.DatabuffMonitor;
import com.databuff.entity.EventEntity;
import com.databuff.metric.AlarmCheckOperatorV2;
import com.databuff.metric.MetricAggregator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 状态检测业务对象
 * 用于检测状态类指标的异常情况，通过统计异常状态的数量来判断是否触发告警
 *
 * @version 1.0
 * @author:yuzhili
 * @date: 2023/10/19
 * @time: 17:52
 * @since 2.7.0.a
 */
@Component
@Slf4j
public class StatusAlarmCheck extends BaseAlarmCheckV2 implements AlarmCheckOperatorV2 {

    private static final String SEPARATOR = ":";
    private static final String REDIS_PREFIX = "monitor:metric:";

    @Override
    public Map<Object, Object> afterCheckResult(DatabuffMonitor monitor, Map<Map, Map<Object, Double>> aggTimeSeries, DetectQueryRequest detectQueryRequest, MetricAggregator metricAggregator, Collection<QueryRequest> queries) {
        Map<Object, Object> ret = new HashMap<>(16);
        if (CollectionUtils.isEmpty(aggTimeSeries)) {
            return ret;
        }

        final Boolean lessDataTimeframe = detectQueryRequest.getLessDataTimeframe();
        if (lessDataTimeframe != null && lessDataTimeframe) {
            aggTimeSeries = this.filterAggTimeSeries(aggTimeSeries);
        }

        //要求周期内完整数据
        final boolean fullWindow = detectQueryRequest.getRequireFullWindow();
        final long period = detectQueryRequest.getPeriod();

        // 删除不满足条件的数据
        for (Iterator<Map.Entry<Map, Map<Object, Double>>> iterator = aggTimeSeries.entrySet().iterator(); iterator.hasNext(); ) {
            Map.Entry<Map, Map<Object, Double>> entry = iterator.next();
            if (entry == null || entry.getKey() == null || entry.getValue() == null) {
                iterator.remove();
                continue;
            }
            Map<Object, Double> retMap = entry.getValue();
            if (CollectionUtils.isEmpty(retMap)) {
                iterator.remove();
                continue;
            }
            if (fullWindow && !dataIntegrityCheck(period, entry)) {
                iterator.remove();
            }
        }

        // 根据时间聚合器和比较符获取分组的聚合值
        if (CollectionUtils.isEmpty(aggTimeSeries)) {
            return ret;
        }

        final String way = detectQueryRequest.getWay();
        final EventEntity.DetectionType detectionType = EventEntity.DetectionType.valueOf(way);
        final ThresholdsDTO thresholds = detectQueryRequest.getThresholds();

        for (Map.Entry<Map, Map<Object, Double>> entry : aggTimeSeries.entrySet()) {
            if (entry == null) {
                continue;
            }
            final Collection<Double> realValues = entry.getValue().values();
            final EventEntity eventEntity = judgeThresholdStatus(thresholds, realValues);
            if (eventEntity == null) {
                continue;
            }
            eventEntity.setWay(detectionType);
            ret.put(entry.getKey(), eventEntity);
        }
        return ret;
    }

    /**
     * 数据完整性检查
     * 检查在指定周期内是否有足够的数据点
     *
     * @param period   检测周期（秒）
     * @param lineData 时间序列数据
     * @return 数据是否完整
     */
    private boolean dataIntegrityCheck(long period, Map.Entry<Map, Map<Object, Double>> lineData) {
        // 期望的数据点数量 = 周期(秒) / 60
        long expectedCount = period / 60L;

        if (lineData == null || lineData.getValue() == null) {
            return false;
        }

        Map<Object, Double> pointMap = lineData.getValue();
        if (CollectionUtils.isEmpty(pointMap)) {
            return false;
        }

        // 统计非空数据点数量
        long actualCount = pointMap.values().stream()
                .filter(Objects::nonNull)
                .filter(v -> v >= 0)
                .count();

        return actualCount >= expectedCount;
    }

    /**
     * 状态阈值判断核心方法
     * 统计异常状态的数量，与配置的阈值进行比较
     *
     * @param thresholds 阈值配置
     * @param realValues 实际状态值集合
     * @return 事件实体
     */
    public EventEntity judgeThresholdStatus(ThresholdsDTO thresholds, Collection<Double> realValues) {
        final Double criticalThreshold = thresholds.getCritical() == null ? null : thresholds.getCritical().doubleValue();
        final Double warnThreshold = thresholds.getWarning() == null ? null : thresholds.getWarning().doubleValue();

        if (criticalThreshold == null && warnThreshold == null) {
            log.warn("阈值配置错误：critical和warning至少需要配置一个");
            return null;
        }

        // 统计异常状态数量
        // 假设状态值：0=正常，1=警告，2=严重，3=致命
        long criticalStateCount = 0;
        long warningStateCount = 0;

        for (Double value : realValues) {
            if (value == null) {
                continue;
            }

            if (value >= 3) {  // 致命状态
                criticalStateCount++;
            } else if (value >= 2) {  // 严重状态
                criticalStateCount++;
            } else if (value >= 1) {  // 警告状态
                warningStateCount++;
            }
            // value == 0 为正常状态，不计入异常统计
        }

        // 根据阈值判断状态
        if (criticalThreshold != null && criticalStateCount >= criticalThreshold) {
            return EventEntity.builder()
                    .way(EventEntity.DetectionType.stateThreshold)
                    .value(criticalStateCount)
                    .status(3)  // 重要告警
                    .threshold(String.valueOf(criticalThreshold))
                    .build();
        } else if (warnThreshold != null && warningStateCount >= warnThreshold) {
            return EventEntity.builder()
                    .way(EventEntity.DetectionType.stateThreshold)
                    .value(warningStateCount)
                    .status(2)  // 次要告警
                    .threshold(String.valueOf(warnThreshold))
                    .build();
        } else {
            return EventEntity.builder()
                    .way(EventEntity.DetectionType.stateThreshold)
                    .value(0)
                    .status(0)  // 正常状态
                    .threshold(StringUtils.EMPTY)
                    .build();
        }
    }

    @Override
    public Map<Object, Object> afterCheckNoDataResult(DatabuffMonitor monitor, String mKey, Set<Map<String, String>> map, DetectQueryRequest detectQueryRequest) {
        Map<Object, Object> ret = new HashMap<>(16);
        // 无数据处理逻辑...
        return ret;
    }
}
```

## 突变检测

### ChangePointAlarmCheck 类

```java
package com.databuff.metric.impl.alarmV2;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.databuff.common.tsdb.dto.detect.DetectQueryRequest;
import com.databuff.common.tsdb.dto.explore.QueryRequest;
import com.databuff.entity.DatabuffMonitor;
import com.databuff.entity.EventEntity;
import com.databuff.metric.AlarmCheckOperatorV2;
import com.databuff.metric.MetricAggregator;
import com.databuff.util.NowTimeThreadLocal;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.databuff.common.utils.TimeUtil.ONE_MIN_MS_LONG;
import static com.databuff.common.utils.TimeUtil.roundDownToMinute;

/**
 * 突变检测类
 * 通过调用外部算法服务检测时间序列数据中的突变点
 * 支持配置检测URL、超时时间等参数
 *
 * @author:TianMing
 * @date: 2023/10/17
 * @time: 17:52
 */
@Component
@Slf4j
@RefreshScope
public class ChangePointAlarmCheck extends BaseAlarmCheckV2 implements AlarmCheckOperatorV2 {

    private static final String SEPARATOR = ":";
    private static final String REDIS_PREFIX = "monitor:metric:";

    @Value("${databuff.changePoint.api.url.detect:http://root-engine:18666/abnormal/detect}")
    private String apiUrl;

    @Value("${databuff.changePoint.api.timeout:30}")
    private int timeout;

    /**
     * 默认时间偏移量, 默认60秒
     * 计算公式：默认时间偏移量 = 最延迟的指标数据时间（3分钟） - 60秒（最近1分钟）
     * 注意：这个延迟参数同时会导致告警延迟，所以需要根据实际情况调整
     */
    @Value("${event.metric.defTimeOffset:60}")
    protected Long defTimeOffset;

    private OkHttpClient client;

    @PostConstruct
    public void init() {
        this.client = new OkHttpClient.Builder()
                .connectTimeout(timeout, TimeUnit.SECONDS)
                .readTimeout(timeout, TimeUnit.SECONDS)
                .writeTimeout(timeout, TimeUnit.SECONDS)
                .build();
    }

    @Override
    public Map<Object, Object> afterCheckResult(DatabuffMonitor monitor, Map<Map, Map<Object, Double>> aggTimeSeries, DetectQueryRequest detectQueryRequest, MetricAggregator metricAggregator, Collection<QueryRequest> queries) {
        final Long nowTime = NowTimeThreadLocal.getNowTime();
        // 触发时间 = endTime-1分钟
        final long endTime = roundDownToMinute(nowTime) - defTimeOffset * 1000;
        final long triggerTime = endTime - ONE_MIN_MS_LONG;
        detectQueryRequest.setTriggerTime(triggerTime);

        Map<Object, Object> ret = new LinkedHashMap<>(16);
        if (CollectionUtils.isEmpty(aggTimeSeries)) {
            return ret;
        }

        // 删除不满足条件的数据
        aggTimeSeries.entrySet().removeIf(entry ->
            entry == null || entry.getKey() == null || entry.getValue() == null ||
            CollectionUtils.isEmpty(entry.getValue()));

        // 准备发送给算法服务的数据
        Collection<JSONObject> datas = new ArrayList<>();
        for (Map.Entry<Map, Map<Object, Double>> entry : aggTimeSeries.entrySet()) {
            final Map<String, String> trigger = entry.getKey();
            datas.add(new JSONObject()
                    .fluentPut("trigger", trigger)
                    .fluentPut("data", entry.getValue()));
        }

        // 调用外部算法服务进行突变检测
        final Collection<JSONObject> aggValue = getAggValueFromApi(detectQueryRequest, datas);

        if (CollectionUtils.isEmpty(aggValue)) {
            return ret;
        }

        // 检测方法
        final String way = detectQueryRequest.getWay();
        final EventEntity.DetectionType detectionType = EventEntity.DetectionType.valueOf(way);

        // 处理算法服务返回的结果
        for (JSONObject result : aggValue) {
            if (result == null) {
                continue;
            }

            final Map<String, String> trigger = (Map<String, String>) result.get("trigger");
            final Double value = result.getDouble("value");
            final Double originalValue = result.getDouble("originalValue");
            final String level = result.getString("level");
            final Double threshold = result.getDouble("threshold");
            final String signal = result.getString("signal");
            final Long timestamp = result.getLong("timestamp");

            if (trigger == null || value == null || level == null) {
                continue;
            }

            final EventEntity eventEntity = createEventEntity(timestamp, value, originalValue, level, threshold, signal);
            if (eventEntity == null) {
                continue;
            }

            eventEntity.setWay(detectionType);
            ret.put(trigger, eventEntity);
        }

        return ret;
    }

    /**
     * 调用外部算法服务进行突变检测
     *
     * @param detectQueryRequest 检测请求参数
     * @param datas 时间序列数据
     * @return 检测结果
     */
    private Collection<JSONObject> getAggValueFromApi(DetectQueryRequest detectQueryRequest, Collection<JSONObject> datas) {
        JSONObject requestBody = new JSONObject();
        requestBody.put("rule", detectQueryRequest);
        requestBody.put("datas", datas);
        long start = System.currentTimeMillis();

        String requestStr = requestBody.toJSONString();
        Request request = new Request.Builder()
                .url(apiUrl)
                .post(RequestBody.create(requestStr, MediaType.parse("application/json; charset=utf-8")))
                .build();

        try (Response response = client.newCall(request).execute()) {
            long duration = System.currentTimeMillis() - start;
            log.debug("突变检测API调用耗时: {}ms", duration);

            if (!response.isSuccessful()) {
                log.error("突变检测API调用失败，状态码: {}", response.code());
                return Collections.emptyList();
            }

            String responseBody = response.body().string();
            JSONObject jsonResponse = JSON.parseObject(responseBody);

            if (jsonResponse == null || jsonResponse.getInteger("code") != 200) {
                log.error("突变检测API返回错误: {}", responseBody);
                return Collections.emptyList();
            }

            Object data = jsonResponse.get("data");
            if (data instanceof Collection) {
                return (Collection<JSONObject>) data;
            } else {
                log.warn("突变检测API返回数据格式异常");
                return Collections.emptyList();
            }

        } catch (IOException e) {
            log.error("突变检测API调用异常", e);
            return Collections.emptyList();
        }
    }

    /**
     * 创建事件实体
     *
     * @param timestamp 时间戳
     * @param value 检测值
     * @param originalValue 原始值
     * @param level 告警级别
     * @param threshold 阈值
     * @param signal 信号类型
     * @return 事件实体
     */
    private EventEntity createEventEntity(long timestamp, double value, double originalValue, String level, double threshold, String signal) {
        EventEntity eventEntity = EventEntity.builder()
                .way(EventEntity.DetectionType.changePoint)
                .value(value)
                .originalValue(originalValue)
                .abnormalTime(timestamp)
                .threshold(String.valueOf(threshold))
                .comparison(signal)
                .build();

        switch (level) {
            case "critical":
                eventEntity.setStatus(3);
                break;
            case "warning":
                eventEntity.setStatus(2);
                break;
            case "nodata":
                eventEntity.setStatus(0);
                eventEntity.setNoData(true);
                break;
            default:
                return null;
        }
        return eventEntity;
    }

    @Override
    public Map<Object, Object> afterCheckNoDataResult(DatabuffMonitor monitor, String mKey, Set<Map<String, String>> map, DetectQueryRequest detectQueryRequest) {
        Map<Object, Object> ret = new HashMap<>(16);
        // 无数据处理逻辑...
        return ret;
    }
}
```

## 基线计算工具类

### CalculateBaseline 类

```java
package com.databuff.metric.impl.alarmV2;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.tsdb.dto.CompositeCondition;
import com.databuff.common.tsdb.dto.explore.QueryRequest;
import com.databuff.lock.DConfigLockOperator;
import com.databuff.metric.MetricAggregator;
import com.databuff.service.JedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import static com.databuff.common.utils.TimeUtil.ONE_DAY_S;

/**
 * 基线计算工具类
 * 负责计算动态基线，支持异步和同步两种计算方式
 * 基线计算基于历史数据，要求至少有2016个数据点（7天*20%*24小时*60分钟）
 */
@Component
@Slf4j
public class CalculateBaseline {

    @Resource
    protected JedisService jedisService;

    @Resource
    protected DConfigLockOperator dConfigLockOperator;

    /**
     * 异步计算基线
     * 适用于不需要立即获取结果的场景
     *
     * @param keyStr Redis缓存键
     * @param queries 查询请求集合
     * @param tags 标签映射
     * @param metricAggregator 指标聚合器
     * @param comparison 比较运算符
     * @param baselineScope 基线范围
     */
    @Async
    public void processAsync(String keyStr, Collection<QueryRequest> queries, Map<String, String> tags,
                           MetricAggregator metricAggregator, String comparison, double baselineScope) {
        // 使用分布式锁确保同一时间只有一个线程计算同一个基线
        dConfigLockOperator.acquireLockAndExecuteCallback("lock:" + keyStr, 2, 120, TimeUnit.SECONDS, () -> {
            for (QueryRequest query : queries) {
                if (query == null) {
                    continue;
                }

                // 构建查询条件
                final Collection<CompositeCondition> fromExt = new ArrayList<>();
                tags.forEach((k, v) -> {
                    if (k == null || v == null) {
                        return;
                    }
                    fromExt.add(CompositeCondition.builder().left(k).right(v).operator("=").build());
                });
                query.setFromExt(fromExt);

                // 调用指标聚合器计算基线
                final Map<Map<String, String>, JSONObject> baselineResult = metricAggregator.baselineResult(query, comparison, baselineScope);
                if (MapUtils.isEmpty(baselineResult)) {
                    continue;
                }

                // 处理基线计算结果
                baselineResult.entrySet().stream().findFirst().ifPresent(e -> {
                    final JSONObject value = e.getValue();
                    if (value == null) {
                        return;
                    }
                    final String valueStr = value.toJSONString();
                    final double num = value.getDoubleValue("num");
                    final double baseline = value.getDoubleValue("baseline");

                    // 检查数据量是否满足要求
                    if (num < 2016 || baseline <= 0) {
                        log.debug("监控[{}]计算基线点数{}，低于所需数据量(分钟粒度下，至少需要满足 7d*20%*24h*60min=2016 的数据量)不满足要求", keyStr, num);
                        // 数据不足时缓存1小时
                        jedisService.setJson(keyStr, valueStr, ONE_DAY_S / 24);
                    } else {
                        // 数据充足时缓存1天
                        jedisService.setJson(keyStr, valueStr, ONE_DAY_S);
                    }
                });
            }
        });
    }

    /**
     * 同步计算基线
     * 适用于需要立即获取结果的场景
     *
     * @param keyStr Redis缓存键
     * @param queries 查询请求集合
     * @param tags 标签映射
     * @param metricAggregator 指标聚合器
     * @param comparison 比较运算符
     * @param baselineScope 基线范围
     * @return 基线计算结果
     */
    public JSONObject process(String keyStr, Collection<QueryRequest> queries, Map<String, String> tags,
                            MetricAggregator metricAggregator, String comparison, double baselineScope) {
        // 使用原子引用保存结果
        AtomicReference<JSONObject> value = new AtomicReference<>();

        // 使用分布式锁确保线程安全
        dConfigLockOperator.acquireLockAndExecuteCallback("lock:" + keyStr, 2, 120, TimeUnit.SECONDS, () -> {
            for (QueryRequest query : queries) {
                if (query == null) {
                    continue;
                }

                // 构建查询条件
                final Collection<CompositeCondition> fromExt = new ArrayList<>();
                tags.forEach((k, v) -> {
                    if (k == null || v == null) {
                        return;
                    }
                    fromExt.add(CompositeCondition.builder().left(k).right(v).operator("=").build());
                });
                query.setFromExt(fromExt);

                // 调用指标聚合器计算基线
                final Map<Map<String, String>, JSONObject> baselineResult = metricAggregator.baselineResult(query, comparison, baselineScope);
                if (MapUtils.isEmpty(baselineResult)) {
                    continue;
                }

                // 处理基线计算结果
                baselineResult.entrySet().stream().findFirst().ifPresent(e -> {
                    value.set(e.getValue());
                    if (value.get() == null) {
                        return;
                    }
                    final String valueStr = value.get().toJSONString();
                    final double num = value.get().getDoubleValue("num");
                    final double baseline = value.get().getDoubleValue("baseline");

                    // 检查数据量是否满足要求
                    if (num < 2016 || baseline <= 0) {
                        log.debug("监控[{}]计算基线点数{}，低于所需数据量(分钟粒度下，至少需要满足 7d*20%*24h*60min=2016 的数据量)不满足要求", keyStr, num);
                        // 数据不足时缓存1小时
                        jedisService.setJson(keyStr, valueStr, ONE_DAY_S / 24);
                    } else {
                        // 数据充足时缓存1天
                        jedisService.setJson(keyStr, valueStr, ONE_DAY_S);
                    }
                });
            }
        });
        return value.get();
    }
}
```

## 事件实体类

### EventEntity 类

```java
package com.databuff.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 事件字段实体
 * 用于封装异常检测产生的事件信息
 *
 * @package com.databuff.webapp.task.monitor
 * @company: dacheng
 * @author: yuzhili
 * @createDate: 2023/10/14
 */
@ApiModel(description = "事件实体")
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EventEntity {
    @ApiModelProperty(value = "主键")
    private String mKey;

    @ApiModelProperty(value = "阈值")
    private String threshold;

    @ApiModelProperty(value = "恢复")
    private String recovery;

    @ApiModelProperty(value = "是否存在重复事件")
    private boolean existsEvent;

    @ApiModelProperty(value = "当前状态：0=正常，2=次要告警，3=重要告警")
    private int status;

    @ApiModelProperty(value = "实际值（计算后）")
    private double value;

    @ApiModelProperty(value = "原始值（未计算前）")
    private double originalValue;

    @ApiModelProperty(value = "比较符号：>、>=、<、<=、==")
    private String comparison;

    @ApiModelProperty(value = "是否无数据")
    private boolean isNoData = false;

    @ApiModelProperty(value = "触发对象列表")
    private String group;

    @ApiModelProperty(value = "指标异常点时间，通常值触发阈值的1个点或多个点对应的时间（后期可能会删除）")
    private long abnormalTime;

    @ApiModelProperty(value = "触发阈值")
    private String trgTrd;

    @ApiModelProperty(value = "检测方式")
    private DetectionType way;

    /**
     * 检测类型枚举
     * 定义了系统支持的五种异常检测方法
     */
    @ApiModel(description = "检测类型枚举")
    public enum DetectionType {

        @ApiModelProperty(value = "阈值检测")
        threshold("阈值检测"),

        @ApiModelProperty(value = "动态基线检测")
        baseline("动态基线检测"),

        @ApiModelProperty(value = "波动检测")
        mutation("波动检测"),

        @ApiModelProperty(value = "状态检测")
        stateThreshold("状态检测"),

        @ApiModelProperty(value = "突变检测")
        changePoint("突变检测");

        private final String name;

        DetectionType(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }
    }

    @Override
    public String toString() {
        return "EventEntity{" +
                "threshold='" + threshold + '\'' +
                ", recovery='" + recovery + '\'' +
                ", existsEvent=" + existsEvent +
                ", status=" + status +
                ", value=" + value +
                ", group='" + group + '\'' +
                '}';
    }
}
```

---

## 总结

本文档包含了DataBuff SkyWalking告警系统中五种异常检测方法的完整代码实现：

1. **阈值检测 (ThresholdAlarmCheck)** - 基于固定阈值的检测方法
2. **动态基线检测 (DynamicBaselineCheck)** - 基于历史数据动态计算基线的检测方法
3. **波动检测 (MutationCheck)** - 通过比较当前与历史数据的变化检测异常波动
4. **状态检测 (StatusAlarmCheck)** - 针对状态类指标的异常检测
5. **突变检测 (ChangePointAlarmCheck)** - 通过外部算法服务检测时间序列突变点

每种检测方法都实现了`AlarmCheckOperatorV2`接口，继承了`BaseAlarmCheckV2`基类，提供了统一的检测框架和丰富的配置选项。
