package com.databuff.entity.dto.datahub;

import lombok.*;

import java.util.List;

/**
 * pipeline processor 配置查询参数
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessorConfigQuery {
    // 某个算子的id，获取该算子的配置信息，这个就是具体查询了
    private Integer id;
    // 是否启用
    private Integer enable;
    // 算子类型
    private String type;
    // 算子父类型
    private String parentType;
    // 算子名称,支持模糊搜索
    private String name;
    // 所属pipeline id
    private List<Integer> pipelineIds;
}