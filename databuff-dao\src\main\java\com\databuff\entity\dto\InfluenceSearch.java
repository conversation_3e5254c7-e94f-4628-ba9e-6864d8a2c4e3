package com.databuff.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Set;
import java.util.TimeZone;

@Data
public class InfluenceSearch {

    private String id;
    private String problemShowId;
    private String apiKey;
    private String service;
    private String problemDesc;
    private Set<String> rootCauseNodes;
    private Set<String> rootCauseTypes;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date fromTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date toTime;

    private Integer interval;

    private Integer page = 0;
    private Integer size = 50;
    private Integer topN = 10;

    private String sortField;
    private String sortOrder;

    // 使用 ThreadLocal 存储线程安全的 SimpleDateFormat 实例，并设置时区
    private static final ThreadLocal<SimpleDateFormat> formatter = ThreadLocal.withInitial(() -> {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdf.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        return sdf;
    });

    private String format(Date date) {
        if (date == null) return null;
        return formatter.get().format(date);
    }

    public String getFormattedFromTime() {
        return format(fromTime);
    }

    public String getFormattedToTime() {
        return format(toTime);
    }
}
