package com.databuff.dao.starrocks;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.databuff.entity.rum.starrocks.RumPage;
import com.databuff.entity.rum.starrocks.RumPageSpan;
import com.databuff.entity.rum.web.RumWebPageGetCriteria;
import com.databuff.entity.rum.web.RumWebPageSearchCriteria;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Set;

@Mapper
@Repository
public interface RumPageMapper extends BaseMapper<RumPage> {

    @Select("<script>" +
            "SELECT * FROM dc_rum_page WHERE app_id = #{appId} AND page_id = #{pageId} " +
            "<if test='fromTime != null'> AND startTime &gt;= #{fromTime}</if>" +
            "<if test='toTime != null'> AND startTime &lt; #{toTime}</if>" +
            "ORDER BY startTime DESC limit 1" +
            "</script>")
    RumPage selectRumPageById(RumWebPageGetCriteria searchCriteria);

    @Select("<script>" +
            "SELECT " +
            "startTime,page_id,location_href,processed_location_href,ip,isp,user_id,session_id,region,full_load_time,fcp,lcp,dcl,is_slow_page,is_slow_full_load_time,is_slow_fcp,is_slow_lcp,is_slow_dcl " +
            "FROM dc_rum_page " +
            "<if test='ew.customSqlSegment != null and ew.customSqlSegment.trim() != \"\"'>" +
            "${ew.customSqlSegment} " +
            "</if>" +
            "<if test='ew.customSqlSegment == null or ew.customSqlSegment.trim() == \"\"'>" +
            "<where> 1=1 </where>" +
            "</if>" +
            "<if test='searchCriteria.appId != null'> AND app_id = #{searchCriteria.appId}</if>" +
            "<if test='searchCriteria.userId != null'> AND user_id = #{searchCriteria.userId}</if>" +
            "<if test='searchCriteria.processedLocationHref != null'> AND processed_location_href = #{searchCriteria.processedLocationHref}</if>" +
            "<if test='searchCriteria.processedLocationHrefLike != null'> AND processed_location_href LIKE CONCAT('%', #{searchCriteria.processedLocationHrefLike}, '%')</if>" +
            "<if test='searchCriteria.locationHrefLike != null'> AND location_href LIKE CONCAT('%', #{searchCriteria.locationHrefLike}, '%')</if>" +
            "<if test='searchCriteria.locationHref != null'> AND location_href = #{searchCriteria.locationHref}</if>" +
            "<if test='searchCriteria.ip != null'>" +
            "    <choose>" +
            "        <when test='searchCriteria.processedLocationHref != null'>" +
            "            AND ip LIKE CONCAT('%', #{searchCriteria.ip}, '%')" +
            "        </when>" +
            "        <otherwise>" +
            "            AND ip = #{searchCriteria.ip}" +
            "        </otherwise>" +
            "    </choose>" +
            "</if>" +
            "<if test='searchCriteria.fromTime != null'> AND startTime &gt;= #{searchCriteria.fromTime}</if>" +
            "<if test='searchCriteria.toTime != null'> AND startTime &lt; #{searchCriteria.toTime}</if>" +
            "<if test='searchCriteria.sessionId != null'> AND session_id = #{searchCriteria.sessionId}</if>" +
            "</script>")
    List<RumPage> searchRumPages(@Param("ew") QueryWrapper<RumPage> queryWrapper, @Param("searchCriteria") RumWebPageSearchCriteria searchCriteria);
    @Select("<script>" +
            "SELECT " +
            "page_id,trace_id ,span_id,parent_id,startTime,start,end,duration,http_url,service " +
            "FROM dc_rum_page_span WHERE app_id = #{appId} AND page_id = #{pageId}" +
            "<if test='fromTime != null'> AND startTime &gt;= #{fromTime}</if>" +
            "<if test='toTime != null'> AND startTime &lt; #{toTime}</if>" +
            "</script>")
    List<RumPageSpan> getPageSpan(RumWebPageGetCriteria searchCriteria);


    @Select("SELECT DISTINCT page_id FROM dc_rum_page WHERE app_id = #{appId} AND startTime BETWEEN #{fromTime} AND #{toTime}")
    Set<Long> getDistinctPageIds(@Param("appId") Integer appId,
                                 @Param("fromTime") Date fromTime,
                                 @Param("toTime") Date toTime);

}