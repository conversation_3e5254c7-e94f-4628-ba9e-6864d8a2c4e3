package com.databuff.dao.mysql;

import com.databuff.entity.TagHostRelationEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface TagHostRelationEntityMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table dc_tag_host_relation
     *
     * @mbg.generated Fri Jul 01 10:17:49 CST 2022
     */
    int insert(TagHostRelationEntity record);

    List<TagHostRelationEntity> selectTagsByHosts(@Param("hostNames") List<String> hostNames,@Param("apiKey") String apiKey,@Param("type") String type);

    void deleteOldTagsByHosts(@Param("hostNames") List<String> hostNames,@Param("apiKey") String apiKey, @Param("type") String type);
    void deleteOldTagsById( @Param("id") Integer id);

    List<String> getHostNamesByTag(@Param("tagKey") String tagKey,@Param("tagValue")  String tagValue,@Param("apiKey") String apiKey);

    List<String> getHostNamesByEnvTag(@Param("apiKey") String apiKey,@Param("envTag1s") List<String> envTag1s,@Param("envTag2s") List<String> envTag2s);

    List<String> getHostNamesWithTagKey(@Param("tagKeys") List<String> tagKeys,@Param("apiKey") String apiKey);

    @Select("SELECT DISTINCT tag_key FROM dc_tag_host_relation")
    List<String> selectDistinctTagKeys();
}