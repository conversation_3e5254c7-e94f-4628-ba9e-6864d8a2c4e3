package com.databuff.entity.extend;

import com.databuff.entity.ResourceAlias;
import com.databuff.entity.SearchParamSetter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Set;

import static com.databuff.common.utils.TimeUtil.OUT_DAY_MS_LONG;

/**
 * @author:TianMing
 * @date: 2021/9/8
 * @time: 11:06
 */
@Data
public class ServiceSearch implements SearchParamSetter {
    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    /**
     * 每页数量
     */
    @ApiModelProperty("每页数量")
    protected Integer size ;
    /**
     * 偏移量 从0开始
     */
    @ApiModelProperty("偏移量 从0开始")
    protected Integer offset;
    @ApiModelProperty(value = "开始时间", example = "2021-07-15 00:00:00")
    private String fromTime;
    @ApiModelProperty(value = "结束时间", example = "2021-07-15 23:59:59")
    private String toTime;
    @ApiModelProperty("是否忽略服务查询时间（用于业务系统下必须查出关联的服务无论是否在线）")
    private boolean ignoreTime = false;
    @ApiModelProperty("排序字段")
    private String sortField;
    @ApiModelProperty("排序方式 asc desc")
    private String sortOrder;
    @ApiModelProperty("服务名称")
    private String serviceName;
    @ApiModelProperty("服务名称列表")
    private List<String> serviceNames;
    @ApiModelProperty("服务类型（db,web,cache,custom,remote）")
    private String serviceType;
    @ApiModelProperty("服务类型（db,web,cache,custom）")
    private List<String> serviceTypes;
    @ApiModelProperty("端点名称")
    private String resource;
    @ApiModelProperty("接口别名")
    private String alias;
    @ApiModelProperty("端点名称模糊查询")
    private String resourceQuery;

    @ApiModelProperty("接口名称")
    private String rootResource;
    @ApiModelProperty("接口名称模糊查询")
    private String rootResourceQuery;


    @ApiModelProperty("服务id")
    private String serviceId;
    @ApiModelProperty("服务ids")
    private List<String> serviceIds;
    @ApiModelProperty("服务实例")
    private String serviceInstance;


    @ApiModelProperty("服务实例模糊查询")
    private String serviceInstanceQuery;
    @ApiModelProperty("服务组件分类 service.rpc,service.http,service.mq,service.redis,service.db,service.es")
    private String componentType;
    @ApiModelProperty("池类型分类 service.http.connection.pool.get,service.db.connection.pool.get,service.object.pool.get")
    private String poolType;
    @ApiModelProperty("池名称")
    private String poolName;
    @ApiModelProperty("标签按key分组查询")
    private List<GroupTag> groupTags;

    @ApiModelProperty("来源服务id")
    private String srcServiceId;
    @ApiModelProperty("来源服务实例")
    private String srcServiceInstance;
    @ApiModelProperty("来源服务实例模糊查询")
    private String srcServiceInstanceQuery;

    @ApiModelProperty("是否只查询自定义web应用")
    private boolean isOnlyWeb;
    @ApiModelProperty("是否只查询虚拟服务，1表示只查询虚拟服务，0表示查询真实服务")
    private Integer virtualService;
    @ApiModelProperty("数据来源 Databuff,SkyWalking,OpenTelemetry")
    private List<String> dataSources;


    @ApiModelProperty("折线图间隔 (秒级)")
    private Integer interval;

    @ApiModelProperty("延迟分布横坐标个数")
    private Integer numBuckets = 100;
    @ApiModelProperty("最大百分位（p50Latency,..,p100Latency）")
    private String maxPercentile;


    @ApiModelProperty("是否按成功错误请求分组（true,false）")
    private boolean reqGroup = false;

    @ApiModelProperty("系统id")
    private Integer sysId;
    @ApiModelProperty("k8sNamespace")
    private String k8sNamespace;

    @ApiModelProperty("apiKey")
    private String apiKey;
    @ApiModelProperty("展示字段")
    private List<String> showFields;
    @ApiModelProperty("当前账号")
    private String account;
    @ApiModelProperty("服务状态, 0表示异常，非0表示正常，其余状态预留接口")
    private Integer statusType;

    @ApiModelProperty("入口1，0")
    private Integer isIn ;
    @ApiModelProperty("出口1，0")
    private Integer isOut ;
    @ApiModelProperty("是否慢sql 1，0")
    private Integer isSlow ;
    @ApiModelProperty("graphStats统计返回内容[avgLatencys,maxLatencys,minLatencys,callCnts,errorCnts,errorRates,percentageLatencys]")
    private Set<String> graphStats ;

    /*
    调用分析查询用
     */
    @ApiModelProperty("url查询")
    private String url;
    @ApiModelProperty("url模糊查询")
    private String urlQuery;
    @ApiModelProperty("状态码")
    private String httpCode;
    @ApiModelProperty("状态码模糊查询")
    private String httpCodeQuery;
    @ApiModelProperty("method")
    private String method;
    @ApiModelProperty("method模糊查询")
    private String methodQuery;
    @ApiModelProperty("topic查询")
    private String topic;
    @ApiModelProperty("topic模糊查询")
    private String topicQuery;
    @ApiModelProperty("分组查询")
    private String group;
    @ApiModelProperty("分组模糊查询")
    private String groupQuery;
    @ApiModelProperty("partition")
    private String partition;
    @ApiModelProperty("partition模糊查询")
    private String partitionQuery;
    @ApiModelProperty("broker查询")
    private String broker;
    @ApiModelProperty("broker模糊查询")
    private String brokerQuery;
    @ApiModelProperty("数据库查询")
    private String sqlDatabase;
    @ApiModelProperty("数据库模糊查询")
    private String sqlDatabaseQuery;
    @ApiModelProperty("SQL内容查询")
    private String sqlContent;
    @ApiModelProperty("SQL内容模糊查询")
    private String sqlContentQuery;
    @ApiModelProperty("数据库操作")
    private String sqlOperation;
    @ApiModelProperty("数据库操作模糊查询")
    private String sqlOperationQuery;
    @ApiModelProperty("命令")
    private String command;
    @ApiModelProperty("命令模糊查询")
    private String commandQuery;

    /**
     * 错误分析条件
     */
    @ApiModelProperty("错误名查询")
    private String exception;
    @ApiModelProperty("错误名模糊查询")
    private String exceptionQuery;
    @ApiModelProperty("聚合参数，（请求名：resource，错误名：exceptionName）")
    private String groupBy;
    @ApiModelProperty("不为空的字段")
    private String notEmptyFields;
    @ApiModelProperty("字段名")
    private String field;
    @ApiModelProperty
    private List<ResourceAlias> aliasList;

    @ApiModelProperty("环境标签1")
    private List<String> envTag1s;
    @ApiModelProperty("环境标签2")
    private List<String> envTag2s;

    public Long getFromTimeVul() {
        if (fromTime == null) {
            return System.currentTimeMillis() - OUT_DAY_MS_LONG;
        }
        if ("".equals(this.fromTime)) {
            return strToDate(YYYY_MM_DD_HH_MM_SS, this.fromTime).getTime() - OUT_DAY_MS_LONG;
        }
        return strToDate(YYYY_MM_DD_HH_MM_SS, this.fromTime).getTime();
    }

    public Long getToTimeVul() {
        if (this.toTime == null) {
            return System.currentTimeMillis();
        }
        if ("".equals(this.toTime)) {
            return System.currentTimeMillis();
        }
        return strToDate(YYYY_MM_DD_HH_MM_SS, this.toTime).getTime();
    }

    public boolean getReqGroup() {
        return reqGroup;
    }


    public static class GroupTag {
        List<String> tags;

        public List<String> getTags() {
            return tags;
        }

        public void setTags(List<String> tags) {
            this.tags = tags;
        }
    }

    private static Date strToDate(String pattern, String source) {
        if (source == null || source.isEmpty()) {
            return null;
        }
        try {
            DateFormat df = new SimpleDateFormat(pattern != null && !pattern.isEmpty() ? pattern : YYYY_MM_DD_HH_MM_SS);
            return df.parse(source);
        } catch (Exception e) {
            System.err.println("Error parsing date: " + e.getMessage());
            return null;
        }
    }
}
