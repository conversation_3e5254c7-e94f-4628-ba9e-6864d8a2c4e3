package com.databuff.entity.dto.topo;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TopoGraphVO {

    /**
     * 节点的唯一标识符。
     */
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    private String id;

    /**
     * 节点的名称。
     */
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    private String name;

    /**
     * 节点的类型。
     */
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    private String nodeType;

    /**
     * 上游节点的集合，表示与当前节点直接相连的上游节点的ID。
     */
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    private Collection<String> upstreamIds;

    /**
     * 下游节点的集合，表示与当前节点直接相连的下游节点的ID。
     */
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    private Collection<String> downstreamIds;

    /**
     * 与节点相关的度量元数据列表。
     */
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    private JSONArray metricMetaData;

    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    private List<JSONObject> fields;
}