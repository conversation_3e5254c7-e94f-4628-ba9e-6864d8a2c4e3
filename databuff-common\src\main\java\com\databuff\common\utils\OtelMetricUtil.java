package com.databuff.common.utils;

import com.databuff.common.metric.OpenTelemetryFactory;
import com.databuff.common.metric.TSDBMetricExporter;
import com.databuff.common.tsdb.TSDBOperateUtil;
import com.databuff.common.tsdb.model.TSDBPoint;
import com.databuff.common.tsdb.pool.TSDBConnectPool;
import com.databuff.common.tsdb.pool.TSDBPoolConfig;
import com.databuff.common.tsdb.pool.TSDBPoolFactory;
import io.opentelemetry.api.GlobalOpenTelemetry;
import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.metrics.DoubleHistogram;
import io.opentelemetry.api.metrics.LongCounter;
import io.opentelemetry.api.metrics.Meter;
import io.opentelemetry.exporter.logging.LoggingMetricExporter;
import io.opentelemetry.sdk.internal.AttributesMap;
import io.opentelemetry.sdk.metrics.data.AggregationTemporality;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.databuff.common.constants.TSDBIndex.DATABASE_NAME_DATABUFF_SYSTEM;

@Slf4j
public class OtelMetricUtil {

    private static final String SCOPE = "otel";
    private static final int TAG_VALUE_LENGTH_LIMIT = 200;
    private static volatile Meter METER = null;
    private static final String METRIC_EXCEPTION_NAME = "exception";
    private static final String TAG_TYPE = "type";
    private static final String TAG_METHOD = "method";
    private static volatile boolean init = false;

    public static volatile TSDBOperateUtil tsdbOperateUtil;

    public static String LOG_SERVICE = "";

    private static TSDBMetricExporter metricExporter;

    /**
     * 指标输出到tsdb（使用TSDBConfig中的tsdbOperateUtil）
     *
     * @param service 服务名称
     * @return OpenTelemetry实例
     */
    public static OpenTelemetry initWithTSDBDBExporter(String service, TSDBOperateUtil tsdbOperateUtil) {
        if (!init) {
            synchronized (OtelMetricUtil.class) {
                if (!init) {
                    LOG_SERVICE = service;
                    String msg = "initWithTSDBDBExporter with service=" + service;
                    System.out.println(msg);
                    log.info(msg);

                    if (tsdbOperateUtil == null) {
                        throw new IllegalStateException("TSDBOperateUtil is null. Check TSDBConfig configuration.");
                    }

                    // 创建指标导出器并初始化
                    OtelMetricUtil.tsdbOperateUtil = tsdbOperateUtil;
                    metricExporter = new TSDBMetricExporter(tsdbOperateUtil);
                    OpenTelemetryFactory.init(service, metricExporter, 10); // 使用默认间隔 10 秒
                    METER = GlobalOpenTelemetry.getMeter(SCOPE);
                    init = true;

                    log.info("Successfully initialized OtelMetricUtil with TSDBConfig for service: {}", service);
                }
            }
        }
        return GlobalOpenTelemetry.get();
    }

    /**
     * 指标输出到tsdb（完整参数版本）
     *
     * @param service       服务名称
     * @param tsdb          TSDB类型
     * @param tsdbServerUrl TSDB服务器URL
     * @param tsdbUser      TSDB用户名
     * @param tsdbPassword  TSDB密码
     * @return OpenTelemetry实例
     */
    public static OpenTelemetry initWithTSDBDBExporter(String service, String tsdb, String tsdbServerUrl,
                                                       String tsdbUser, String tsdbPassword) {
        if (!init) {
            synchronized (OtelMetricUtil.class) {
                if (!init) {
                    LOG_SERVICE = service;
                    String msg = "initWithMoreDBExporter with service=" + service + " tsdb=" + tsdb + " url=" + tsdbServerUrl;
                    System.out.println(msg);
                    log.info(msg);

                    // 使用builder模式创建TSDBPoolFactory
                    TSDBPoolFactory tsdbPoolFactory = TSDBPoolFactory.builder()
                            .tsdb(tsdb)
                            .url(tsdbServerUrl)
                            .user(tsdbUser)
                            .pwd(tsdbPassword)
                            .build();
                    TSDBConnectPool moreDBConnectPool = new TSDBConnectPool(tsdbPoolFactory, new TSDBPoolConfig());

                    tsdbOperateUtil = new TSDBOperateUtil(moreDBConnectPool);
                    metricExporter = new TSDBMetricExporter(tsdbOperateUtil);
                    OpenTelemetryFactory.init(service, metricExporter, 10);
                    METER = GlobalOpenTelemetry.getMeter(SCOPE);
                    init = true;
                }
            }
        }
        return GlobalOpenTelemetry.get();
    }


    /**
     * 指标输出到日志
     *
     * @param service
     */
    public static OpenTelemetry initWithLogExporter(String service, int interval) {
        if (!init) {
            synchronized (OtelMetricUtil.class) {
                if (!init) {
                    OpenTelemetryFactory.init(service, LoggingMetricExporter.create(AggregationTemporality.DELTA), interval);
                    METER = GlobalOpenTelemetry.getMeter(SCOPE);
                }
            }
        }
        return GlobalOpenTelemetry.get();
    }

    /**
     * 使用姿势如下
     * MetricUtil.logLongCounter("testCounter", tags, 1);
     *
     * @param metricName
     * @param value
     */
    public static void logCounter(String metricName, long value) {
        if (!init) {
            return;
        }
        logCounter(metricName, null, value);
    }

    /**
     * 使用姿势如下
     * MetricUtil.logLongCounter("testCounter", tags, 1);
     *
     * @param metricName
     * @param value
     */
    public static void logCounter(String metricName, long value, String type) {
        if (!init) {
            return;
        }
        Map<String, String> tags = new HashMap<>();
        tags.put("type", type);
        logCounter(metricName, tags, value);
    }

    /**
     * 使用姿势如下
     * Map<String, String> tags = new HashMap<>();
     * tags.put("key1", "value1");
     * MetricUtil.logLongCounter("testCounter", tags, 5);
     *
     * @param metricName
     * @param tags
     * @param value
     */
    public static void logCounter(String metricName, Map<String, String> tags, long value) {
        if (!init) {
            return;
        }
        try {
            LongCounter longCounter = METER.counterBuilder(validateAndCorrectMetricName(metricName)).build();
            longCounter.add(value, convert(tags));
        } catch (Throwable e) {
            log.error("logCounter error", e);
        }
    }

    /**
     * 使用姿势如下
     * OtelMetricUtil.logHistogram("testHistogram", tags, 1);
     *
     * @param metricName
     * @param durationMs
     */
    public static void logHistogram(String metricName, long durationMs) {
        if (!init) {
            return;
        }
        logHistogram(metricName, null, durationMs);
    }

    public static void logHistogram(String metricName, long value, String type) {
        if (!init) {
            return;
        }
        Map<String, String> tags = new HashMap<>();
        tags.put("type", type);
        logHistogram(metricName, tags, value);
    }

    /**
     * 使用姿势如下
     * Map<String, String> tags = new HashMap<>();
     * tags.put("key1", "value1");
     * OtelMetricUtil.logHistogram("testHistogram", tags, 1);
     *
     * @param metricName
     * @param tags
     * @param durationMs
     */
    public static void logHistogram(String metricName, Map<String, String> tags, long durationMs) {
        if (!init) {
            return;
        }
        if (durationMs < 0) {
            //耗时为负则是领先数据
            if (tags == null) {
                tags = new HashMap<>();
            }
            tags.put("lead", "1");
            durationMs = Math.abs(durationMs);
        }

        try {
            DoubleHistogram histogram = METER.histogramBuilder(validateAndCorrectMetricName(metricName)).build();
            histogram.record(durationMs, convert(tags));
        } catch (Throwable e) {
            log.error("logHistogram error", e);
        }
    }

    /**
     * 进行异常打点
     *
     * @param exception
     */
    public static void logException(String method, Throwable exception) {
        if (!init) {
            return;
        }
        try {
            Map<String, String> tags = new HashMap<>();
            tags.put(TAG_METHOD, method);
            tags.put(TAG_TYPE, exception.getClass().getName());
            logCounter(METRIC_EXCEPTION_NAME, tags, 1);
        } catch (Throwable e) {
            e.printStackTrace();
            log.error("logException error", e);
        }
    }

    private static AttributesMap convert(Map<String, String> tags) {
        AttributesMap attributesMap = AttributesMap.create(8, TAG_VALUE_LENGTH_LIMIT);
        if (tags != null && tags.size() > 0) {
            for (Map.Entry<String, String> entry : tags.entrySet()) {
                attributesMap.put(AttributeKey.stringKey(entry.getKey()), entry.getValue());
            }
        }
        return attributesMap;
    }

    private static String validateAndCorrectMetricName(String metricName) {
        // 检查名称是否以字母开头
        if (!Character.isLetter(metricName.charAt(0))) {
            metricName = "a" + metricName;
        }
        // 检查名称是否包含非法字符，并替换为下划线
        metricName = metricName.replaceAll("[^a-zA-Z0-9_.-]", "_");
        // 检查名称的长度，如果超过 63 个字符，截断它
        if (metricName.length() > 63) {
            metricName = metricName.substring(0, 63);
        }

        return metricName;
    }

    /**
     * 使用姿势如下
     * Map<String, String> tags = new HashMap<>();
     * tags.put("key1", "value1");
     * OtelMetricUtil.logHistogram("testHistogram", tags, 1);
     *
     * @param metricName 表名
     * @param value      指标值
     * @param tags
     * @param ts         毫秒
     */
    public static void logOriginalData(String metricName, Number value, Map<String, String> tags, long ts) {
        if (!init) {
            return;
        }
        List<TSDBPoint> pointList = new ArrayList<>();
        TSDBPoint point = new TSDBPoint(LOG_SERVICE + "." + metricName, ts);
        if (tags != null) {
            tags.entrySet().removeIf(entry -> entry.getKey() == null || entry.getValue() == null);
            point.setTags(tags);
        }
        Map<String, Object> fields = new HashMap<>();
        fields.put("value", value);
        point.setFields(fields);
        pointList.add(point);
        tsdbOperateUtil.writePoints(DATABASE_NAME_DATABUFF_SYSTEM, pointList);
    }

    /**
     * 使用姿势如下
     * Map<String, String> tags = new HashMap<>();
     * tags.put("key1", "value1");
     * Map<String, Number> fields = new HashMap<>();
     * fields.put("field1", 1);
     * fields.put("field2", 2);
     * OtelMetricUtil.logHistogram("testHistogram", fields, tags, System.currentTimeMillis());
     *
     * @param metricName 表名
     * @param fields     指标值列表
     * @param tags       标签
     * @param ts         入库时间(毫秒)
     * <AUTHOR>
     */
    public static void logOriginalData(String metricName, Map<String, Number> fields, Map<String, String> tags, long ts) {
        if (!init) {
            return;
        }
        if (fields == null) {
            return;
        }
        List<TSDBPoint> pointList = new ArrayList<>();
        TSDBPoint point = new TSDBPoint(LOG_SERVICE + "." + metricName, ts);
        if (tags != null) {
            tags.entrySet().removeIf(entry -> entry.getKey() == null || entry.getValue() == null);
            point.setTags(tags);
        }
        Map<String, Object> pFields = new HashMap<>();
        for (Map.Entry<String, Number> entry : fields.entrySet()) {
            pFields.put(entry.getKey(), entry.getValue());
        }
        point.setFields(pFields);
        pointList.add(point);
        tsdbOperateUtil.writePoints(DATABASE_NAME_DATABUFF_SYSTEM, pointList);
    }

    public static String getIp() {
        return IpUtil.getIp();
    }

    public static String getHostName() {
        return IpUtil.getLocalAddress().getHostName();
    }

}
