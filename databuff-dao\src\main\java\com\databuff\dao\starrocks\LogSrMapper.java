package com.databuff.dao.starrocks;

import com.databuff.entity.dto.LogEntity;
import com.databuff.entity.extend.LogSearch;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface LogSrMapper {

    /**
     * 日志查询
     *
     * @param search
     * @return
     */
    List<LogEntity> logSearch(LogSearch search);

    Long logSearchCount(LogSearch search);

    List<String> logSpanIdSearch(LogSearch search);
}
