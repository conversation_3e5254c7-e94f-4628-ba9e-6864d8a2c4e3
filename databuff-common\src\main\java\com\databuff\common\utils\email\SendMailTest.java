package com.databuff.common.utils.email;


/**
 * @author:Tian<PERSON>ing
 * @date: 2021/12/20
 * @time: 11:42
 */

public class SendMailTest {

    public void send() {
        String smtpHost = "smtp.exmail.qq.com";
//        String smtpHost = "smtp.126.com";
        int  smtpPort = 465;
        Boolean useSsl = true ;


//        String user = "<EMAIL>" ;
//        String password = "Dacheng.20190528+" ;
//                String user = "<EMAIL>" ;
//        String password = "Yzl@20230601" ;
        String user = "" ;
        String password = "" ;

        MailConfig mailConfig = new MailConfig(smtpHost,smtpPort,useSsl,user,password);
        mailConfig.setCode("8fPoaiTegh2Zt99V");
//        MailUtil.send("<EMAIL>", "测试报告3_16_0725_1652", "测试报告3_16_0725_1652", "C:\\Users\\<USER>\\Desktop\\测试报告3-16-07-25_15_48.docx",mailConfig);



        StringBuilder content = new StringBuilder();
        content.append("会议内容】\n" +
                "\n" +
                "1、        阐述目前测试遇到的问题和bug以及进度，已经进行3轮测试\n" +
                "\n" +
                "2、        针对测试场景出现的需求更改问题，需进行需求开发（限今日改完）\n" +
                "\n" +
                "3、        针对APM需求，延期至节后测试，或并入V2.2版本测试\n" +
                "\n" +
                "4、        针对国庆假期安排稳定性测试\n" +
                "\n" +
                "5、        针对提测版本号需进行统一规划管制，包括文档、测试包、agent安装包");
//        content.append("Databuff提醒:");
//        content.append("\n\n");//换行
//        content.append("------"); //下划线
//        content.append("\n\n");//换行
//        content.append("下班了，今天no add！");
//        content.append("\n\n &nbsp;");//多个\n只能换一行，加上一个空格即可辅助完成换行
//        content.append("\n\n");
//        content.append("<br>"+ DateUtils.getDate("yyyy-MM-dd HH:mm:ss"));

        //this("<EMAIL>", "Dacheng.20190528+");

//        mailConfig.setCode("ubxexuqspoybbicc");
//        mailConfig.setCode("LIDLHLKZYKWYLAIF");
        String title = "Databuff会议纪要";
        String res = MailUtil.send("<EMAIL>,<EMAIL>",title,content.toString(),mailConfig);
        System.out.println(res);
//        MailUtil.send("<EMAIL>",title,content.toString(),mailConfig);

    }



    public static void main(String[] args) {
        new SendMailTest().send();
    }
}
