package com.databuff.common.ringbuffer;

import com.databuff.common.utils.OtelMetricUtil;
import com.lmax.disruptor.RingBuffer;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;

import static com.databuff.common.constants.MetricName.*;

@Slf4j
public class RingBufferMonitor<T> {

    private final Integer parallel;

    private final RingBuffer<T> ringBuffer;

    private final Timer timer = new Timer();

    private final String type;

    public RingBufferMonitor(RingBuffer<T> ringBuffer, String type, int parallel) {
        this.ringBuffer = ringBuffer;
        this.type = type;
        this.parallel = parallel;
    }

    public RingBufferMonitor(RingBuffer<T> ringBuffer, String type) {
        this.ringBuffer = ringBuffer;
        this.type = type;
        this.parallel = 1;
    }

    public void startMonitoring(long interval) {
        timer.scheduleAtFixedRate(new MonitorTask(), 0, interval);
    }

    private class MonitorTask extends TimerTask {
        @Override
        public void run() {
            try {
                long minimumGatingSequence = ringBuffer.getMinimumGatingSequence();
                long bufferSize = ringBuffer.getBufferSize();
                long remainingCapacity = ringBuffer.remainingCapacity();
                double fillRatio = BigDecimal.valueOf((bufferSize - remainingCapacity) / (double) bufferSize).setScale(2, RoundingMode.HALF_UP).doubleValue();
                Map<String, String> tags = new HashMap<>();
                tags.put("type", type);
                tags.put("ip", OtelMetricUtil.getIp());
                tags.put("hostName", OtelMetricUtil.getHostName());
                OtelMetricUtil.logOriginalData(RINGBUFFER_PARALLEL, parallel, tags, System.currentTimeMillis());
                OtelMetricUtil.logOriginalData(RINGBUFFER_FILL_RATIO, fillRatio, tags, System.currentTimeMillis());
                OtelMetricUtil.logOriginalData(RINGBUFFER_MINIMUM_GATING_SEQUENCE, minimumGatingSequence, tags, System.currentTimeMillis());
            } catch (Throwable e) {
                log.error("MonitorTask error", e);
            }
        }
    }

    public void shutdown() {
        timer.cancel();
        timer.purge();
    }

}
