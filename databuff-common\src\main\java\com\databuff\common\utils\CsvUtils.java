package com.databuff.common.utils;

import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Slf4j
public class CsvUtils {

    /**
     * 导出csv文件
     * @param titles    导出文件列头
     * @param list      具体导出数据
     * @param fileName  文件名称
     * @param response
     * @throws IOException
     * @throws IllegalArgumentException
     * @throws IllegalAccessException
     */
    public static void exportCsv(List<String> titles, List<List<String>> list, String fileName, HttpServletResponse response) throws IOException, IllegalArgumentException, IllegalAccessException{
        StringBuffer expStr = new StringBuffer();
        OutputStream o = null;
        try {

            expStr = getExpStr(expStr,titles,list);

            response.setContentType("application/download;charset=UTF-8");
            response.setContentType("Content-type:application/vnd.ms-excel;charset=UTF-8");
            response.setHeader("Content-disposition","attachment;filename="+ java.net.URLEncoder.encode(fileName, "UTF-8"));
            o = response.getOutputStream();
//            o.write(expStr.toString().getBytes("GBK"));
            o.write(expStr.toString().getBytes("UTF-8"));
        } catch (Exception e){
            log.error("exportCsv error:{}",e);
        }finally {
            // 关闭
            if(o != null){
                o.close();
            }
        }
    }

    /**
     * 导出csv压缩文件
     * @param titles    导出文件列头
     * @param list      具体导出数据
     * @param fileName  压缩文件名称 例：aa.zip
     * @param response
     * @throws IOException
     * @throws IllegalArgumentException
     * @throws IllegalAccessException
     */
    public static String exportCsvZip(List<String> titles, List<List<String>> list, String fileName, HttpServletResponse response) throws IOException, IllegalArgumentException, IllegalAccessException{
        StringBuffer expStr = new StringBuffer();
        ZipOutputStream zos = null;
        OutputStream o = null;
        try {

            expStr = getExpStr(expStr,titles,list);

            response.setContentType("application/download;charset=UTF-8");
            response.setContentType("Content-type:application/vnd.ms-excel;charset=UTF-8");
            response.setHeader("Content-disposition","attachment;filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));

            o = response.getOutputStream();
            zos =  new ZipOutputStream(new BufferedOutputStream(o));
            //创建压缩文件内的文件
            zos.putNextEntry(new ZipEntry(fileName.replace("zip","csv")));
            zos.write(expStr.toString().getBytes("GBK"));
            zos.closeEntry();
        } catch (Exception e){
            log.error("exportCsvZip error:{}",e);
        }finally {
            if(zos != null){
                zos.close();
            }
            if(o != null){
                o.close();
            }
        }
        return "ok";
    }

    /**
     * 导出csv压缩文件
     * @param titles    导出文件列头
     * @param list      具体导出数据
     * @param fileName  压缩文件名称 例：aa.zip
     * @throws IOException
     * @throws IllegalArgumentException
     * @throws IllegalAccessException
     */
    public static void exportBachCsvZip( ZipOutputStream zipOutputStream,DataOutputStream os,List<String> titles, List<List<String>> list, String fileName){
        try {

            //创建压缩文件内的文件
            zipOutputStream.putNextEntry(new ZipEntry(fileName.replace("zip","csv")));
            writeExpStr(os,titles,list);
        } catch (Exception e){
            log.error("exportBachCsvZip error {}", e);
        }
    }

    private static StringBuffer getExpStr(StringBuffer expStr , List<String> titles, List<List<String>> list){
        //csv文件是逗号分隔，除第一个外，每次写入一个单元格数据后需要输入逗号
        for(String title : titles){
            expStr.append(title).append(",");
        }
        //写完文件头后换行
        expStr.append("\n");
        //写内容
        for(List<String> ls : list){
            for (String str : ls) {
                expStr.append(str).append(",");
            }
            //写完一行换行
            expStr.append("\n");
        }
        return expStr ;
    }
    private static void writeExpStr(DataOutputStream os , List<String> titles, List<List<String>> list) throws IOException {
        //csv文件是逗号分隔，除第一个外，每次写入一个单元格数据后需要输入逗号
        StringBuffer expStr = new StringBuffer();
        for(String title : titles){
            expStr.append(processValueForCsv(title)).append(",");
        }
        //写完文件头后换行
        expStr.append("\n");
        //写内容
        for(List<String> ls : list){
            for (String str : ls) {
//                if (str.length()>30000){
//                    //csv单元格大小有限制32767个字符，所以当单个字符串内容超过30000就切到后面一个单元格，否在会错乱
//                    int l = 0 ;
//                    do {
//                        if (l+30000>str.length()){
//                            expStr.append(processValueForCsv(str.substring(l))).append(",");
//                        }else{
//                            expStr.append(processValueForCsv(str.substring(l,l+30000))).append(",");
//                        }
//                        l = l+30000;
//                    }while (str.length()>l);
//                }else{
//                    expStr.append(processValueForCsv(str)).append(",");
//                }
                expStr.append(processValueForCsv(str)).append(",");
            }
            //写完一行换行
            expStr.append("\n");
        }
        os.write(expStr.toString().getBytes("GBK"));

    }

    /**
     *   处理csv文件字段中需要转义的引号
     *   添加双引号，防止被字段中的逗号和换行符干扰
     *   使其显示为一个单元格
     */
    public static String processValueForCsv(String value) {
        if (value == null) {
            return "";
        }
        //如果有逗号
        if(value.contains(",")){
            //如果还有双引号，先将双引号转义，避免两边加了双引号后转义错误
            if(value.contains("\"")){
                value=value.replace("\"", "\"\"");
            }
            //在将逗号转义
            value="\""+value+"\"";
        }
        if (value.contains("\r\n")) {
            value = value.replaceAll("\r\n", " ");
        }
        if (value.contains("\r")) {
            value = value.replaceAll("\r", " ");
        }
        if (value.contains("\n")) {
            value = value.replaceAll("\n", " ");
        }
        return value;
    }

}

