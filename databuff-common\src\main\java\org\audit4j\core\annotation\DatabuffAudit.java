package org.audit4j.core.annotation;

import javax.interceptor.InterceptorBinding;
import java.lang.annotation.*;

/**
 * 审计注解。
 *
 * 该注解可以用于类级别或方法级别。应用此注解时，类或方法将被标记为可审计的。
 *
 * <AUTHOR> href="mailto:<EMAIL>">yuz<PERSON>i</a>
 *
 * @since 1.0.0
 */
@InterceptorBinding
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(value = { ElementType.TYPE, ElementType.METHOD })
@Audit
public @interface DatabuffAudit  {

    /**
     * 操作定义方法或操作。
     *
     * @return 字符串
     */
    public String action() default "action";

    /**
     * 选择。
     *
     * @return 字符串
     * @deprecated : 此属性不再使用。
     */
    @Deprecated
    public SelectionType selection() default SelectionType.ALL;

    /**
     * 操作实体类型。
     *
     * @return 字符串
     */
    public String entityType() default "default";

    /**
     * 是否有链接。
     *
     * @return 字符串
     */
    public boolean hasLink() default true;

    /**
     * 仓库。此属性用于定义审计日志所在的仓库。
     *
     * @return 字符串
     */
    public String repository() default "default";
}