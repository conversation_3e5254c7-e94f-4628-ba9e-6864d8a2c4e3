package com.databuff.dao.starrocks.profiling.v3;

import com.databuff.entity.profiling.v3.ProfilingStackBase;
import com.databuff.handler.StringArrayToListTypeHandler;
import org.apache.ibatis.annotations.*;

@Mapper
public interface ProfilingStackBaseMapper {

    @Select("SELECT excerptId,stackTrace,frameTypeIds FROM dc_profiling_stack_base WHERE excerptId = #{excerptId}")
    @Results({
            @Result(column = "stackTrace", property = "stackTrace", typeHandler = StringArrayToListTypeHandler.class)
    })
    ProfilingStackBase getProfilingStackBaseByExcerptId(@Param("excerptId") String excerptId);
}