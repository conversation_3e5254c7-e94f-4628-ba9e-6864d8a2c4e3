package com.databuff.common.utils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TimeUtil类的单元测试
 */
public class TimeUtilTest {

    /**
     * 为testComputeDayDifference提供测试数据
     */
    private static Stream<Arguments> provideDatesForDayDifference() {
        return Stream.of(
                // 相同天
                Arguments.of(1609459200000L, 1609459200000L, 0), // 2021-01-01 00:00:00 和 2021-01-01 00:00:00
                Arguments.of(1609459200000L, 1609545199999L, 0), // 2021-01-01 00:00:00 和 2021-01-01 23:59:59.999

                // 相邻天
                Arguments.of(1609545200000L, 1609459200000L, 1), // 2021-01-02 00:00:00 和 2021-01-01 00:00:00
                Arguments.of(1609459200000L, 1609545200000L, -1), // 2021-01-01 00:00:00 和 2021-01-02 00:00:00

                // 相隔多天
                Arguments.of(1609891200000L, 1609459200000L, 5), // 2021-01-06 00:00:00 和 2021-01-01 00:00:00
                Arguments.of(1609459200000L, 1609891200000L, -5), // 2021-01-01 00:00:00 和 2021-01-06 00:00:00

                // 非整天时间戳
                Arguments.of(1609545199999L, 1609459200001L, 0), // 2021-01-01 23:59:59.999 和 2021-01-01 00:00:00.001
                Arguments.of(1609545200001L, 1609459199999L, 1) // 2021-01-02 00:00:00.001 和 2021-01-01 23:59:59.999
        );
    }

    /**
     * 测试getTime方法
     */
    @Test
    public void testGetTime() {
        // 测试特定时间戳
        long timestamp = 1609459200000L; // 2021-01-01 00:00:00
        String formattedTime = TimeUtil.getTime(timestamp);
        assertEquals("2021-01-01 00:00:00", formattedTime);

        // 测试当前时间
        long currentTime = System.currentTimeMillis();
        String currentFormattedTime = TimeUtil.getTime(currentTime);
        assertNotNull(currentFormattedTime);
        assertTrue(currentFormattedTime.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}"));
    }

    /**
     * 测试formatLongToString方法
     */
    @Test
    public void testFormatLongToString() {
        // 测试特定时间戳
        long timestamp = 1609459200000L; // 2021-01-01 00:00:00
        String formattedTime = TimeUtil.formatLongToString(timestamp);
        assertEquals("2021-01-01 00:00:00", formattedTime);

        // 测试当前时间
        long currentTime = System.currentTimeMillis();
        String currentFormattedTime = TimeUtil.formatLongToString(currentTime);
        assertNotNull(currentFormattedTime);
        assertTrue(currentFormattedTime.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}"));
    }

    /**
     * 测试formatLongToString2方法
     */
    @Test
    public void testFormatLongToString2() {
        // 测试特定时间戳
        long timestamp = 1609459200000L; // 2021-01-01 00:00:00
        String formattedTime = TimeUtil.formatLongToString2(timestamp);
        assertEquals("2021-01-01 00:00:00", formattedTime);

        // 测试带有秒的时间戳
        long timestampWithSeconds = 1609459225000L; // 2021-01-01 00:00:25
        String formattedTimeWithSeconds = TimeUtil.formatLongToString2(timestampWithSeconds);
        assertEquals("2021-01-01 00:00:00", formattedTimeWithSeconds); // 秒应该被设置为00
    }

    /**
     * 测试padTo19Digits方法
     */
    @Test
    public void testPadTo19Digits() {
        // 测试已经是19位的数字
        long number19Digits = 1000000000000000000L; // 19位数字
        assertEquals(1000000000000000000L, TimeUtil.padTo19Digits(number19Digits));

        // 测试小于19位的数字
        long number10Digits = 1234567890L; // 10位数字
        assertEquals(1234567890000000000L, TimeUtil.padTo19Digits(number10Digits));

        // 测试13位时间戳（毫秒级）
        long timestamp13Digits = 1609459200000L; // 13位时间戳
        assertEquals(1609459200000000000L, TimeUtil.padTo19Digits(timestamp13Digits));
    }

    /**
     * 测试roundUpToMinute方法
     */
    @Test
    public void testRoundUpToMinute() {
        // 测试整分钟时间戳
        long exactMinute = 1609459200000L; // 2021-01-01 00:00:00
        assertEquals(1609459260000L, TimeUtil.roundUpToMinute(exactMinute)); // 应该进到下一分钟

        // 测试非整分钟时间戳
        long notExactMinute = 1609459225000L; // 2021-01-01 00:00:25
        assertEquals(1609459260000L, TimeUtil.roundUpToMinute(notExactMinute)); // 应该进到下一分钟

        // 测试接近下一分钟的时间戳
        long almostNextMinute = 1609459259999L; // 2021-01-01 00:00:59.999
        assertEquals(1609459260000L, TimeUtil.roundUpToMinute(almostNextMinute)); // 应该进到下一分钟
    }

    /**
     * 测试roundDownToMinute方法
     */
    @Test
    public void testRoundDownToMinute() {
        // 测试整分钟时间戳
        long exactMinute = 1609459200000L; // 2021-01-01 00:00:00
        assertEquals(1609459200000L, TimeUtil.roundDownToMinute(exactMinute)); // 应该保持不变

        // 测试非整分钟时间戳
        long notExactMinute = 1609459225000L; // 2021-01-01 00:00:25
        assertEquals(1609459200000L, TimeUtil.roundDownToMinute(notExactMinute)); // 应该舍到当前分钟

        // 测试接近下一分钟的时间戳
        long almostNextMinute = 1609459259999L; // 2021-01-01 00:00:59.999
        assertEquals(1609459200000L, TimeUtil.roundDownToMinute(almostNextMinute)); // 应该舍到当前分钟
    }

    /**
     * 测试roundDownToDay方法
     */
    @Test
    public void testRoundDownToDay() {
        // 测试整天时间戳
        long exactDay = 1609459200000L; // 2021-01-01 00:00:00
        assertEquals(1609459200000L, TimeUtil.roundDownToDay(exactDay)); // 应该保持不变

        // 测试非整天时间戳
        long notExactDay = 1609545199999L; // 2021-01-01 23:59:59.999
        assertEquals(1609459200000L, TimeUtil.roundDownToDay(notExactDay)); // 应该舍到当天开始

        // 测试接近下一天的时间戳
        long almostNextDay = 1609545199999L; // 2021-01-01 23:59:59.999
        assertEquals(1609459200000L, TimeUtil.roundDownToDay(almostNextDay)); // 应该舍到当天开始
    }

    /**
     * 测试computeDayDifference方法
     */
    @ParameterizedTest
    @MethodSource("provideDatesForDayDifference")
    public void testComputeDayDifference(long timestamp1, long timestamp2, long expectedDifference) {
        assertEquals(expectedDifference, TimeUtil.computeDayDifference(timestamp1, timestamp2));
    }

    /**
     * 测试DATE_TIME_FORMAT的线程安全性
     */
    @Test
    public void testDateTimeFormatThreadSafety() throws InterruptedException {
        int threadCount = 10;
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);

        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            executorService.submit(() -> {
                try {
                    // 每个线程使用不同的时间戳
                    long timestamp = 1609459200000L + threadId * 1000;
                    String formattedTime = TimeUtil.DATE_TIME_FORMAT.get().format(new Date(timestamp));
                    // 验证格式化结果是否正确
                    SimpleDateFormat localSdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String expectedTime = localSdf.format(new Date(timestamp));
                    assertEquals(expectedTime, formattedTime);
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await(); // 等待所有线程完成
        executorService.shutdown();
    }

    /**
     * 测试DATE_TIME_FORMAT_2的线程安全性
     */
    @Test
    public void testDateTimeFormat2ThreadSafety() throws InterruptedException {
        int threadCount = 10;
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);

        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            executorService.submit(() -> {
                try {
                    // 每个线程使用不同的时间戳
                    long timestamp = 1609459200000L + threadId * 1000;
                    String formattedTime = TimeUtil.DATE_TIME_FORMAT_2.get().format(new Date(timestamp));
                    // 验证格式化结果是否正确
                    SimpleDateFormat localSdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");
                    String expectedTime = localSdf.format(new Date(timestamp));
                    assertEquals(expectedTime, formattedTime);
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await(); // 等待所有线程完成
        executorService.shutdown();
    }

    /**
     * 测试常量值是否正确
     */
    @Test
    public void testConstants() {
        // 测试分钟常量
        assertEquals(60000, TimeUtil.ONE_MINUTE_MS);
        assertEquals(60L, TimeUtil.ONE_MIN_SEC_LONG);
        assertEquals(60, TimeUtil.ONE_MIN_SEC_INT);
        assertEquals(60 * 1000L, TimeUtil.ONE_MIN_MS_LONG);
        assertEquals(60 * 1000, TimeUtil.ONE_MIN_MS_INT);

        // 测试小时常量
        assertEquals(3600000, TimeUtil.ONE_HOUR_MS);
        assertEquals(60 * 60 * 1000L, TimeUtil.ONE_HOUR_MS_LONG);

        // 测试天常量
        assertEquals(24 * 3600000L, TimeUtil.ONE_DAY_MS);
        assertEquals(24 * 3600, TimeUtil.ONE_DAY_S);
        assertEquals(24 * 60 * 60 * 1000L, TimeUtil.ONE_DAY_MS_LONG);

        // 测试其他常量
        assertEquals(30 * 24 * 60 * 60 * 1000L, TimeUtil.OUT_DAY_MS_LONG);
        assertEquals(30 * 24 * 60 * 60, TimeUtil.OUT_DAY_SEC_INT);
    }

    /**
     * 测试边界情况
     */
    @Test
    public void testEdgeCases() {
        // 测试0时间戳
        assertEquals("1970-01-01 08:00:00", TimeUtil.getTime(0)); // 考虑到中国时区为UTC+8
        assertEquals("1970-01-01 08:00:00", TimeUtil.formatLongToString(0)); // 考虑到中国时区为UTC+8
        assertEquals("1970-01-01 08:00:00", TimeUtil.formatLongToString2(0)); // 考虑到中国时区为UTC+8
        assertEquals(0, TimeUtil.padTo19Digits(0));
        assertEquals(TimeUtil.ONE_MIN_MS_LONG, TimeUtil.roundUpToMinute(0));
        assertEquals(0, TimeUtil.roundDownToMinute(0));
        assertEquals(0, TimeUtil.roundDownToDay(0));

        // 测试负时间戳
        long negativeTimestamp = -86400000L; // 1970-01-01之前的一天
        assertNotNull(TimeUtil.getTime(negativeTimestamp));
        assertNotNull(TimeUtil.formatLongToString(negativeTimestamp));
        assertNotNull(TimeUtil.formatLongToString2(negativeTimestamp));
        assertEquals(-86400000L, TimeUtil.roundDownToDay(negativeTimestamp));
        assertEquals(1, TimeUtil.computeDayDifference(0, negativeTimestamp));
    }
}
