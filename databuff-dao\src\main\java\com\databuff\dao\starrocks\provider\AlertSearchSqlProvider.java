package com.databuff.dao.starrocks.provider;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.databuff.common.threadLocal.ThreadLocalUtil;
import com.databuff.common.utils.AlarmIdGenerator;
import com.databuff.common.utils.DateUtils;
import com.databuff.entity.dto.DCAlarmDto;
import com.databuff.service.DomainManagerObjService;
import com.databuff.util.SpringContextUtil;
import com.google.common.collect.Lists;
import dto.DConf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.builder.annotation.ProviderMethodResolver;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.core.env.Environment;

import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.databuff.common.utils.DateUtils.stringToLong;
import static com.databuff.service.DomainManagerObjService.getGidFromThread;

@Slf4j
public class AlertSearchSqlProvider implements ProviderMethodResolver {


    private final DomainManagerObjService domainManagerObjService;

    private static final String ALARM_TEST_KEY = "databuff.alarm.test";

    /**
     * 获取告警测试配置信息
     * <p>
     * 从Spring环境变量中获取指定配置键对应的JSON配置，将其反序列化为DConf<Boolean>对象，
     * 最终返回配置的布尔值结果。整个过程包含异常处理机制。
     *
     * @return boolean - 当成功获取并解析配置，且配置值为true时返回true；
     * 配置不存在/解析失败/配置值为false时返回false
     */
    private boolean optimizeSwitch() {
        // 从Spring上下文获取环境变量配置
        try {
            final Environment env = SpringContextUtil.getBean(Environment.class);
            final String property = env.getProperty(ALARM_TEST_KEY);

            // 处理配置不存在的情况
            if (property == null) {
                log.debug("Alarm test config property {} is null", ALARM_TEST_KEY);
                return true;
            }

            // 反序列化JSON配置并提取布尔值结果
            DConf<Boolean> dConf = JSON.parseObject(property, new TypeReference<DConf<Boolean>>() {
            });
            return (dConf == null || dConf.getValue() == null) ? true : dConf.getValue();
        }
        // 处理JSON解析异常
        catch (JSONException e) {
            log.error("Failed to parse alarm test config: {}", e.getMessage());
            return true;
        }
    }


    public AlertSearchSqlProvider() {
        this.domainManagerObjService = SpringContextUtil.getBean(DomainManagerObjService.class);
    }


    public String countByServiceIdStatus(@Param("id") String id,
                                         @Param("allEntityPermission") boolean allEntityPermission,
                                         @Param("gids") Collection<String> gids,
                                         @Param("idLike") String idLike,
                                         @Param("apiKey") String apiKey,
                                         @Param("fromTime") String fromTime,
                                         @Param("toTime") String toTime,
                                         @Param("descriptionLowerCase") String descriptionLowerCase,
                                         @Param("trigger") JSONObject trigger,
                                         @Param(Constants.WRAPPER) Wrapper<DCAlarmDto> queryWrapper) {

        final List<String> createDts;
        if (id != null) {
            createDts = Lists.newArrayList(AlarmIdGenerator.extractStrDateFromId(id));
        } else {
            createDts = DateUtils.getFormattedDateRange(fromTime, toTime);
        }

        SQL sql = new SQL();
        sql.SELECT("get_json_string(`trigger`, \"$.serviceId\") as triggerId  ,da.`status` , count(1) as cnt");
        sql.FROM("dc_alarm da");
        sql.WHERE("apiKey = #{apiKey} AND get_json_string(`trigger`, \"$.serviceId\") is not null");
        final boolean domainManagerStatusOpen = domainManagerObjService.getDomainManagerStatusOpen();
        sql.WHERE(domainManagerStatusOpen ? "da.gid IS NOT NULL" : "da.gid IS NULL");
        // 若有全局权限，则尝试调用全局权限
        if (!allEntityPermission) {
            allEntityPermission = domainManagerObjService.hasAllEntityPermission();
        }
        if (!allEntityPermission) {
            gids = (gids == null || gids.isEmpty()) ? getGidFromThread() : gids;
            sql.WHERE((gids != null && !gids.isEmpty()) ?
                    String.format("(da.gid in ('%s'))", String.join("','", gids)) :
                    "(1=0)");
        }

        addCommonConditions(sql, id, idLike, fromTime, toTime, descriptionLowerCase, createDts, queryWrapper, "da.", gids);
        if (trigger != null) {
            addTriggerConditions(sql, trigger, null, "da.");
        }
        sql.GROUP_BY("triggerId", "da.`status`");
        sql.ORDER_BY("triggerId ASC");
        return sql.toString();
    }

    public String countByHostStatus(@Param("id") String id,
                                    @Param("allEntityPermission") boolean allEntityPermission,
                                    @Param("gids") Collection<String> gids,
                                    @Param("idLike") String idLike,
                                    @Param("apiKey") String apiKey,
                                    @Param("fromTime") String fromTime,
                                    @Param("toTime") String toTime,
                                    @Param("descriptionLowerCase") String descriptionLowerCase,
                                    @Param("trigger") JSONObject trigger,
                                    @Param(Constants.WRAPPER) Wrapper<DCAlarmDto> queryWrapper) {

        final List<String> createDts;
        if (id != null) {
            createDts = Lists.newArrayList(AlarmIdGenerator.extractStrDateFromId(id));
        } else {
            createDts = DateUtils.getFormattedDateRange(fromTime, toTime);
        }

        SQL sql = new SQL();
        sql.SELECT("get_json_string(`trigger`, \"$.host\") as triggerId  ,da.`status` , count(1) as cnt");
        sql.FROM("dc_alarm da");
        sql.WHERE("apiKey = #{apiKey} AND get_json_string(`trigger`, \"$.host\") is not null");
        final boolean domainManagerStatusOpen = domainManagerObjService.getDomainManagerStatusOpen();
        sql.WHERE(domainManagerStatusOpen ? "da.gid IS NOT NULL" : "da.gid IS NULL");
        // 若有全局权限，则尝试调用全局权限
        if (!allEntityPermission) {
            allEntityPermission = domainManagerObjService.hasAllEntityPermission();
        }
        if (!allEntityPermission) {
            gids = (gids == null || gids.isEmpty()) ? getGidFromThread() : gids;
            sql.WHERE((gids != null && !gids.isEmpty()) ?
                    String.format("(da.gid in ('%s'))", String.join("','", gids)) :
                    "(1=0)");
        }

        addCommonConditions(sql, id, idLike, fromTime, toTime, descriptionLowerCase, createDts, queryWrapper, "da.", gids);
        if (trigger != null) {
            addTriggerConditions(sql, trigger, null, "da.");
        }
        sql.GROUP_BY("triggerId", "da.`status`");
        sql.ORDER_BY("triggerId ASC");
        return sql.toString();
    }

    public String countByServiceId(@Param("id") String id,
                                   @Param("allEntityPermission") boolean allEntityPermission,
                                   @Param("gids") Collection<String> gids,
                                   @Param("idLike") String idLike,
                                   @Param("apiKey") String apiKey,
                                   @Param("fromTime") String fromTime,
                                   @Param("toTime") String toTime,
                                   @Param("descriptionLowerCase") String descriptionLowerCase,
                                   @Param("trigger") JSONObject trigger,
                                   @Param(Constants.WRAPPER) Wrapper<DCAlarmDto> queryWrapper) {

        final List<String> createDts;
        if (id != null) {
            createDts = Lists.newArrayList(AlarmIdGenerator.extractStrDateFromId(id));
        } else {
            createDts = DateUtils.getFormattedDateRange(fromTime, toTime);
        }

        SQL sql = new SQL();
        sql.SELECT("get_json_string(`trigger`, \"$.serviceId\") as triggerId  , count(1) as cnt");
        sql.FROM("dc_alarm da");
        sql.WHERE("apiKey = #{apiKey} AND get_json_string(`trigger`, \"$.serviceId\") is not null");
        final boolean domainManagerStatusOpen = domainManagerObjService.getDomainManagerStatusOpen();
        sql.WHERE(domainManagerStatusOpen ? "da.gid IS NOT NULL" : "da.gid IS NULL");
        // 若有全局权限，则尝试调用全局权限
        if (!allEntityPermission) {
            allEntityPermission = domainManagerObjService.hasAllEntityPermission();
        }
        if (!allEntityPermission) {
            gids = (gids == null || gids.isEmpty()) ? getGidFromThread() : gids;
            sql.WHERE((gids != null && !gids.isEmpty()) ?
                    String.format("(da.gid in ('%s'))", String.join("','", gids)) :
                    "(1=0)");
        }
        addCommonConditions(sql, id, idLike, fromTime, toTime, descriptionLowerCase, createDts, queryWrapper, "da.", gids);
        if (trigger != null) {
            addTriggerConditions(sql, trigger, null, "da.");
        }
        sql.GROUP_BY("triggerId");
        sql.ORDER_BY("triggerId ASC");
        return sql.toString();
    }

    public String countByServiceInstance(@Param("id") String id,
                                         @Param("allEntityPermission") boolean allEntityPermission,
                                         @Param("gids") Collection<String> gids,
                                         @Param("idLike") String idLike,
                                         @Param("apiKey") String apiKey,
                                         @Param("fromTime") String fromTime,
                                         @Param("toTime") String toTime,
                                         @Param("descriptionLowerCase") String descriptionLowerCase,
                                         @Param("trigger") JSONObject trigger,
                                         @Param(Constants.WRAPPER) Wrapper<DCAlarmDto> queryWrapper) {

        final List<String> createDts;
        if (id != null) {
            createDts = Lists.newArrayList(AlarmIdGenerator.extractStrDateFromId(id));
        } else {
            createDts = DateUtils.getFormattedDateRange(fromTime, toTime);
        }

        SQL sql = new SQL();
        sql.SELECT("get_json_string(`trigger`, \"$.serviceInstance\") as triggerId  , count(1) as cnt");
        sql.FROM("dc_alarm da");
        sql.WHERE("apiKey = #{apiKey} AND get_json_string(`trigger`, \"$.serviceInstance\") is not null");
        final boolean domainManagerStatusOpen = domainManagerObjService.getDomainManagerStatusOpen();
        sql.WHERE(domainManagerStatusOpen ? "da.gid IS NOT NULL" : "da.gid IS NULL");
        // 若有全局权限，则尝试调用全局权限
        if (!allEntityPermission) {
            allEntityPermission = domainManagerObjService.hasAllEntityPermission();
        }
        if (!allEntityPermission) {
            gids = (gids == null || gids.isEmpty()) ? getGidFromThread() : gids;
            sql.WHERE((gids != null && !gids.isEmpty()) ?
                    String.format("(da.gid in ('%s'))", String.join("','", gids)) :
                    "(1=0)");
        }
        addCommonConditions(sql, id, idLike, fromTime, toTime, descriptionLowerCase, createDts, queryWrapper, "da.", gids);
        if (trigger != null) {
            addTriggerConditions(sql, trigger, null, "da.");
        }
        sql.GROUP_BY("triggerId");
        sql.ORDER_BY("triggerId ASC");
        return sql.toString();
    }

    public String countByHost(@Param("id") String id,
                              @Param("allEntityPermission") boolean allEntityPermission,
                              @Param("gids") Collection<String> gids,
                              @Param("idLike") String idLike,
                              @Param("apiKey") String apiKey,
                              @Param("fromTime") String fromTime,
                              @Param("toTime") String toTime,
                              @Param("descriptionLowerCase") String descriptionLowerCase,
                              @Param("trigger") JSONObject trigger,
                              @Param(Constants.WRAPPER) Wrapper<DCAlarmDto> queryWrapper) {

        final List<String> createDts;
        if (id != null) {
            createDts = Lists.newArrayList(AlarmIdGenerator.extractStrDateFromId(id));
        } else {
            createDts = DateUtils.getFormattedDateRange(fromTime, toTime);
        }

        SQL sql = new SQL();
        sql.SELECT("get_json_string(`trigger`, \"$.host\") as host, count(1) as cnt");
        sql.FROM("dc_alarm da");
        sql.WHERE("apiKey = #{apiKey} AND get_json_string(`trigger`, \"$.host\") is not null");
        final boolean domainManagerStatusOpen = domainManagerObjService.getDomainManagerStatusOpen();
        sql.WHERE(domainManagerStatusOpen ? "da.gid IS NOT NULL" : "da.gid IS NULL");
        // 若有全局权限，则尝试调用全局权限
        if (!allEntityPermission) {
            allEntityPermission = domainManagerObjService.hasAllEntityPermission();
        }
        if (!allEntityPermission) {
            gids = (gids == null || gids.isEmpty()) ? getGidFromThread() : gids;
            sql.WHERE((gids != null && !gids.isEmpty()) ?
                    String.format("(da.gid in ('%s'))", String.join("','", gids)) :
                    "(1=0)");
        }
        addCommonConditions(sql, id, idLike, fromTime, toTime, descriptionLowerCase, createDts, queryWrapper, "da.", gids);
        if (trigger != null) {
            addTriggerConditions(sql, trigger, null, "da.");
        }
        sql.GROUP_BY("get_json_string(`trigger`, \"$.host\")");
        sql.ORDER_BY("get_json_string(`trigger`, \"$.host\") ASC");
        return sql.toString();
    }

    /**
     * 根据指定的字段分组统计告警数量。
     *
     * @param id                   告警ID
     * @param allEntityPermission  是否具有所有实体的权限
     * @param gids                 实体ID集合
     * @param idLike               类似ID的字符串
     * @param apiKey               API密钥
     * @param fromTime             开始时间
     * @param toTime               结束时间
     * @param descriptionLowerCase 描述的字符串（小写）
     * @param trigger              触发条件的JSON对象
     * @param queryWrapper         查询条件包装器
     * @param groupTriggerFields   触发器字段分组列表
     * @param groupFields          普通字段分组列表
     * @return 构建的SQL查询字符串
     */
    public String countByFields(@Param("id") String id,
                                @Param("allEntityPermission") boolean allEntityPermission,
                                @Param("domainManagerStatusOpen") boolean domainManagerStatusOpen,
                                @Param("gids") Collection<String> gids,
                                @Param("idLike") String idLike,
                                @Param("apiKey") String apiKey,
                                @Param("fromTime") String fromTime,
                                @Param("toTime") String toTime,
                                @Param("descriptionLowerCase") String descriptionLowerCase,
                                @Param("trigger") JSONObject trigger,
                                @Param(Constants.WRAPPER) Wrapper<DCAlarmDto> queryWrapper,
                                @Param("groupTriggerFields") List<String> groupTriggerFields,
                                @Param("groupFields") List<String> groupFields) {

        final List<String> createDts;
        if (id != null) {
            createDts = Lists.newArrayList(AlarmIdGenerator.extractStrDateFromId(id));
        } else {
            createDts = DateUtils.getFormattedDateRange(fromTime, toTime);
        }

        SQL sql = new SQL();
        // 处理触发器字段
        String triggerSelectFields = "";
        if (groupTriggerFields != null && !groupTriggerFields.isEmpty()) {
            triggerSelectFields = groupTriggerFields.stream()
                    .map(field -> String.format("get_json_string(`trigger`, \"$.%s\") as %s", field, field))
                    .collect(Collectors.joining(", "));
        }

        // 处理普通字段
        String normalSelectFields = "";
        if (groupFields != null && !groupFields.isEmpty()) {
            normalSelectFields = groupFields.stream()
                    .map(field -> String.format("da.%s", field))
                    .collect(Collectors.joining(", "));
        }

        // 组合SELECT字段
        String selectFields = triggerSelectFields;
        if (groupFields != null && !groupFields.isEmpty() && !normalSelectFields.isEmpty()) {
            selectFields = selectFields.isEmpty() ? normalSelectFields : selectFields + ", " + normalSelectFields;
        }

        // 处理两个字段列表都为空的情况
        if (selectFields.isEmpty()) {
            sql.SELECT("count(1) as cnt");
        } else {
            sql.SELECT(selectFields + ", count(1) as cnt");
        }
        sql.FROM("dc_alarm da");
        sql.WHERE("apiKey = #{apiKey}");
        if (!domainManagerStatusOpen) {
            domainManagerStatusOpen = domainManagerObjService.getDomainManagerStatusOpen();
        }
        sql.WHERE(domainManagerStatusOpen ? "da.gid IS NOT NULL" : "da.gid IS NULL");

        if (!allEntityPermission) {
            allEntityPermission = domainManagerObjService.hasAllEntityPermission();
        }
        if (!allEntityPermission) {
            gids = (gids == null || gids.isEmpty()) ? getGidFromThread() : gids;
            sql.WHERE((gids != null && !gids.isEmpty()) ?
                    String.format("(da.gid in ('%s'))", String.join("','", gids)) :
                    "(1=0)");
        }

        addCommonConditions(sql, id, idLike, fromTime, toTime, descriptionLowerCase, createDts, queryWrapper, "da.", gids);
        if (trigger != null) {
            addTriggerConditions(sql, trigger, null, "da.");
        }

        // 组合GROUP BY字段
        String triggerGroupByFields = "";
        if (groupTriggerFields != null && !groupTriggerFields.isEmpty()) {
            triggerGroupByFields = groupTriggerFields.stream()
                    .map(field -> String.format("get_json_string(`trigger`, \"$.%s\")", field))
                    .collect(Collectors.joining(", "));
        }

        String normalGroupByFields = "";
        if (groupFields != null && !groupFields.isEmpty()) {
            normalGroupByFields = groupFields.stream()
                    .map(field -> String.format("da.%s", field))
                    .collect(Collectors.joining(", "));
        }

        String groupByFields = triggerGroupByFields;
        if (groupFields != null && !groupFields.isEmpty() && !normalGroupByFields.isEmpty()) {
            groupByFields = groupByFields.isEmpty() ? normalGroupByFields : groupByFields + ", " + normalGroupByFields;
        }

        if (!groupByFields.isEmpty()) {
            sql.GROUP_BY(groupByFields);
        }

        // 组合ORDER BY字段
        String triggerOrderByFields = "";
        if (groupTriggerFields != null && !groupTriggerFields.isEmpty()) {
            triggerOrderByFields = groupTriggerFields.stream()
                    .map(field -> String.format("%s ASC", field))
                    .collect(Collectors.joining(", "));
        }

        String normalOrderByFields = "";
        if (groupFields != null && !groupFields.isEmpty()) {
            normalOrderByFields = groupFields.stream()
                    .map(field -> String.format("%s ASC", field))
                    .collect(Collectors.joining(", "));
        }

        String orderByFields = triggerOrderByFields;
        if (groupFields != null && !groupFields.isEmpty() && !normalOrderByFields.isEmpty()) {
            orderByFields = orderByFields.isEmpty() ? normalOrderByFields : orderByFields + ", " + normalOrderByFields;
        }

        if (!orderByFields.isEmpty()) {
            sql.ORDER_BY(orderByFields);
        }

        return sql.toString();
    }

    public String count(@Param("id") String id,
                        @Param("allEntityPermission") boolean allEntityPermission,
                        @Param("gids") Collection<String> gids,
                        @Param("idLike") String idLike,
                        @Param("apiKey") String apiKey,
                        @Param("fromTime") String fromTime,
                        @Param("toTime") String toTime,
                        @Param("descriptionLowerCase") String descriptionLowerCase,
                        @Param("trigger") JSONObject trigger,
                        @Param(Constants.WRAPPER) Wrapper<DCAlarmDto> queryWrapper) {

        final List<String> createDts;
        if (id != null) {
            createDts = Lists.newArrayList(AlarmIdGenerator.extractStrDateFromId(id));
        } else {
            createDts = DateUtils.getFormattedDateRange(fromTime, toTime);
        }

        SQL sql = new SQL();
        sql.SELECT("count(1)");
        sql.FROM("dc_alarm da");
        sql.WHERE("apiKey = #{apiKey}");
        final boolean domainManagerStatusOpen = domainManagerObjService.getDomainManagerStatusOpen();
        sql.WHERE(domainManagerStatusOpen ? "da.gid IS NOT NULL" : "da.gid IS NULL");
        // 若有全局权限，则尝试调用全局权限
        if (!allEntityPermission) {
            allEntityPermission = domainManagerObjService.hasAllEntityPermission();
        }
        if (!allEntityPermission) {
            gids = (gids == null || gids.isEmpty()) ? getGidFromThread() : gids;
            sql.WHERE((gids != null && !gids.isEmpty()) ?
                    String.format("(da.gid in ('%s'))", String.join("','", gids)) :
                    "(1=0)");
        }
        addCommonConditions(sql, id, idLike, fromTime, toTime, descriptionLowerCase, createDts, queryWrapper, "da.", gids);
        if (trigger != null) {
            addTriggerConditions(sql, trigger, null, "da.");
        }
        return sql.toString();
    }

    private void addCommonConditionsForTrend(SQL sql, String id, String idLike, String descriptionLowerCase, List<String> createDts, Wrapper<DCAlarmDto> queryWrapper, String tbPrefix, Collection<String> gids) {
        if (tbPrefix == null) {
            tbPrefix = StringUtils.EMPTY;
        }
        if (StringUtils.isNotEmpty(id)) {
            sql.AND();
            sql.WHERE(String.format("%sid = #{id}", tbPrefix));
        }
        if (StringUtils.isNotEmpty(idLike)) {
            sql.AND();
            sql.WHERE(String.format("%sid LIKE CONCAT('%%', #{idLike}, '%%')", tbPrefix));
        }
        if (createDts != null && createDts.stream().filter(Objects::nonNull).count() > 0) {
            sql.AND();
            sql.WHERE(String.format("%screateDt IN (%s)", tbPrefix, createDts.stream().map(dt -> "'" + dt + "'").collect(Collectors.joining(", "))));
        }

        if (StringUtils.isNotEmpty(descriptionLowerCase)) {
            sql.AND();
            sql.WHERE(String.format("LOWER(%sdescription) LIKE CONCAT('%%', LOWER(#{descriptionLowerCase}), '%%')", tbPrefix));
        }
        if (queryWrapper != null && !queryWrapper.getSqlSegment().isEmpty()) {
            sql.AND();
            sql.WHERE(queryWrapper.getSqlSegment());
        }

        // 设置域ID列表 @see com.databuff.interceptor.SqlGIDConditionInterceptor
        if (gids != null && !gids.isEmpty()) {
            ThreadLocalUtil.setGids(gids);
        }
    }

    /**
     * 添加通用查询条件到SQL对象中
     *
     * @param sql                  SQL构建对象
     * @param id                   ID精确匹配条件值
     * @param idLike              ID模糊匹配条件值
     * @param fromTime            起始时间条件（格式要求：yyyy-MM-dd HH:mm:ss）
     * @param toTime              结束时间条件（格式要求：yyyy-MM-dd HH:mm:ss）
     * @param descriptionLowerCase 描述字段小写匹配条件
     * @param createDts           创建日期列表条件（多值匹配）
     * @param queryWrapper        附加查询条件包装器
     * @param tbPrefix            表前缀（用于字段名前缀处理，null时自动转为空字符串）
     * @param gids                域ID集合（用于权限过滤）
     */
    private void addCommonConditions(SQL sql, String id, String idLike, String fromTime, String toTime, String descriptionLowerCase, List<String> createDts, Wrapper<DCAlarmDto> queryWrapper, String tbPrefix, Collection<String> gids) {
        // 处理表前缀默认值
        if (tbPrefix == null) {
            tbPrefix = StringUtils.EMPTY;
        }

        /* ID条件处理 */
        if (StringUtils.isNotEmpty(id)) {
            sql.AND();
            sql.WHERE(String.format("%sid = #{id}", tbPrefix));
        }

        /* 模糊ID条件处理 */
        if (StringUtils.isNotEmpty(idLike)) {
            sql.AND();
            sql.WHERE(String.format("%sid LIKE CONCAT('%%', #{idLike}, '%%')", tbPrefix));
        }

        /* 创建日期多值匹配 */
        if (createDts != null && createDts.stream().filter(Objects::nonNull).count() > 0) {
            sql.AND();
            sql.WHERE(String.format("%screateDt IN (%s)", tbPrefix, createDts.stream().map(dt -> "'" + dt + "'").collect(Collectors.joining(", "))));
        }

        /* 时间范围条件处理（分优化模式/普通模式） */
        if (optimizeSwitch()) {
            // 优化模式下直接处理时间戳条件
            final String pattern = "yyyy-MM-dd HH:mm:ss";
            boolean hasCondition = false;

            if (fromTime != null) {
                if (!isValidTime(fromTime, pattern)) throw new IllegalArgumentException("Invalid fromTime format");
                sql.AND();
                sql.WHERE(String.format("%stimestamp >= %s", tbPrefix, stringToLong(pattern, fromTime)));
                hasCondition = true;
            }

            if (toTime != null) {
                if (!isValidTime(toTime, pattern)) throw new IllegalArgumentException("Invalid toTime format");
                if (hasCondition) sql.AND();
                sql.WHERE(String.format("%sstartTriggerTime < %s", tbPrefix, stringToLong(pattern, toTime)));
            }
        } else {
            // 普通模式下使用子查询处理时间范围
            if (fromTime != null && toTime != null) {
                sql.AND();
                sql.WHERE("EXISTS (SELECT 1 FROM dc_alarm_aggregate daa WHERE daa.id = da.id AND daa.`time` >= #{fromTime} AND daa.`time` < #{toTime})");
            } else if (fromTime != null) {
                sql.AND();
                sql.WHERE("EXISTS (SELECT 1 FROM dc_alarm_aggregate daa WHERE daa.id = da.id AND daa.`time` >= #{fromTime})");
            } else if (toTime != null) {
                sql.AND();
                sql.WHERE("EXISTS (SELECT 1 FROM dc_alarm_aggregate daa WHERE daa.id = da.id AND daa.`time` < #{toTime})");
            }
        }

        /* 描述字段模糊匹配（强制小写匹配） */
        if (StringUtils.isNotEmpty(descriptionLowerCase)) {
            sql.AND();
            sql.WHERE(String.format("LOWER(%sdescription) LIKE CONCAT('%%', LOWER(#{descriptionLowerCase}), '%%')", tbPrefix));
        }

        /* 附加自定义查询条件 */
        if (queryWrapper != null && !queryWrapper.getSqlSegment().isEmpty()) {
            sql.AND();
            sql.WHERE(queryWrapper.getSqlSegment());
        }

        /* 设置域ID过滤条件（配合SqlGIDConditionInterceptor使用） */
        if (gids != null && !gids.isEmpty()) {
            ThreadLocalUtil.setGids(gids);
        }
    }


    private boolean isValidTime(String timeStr, String pattern) {
        try {
            new SimpleDateFormat(pattern).parse(timeStr);
            return true;
        } catch (ParseException e) {
            return false;
        }
    }

    /**
     * 添加触发条件到SQL查询中，并防止SQL注入
     *
     * @param sql SQL构建对象
     * @param trigger 触发条件Map
     * @param field 字段名
     * @param tbPrefix 表前缀
     */
    private void addTriggerConditions(SQL sql, Map<String, Object> trigger, String field, String tbPrefix) {
        if (trigger == null) {
            return;
        }
        if (field == null) {
            field = StringUtils.EMPTY;
        }
        if (tbPrefix == null) {
            tbPrefix = StringUtils.EMPTY;
        }

        for (Map.Entry<String, Object> entry : trigger.entrySet()) {
            final String left = entry.getKey();
            if (left == null) {
                continue;
            }
            final Object right = entry.getValue();
            if (right == null) {
                continue;
            }
            // 处理字符串类型的值
            if (right instanceof String) {
                sql.AND();
                String rightValue = (String) right;

                // 如果值看起来像SQL语句，记录日志但仍然正常处理
                if (isSqlLike(rightValue)) {
                    log.debug("检测到可能的SQL语句作为触发条件值: {}", rightValue);
                    // 不需要特殊处理，所有字段（包括resource）都会通过escapeSqlString进行安全处理
                }

                // 处理busName字段
                if (left.equals("busName")) {
                    // 【优化方案】原 array_contains_all 判断全集改为功能性更强的 any_match 存在性判断
                    String arrCast = "CAST(JSON_QUERY(tags, \"$.busName\") AS ARRAY<VARCHAR(255)>)";
                    // 安全地转义单引号，防止SQL注入
                    String safeValue = escapeSqlString(rightValue);
                    String condition = String.format("any_match(x -> x IN ('%s'), %s)", safeValue, arrCast); // IN单值等价于直接匹配
                    sql.WHERE(condition); // 条件示例: any_match(x -> x IN ('E10S202502'), busName数组)
                } else if (left.endsWith("id") || left.endsWith("Id") || left.endsWith("ID")) {
                    // 以id结尾的字段，使用精确匹配，并防止SQL注入
                    String safeValue = escapeSqlString(rightValue);
                    sql.WHERE(String.format("get_json_string(%s`trigger`, \"$.%s%s\") = \"%s\"",
                            tbPrefix, field, left, safeValue));
                } else {
                    // 其他字段使用LIKE匹配，并防止SQL注入
                    String safeValue = escapeSqlString(rightValue);
                    sql.WHERE(String.format("get_json_string(%s`trigger`, \"$.%s%s\") LIKE \"%s\"",
                            tbPrefix, field, left, "%" + safeValue + "%"));
                }
            } else if (right instanceof Integer || right instanceof Long) {
                // 数值类型不需要特殊处理，因为它们不会导致SQL注入
                sql.AND();
                sql.WHERE(String.format("get_json_int(%s`trigger`, \"$.%s%s\") = %s",
                        tbPrefix, field, left, right));
            } else if (right instanceof Double) {
                sql.AND();
                sql.WHERE(String.format("get_json_double(%s`trigger`, \"$.%s%s\") = %s",
                        tbPrefix, field, left, right));
            } else if (right instanceof Boolean) {
                sql.AND();
                sql.WHERE(String.format("get_json_bool(%s`trigger`, \"$.%s%s\") = %s",
                        tbPrefix, field, left, right));
            } else if (right instanceof Collection && !((Collection<?>) right).isEmpty()) {
                sql.AND();
                if (left.equals("busName")) {
                    Collection<String> values = ((Collection<?>) right).stream()
                            .filter(Objects::nonNull)
                            .map(Object::toString)
                            .collect(Collectors.toList());

                    // 【性能提升关键】将多条件 OR array_contains_all 替换为单条件 any_match + IN列表
                    String arrCast = "CAST(JSON_QUERY(tags, \"$.busName\") AS ARRAY<VARCHAR(255)>)";
                    // 安全地处理集合中的每个值
                    String inList = String.join(",", values.stream()
                            .map(v -> "'" + escapeSqlString(v) + "'")
                            .collect(Collectors.toList()));
                    String condition = String.format("any_match(x -> x IN (%s), %s)", inList, arrCast);
                    sql.WHERE(condition); // 条件示例: any_match(x -> x IN ('E10S202502','E10S202503'), busName数组)
                } else {
                    // 安全地处理集合中的每个值
                    String inValues = String.join("','",
                            ((Collection<?>) right).stream()
                                    .filter(Objects::nonNull)
                                    .map(Object::toString)
                                    .map(this::escapeSqlString)
                                    .collect(Collectors.toList()));
                    sql.WHERE(MessageFormat.format("get_json_string({0}`trigger`, \"$.{1}{2}\") IN (\''{3}\'')", tbPrefix, field, left, inValues));
                }
            } else if (right instanceof Map) {
                addTriggerConditions(sql, (Map<String, Object>) right, left, tbPrefix);
            }
        }
    }

    /**
     * 检查字符串是否看起来像SQL语句
     *
     * @param value 要检查的字符串
     * @return 如果字符串看起来像SQL语句，则返回true
     */
    private boolean isSqlLike(String value) {
        if (value == null || value.trim().isEmpty()) {
            return false;
        }

        // 转换为小写以便于检查
        String lowerValue = value.toLowerCase().trim();

        // 检查是否包含常见的SQL关键字
        return lowerValue.contains(" select ") ||
                lowerValue.startsWith("select ") ||
                lowerValue.contains(" from ") ||
                lowerValue.contains(" where ") ||
                lowerValue.contains(" group by ") ||
                lowerValue.contains(" order by ") ||
                lowerValue.contains(" insert ") ||
                lowerValue.contains(" update ") ||
                lowerValue.contains(" delete ") ||
                // 检查可能导致SQL注入的字符序列
                lowerValue.contains(";") ||
                lowerValue.contains("--") ||
                lowerValue.contains("/*") ||
                lowerValue.contains("*/");
    }

    /**
     * 转义SQL字符串中的特殊字符，防止SQL注入
     *
     * @param input 输入字符串
     * @return 转义后的安全字符串
     */
    private String escapeSqlString(String input) {
        if (input == null) {
            return "";
        }

        // 转义单引号（最常见的SQL注入向量）
        String result = input.replace("'", "\\'");

        // 转义其他可能导致SQL注入的字符
        result = result.replace("\"", "\\\""); // 双引号
        result = result.replace(";", "\\;");     // 分号
        result = result.replace("--", "\\--");   // SQL注释
        result = result.replace("/*", "\\/\\*"); // 多行注释开始
        result = result.replace("*/", "\\*\\/"); // 多行注释结束

        return result;
    }

    public String selectList(@Param("id") String id,
                             @Param("allEntityPermission") boolean allEntityPermission,
                             @Param("gids") Collection<String> gids,
                             @Param("idLike") String idLike,
                             @Param("apiKey") String apiKey,
                             @Param("fromTime") String fromTime,
                             @Param("toTime") String toTime,
                             @Param("descriptionLowerCase") String descriptionLowerCase,
                             @Param("trigger") JSONObject trigger,
                             @Param(Constants.WRAPPER) Wrapper<DCAlarmDto> queryWrapper) {

        final List<String> createDts;
        if (id != null) {
            createDts = Lists.newArrayList(AlarmIdGenerator.extractStrDateFromId(id));
        } else {
            createDts = DateUtils.getFormattedDateRange(fromTime, toTime);
        }

        SQL sql = new SQL();
        sql.SELECT("da.id");
        sql.FROM("dc_alarm da");
        sql.WHERE("da.apiKey = #{apiKey}");

        final boolean domainManagerStatusOpen = domainManagerObjService.getDomainManagerStatusOpen();
        sql.WHERE(domainManagerStatusOpen ? "da.gid IS NOT NULL" : "da.gid IS NULL");

        if(!allEntityPermission){
            allEntityPermission = domainManagerObjService.hasAllEntityPermission();
        }
        if (!allEntityPermission) {
            gids = (gids == null || gids.isEmpty()) ? getGidFromThread() : gids;
            sql.WHERE((gids != null && !gids.isEmpty()) ? String.format("(da.gid in ('%s'))", String.join("','", gids)) : "(1=0)");
        }
        addCommonConditions(sql, id, idLike, fromTime, toTime, descriptionLowerCase, createDts, queryWrapper, "da.", gids);
        if (trigger != null) {
            addTriggerConditions(sql, trigger, null, "da.");
        }
        return sql.toString();
    }

    public String searchParams(@Param("id") String id,
                               @Param("allEntityPermission") boolean allEntityPermission,
                               @Param("gids") Collection<String> gids,
                               @Param("idLike") String idLike,
                               @Param("apiKey") String apiKey,
                               @Param("fromTime") String fromTime,
                               @Param("toTime") String toTime,
                               @Param("descriptionLowerCase") String descriptionLowerCase,
                               @Param("trigger") JSONObject trigger,
                               @Param(Constants.WRAPPER) Wrapper<DCAlarmDto> queryWrapper) {

        final List<String> createDts;
        if (id != null) {
            createDts = Lists.newArrayList(AlarmIdGenerator.extractStrDateFromId(id));
        } else {
            createDts = DateUtils.getFormattedDateRange(fromTime, toTime);
        }

        SQL sql = new SQL();
        sql.SELECT("da.trigger", "da.tags");
        sql.FROM("dc_alarm da");
        sql.WHERE("da.apiKey = #{apiKey}");
        final boolean domainManagerStatusOpen = domainManagerObjService.getDomainManagerStatusOpen();
        sql.WHERE(domainManagerStatusOpen ? "da.gid IS NOT NULL" : "da.gid IS NULL");

        if(!allEntityPermission) {
            allEntityPermission = domainManagerObjService.hasAllEntityPermission();
        }
        if (!allEntityPermission) {
            gids = (gids == null || gids.isEmpty()) ? getGidFromThread() : gids;
            sql.WHERE((gids != null && !gids.isEmpty()) ?
                    String.format("(gid in ('%s'))", String.join("','", gids)) :
                    "(1=0)");
        }
        addCommonConditions(sql, id, idLike, fromTime, toTime, descriptionLowerCase, createDts, queryWrapper, "da.", gids);
        if (trigger != null) {
            addTriggerConditions(sql, trigger, null, "da.");
        }
        return sql.toString();
    }


    public String getAlarmCntTrend(@Param("allEntityPermission") boolean allEntityPermission,
                                   @Param("gids") Collection<String> gids,
                                   @Param("apiKey") String apiKey,
                                   @Param("interval") Integer interval,
                                   @Param("fromTime") String fromTime,
                                   @Param("toTime") String toTime,
                                   @Param("idLike") String idLike,
                                   @Param("descriptionLowerCase") String descriptionLowerCase,
                                   @Param("trigger") JSONObject trigger,
                                   @Param(Constants.WRAPPER) Wrapper<DCAlarmDto> queryWrapper) {

        final List<String> createDts = DateUtils.getFormattedDateRange(fromTime, toTime);

        SQL sql = new SQL();
        sql.SELECT("UNIX_TIMESTAMP(time_slice(daa.`time`, INTERVAL #{interval} SECOND)) * 1000 as timeBucket, "
                + "da.status, COUNT(DISTINCT da.id) as cnt");

        sql.FROM("dc_alarm da INNER JOIN dc_alarm_aggregate daa ON daa.id = da.id "
                + "AND daa.`time` >= #{fromTime} AND daa.`time` < #{toTime}");

        sql.WHERE("da.apiKey = #{apiKey}");
        final boolean domainManagerStatusOpen = domainManagerObjService.getDomainManagerStatusOpen();
        sql.WHERE(domainManagerStatusOpen ? "da.gid IS NOT NULL" : "da.gid IS NULL");

        // 权限控制逻辑（保持原有结构）
        if (!allEntityPermission) {
            allEntityPermission = domainManagerObjService.hasAllEntityPermission();
        }
        if (!allEntityPermission) {
            gids = (gids == null || gids.isEmpty()) ? getGidFromThread() : gids;
            sql.WHERE((gids != null && !gids.isEmpty())
                    ? String.format("(da.gid in ('%s'))", String.join("','", gids))
                    : "(1=0)");
        }

        addCommonConditionsForTrend(sql, null, idLike, descriptionLowerCase, createDts, queryWrapper, "da.", gids);
        if (trigger != null) {
            addTriggerConditions(sql, trigger, null, "da.");
        }

        sql.GROUP_BY("timeBucket", "da.status");
        sql.ORDER_BY("timeBucket ASC");

        return sql.toString();
    }

    public String countByBusName(@Param("id") String id,
                                 @Param("allEntityPermission") boolean allEntityPermission,
                                 @Param("gids") Collection<String> gids,
                                 @Param("idLike") String idLike,
                                 @Param("apiKey") String apiKey,
                                 @Param("fromTime") String fromTime,
                                 @Param("toTime") String toTime,
                                 @Param("descriptionLowerCase") String descriptionLowerCase,
                                 @Param("trigger") JSONObject trigger,
                                 @Param(Constants.WRAPPER) Wrapper<DCAlarmDto> queryWrapper) {

        final List<String> createDts;
        if (id != null) {
            createDts = Lists.newArrayList(AlarmIdGenerator.extractStrDateFromId(id));
        } else {
            createDts = DateUtils.getFormattedDateRange(fromTime, toTime);
        }

        SQL sql = new SQL();
        sql.SELECT("get_json_string(`tags`, \"$.busName\") as names , count(1) as cnt");
        sql.FROM("dc_alarm da");
        sql.WHERE("apiKey = #{apiKey} AND get_json_string(`tags`, \"$.busName\") is not null");
        final boolean domainManagerStatusOpen = domainManagerObjService.getDomainManagerStatusOpen();
        sql.WHERE(domainManagerStatusOpen ? "da.gid IS NOT NULL" : "da.gid IS NULL");
        // 若有全局权限，则尝试调用全局权限
        if (!allEntityPermission) {
            allEntityPermission = domainManagerObjService.hasAllEntityPermission();
        }
        if (!allEntityPermission) {
            gids = (gids == null || gids.isEmpty()) ? getGidFromThread() : gids;
            sql.WHERE((gids != null && !gids.isEmpty()) ?
                    String.format("(da.gid in ('%s'))", String.join("','", gids)) :
                    "(1=0)");
        }

        addCommonConditions(sql, id, idLike, fromTime, toTime, descriptionLowerCase, createDts, queryWrapper, "da.", gids);
        if (trigger != null) {
            addTriggerConditions(sql, trigger, null, "da.");
        }
        sql.GROUP_BY("names");
        sql.ORDER_BY("names ASC");
        return sql.toString();
    }
}