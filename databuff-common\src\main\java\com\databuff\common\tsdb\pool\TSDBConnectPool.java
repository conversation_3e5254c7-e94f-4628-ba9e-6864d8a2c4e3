package com.databuff.common.tsdb.pool;

import com.databuff.common.tsdb.wrapper.DatabaseWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.PooledObjectFactory;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;

import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
public class TSDBConnectPool extends GenericObjectPool<DatabaseWrapper> {

    private final AtomicBoolean isClosed = new AtomicBoolean(false);
    public TSDBConnectPool(PooledObjectFactory<DatabaseWrapper> factory, GenericObjectPoolConfig config) {
        super(factory, config);
    }
    /**
     * 获取链接
     *
     * @return
     * @throws Exception
     */
    public DatabaseWrapper getTSDBClient() throws Exception {
        return this.borrowObject();
    }

    public void releaseConnection(DatabaseWrapper client) {
        if (client != null && !isClosed.get()) {
            try {
                this.returnObject(client);
            } catch (Exception e) {
                log.error("Error returning connection to pool", e);
                try {
                    client.close();
                } catch (Exception ex) {
                    log.error("Error closing client", ex);
                }
            }
        }
    }

    @Override
    public void close() {
        if (isClosed.compareAndSet(false, true)) {
            log.info("Closing pool");
            try {
                super.close();
            } catch (Exception e) {
                log.error("Error closing pool", e);
            }
        }
    }
}
