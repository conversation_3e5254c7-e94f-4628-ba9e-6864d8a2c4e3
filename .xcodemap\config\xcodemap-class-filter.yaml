autoDetectedPackages:
- com.databuff.audit
- com.databuff.client
- com.databuff.common.annotation
- com.databuff.common.audit
- com.databuff.common.constants
- com.databuff.common.exception
- com.databuff.common.metric
- com.databuff.common.model
- com.databuff.common.ringbuffer
- com.databuff.common.threadLocal
- com.databuff.common.tsdb
- com.databuff.common.utils
- com.databuff.config
- com.databuff.dao.mysql
- com.databuff.dao.starrocks
- com.databuff.dss
- com.databuff.dts
- com.databuff.engine.constant
- com.databuff.engine.metric
- com.databuff.engine.npm
- com.databuff.engine.process
- com.databuff.engine.resource
- com.databuff.engine.serializer
- com.databuff.engine.sink
- com.databuff.engine.trace
- com.databuff.engine.util
- com.databuff.entity
- com.databuff.event
- com.databuff.handler
- com.databuff.interceptor
- com.databuff.kafka
- com.databuff.lock
- com.databuff.metric
- com.databuff.processor.apm
- com.databuff.processor.notify
- com.databuff.service
- com.databuff.sink
- com.databuff.tasks
- com.databuff.tsdb.metric
- com.databuff.typehandler
- com.databuff.util
- com.databuff.webapp
- com.starrocks.connector.flink
- com.starrocks.streamload.shade
- com.xxl.job.core
- dto
- org.audit4j.core
- org.audit4j.handler.db
- org.audit4j.integration.spring
- org.springframework.cloud.zookeeper
enableAutoDetect: true
entryDisplayConfig:
  excludedPathPatterns: []
  skipJsCss: true
funcDisplayConfig:
  skipConstructors: false
  skipFieldAccess: true
  skipFieldChange: true
  skipGetters: false
  skipNonProjectPackages: false
  skipPrivateMethods: false
  skipSetters: false
ignoreSameClassCall: null
ignoreSamePackageCall: null
includedPackagePrefixes: null
includedParentClasses: null
maxColSize: 32
maxNumFirst: 12
maxNumFirstImportant: 1024
maxNumHash: 3
maxNumHashImportant: 256
maxObjectDepth: 4
maxStrSize: 4096
name: xcodemap-filter
recordMode: all
sourceDisplayConfig:
  color: blue
startOnDebug: false
