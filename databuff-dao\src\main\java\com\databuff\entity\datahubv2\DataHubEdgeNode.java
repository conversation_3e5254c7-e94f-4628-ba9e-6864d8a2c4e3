package com.databuff.entity.datahubv2;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName(value = "datahub_edge_node", autoResultMap = true)
public class DataHubEdgeNode {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField(value = "processor_id")
    private Integer processorId;

    @TableField(value = "pipeline_id")
    private Integer pipelineId;
    
    // getters and setters
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public Integer getProcessorId() {
        return processorId;
    }
    
    public void setProcessorId(Integer processorId) {
        this.processorId = processorId;
    }
    
    public Integer getPipelineId() {
        return pipelineId;
    }
    
    public void setPipelineId(Integer pipelineId) {
        this.pipelineId = pipelineId;
    }
}
