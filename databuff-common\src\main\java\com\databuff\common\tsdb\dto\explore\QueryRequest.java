package com.databuff.common.tsdb.dto.explore;

import com.databuff.common.tsdb.builder.QueryBuilderX;
import com.databuff.common.tsdb.dto.CompositeCondition;
import com.databuff.common.tsdb.dto.preview.OrderDTO;
import com.databuff.common.tsdb.model.Aggregation;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;

/**
 * 表示查询请求的DTO对象，包含查询条件、表达式、时间范围及间隔等参数。
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QueryRequest {

    @ApiModelProperty(
            value = "时间戳偏移量（单位：毫秒），用于调整查询时间窗口",
            example = "3600000"
    )
    private int timeOffset;

    @ApiModelProperty(
            value = "当前统计时间戳（单位：毫秒），用于相对时间计算基准",
            example = "1717027200000"
    )
    private long currentStatTime;

    /**
     * npm 专用
     */
    private String name;
    /**
     * npm 专用
     */
    private String source;
    /**
     * npm 专用
     */
    private String target;

    /**
     * 排序参数
     */
    private OrderDTO order;

    @ApiModelProperty(value = "聚合函数")
    private List<Aggregation> aggregations;

    /**
     * 将指定的聚合对象添加到查询请求的聚合集合中，并返回当前实例以支持链式调用。
     *
     * @param aggregation 需要添加的聚合对象
     * @return 当前QueryRequest实例，用于链式调用
     */
    public QueryRequest addAgg(Aggregation aggregation) {
        if (aggregation == null) {
            return this;
        }
        ensureAggregationsInitialized();
        this.aggregations.add(aggregation);
        return this;
    }


    private synchronized void ensureAggregationsInitialized() {
        if (this.aggregations == null) {
            this.aggregations = new ArrayList<>();
        }
    }

    @ApiModelProperty(value = "标签白名单", example = "[\"error\", \"warning\"]")
    private Collection<String> whiteKeys;

    @ApiModelProperty(
            value = "指标名称（单指标查询专用），如CPU使用率",
            example = "cpu_usage",
            notes = "当metrics字段存在时，该字段会被忽略"
    )
    private String metric;

    /**
     * 获取指标列表
     *
     * @return
     */
    public String getMetric() {
        if (metric != null) {
            return metric;
        }
        if (metrics != null && metrics.size() > 0) {
            return metrics.iterator().next();
        }
        return null;
    }

    @ApiModelProperty(value = "指标名称，如CPU使用率", example = "cpu_usage")
    private Collection<String> metrics = new HashSet<>();

    /**
     * 获取指标列表
     *
     * @return
     */
    public Collection<String> getMetrics() {
        return metrics;
    }

    @ApiModelProperty(value = "数据库", example = "databuff")
    private String db;

    @ApiModelProperty(value = "表名", example = "cpu")
    private String tb;

    @ApiModelProperty(value = "聚合函数类型，如平均值/最大值", example = "mean")
    private String aggs;

    @ApiModelProperty(value = "分组字段列表", example = "[\"host\", \"service\"]")
    private Collection<String> by;

    @ApiModelProperty(value = "类型过滤条件", example = "[\"error\", \"warning\"]")
    private List<String> types;

    @ApiModelProperty(value = "复合过滤条件列表", example = "[{\"type\":\"composite\", \"connector\":\"AND\"}]")
    private Collection<CompositeCondition> from;

    @ApiModelProperty(value = "扩展查询条件列表", example = "[{\"type\":\"composite\", \"connector\":\"AND\"}]")
    private Collection<CompositeCondition> fromExt;

    public Collection<CompositeCondition> getFrom() {
        ensureFromInitialized();
        return Collections.unmodifiableCollection(from);
    }

    private void ensureFromInitialized() {
        if (from == null) {
            from = new ArrayList<>();
        }
    }

    @ApiModelProperty(value = "分页下拉关键字", example = "system.core.usage")
    private String lastKey;

    @ApiModelProperty(value = "分页页码", example = "1")
    private int pageNum;

    @ApiModelProperty(value = "每页记录数", example = "20")
    private int pageSize;

    @ApiModelProperty(value = "是否禁用分页", example = "false")
    private boolean noPage;

    @ApiModelProperty(value = "查询的开始时间戳，单位为毫秒", example = "1609459200000")
    private Long start;

    @ApiModelProperty(value = "查询的结束时间戳，单位为毫秒", example = "1609462800000")
    private Long end;

    @ApiModelProperty(value = "最近时间区间，单位为秒", example = "3600")
    private Integer period;

    @ApiModelProperty(value = "查询时间间隔，单位为秒", example = "60")
    private Integer interval;

    @ApiModelProperty(value = "API密钥，用于身份认证", example = "your_api_key_123")
    private String apiKey;

    @ApiModelProperty(value = "是否包含注释信息，默认为false", example = "true")
    private boolean hasAnnotate;

    @ApiModelProperty(value = "是否拥有所有权限", example = "true")
    private Boolean allPermission;

    @ApiModelProperty(value = "是否开启领域管理状态", example = "false")
    private Boolean domainManagerStatusOpen;

    @ApiModelProperty(value = "组ID集合，用于过滤查询条件", example = "[\"group1\", \"group2\"]")
    private Collection<String> gids;

    public QueryBuilderX convert() {
        QueryBuilderX builder = new QueryBuilderX();

        // 基础参数映射
        builder.setMetric(this.getMetric());

        // 时间偏移量映射
        final int timeOffsetMS = this.getTimeOffset() * 1000;

        // 时间范围映射 - 拆分start和end分别添加到builder
        Long start = this.getStart();
        Long end = this.getEnd();

        // 时间范围映射
        final Integer period = getPeriod();
        if (start == null && end == null && period != null && period > 0) {
            final long nowMs = System.currentTimeMillis();
            builder.start(nowMs - period * 1000 - timeOffsetMS);
            builder.end(nowMs - timeOffsetMS);
        } else {
            if (start != null) {
                builder.start(start - timeOffsetMS);
            }
            if (end != null) {
                builder.end(end - timeOffsetMS);
            }
        }

        // 处理Interval字段的空值风险
        Integer interval = this.getInterval();
        if (interval != null) {
            builder.setInterval(interval.intValue());
        }

        // 分页参数映射（启用逻辑，假设QueryBuilderX支持相关接口）
        builder.setPage(this.getPageSize(), this.getPageNum());

        return builder;
    }
}
