package com.databuff.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@ApiModel(description = "时间序列查询")
@Data
public class TimeSeriesQueryParams {
    @ApiModelProperty(value = "apiKey", example = "NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4")
    private String apiKey;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date fromTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date toTime;

    @ApiModelProperty(value = "间隔(秒)", example = "60")
    private Integer interval;
}