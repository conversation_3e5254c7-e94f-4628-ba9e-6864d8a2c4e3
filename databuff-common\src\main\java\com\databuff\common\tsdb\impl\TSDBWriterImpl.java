package com.databuff.common.tsdb.impl;

import com.databuff.common.tsdb.TSDBWriter;
import com.databuff.common.tsdb.adapter.DatabaseAdapterFactory;
import com.databuff.common.tsdb.executor.TSDBExecutor;
import com.databuff.common.tsdb.model.TSDBDatabaseInfo;
import com.databuff.common.tsdb.model.TSDBPoint;
import com.databuff.common.tsdb.pool.TSDBConnectPool;
import com.databuff.common.tsdb.wrapper.DatabaseWrapper;
import com.databuff.common.tsdb.wrapper.MoreDBWrapper;
import com.databuff.common.utils.OtelMetricUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.databuff.common.constants.MetricName.*;
import static com.databuff.common.tsdb.TSDBOperateUtil.getKeepDay;

/**
 * TSDB写入操作实现类
 * 实现TSDBWriter接口
 */
@Slf4j
@Component
public class TSDBWriterImpl implements TSDBWriter {

    private TSDBExecutor tsdbExecutor;
    private TSDBConnectPool pool;

    public TSDBWriterImpl() {
    }

    @Autowired(required = false)
    public TSDBWriterImpl(TSDBConnectPool tsdbConnectPool) {
        if (tsdbConnectPool == null) {
            return;
        }
        this.pool = tsdbConnectPool;
        this.tsdbExecutor = new TSDBExecutor(new DatabaseAdapterFactory().getAdapter(tsdbConnectPool));
    }

    @Override
    public boolean createDatabase(TSDBDatabaseInfo databaseInfo) {
        String dbName = databaseInfo.getDatabaseName();
        Map<String, String> tags = new HashMap<>();
        tags.put("database", dbName);
        long startTime = System.currentTimeMillis();
        DatabaseWrapper dbClient = null;
        try {
            dbClient = pool.getTSDBClient();
            long getClientTime = System.currentTimeMillis();
            OtelMetricUtil.logHistogram(TSDB_CONNECTION_COST, tags, getClientTime - startTime);
            if (dbClient == null) {
                throw new RuntimeException("无法获取数据库连接");
            }
            return tsdbExecutor.createDatabase(dbClient, databaseInfo);
        }catch (Exception e) {
            log.error("createDatabase失败,databaseName:{},error:", dbName, e);
            OtelMetricUtil.logCounter(TSDB_CREATE_FAILED, tags, 1);
            throw new RuntimeException("createDatabase 查询数据失败", e);
        } finally {
            if (dbClient != null) {
                pool.releaseConnection(dbClient);
            }
            long endTime = System.currentTimeMillis();
            OtelMetricUtil.logHistogram(TSDB_CREATE_COST, tags, endTime - startTime);
        }
    }

    /**
     * 创建数据库（简化版）
     * 只需要提供数据库名称，其他参数从tsdbWriters中获取
     * 适用于在已有连接配置的情况下快速创建数据库
     *
     * @param databaseName 数据库名称
     * @return 是否成功创建数据库
     */
    @Override
    public boolean createDatabase(String databaseName) {
        return createDatabase(databaseName, null);
    }

    /**
     * 创建数据库（简化版）
     * 只需要提供数据库名称，其他参数从tsdbWriters中获取
     * 适用于在已有连接配置的情况下快速创建数据库
     *
     * @param databaseName 数据库名称
     * @param interval     数据点间隔（秒）
     * @return 是否成功创建数据库
     */
    @Override
    public boolean createDatabase(String databaseName, Integer interval) {
        boolean result = false;

        try {
            // 从写入器配置中获取参数
            Map<String, Object> config = this.getConfigs();
            String userName = config.get("user").toString();
            String password = config.get("password").toString();
            Integer shard = Integer.valueOf(config.get("shard").toString());
            Integer replication = Integer.valueOf(config.get("replication").toString());
            String duration = config.get("duration").toString();
            if (interval == null) {
                interval = Integer.valueOf(config.get("interval").toString());
            }

            // 创建数据库信息对象
            TSDBDatabaseInfo databaseInfo = new TSDBDatabaseInfo(
                    databaseName,
                    userName,
                    password,
                    shard,
                    replication,
                    getKeepDay(duration),
                    interval
            );

            // 调用原始方法创建数据库
            boolean writerResult = createDatabase(databaseInfo);
            if (writerResult) {
                result = true;
            }
        } catch (Exception e) {
            log.error("Failed to create database {} with writer {}: {}", databaseName, this.getClass().getSimpleName(), e.getMessage());
        }
        return result;
    }

    @Override
    public boolean writePoint(String databaseName, TSDBPoint point) {
        Map<String, String> tags = new HashMap<>();
        tags.put("database", databaseName);
        long startTime = System.currentTimeMillis();
        DatabaseWrapper dbClient = null;
        try {
            dbClient = pool.getTSDBClient();
            long getClientTime = System.currentTimeMillis();
            OtelMetricUtil.logHistogram(TSDB_CONNECTION_COST, tags, getClientTime - startTime);
            if (dbClient == null) {
                throw new RuntimeException("无法获取数据库连接");
            }
            OtelMetricUtil.logCounter(TSDB_SEND_POINTS, tags, 1);
            return tsdbExecutor.writePoint(dbClient, databaseName, point);
        } catch (Throwable e) {
            log.error("插入数据失败", e);
            OtelMetricUtil.logCounter(TSDB_SEND_FAILED, tags, 1);
            return false;
        } finally {
            if (dbClient != null) {
                pool.releaseConnection(dbClient);
            }
            long endTime = System.currentTimeMillis();
            OtelMetricUtil.logHistogram(TSDB_SEND_COST, tags, endTime - startTime);
        }
    }

    @Override
    public boolean writePoints(String databaseName, List<TSDBPoint> points) {
        Map<String, String> tags = new HashMap<>();
        tags.put("database", databaseName);
        long startTime = System.currentTimeMillis();
        DatabaseWrapper dbClient = null;
        try {
            dbClient = pool.getTSDBClient();
            long getClientTime = System.currentTimeMillis();
            OtelMetricUtil.logHistogram(TSDB_CONNECTION_COST, tags, getClientTime - startTime);
            if (dbClient == null) {
                throw new RuntimeException("无法获取数据库连接");
            }
            OtelMetricUtil.logCounter(TSDB_SEND_POINTS, tags, points.size());
            return tsdbExecutor.writePoints(dbClient, databaseName, points);
        } catch (Throwable e) {
            log.error("插入数据失败", e);
            OtelMetricUtil.logCounter(TSDB_SEND_FAILED, tags, 1);
            return false;
        } finally {
            if (dbClient != null) {
                pool.releaseConnection(dbClient);
            }
            long endTime = System.currentTimeMillis();
            OtelMetricUtil.logHistogram(TSDB_SEND_COST, tags, endTime - startTime);
        }
    }

    @Override
    public void close() throws Exception {
        tsdbExecutor.close();
    }

    @Override
    public Map<String, Object> getConfigs() {
        return tsdbExecutor.getConfigs();
    }
}
