package com.databuff.entity.extend;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author:TianMing
 * @date: 2024/1/6
 * @time: 19:41
 */
@Data
public class DcRcaSearch {
    private String eventId;
    private String problemId;
    private Integer groupId;
    private String startTime;
    private String endTime;
    private String apiKey;
    private String checkName ;
    private List<Long> userIds ;
    List<String> checkNames ;
    /**
     * 每页数量
     */
    @ApiModelProperty("每页数量")
    protected Integer size = 15;
    /**
     * 偏移量 从0开始
     */
    @ApiModelProperty("偏移量 从0开始")
    protected Integer offset = 0;
}
