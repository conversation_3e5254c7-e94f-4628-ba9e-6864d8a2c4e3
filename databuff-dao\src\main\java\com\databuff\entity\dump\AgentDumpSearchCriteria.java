package com.databuff.entity.dump;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collection;
import java.util.Date;

@Data
public class AgentDumpSearchCriteria {

    @ApiModelProperty(value = "页码", example = "1")
    protected Integer pageNum = 1;
    @ApiModelProperty(value = "分页数", example = "10")
    protected Integer pageSize = 10;
    /**
     * 日志文件名称
     */
    private String fileName;
    /**
     * 文件大小
     */
    private Long fileSize;
    /**
     * 日志文件路径
     */
    private String path;
    /**
     * 服务名称
     */
    private String service;
    /**
     * 服务ID
     */
    private String serviceId;
    /**
     * 服务实例
     */
    private String serviceInstance;
    /**
     * 账号
     */
    private String account;
    /**
     * 状态
     */
    private String status;
    private Date startTime;
    private Date endTime;


    /**
     * 域ID
     */
    private Collection<String> gids;
}