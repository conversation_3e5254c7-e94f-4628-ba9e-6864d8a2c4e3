package com.databuff.entity.dto;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.databuff.common.utils.TimeUtil;
import com.databuff.entity.ConvergencePolicy;
import com.databuff.handler.StringArrayToSetTypeHandler;
import com.databuff.tasks.convergence.ConvergenceType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(description = "告警实体")
public class DCAlarmDto {
    @ApiModelProperty(value = "ID")
    private String id;

    @ApiModelProperty(value = "域ID")
    private String gid;

    @ApiModelProperty(value = "最新问题ID，下钻到问题详情")
    private String issueId;

    @ApiModelProperty(value = "最新影响面ID，下钻到影响面详情")
    private String problemId;

    @ApiModelProperty(value = "根因服务")
    private String problemService;

    @ApiModelProperty(value = "影响服务数量")
    private Integer influenceServiceCount;

    @ApiModelProperty(value = "创建日期")
    private String createDt;

    @ApiModelProperty(value = "收敛类型")
    private ConvergenceType convergenceType;

    @ApiModelProperty(value = "API密钥")
    private String apiKey;

    @ApiModelProperty(value = "告警实际值(max)")
    private double value;

    @ApiModelProperty(value = "持续时间(max)")
    private double duration;

    @ApiModelProperty(value = "时间戳")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String time;

    /**
     * if (timestamp == 0) { return time; }：如果 timestamp 的值为 0，则直接返回 time 字符串。
     * if (timestamp != oldTimestamp) { oldTimestamp = timestamp; time = TimeUtil.getTime(timestamp); }：如果 timestamp 的值与 oldTimestamp 不同，则更新 oldTimestamp 为当前的 timestamp，并使用 TimeUtil.getTime(timestamp) 方法将 timestamp 转换为格式化的时间字符串，并赋值给 time。
     * return time;：返回 time 字符串。
     * @return
     */
    public String getTime() {
        if (timestamp == 0) {
            return time;
        }
        if (timestamp != oldTimestamp) {
            oldTimestamp = timestamp;
            time = TimeUtil.getTime(timestamp);
        }
        return time;
    }

    @ApiModelProperty(value = "时间戳")
    private long timestamp;

    private long oldTimestamp;

    @ApiModelProperty(value = "开始触发时间(min)")
    private long startTriggerTime;

    @ApiModelProperty(value = "结束触发时间(min)")
    private long endTriggerTime;

    @ApiModelProperty(value = "最后触发时间(max)")
    private long lastTriggerTime;

    @ApiModelProperty(value = "最后更新时间")
    private long updateTime;

    @ApiModelProperty(value = "应该在此时间之后告警过期")
    private long expiredAt;

    @ApiModelProperty(value = "告警消息")
    private String alarmMsg;

    @ApiModelProperty(value = "描述")
    private String description;

    public void setDescription(String description) {
        //  VARCHAR(5000) 限制描述长度,最多可以存储1666个汉字
        int maxLength = 5000 / 3;
        if (description.length() > maxLength) {
            description = description.substring(0, maxLength - 3) + "...";
        }
        this.description = description;
    }

    /**
     * 动态获取告警描述
     *
     * @return
     */
    public String getDescription() {
        if (StringUtils.isNotBlank(description)) {
            //  VARCHAR(5000) 限制描述长度,最多可以存储1666个汉字
            int maxLength = 5000 / 3;
            if (description.length() > maxLength) {
                description = description.substring(0, maxLength - 3) + "...";
            }
            return description;
        }
        resetDescription(variableMap, pattern);
        return description;
    }

    public void resetDescription() {
        resetDescription(variableMap, pattern);
    }

    public void resetDescription(Map<Integer, Set> variableMap, String pattern) {
        if (variableMap == null) {
            return;
        }
        if (pattern == null) {
            return;
        }
        List<String> vars = new ArrayList<>();
        final Collection<Set> values = variableMap.values();
        for (Set set : values) {
            String strVar = StringUtils.EMPTY;
            if (CollectionUtils.isNotEmpty(set)) {
                strVar = String.join(",", set);
            }
            vars.add(strVar);
        }
        description = MessageFormat.format(pattern, vars.toArray());
        //  VARCHAR(5000) 限制描述长度,最多可以存储1666个汉字
        int maxLength = 5000 / 3;
        if (description.length() > maxLength) {
            description = description.substring(0, maxLength - 3) + "...";
        }
    }

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "告警状态")
    private String alarmStatus;

    @ApiModelProperty(value = "规则创建者ID")
    private String creatorId;

    @ApiModelProperty(value = "规则修改者ID")
    private String editorId;

    @ApiModelProperty(value = "影响面问题描述")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String problemDesc;

    @ApiModelProperty(value = "事件ID列表")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @TableField(value = "eventId", typeHandler = StringArrayToSetTypeHandler.class)
    private Set<String> eventId;

    @ApiModelProperty(value = "总收敛的事件ID列表数量")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private int eventCnt;

    @ApiModelProperty(value = "本次收敛的事件ID列表数量")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private int currentEventCnt;

    @ApiModelProperty(value = "本次收敛的事件ID列表数量")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private AtomicInteger currentMinEventCnt;

    /**
     * 记录上一次currentMinEventCnt被重置的时间戳。
     */
    private long previousTimestamp;

    /**
     * 增加当前分钟的事件计数器。
     * 如果传入的时间戳与当前对象的时间戳相差超过或等于一分钟，则重置计数器为0。
     * 然后将计数器加1并返回新的值。
     *
     * @param timestamp 当前事件的时间戳
     * @return 增加后的当前分钟事件计数器的值
     */
    public synchronized int incrementCurrentMinEventCnt(long timestamp, AtomicBoolean minuteFirst) {
        if (currentMinEventCnt == null) {
            currentMinEventCnt = new AtomicInteger(0);
            this.previousTimestamp = timestamp;
        }
        if ((timestamp / 60000) - (this.previousTimestamp / 60000) >= 1) {
            currentMinEventCnt.set(0);
            this.previousTimestamp = timestamp;
        }
        int eventCnt = currentMinEventCnt.incrementAndGet();
        if (eventCnt == 1) {
            minuteFirst.set(true);
        }
        return eventCnt;
    }

    @ApiModelProperty(value = "事件数量")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public Integer getEventCnt() {
        return eventId != null && eventId.size() > 0 ? eventId.size() : eventCnt;
    }

    @ApiModelProperty(value = "监视器ID列表")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @TableField(value = "monitorId", typeHandler = StringArrayToSetTypeHandler.class)
    private Set<String> monitorId;

    @ApiModelProperty(value = "分类列表")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @TableField(value = "classification", typeHandler = StringArrayToSetTypeHandler.class)
    private Set<String> classification;

    @ApiModelProperty(value = "指标列表")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @TableField(value = "metric", typeHandler = StringArrayToSetTypeHandler.class)
    private Set<String> metric;

    @ApiModelProperty(value = "指标列表(多指标)")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @TableField(value = "metrics", typeHandler = StringArrayToSetTypeHandler.class)
    private Set<String> metrics;

    @ApiModelProperty(value = "业务系统列表")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @TableField(value = "busName", typeHandler = StringArrayToSetTypeHandler.class)
    private Set<String> busName;

    @ApiModelProperty(value = "主机列表")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @TableField(value = "host", typeHandler = StringArrayToSetTypeHandler.class)
    private Set<String> host;

    @ApiModelProperty(value = "服务ID列表")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @TableField(value = "serviceId", typeHandler = StringArrayToSetTypeHandler.class)
    private Set<String> serviceId;

    @ApiModelProperty(value = "服务实例列表")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @TableField(value = "serviceInstance", typeHandler = StringArrayToSetTypeHandler.class)
    private Set<String> serviceInstance;

    @ApiModelProperty(value = "磁盘分区列表")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @TableField(value = "deviceName", typeHandler = StringArrayToSetTypeHandler.class)
    private Set<String> deviceName;

    @ApiModelProperty(value = "告警标签")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @TableField(value = "tags", typeHandler = FastjsonTypeHandler.class)
    private JSONObject tags;

    @ApiModelProperty(value = "告警触发对象")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @TableField(value = "trigger", typeHandler = FastjsonTypeHandler.class)
    private JSONObject trigger;

    @ApiModelProperty(value = "告警变量")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Map<Integer, Set> variableMap;

    @ApiModelProperty(value = "告警描述模板")
    @TableField(value = "pattern")
    private String pattern;

    @ApiModelProperty(value = "是否首次告警")
    private boolean first;

    public boolean getFirst() {
        return first;
    }

    @ApiModelProperty(value = "是否最后一次告警")
    private boolean last;

    public boolean getLast() {
        return last;
    }

    @ApiModelProperty(value = "是否为智能窗口")
    private Boolean sameRootCause;

    @ApiModelProperty(value = "状态(0未处理/2处理中/3已处理)")
    private Integer status;

    @ApiModelProperty(value = "处理备注")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @TableField(value = "remark", typeHandler = FastjsonTypeHandler.class)
    private JSONArray remark;

    @ApiModelProperty(value = "告警等级(max)")
    private Integer level;

    @ApiModelProperty(value = "收敛策略")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ConvergencePolicy policy;

    @ApiModelProperty(value = "收敛策略ID")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer policyId;

    @ApiModelProperty(value = "收敛策略名称")
    @JSONField(serialzeFeatures = {SerializerFeature.WriteMapNullValue})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String policyName;

    private boolean aiEnable;
    /**
     * 将当前的 AlarmEntity 与新的 AlarmEntity 合并。
     * 合并操作遵循以下规则：
     * 1. 如果新的AlarmEntity中某个字段为null，则忽略该字段。
     * 2.如果某个字段是Collection，则新Collection的元素会添加到旧Collection中。
     * 在此过程中删除重复项。
     * 3.如果某个字段是Map，则将新Map的条目添加到旧Map中。
     * 如果Map中的值是Collection或Map，则按照规则2和3进行合并。
     * 否则，新Map中的值会覆盖旧Map中的值。
     * 4. 对于所有其他字段类型，新 AlarmEntity 中的值将覆盖旧 AlarmEntity 中的值。
     *
     * @param newEntity 要与当前 AlarmEntity 合并的 AlarmEntity。
     * @return 合并操作后的当前AlarmEntity。
     * @throws IllegalAccessException 如果由于 Java 语言访问控制而无法访问 AlarmEntity 中的字段。
     */
    public DCAlarmDto mergeWith(DCAlarmDto newEntity) throws IllegalAccessException {
        Field[] fields = DCAlarmDto.class.getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true);
            Object oldValue = field.get(this);
            Object newValue = field.get(newEntity);

            if (newValue == null) {
                continue;
            }
            if (oldValue instanceof Set && newValue instanceof Set) {
                Set oldCollection = (Set) oldValue;
                Set newCollection = (Set) newValue;
                Set mergedCollection = new HashSet();
                if (newCollection != null) {
                    mergedCollection.addAll(newCollection);
                }
                if (oldCollection != null) {
                    mergedCollection.addAll(oldCollection);
                }
                field.set(this, mergedCollection);
            } else if (oldValue instanceof Collection && newValue instanceof Collection) {
                Collection oldCollection = (Collection) oldValue;
                Collection newCollection = (Collection) newValue;
                Collection mergedCollection = new LinkedHashSet();
                if (newCollection != null) {
                    mergedCollection.addAll(newCollection);
                }
                if (oldCollection != null) {
                    mergedCollection.addAll(oldCollection);
                }
                field.set(this, new ArrayList<>(mergedCollection));
            } else if (oldValue instanceof Map && newValue instanceof Map) {
                Map<String, Object> oldMap = (Map<String, Object>) oldValue;
                Map<String, Object> newMap = (Map<String, Object>) newValue;
                Map<String, Object> mergedMap = new HashMap<>(oldMap);

                for (Map.Entry<String, Object> entry : newMap.entrySet()) {
                    String key = entry.getKey();
                    Object value = entry.getValue();

                    if (value instanceof Collection && oldMap.get(key) instanceof Collection) {
                        Collection oldCollection = (Collection) oldMap.get(key);
                        Collection newCollection = (Collection) value;
                        Collection mergedCollection = new LinkedHashSet();
                        if (newCollection != null) {
                            mergedCollection.addAll(newCollection);
                        }
                        if (oldCollection != null) {
                            mergedCollection.addAll(oldCollection);
                        }
                        mergedMap.put(key, new ArrayList<>(mergedCollection));
                    } else if (value instanceof Map && oldMap.get(key) instanceof Map) {
                        Map<String, Object> oldInnerMap = (Map<String, Object>) oldMap.get(key);
                        Map<String, Object> newInnerMap = (Map<String, Object>) value;
                        Map<String, Object> mergedInnerMap = new HashMap<>(oldInnerMap);
                        mergedInnerMap.putAll(newInnerMap);
                        mergedMap.put(key, mergedInnerMap);
                    } else {
                        mergedMap.put(key, value);
                    }
                }

                field.set(this, new JSONObject(mergedMap));
            } else {
                field.set(this, newValue);
            }
        }

        return this;
    }
}