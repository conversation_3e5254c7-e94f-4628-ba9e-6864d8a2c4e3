package com.databuff.common.tsdb.pool;

import com.databuff.common.constants.Constant;
import com.databuff.common.tsdb.model.DatabaseType;
import com.databuff.common.tsdb.wrapper.DatabaseWrapper;
import com.databuff.common.tsdb.wrapper.MoreDBWrapper;
import com.databuff.common.tsdb.wrapper.OpenGeminiWrapper;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.PooledObjectFactory;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


/**
 * TSDB 连接池
 *
 */
@Component
@Slf4j
@Data
public class TSDBPoolFactory implements PooledObjectFactory<DatabaseWrapper> {

    private static final Logger LOGGER = LoggerFactory.getLogger(TSDBPoolFactory.class);

    @Value("${tsdb.db:moredb}")
    private String tsdb;

    @Value("${tsdb.url:moredb:2890}")
    private String url;
    @Value("${tsdb.api:http://moredb:8080/api}")
    private String tsdbApi;
    @Value("${tsdb.user:databuff}")
    private String user;
    @Value("${tsdb.password:databuff666}")
    private String pwd;
    @Value("${tsdb.queryTimeout:30000}")
    private long queryTimeout;
    @Value("${tsdb.duration:7d}")
    private String duration;
    @Value("${tsdb.shard:2}")
    private Integer shard;
    @Value("${tsdb.replication:1}")
    private Integer replication;
    @Value("${tsdb.interval:60}")
    private Integer interval;

    private boolean jmxEnabled = false;

    // 无参构造函数
    public TSDBPoolFactory() {
    }

    // 简单的两参构造函数，为了兼容现有代码
    public TSDBPoolFactory(String tsdb, String url) {
        this.tsdb = tsdb;
        this.url = url;
    }

    // 全参数构造函数，用于 Builder 模式
    @Builder
    public TSDBPoolFactory(String tsdb, String url, String tsdbApi, String user, String pwd,
                           long queryTimeout, String duration, Integer shard, Integer replication,
                           Integer interval, boolean jmxEnabled) {
        this.tsdb = tsdb;
        this.url = url;
        this.tsdbApi = tsdbApi;
        this.user = user;
        this.pwd = pwd;
        this.queryTimeout = queryTimeout;
        this.duration = duration;
        this.shard = shard;
        this.replication = replication;
        this.interval = interval;
        this.jmxEnabled = jmxEnabled;
    }


    /**
     * 创建一个新的数据库连接包装器对象
     * 这是对象池中最核心的方法，负责创建新的连接对象
     * 支持集群多节点访问，当URL为逗号分隔的地址列表时，会尝试连接到第一个可用节点
     *
     * @return 包装在PooledObject中的数据库连接包装器
     */
    /**
     * 创建基本配置参数
     * @return 包含基本配置的Map
     */
    private Map<String, Object> createBaseConfig() {
        Map<String, Object> config = new HashMap<>();

        if (queryTimeout <= 0) {
            queryTimeout = 30000;
        }
        config.put("queryTimeout", String.valueOf(queryTimeout));
        config.put("api", tsdbApi);
        config.put("user", user);
        config.put("password", pwd);
        config.put("duration", duration);
        config.put("shard", shard);
        config.put("replication", replication);
        config.put("interval", interval);

        return config;
    }

    /**
     * 创建数据库包装器
     * @param config 配置参数
     * @return 数据库包装器
     */
    private DatabaseWrapper createWrapper(Map<String, Object> config) {
        switch (tsdb) {
            case Constant.TS_DB.MOREDB:
                return new MoreDBWrapper(config, DatabaseType.MOREDB);
            case Constant.TS_DB.OPENGEMINI:
                return new OpenGeminiWrapper(config, DatabaseType.OPENGEMINI);
            default:
                throw new IllegalArgumentException("Unsupported database type: " + tsdb);
        }
    }

    /**
     * 安全关闭连接
     * @param wrapper 要关闭的包装器
     */
    private void safeClose(DatabaseWrapper wrapper) {
        if (wrapper != null) {
            try {
                wrapper.close();
            } catch (Exception e) {
                LOGGER.warn("Error closing connection: {}", e.getMessage());
            }
        }
    }

    /**
     * 解析URL字符串中的服务器列表
     * 处理各种特殊格式和分隔符
     *
     * @param urlString URL字符串
     * @return 有效的服务器列表
     */
    private List<String> parseServerList(String urlString) {
        if (urlString == null || urlString.isEmpty()) {
            return Collections.emptyList();
        }

        // 先对整个URL字符串进行处理
        // 1. 去除字符串两端的空白
        urlString = urlString.trim();
        // 2. 将中文逗号、中文句号等替换为英文逗号
        urlString = urlString.replace('，', ',').replace('。', ',');
        // 3. 将多个连续的空格替换为单个空格
        urlString = urlString.replaceAll("\\s+", " ");
        // 4. 将逗号前后的空格去除，例如 "a , b" 变为 "a,b"
        urlString = urlString.replaceAll("\\s*,\\s*", ",");

        // 使用逗号分隔
        String[] parts = urlString.split(",");

        // 过滤空字符串并再次去除前后空白（以防万一）
        return Arrays.stream(parts)
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
    }

    /**
     * 尝试连接到集群节点
     * @param baseConfig 基本配置
     * @param serverList 服务器节点列表
     * @return 成功连接的包装器或null
     * @throws IllegalStateException 如果所有节点都连接失败
     */
    private DatabaseWrapper connectToCluster(Map<String, Object> baseConfig, List<String> serverList) {
        if (serverList.isEmpty()) {
            throw new IllegalArgumentException("Server list cannot be empty");
        }

        Exception lastException = null;
        LOGGER.info("Attempting to connect to cluster with {} nodes: {}", serverList.size(), serverList);

        for (String server : serverList) {
            DatabaseWrapper wrapper = null;
            try {
                // 为当前节点创建配置
                Map<String, Object> nodeConfig = new HashMap<>(baseConfig);
                nodeConfig.put("url", server);
                nodeConfig.put("current_node", server);

                // 创建并验证连接
                wrapper = createWrapper(nodeConfig);
                if (wrapper.isConnected()) {
                    LOGGER.info("Successfully connected to node: {}", server);
                    return wrapper;
                } else {
                    LOGGER.warn("Connection to node {} is not valid", server);
                    safeClose(wrapper);
                }
            } catch (Exception e) {
                LOGGER.warn("Failed to connect to node {}: {}", server, e.getMessage());
                lastException = e;
                safeClose(wrapper);
            }
        }

        // 所有节点都连接失败
        String errorMsg = "Failed to connect to any node in the cluster: " + serverList;
        LOGGER.error(errorMsg);
        if (lastException != null) {
            throw new IllegalStateException(errorMsg, lastException);
        } else {
            throw new IllegalStateException(errorMsg);
        }
    }

    @Override
    public synchronized PooledObject<DatabaseWrapper> makeObject() {
        try {
            LOGGER.info("Creating new database connection wrapper for {}", tsdb);
            Map<String, Object> config = createBaseConfig();
            DatabaseWrapper wrapper;

            // 处理URL，支持逗号分隔的集群配置
            List<String> serverList = parseServerList(url);

            if (serverList.size() > 1) {
                // 集群模式
                LOGGER.info("Cluster configuration detected with {} nodes", serverList.size());

                // 保存原始集群配置，便于后续重连时使用
                config.put("cluster_nodes", String.join(",", serverList));

                // 尝试连接到可用节点
                wrapper = connectToCluster(config, serverList);
            } else if (serverList.size() == 1) {
                // 单节点模式
                String singleServer = serverList.get(0);
                config.put("url", singleServer);
                wrapper = createWrapper(config);

                // 验证连接是否可用
                if (!wrapper.isConnected()) {
                    safeClose(wrapper);
                    throw new IllegalStateException("Failed to establish database connection to " + singleServer);
                }

                LOGGER.info("Successfully created database connection wrapper for {} to {}", tsdb, singleServer);
            } else {
                // 没有有效的服务器
                throw new IllegalArgumentException("No valid server specified in URL: " + url);
            }

            return new DefaultPooledObject<>(wrapper);
        } catch (Exception e) {
            LOGGER.error("Error creating database connection wrapper: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 销毁一个数据库连接包装器对象
     * 当对象被移除出池时调用，负责释放资源
     *
     * @param p 要销毁的对象
     * @throws Exception 如果销毁过程中发生错误
     */
    @Override
    public void destroyObject(PooledObject<DatabaseWrapper> p) throws Exception {
        if (p == null) {
            return;
        }

        DatabaseWrapper wrapper = p.getObject();
        if (wrapper != null) {
            LOGGER.info("Destroying database connection wrapper for {}", tsdb);
            try {
                wrapper.close();
                LOGGER.info("Successfully destroyed database connection wrapper");
            } catch (Exception e) {
                LOGGER.error("Error destroying database connection wrapper: {}", e.getMessage(), e);
                // 不抛出异常，避免影响池的其他操作
            }
        }
    }

    /**
     * 验证一个数据库连接包装器对象是否仍然有效
     * 用于检查对象是否可以安全地重用
     * 此方法在test-on-borrow=true时被调用
     *
     * @param p 要验证的对象
     * @return true表示对象有效，false表示无效
     */
    @Override
    public boolean validateObject(PooledObject<DatabaseWrapper> p) {
        if (p == null || p.getObject() == null) {
            return false;
        }

        DatabaseWrapper wrapper = p.getObject();
        try {
            // 执行简单的验证，检查连接是否仍然有效
            // 这里调用isConnected()方法，该方法在不同的数据库包装器中有不同的实现
            // 在OpenGeminiWrapper中，它会尝试ping或执行简单查询
            // 在MoreDBWrapper中，它会检查活跃节点
            boolean isValid = wrapper.isConnected();
            if (!isValid) {
                LOGGER.warn("Database connection validation failed for {}", tsdb);

                // 获取配置信息，检查是否有集群配置
                Map<String, Object> config = wrapper.getConfigs();
                String clusterNodes = (String) config.get("cluster_nodes");
                String currentNode = (String) config.get("current_node");

                if (clusterNodes != null && currentNode != null) {
                    LOGGER.info("Connection to node {} failed, but cluster configuration is available", currentNode);
                    // 这里不尝试重连，只返回验证失败
                    // 重连将在下一次调用makeObject()时尝试其他节点
                }
            }
            return isValid;
        } catch (Exception e) {
            LOGGER.warn("Error validating database connection: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 激活一个数据库连接包装器对象，使其准备好被使用
     * 当对象从池中借出前调用
     * 此方法在每次从连接池获取连接时都会被调用
     *
     * @param p 要激活的对象
     * @throws Exception 如果激活过程中发生错误
     */
    @Override
    public void activateObject(PooledObject<DatabaseWrapper> p) throws Exception {
        if (p == null || p.getObject() == null) {
            return;
        }
    }

    /**
     * 钝化一个数据库连接包装器对象，使其准备好返回池中
     * 当对象返回池中前调用
     *
     * @param p 要钝化的对象
     * @throws Exception 如果钝化过程中发生错误
     */
    @Override
    public void passivateObject(PooledObject<DatabaseWrapper> p) throws Exception {
        if (p == null || p.getObject() == null) {
            return;
        }

        DatabaseWrapper wrapper = p.getObject();
        try {
            // 清理临时状态，但保持连接打开
            wrapper.clearTemporaryState();
        } catch (Exception e) {
            LOGGER.warn("Error passivating database connection: {}", e.getMessage());
            // 不抛出异常，避免影响池的其他操作
        }
    }
}
