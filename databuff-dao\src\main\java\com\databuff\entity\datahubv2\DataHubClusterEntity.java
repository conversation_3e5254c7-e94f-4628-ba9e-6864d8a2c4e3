package com.databuff.entity.datahubv2;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.databuff.handler.TimestampToLongTypeHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

/**
 * DataHub集群信息
 */
@TableName(value = "dc_databuff_datahub_cluster", autoResultMap = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataHubClusterEntity {
    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("集群名称")
    @TableField(value = "cluster_name")
    private String clusterName;

    @ApiModelProperty("集群描述")
    @TableField(value = "remark")
    private String remark;

    @ApiModelProperty("集群是否启用")
    @TableField(value = "enabled")
    private Integer enabled;

    @ApiModelProperty("集群内可分配的端口")
    @TableField(value = "port")
    private Integer port;

    @ApiModelProperty("是否删除")
    @TableField(value = "is_delete")
    private Long isDeleted;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", typeHandler = TimestampToLongTypeHandler.class)
    private Long createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", typeHandler = TimestampToLongTypeHandler.class)
    private Long updateTime;

    @ApiModelProperty("创建用户id")
    @TableField(value = "create_user_id")
    private String createUserId;

    @ApiModelProperty("更新用户id")
    @TableField(value = "update_user_id")
    private String updateUserId;

    /* Getter 和 Setter 方法 */

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getClusterName() {
        return clusterName;
    }

    public void setClusterName(String clusterName) {
        this.clusterName = clusterName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public Long getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Long isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public String getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(String updateUserId) {
        this.updateUserId = updateUserId;
    }
}
