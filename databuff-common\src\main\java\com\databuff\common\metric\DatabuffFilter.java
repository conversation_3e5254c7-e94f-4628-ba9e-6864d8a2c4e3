package com.databuff.common.metric;

import com.databuff.common.audit.AuditParamUtil;
import com.databuff.common.constants.Constant;
import com.databuff.common.utils.OtelMetricUtil;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static com.databuff.common.constants.Constant.HOST;

public class DatabuffFilter implements Filter {

    private static final String HTTP_REQUEST = "http.request";
    private static final String HTTP_REQUEST_FAILED = "http.request.failed";
    private boolean addAgentHostTag = true;

    public DatabuffFilter(boolean addAgentHostTag) {
        this.addAgentHostTag = addAgentHostTag;
    }

    public DatabuffFilter() {
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        if (!(servletRequest instanceof HttpServletRequest)) {
            filterChain.doFilter(servletRequest, servletResponse);
            return;
        }
        HttpServletRequest httpServletRequest = (HttpServletRequest) servletRequest;
        String url = httpServletRequest.getRequestURI();
        if (StringUtils.isEmpty(url)) {
            url = "/";
        }
        Map<String, String> tags = new HashMap<>();
        tags.put("type", url);
        if (addAgentHostTag) {
            String agentHost = httpServletRequest.getHeader(Constant.AGENT_HOSTNAME2) == null ? httpServletRequest.getHeader(Constant.AGENT_HOSTNAME) : httpServletRequest.getHeader(Constant.AGENT_HOSTNAME2);
            if (StringUtils.isEmpty(agentHost)) {
                agentHost = httpServletRequest.getHeader(HOST);
            }
            if (StringUtils.isNotEmpty(agentHost)) {
                tags.put("agentHost", agentHost);
            }
        }
        long start = System.currentTimeMillis();
        try {
            filterChain.doFilter(servletRequest, servletResponse);
        } catch (Throwable e) {
            OtelMetricUtil.logCounter(HTTP_REQUEST_FAILED, tags, 1);
            throw e;
        } finally {
            long duration = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(HTTP_REQUEST, tags, duration);
            AuditParamUtil.remove();
        }
    }

    @Override
    public void destroy() {

    }
}
