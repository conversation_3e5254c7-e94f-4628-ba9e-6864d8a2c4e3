package com.databuff.common.tsdb.factory;

import com.databuff.common.tsdb.TSDBReader;
import com.databuff.common.tsdb.TSDBWriter;
import com.databuff.common.tsdb.config.TSDBRefreshScopeConfig;
import com.databuff.common.tsdb.impl.TSDBReaderImpl;
import com.databuff.common.tsdb.impl.TSDBWriterImpl;
import com.databuff.common.tsdb.pool.TSDBConnectPool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * TSDB操作工厂类
 * 用于创建读写操作实例
 */
@Component
public class TSDBOperatorFactory {

    private final TSDBConnectPool tsdbConnectPool;
    private final TSDBRefreshScopeConfig tsdbRefreshScopeConfig;

    @Autowired
    public TSDBOperatorFactory(
            @Autowired(required = false) TSDBConnectPool tsdbConnectPool,
            @Autowired(required = false) TSDBRefreshScopeConfig tsdbRefreshScopeConfig) {
        this.tsdbConnectPool = tsdbConnectPool;
        this.tsdbRefreshScopeConfig = tsdbRefreshScopeConfig;
    }

    /**
     * 创建读操作实例
     *
     * @return 读操作实例
     */
    public TSDBReader createReader() {
        return new TSDBReaderImpl(tsdbConnectPool, tsdbRefreshScopeConfig);
    }

    /**
     * 创建写操作实例
     *
     * @return 写操作实例
     */
    public TSDBWriter createWriter() {
        return new TSDBWriterImpl(tsdbConnectPool);
    }
}
