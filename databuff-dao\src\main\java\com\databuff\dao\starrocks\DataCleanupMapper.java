package com.databuff.dao.starrocks;


import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DataCleanupMapper {

    List<Object> selectExpiredSpuids(@Param("tableName") String tableName,
                                     @Param("expireTimestamp") long expireTimestamp,
                                     @Param("limit") int limit);

    void deleteRecordsBySpuids(@Param("tableName") String tableName,
                               @Param("spuids") List<Object> spuids);

}
