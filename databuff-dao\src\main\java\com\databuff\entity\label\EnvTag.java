package com.databuff.entity.label;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

@Data
public class EnvTag {
    @TableId(type = IdType.AUTO)
    private String id;
    private String tagKey;    // 标签英文名
    private String tagName;   // 标签中文名
    private Boolean isEnvTag; // 是否环境标签
    private Date createTime;
    private Date updateTime;
}
